async:
  core-pool-size: 60
#  queue-capacity: 60
#  max-pool-size: 60
aws:
  credentials:
    access-key:
    secret-key:
    salt:
  ses:
    host: email-smtp.ap-northeast-1.amazonaws.com
    port: 587
    username:
    password:
  s3:
    kyc-bucket:
      name: 
    year-report-bucket:
      name:
cloud:
  aws:
    credentials:
      access-key:
      secret-key:
      use-default-aws-credentials-chain: false
    region:
      auto: false
      static: ap-northeast-1
    stack:
      auto: false
common:
  log:
    outputRequestDetails: true
    console:
      appender: CONSOLE_DEFAULT
point-app:
  no-reply-email:
  support-email:
  allowed-origin: http://localhost:3000
  security:
    enable-invest-login-whitelist: true
point-common:
  account-lock:
    max-attempt: 5 # negative means max-value
    expire-seconds: 0 # negative means max-value
  sns:
    expired_minute: 60 # SMS認証コード有効時(分単位)
management:
  server:
    port: 8082
  endpoint:
    health:
      group:
        liveness:
          include: "livenessState"
        readiness:
          include: "readinessState,manual"
      probes:
        enabled: true
  endpoints:
    metrics:
      enabled: false
    prometheus:
      enabled: false
    web:
      base-path: /actuator
      exposure:
        include: "*"
  metrics:
    export:
      cloudwatch:
        batchSize: 20
        enabled: false
        namespace: point-app
        step: 1m
      prometheus:
        enabled: false
    web:
      server:
        request:
          autotime:
            # https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html#production-ready-metrics-spring-mvc
            enabled: true
          metric-name: http.server.requests
postcode:
  url:
  api-key:
server:
  port: 8080
  shutdown: graceful
spring:
  config:
    domain: localhost:8080
    environment: local
  data:
    redis:
      host: point-001.9srtan.0001.apne1.cache.amazonaws.com
      port: 6379
  datasource:
    master:
      driver-class-name: org.mariadb.jdbc.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.MySQL5Dialect
      packages-to-scan: point.common.entity,point.pos.entity,point.operate.entity
      url: # migrated to AWS Secrets Manager
      username: point
      password:
      minimum-idle: 10
      maximum-pool-size: 66
      max-lifetime: 600000
      idle-timeout: 500000
      leak-detection-threshold: 5000
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
      connection-test-query: select 1
      auto-commit: false
      hibernate-dialect: org.hibernate.dialect.PostgreSQL82Dialect
      packages-to-scan: point.admin.entity,point.common.entity,point.pos.entity
      url: # migrated to AWS Secrets Manager
      username: point
      password:
      minimum-idle: 10
      maximum-pool-size: 66
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
    show-sql: true
  main:
    banner-mode: off
  mvc:
    throw-exception-if-no-handler-found: true
  recaptcha:
    url: https://www.google.com/recaptcha/api/siteverify
    secret-key: # store secrets in AWS Secrets Manager
  servlet:
    multipart:
      max-file-size: 16MB
      max-request-size: 17MB
  session:
    store-type: redis
  web:
    resources:
      add-mappings: false

jwt:
  issuer: bs-point-server
  secret: **********   #  migrated to AWS Secrets Manager
  token-ttl: 7600
  refresh-token-ttl: 604800
  cache-token-ttl: 604800
mail:
  message:
    account-created:
      key: ACCOUNT-CREATED
      base-url:
    forgot-login-password:
      key: FORGOT-LOGIN-PASSWORD
      base-url:
customer:
  login-password:
    token:
      effective-time: ********
  register:
    enabled: true
  forgot-password:
    token:
      forgot-effective-time: 600000
  fiat-withdrawal:
    user-daily-withdrawal-limit: ********

# store secrets in AWS Secrets Manager
sms:
  host:
  token:

# unused
ekyc:
  secret:
  token:
  api-auth-key:
  enterprise-id:
  transition-url:
  url:
    applicant-id:
    ekyc-url:
    bpo-result:

# store secrets in AWS Secrets Manager
refinitiv:
  host:
  api-key:
  secret-key:
  group-id:

swagger:
  enabled:

gmo:
  holder-name-kana:
  client-id:
  secret:
  stg-base-endpoint:
  redirect-uri:
  authorization-token:
  accounts-uri:
  issue-account-uri:
  refresh-token-interval:
  scope:

coin:
  cus:
    host:
    host-external:

point-pos:
  best-price:
    amber:
      api-host:
      api-path: /api/v2/trade/rfq
      access-key:
      access-secret:
  base-trade:
    amber:
      api-host:
  price-range-percent: 0.0005

exchange-websocket:
  subscription-limit-per-session: 100 # total subscription number for each client session
  redis-pubsub-subscriber:
    enabled: true # true: the webserver works as subscriber for redis pubsub message
  redis-pubsub-cache:
    enabled: true # true: cache the message to avoid duplicate sending
    expire-in-minutes: 5 # cache will be expired in the specified period, the message will be sent even duplicate
  stomp-broker: # external broker config
    enabled: false # Notice: true means using external broker - rabbitmq, DO NOT CHANGE after go-live
    host: localhost
    port: 61613
    client-login: guest
    client-passcode: guest
    single-consumer-lock-expired-in-seconds: 5
    heartbeat-client-send-interval-in-milliseconds: 10000 # only available when the stomp-broker.enabled = false - simp broker, change WebSocketConfig to support configuration external broker
    heartbeat-server-send-interval-in-milliseconds: 20000 # only available when the stomp-broker.enabled = false - simp broker, change WebSocketConfig to support configuration external broker
    # when external broker is enabled, the message coming from redis pubsub will be consumed by all subscribers in cluster, those subscribers will send the duplicate messages to client.
    # we make sure there will be only one subscriber sending the message who can acquire the single consumer lock.
    # the lock will be expired in 5 seconds.
    # if the message are generated as the same during the 5 seconds, only the 1st message will be sent to client, the following duplicate messages will be blocked during the lock period.
logging:
  level:
    com:
      amazonaws:
        internal:
          InstanceMetadataServiceResourceFetcher: error
        util:
          EC2MetadataUtils: error

ponta:
  login-url:
  callback-url:
  login-success-url:
  login-failure-url:
  partner-number:
  credentials:
  if1507-Url:
  if1511-Url:
  webIf-Url:
  otp-ttl: 5 # minute
  game-home-url:
  game-login-success-url:
  game-login-failure-url:
  keystore-password:  # migrated to AWS Secrets Manager
  keystore-biz-password:  # migrated to AWS Secrets Manager
  transfer-fee: 0.01
springdoc:
  swagger-ui:
    path: /app/swagger-ui.html
    enabled: true
  api-docs:
    path: /app/v3/api-docs
    enabled: true
  servers:
    - url:
  packages-to-scan: