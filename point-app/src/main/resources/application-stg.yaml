aws:
  s3:
    kyc-bucket:
      name: kyc.bs-point-stg
    year-report-bucket:
      name: year-report.bs-point-stg
cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
point-app:
  support-email: <EMAIL>
  allowed-origin: https://stg.backseat-service.com
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
server:
  servlet:
    session:
      cookie:
        secure: true
spring:
  config:
    domain: stg.backseat-service.com
    environment: stg
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
mail:
  message:
    account-created:
      key: ACCOUNT-CREATED
      base-url: https://stg.backseat-service.com/invest/?cid={0}&token={1}&exp={2}
    forgot-login-password:
      key: FORGOT-LOGIN-PASSWORD
      base-url: https://stg.backseat-service.com/signIn/reset/?cid={0}&token={1}&exp={2}

logging:
  level:
    root: info
    com:
      zaxxer:
        hikari:
          HikariConfig: DEBUG
          pool:
            HikariPool: DEBUG
coin:
  cus:
    host: https://stg.backseat-service.com
    host-external: https://stg.backseat-service.com

ponta:
  login-url: https://st.ponta.jp/front/LWAS900/SLWAS900110.htm # ponta login screen
  callback-url: /oauth
  login-success-url: https://stg.backseat-service.com/oauth/callback/
  login-failure-url: https://stg.backseat-service.com/oauth/callback/
  partner-number: BC202502_00001
  credentials:
  if1507-Url: https://onlineit102.lw.loyalty.co.jp:28443/ifs/LWIF1507
  if1511-Url: https://onlineit102.lw.loyalty.co.jp:28443/ifs/LWIF1511
  webIf-Url: https://onlinest01.lp.loyalty.co.jp:8443/webif/bizinvoker
  otp-ttl: 5 # minute
  game-home-url: https://stg.backseat-service.com/farm-game/
  game-login-success-url: https://stg.backseat-service.com/farm-game/farm/
  game-login-failure-url: https://stg.backseat-service.com/farm-game/

point-pos:
  best-price:
    amber:
      api-host: https://be-alpha.whalefin.com
  base-trade:
    amber:
      api-host: https://be-alpha.whalefin.com

swagger:
  enabled: true
springdoc:
  swagger-ui:
    enabled: true
  servers:
    - url: https://stg.backseat-service.com/
  packages-to-scan: point.app.invest.controller,point.app.invest.controller.pontaApi, point.app.operate.controller, point.app.operate.controller.pontaApi,point.app.game.controller