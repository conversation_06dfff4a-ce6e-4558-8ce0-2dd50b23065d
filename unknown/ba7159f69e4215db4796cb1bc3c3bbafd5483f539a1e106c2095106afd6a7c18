package point.common.constant;

public enum PosOrderStatus {
    WAITING, // 処理待ち
    FAILED_FILLED, // 注文失効
    PARTIALLY_FILLED, // 部分約定
    FULLY_FILLED; // 全約定

    public static PosOrderStatus valueOfName(String name) {
        for (PosOrderStatus orderStatus : values()) {
            if (orderStatus.name().equals(name)) {
                return orderStatus;
            }
        }
        return null;
    }

    public static PosOrderStatus[] INACTIVE_ORDER_STATUSES =
            new PosOrderStatus[] {FULLY_FILLED, FAILED_FILLED};
}
