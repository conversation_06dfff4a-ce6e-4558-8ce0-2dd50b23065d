package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(BcOrderCurrencySplit.class)
public abstract class BcOrderCurrencySplit_ extends AbstractEntity_ {

    public static volatile SingularAttribute<BcOrderCurrencySplit, Long> symbolId;
    public static volatile SingularAttribute<BcOrderCurrencySplit, BigDecimal> amount;
    public static volatile SingularAttribute<BcOrderCurrencySplit, Long> orderId;
    public static volatile SingularAttribute<BcOrderCurrencySplit, Long> tradeId;

    private static final String SYMBOL_ID = "symbolId";
    private static final String AMOUNT = "amount";
    private static final String ORDER_ID = "orderId";
    private static final String TRADE_ID = "tradeId";
}
