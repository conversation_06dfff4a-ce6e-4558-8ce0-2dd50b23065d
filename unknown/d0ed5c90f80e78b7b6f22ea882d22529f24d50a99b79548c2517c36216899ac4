package point.common.component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import point.common.exception.CustomException;
import point.common.util.CharsetUtil;

@RequiredArgsConstructor
@Component
@Slf4j
public class CsvDownloadManager<T> {

    protected String download(
            HttpServletResponse response,
            byte[] bytes,
            String fileNamePrefix,
            CharsetUtil charsetUtil)
            throws Exception {
        String fileName = fileNamePrefix + ".csv";
        response.addHeader("Content-disposition", "attachment;filename=" + fileName);
        response.setCharacterEncoding(charsetUtil.getCode());
        response.setContentType(MediaType.TEXT_PLAIN_VALUE);
        response.setContentLength(bytes.length);

        log.info("****************assetSummaryLog IOUtils.copy Start********************");
        try {
            IOUtils.copy(new ByteArrayInputStream(bytes), response.getOutputStream());
            response.flushBuffer();
        } catch (IOException e) {
            throw new CustomException(e);
        }
        log.info("****************assetSummaryLog IOUtils.copy End********************");
        return fileName;
    }

    /*
     * 任意フォーマットのCSVをダウンロードしたい場合はこちら
     */
    public void download(
            HttpServletResponse response, StringWriter stringWriter, String fileNamePrefix)
            throws Exception {
        download(
                response,
                stringWriter.toString().getBytes(CharsetUtil.MS932.getCharset()),
                fileNamePrefix,
                CharsetUtil.MS932);
    }

    public void download(HttpServletResponse response, String string, String fileNamePrefix)
            throws Exception {
        StringWriter stringWriter = new StringWriter();
        stringWriter.write(string);
        download(response, stringWriter, fileNamePrefix);
    }

    /*
     * model.responseとかをそのままCSV化したい場合はこちら
     */
    public void download(
            HttpServletResponse response,
            List<T> baseData,
            String fileNamePrefix,
            boolean needsHeader)
            throws Exception {
        download(response, convertToCsv(baseData, needsHeader), fileNamePrefix);
    }

    private StringWriter convertToCsv(List<T> baseList, boolean needsHeader)
            throws JsonProcessingException {
        StringWriter stringWriter = new StringWriter();
        if (baseList.size() > 0) {
            T entry = baseList.get(0);
            CsvMapper mapper = new CsvMapper();
            CsvSchema schema = mapper.schemaFor(entry.getClass());
            if (needsHeader) {
                schema = schema.withHeader();
            }
            stringWriter.write(mapper.writer(schema).writeValueAsString(baseList));
        }
        return stringWriter;
    }

    public void download(
            HttpServletResponse response,
            List<T> baseData,
            String fileNamePrefix,
            String customHeader)
            throws Exception {
        download(response, convertToCsv(baseData, customHeader), fileNamePrefix);
    }

    private StringWriter convertToCsv(List<T> baseList, String customHeader)
            throws JsonProcessingException {
        StringWriter stringWriter = new StringWriter();
        if (baseList.size() > 0) {
            T entry = baseList.get(0);
            CsvMapper mapper = new CsvMapper();
            CsvSchema schema = mapper.schemaFor(entry.getClass());
            if (!StringUtils.right(customHeader, 0).equals("\n")) {
                customHeader = customHeader + "\n";
            }
            stringWriter.write(customHeader + mapper.writer(schema).writeValueAsString(baseList));
        }
        return stringWriter;
    }
}
