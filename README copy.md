# cb-server

## Eclipse や IntelliJ を起動する時の作業

1. local.env を生成します。

この作業は初回時、もしくは dev 環境の Secrets Manager が更新されたときに行います。

```
cd cb-server
./scripts/create_local_env.sh
```

2. 現在のシェルで local.env を読み込み環境変数を設定したあとで Eclipse IntelliJ などを起動します。

Eclipse
```
source local.env
open -n /Applications/Eclipse_2021-12.app
```

IntelliJ
```
source local.env
open -n '/Applications/IntelliJ IDEA.app'
```

## 起動方法

rootフォルダから起動
```
gradle build  # 全部ビルドしたいとき。
# exchange-app を起動
gradle :exchange-app:bootRun --args='--spring.profiles.active=local ALL'
```

各プロジェクトのフォルダから起動
```
cd exchange-app
gradle bootRun --args='--spring.profiles.active=local ALL'
```


## 元のプロジェクトに対する変更
- submoduleは、src/main/javaフォルダと、設定ファイルのプロジェクトに変更
- exchange-common と exchange-spotが循環参照になっているので、 common にまとめる。

### プロジェクトに依存関係の追加
必要なプロジェクトを下記のように追加する
```
dependencies {
  implementation project(':exchange-common')
  ...省略...
```

### ライブラリ用プロジェクトは、下記の設定が必要 

build.gradle
```
plugins {
  id 'org.springframework.boot' version '2.5.2' apply false
  id 'io.spring.dependency-management' version '1.0.11.RELEASE'
  // ... other plugins
}

dependencyManagement {
  imports {
    mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
  }
}
```

## 参考
https://spring.io/guides/gs/multi-module/
