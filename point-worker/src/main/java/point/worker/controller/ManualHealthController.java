package point.worker.controller;

import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.web.annotation.RestControllerEndpoint;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import point.worker.component.ManualHealthHolder;

@Component
@RestControllerEndpoint(id = "custom")
public class ManualHealthController {

    private static final Logger log = LoggerFactory.getLogger(ManualHealthController.class);

    @Autowired private ManualHealthHolder manualHealthHolder;

    @GetMapping("/health")
    public String isHealthy() {
        if (manualHealthHolder.isHealthy()) {
            return "Healthy";
        }
        return "Unhealthy";
    }

    @GetMapping("/health/healthy")
    public ResponseEntity<Object> healthy(
            HttpServletRequest request, @RequestParam(name = "wait", required = false) Integer wait)
            throws Exception {
        manualHealthHolder.healthy();
        log.info("set healthy");
        if (wait != null) {
            try {
                Thread.sleep(wait);
            } catch (InterruptedException e) {
            }
        }
        return ResponseEntity.ok().build();
    }

    @GetMapping("/health/unhealthy")
    public ResponseEntity<Object> unhealthy(
            HttpServletRequest request, @RequestParam(name = "wait", required = false) Integer wait)
            throws Exception {
        manualHealthHolder.unhealthy();
        log.info("set unhealthy");
        if (wait != null) {
            try {
                Thread.sleep(wait);
            } catch (InterruptedException e) {
            }
        }
        return ResponseEntity.ok().build();
    }
}
