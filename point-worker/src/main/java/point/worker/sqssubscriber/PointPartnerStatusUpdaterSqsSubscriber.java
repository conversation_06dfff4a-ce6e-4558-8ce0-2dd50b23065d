package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.PointPartnerStatusUpdater;

@Component
public class PointPartnerStatusUpdaterSqsSubscriber
        extends SqsSubscriber<PointPartnerStatusUpdater> {

    @Override
    public Class<PointPartnerStatusUpdater> getWorkerClass() {
        return PointPartnerStatusUpdater.class;
    }
}
