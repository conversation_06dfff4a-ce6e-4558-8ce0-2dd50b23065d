package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.AssetSummaryRecalculator;

@Component
public class AssetSummaryRecalculatorSqsSubscriber extends SqsSubscriber<AssetSummaryRecalculator> {

    @Override
    public Class<AssetSummaryRecalculator> getWorkerClass() {
        return AssetSummaryRecalculator.class;
    }
}
