package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.ExchangeSummaryCalculator;

@Component
public class ExchangeSummarySqsSubscriber extends SqsSubscriber<ExchangeSummaryCalculator> {

    @Override
    public Class<ExchangeSummaryCalculator> getWorkerClass() {
        return ExchangeSummaryCalculator.class;
    }
}
