package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.OperateAssetSummaryCalculator;

@Component
public class OperateAssetSummaryCalculatorSqsSubscriber
        extends SqsSubscriber<OperateAssetSummaryCalculator> {

    @Override
    public Class<OperateAssetSummaryCalculator> getWorkerClass() {
        return OperateAssetSummaryCalculator.class;
    }
}
