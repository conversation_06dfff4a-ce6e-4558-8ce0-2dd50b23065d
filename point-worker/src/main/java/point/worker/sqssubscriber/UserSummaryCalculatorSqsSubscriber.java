package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.UserSummaryCalculator;

@Component
public class UserSummaryCalculatorSqsSubscriber extends SqsSubscriber<UserSummaryCalculator> {

    @Override
    public Class<UserSummaryCalculator> getWorkerClass() {
        return UserSummaryCalculator.class;
    }
}
