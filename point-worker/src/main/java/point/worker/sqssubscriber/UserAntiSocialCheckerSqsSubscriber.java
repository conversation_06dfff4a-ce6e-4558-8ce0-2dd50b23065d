package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.UserAntiSocialChecker;

@Component
public class UserAntiSocialCheckerSqsSubscriber extends SqsSubscriber<UserAntiSocialChecker> {

    @Override
    public Class<UserAntiSocialChecker> getWorkerClass() {
        return UserAntiSocialChecker.class;
    }
}
