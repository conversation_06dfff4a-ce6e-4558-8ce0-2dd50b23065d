package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.ChoicePowerCalculator;

@Component
public class ChoicePowerCalculatorSqsSubscriber extends SqsSubscriber<ChoicePowerCalculator> {

    @Override
    public Class<ChoicePowerCalculator> getWorkerClass() {
        return ChoicePowerCalculator.class;
    }
}
