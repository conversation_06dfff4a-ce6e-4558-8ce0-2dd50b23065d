package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.BulkTransferExecutor;

@Component
public class BulkTransferExecutorSqsSubscriber extends SqsSubscriber<BulkTransferExecutor> {

    @Override
    public Class<BulkTransferExecutor> getWorkerClass() {
        return BulkTransferExecutor.class;
    }
}
