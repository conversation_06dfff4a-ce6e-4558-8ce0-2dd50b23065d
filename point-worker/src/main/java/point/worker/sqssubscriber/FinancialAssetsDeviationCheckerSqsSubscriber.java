package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.FinancialAssetsDeviationChecker;

@Component
public class FinancialAssetsDeviationCheckerSqsSubscriber
        extends SqsSubscriber<FinancialAssetsDeviationChecker> {

    @Override
    public Class<FinancialAssetsDeviationChecker> getWorkerClass() {
        return FinancialAssetsDeviationChecker.class;
    }
}
