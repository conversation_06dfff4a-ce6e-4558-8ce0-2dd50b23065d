package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.BulkTransferResultChecker;

@Component
public class BulkTransferResultCheckerSqsSubscriber
        extends SqsSubscriber<BulkTransferResultChecker> {

    @Override
    public Class<BulkTransferResultChecker> getWorkerClass() {
        return BulkTransferResultChecker.class;
    }
}
