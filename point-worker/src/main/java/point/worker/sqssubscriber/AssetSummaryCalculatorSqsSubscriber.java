package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.AssetSummaryCalculator;

@Component
public class AssetSummaryCalculatorSqsSubscriber extends SqsSubscriber<AssetSummaryCalculator> {

    @Override
    public Class<AssetSummaryCalculator> getWorkerClass() {
        return AssetSummaryCalculator.class;
    }
}
