package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.UserEkycBpoResultUpdater;

@Component
public class UserEkycBpoResultUpdaterSqsSubscriber extends SqsSubscriber<UserEkycBpoResultUpdater> {

    @Override
    public Class<UserEkycBpoResultUpdater> getWorkerClass() {
        return UserEkycBpoResultUpdater.class;
    }
}
