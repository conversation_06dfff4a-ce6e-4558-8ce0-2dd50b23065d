package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.UserVoteRewardExpireChecker;

@Component
public class UserVoteRewardExpireSqsSubscriber extends SqsSubscriber<UserVoteRewardExpireChecker> {

    @Override
    public Class<UserVoteRewardExpireChecker> getWorkerClass() {
        return UserVoteRewardExpireChecker.class;
    }
}
