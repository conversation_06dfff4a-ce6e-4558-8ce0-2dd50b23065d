package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.HighValueTraderChecker;

@Component
public class HighValueTraderCheckerSqsSubscriber extends SqsSubscriber<HighValueTraderChecker> {

    @Override
    public Class<HighValueTraderChecker> getWorkerClass() {
        return HighValueTraderChecker.class;
    }
}
