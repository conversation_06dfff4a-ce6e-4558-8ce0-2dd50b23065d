package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.PointUserSummaryCalculator;

@Component
public class PointUserSummaryCalculatorSqsSubscriber
        extends SqsSubscriber<PointUserSummaryCalculator> {

    @Override
    public Class<PointUserSummaryCalculator> getWorkerClass() {
        return PointUserSummaryCalculator.class;
    }
}
