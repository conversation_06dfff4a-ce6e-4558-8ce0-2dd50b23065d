package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.OperateExchangeSummaryCalculator;

@Component
public class OperateExchangeSummarySqsSubscriber
        extends SqsSubscriber<OperateExchangeSummaryCalculator> {

    @Override
    public Class<OperateExchangeSummaryCalculator> getWorkerClass() {
        return OperateExchangeSummaryCalculator.class;
    }
}
