package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.ChoicePowerMonthlyCalculator;

@Component
public class ChoicePowerMonthlyCalculatorSqsSubscriber
        extends SqsSubscriber<ChoicePowerMonthlyCalculator> {

    @Override
    public Class<ChoicePowerMonthlyCalculator> getWorkerClass() {
        return ChoicePowerMonthlyCalculator.class;
    }
}
