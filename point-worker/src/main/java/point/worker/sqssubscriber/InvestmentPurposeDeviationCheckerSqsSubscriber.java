package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.InvestmentPurposeDeviationChecker;

@Component
public class InvestmentPurposeDeviationCheckerSqsSubscriber
        extends SqsSubscriber<InvestmentPurposeDeviationChecker> {

    @Override
    public Class<InvestmentPurposeDeviationChecker> getWorkerClass() {
        return InvestmentPurposeDeviationChecker.class;
    }
}
