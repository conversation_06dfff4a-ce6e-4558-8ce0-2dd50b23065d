package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.UserVoteRewardCalculator;

@Component
public class UserVoteRewardCalculatorSqsSubscriber extends SqsSubscriber<UserVoteRewardCalculator> {

    @Override
    public Class<UserVoteRewardCalculator> getWorkerClass() {
        return UserVoteRewardCalculator.class;
    }
}
