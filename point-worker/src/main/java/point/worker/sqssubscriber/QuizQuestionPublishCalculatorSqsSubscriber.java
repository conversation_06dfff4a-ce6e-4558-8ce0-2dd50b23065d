package point.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import point.worker.component.SqsSubscriber;
import point.worker.worker.QuizQuestionPublishUpdater;

@Component
public class QuizQuestionPublishCalculatorSqsSubscriber
        extends SqsSubscriber<QuizQuestionPublishUpdater> {

    @Override
    public Class<QuizQuestionPublishUpdater> getWorkerClass() {
        return QuizQuestionPublishUpdater.class;
    }
}
