package point.worker.component;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import point.common.component.RedisManager;
import point.common.service.WorkerMasterService;

public abstract class SqsSubscriber<W extends Worker> implements Runnable {

    private static final int MAX_NUMBER_OF_MESSAGES = 10;

    @Autowired protected RedisManager redisManager;

    @Autowired protected SqsManager sqsManager;

    @Autowired protected WorkerMasterService workerMasterService;

    @Autowired protected SqsBeanName sqsBeanName;

    public abstract Class<W> getWorkerClass();

    public String createQueueName() {
        return sqsBeanName.toQueueName(getWorkerClass());
    }

    @Override
    public void run() {
        String queueName = createQueueName();

        MDC.put("queueName", queueName);

        try {
            sqsManager.subscribe(queueName, MAX_NUMBER_OF_MESSAGES);
        } finally {
            MDC.remove("queueName");
        }
    }
}
