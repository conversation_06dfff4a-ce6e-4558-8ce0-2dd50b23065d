package point.worker.worker;

import com.ibm.icu.text.Transliterator;
import io.netty.util.internal.StringUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import point.common.entity.FiatWithdrawal;
import point.common.entity.Symbol;
import point.common.exception.GmoRequestException;
import point.common.model.request.BulkTransfer;
import point.common.model.request.BulkTransferToUpd;
import point.common.model.response.BulkTransferRequestResponse;
import point.common.service.FiatWithdrawalService;
import point.common.service.GmoService;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.worker.component.Worker;

@Component
@RequiredArgsConstructor
@Slf4j
public class BulkTransferExecutor extends Worker {

    private final FiatWithdrawalService fiatWithdrawalService;
    private final GmoService gmoService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {

        log.info(
                " ==============================BulkTransferExecutor start==============================");
        log.info("総合振込は開始しました。");

        // 総合振込依頼結果と状況照会(前回依頼が期限切のデータを今回の実行対象とする)
        bulkTransferResultAndStatus();

        // 振込依頼を実施
        Date now = new Date();
        String today = FormatUtil.format(now, FormatPattern.YYYYMMDD);
        List<FiatWithdrawal> todayFiatWithdrawals =
                fiatWithdrawalService.findLikeTodaysApplyNo(today);
        // 今日依頼既にある場合、再実施不要
        if (todayFiatWithdrawals == null || todayFiatWithdrawals.size() == 0) {
            List<BulkTransfer> bulkTransferList = new ArrayList<>();
            List<Long> waittingList = new ArrayList<>();
            List<BulkTransferToUpd> bulkTransferIDList = new ArrayList<BulkTransferToUpd>();
            BigDecimal totalAmount =
                    getBulkTransferList(bulkTransferList, waittingList, bulkTransferIDList);

            if (waittingList.size() > 0) {
                // 待機中
                for (Long id : waittingList) {
                    fiatWithdrawalService.applyWaitting(id);
                }
            }

            if (bulkTransferList.size() > 0) {
                ResponseEntity<BulkTransferRequestResponse> bulkTransferRequestResponse =
                        gmoService.bulkTransfer(totalAmount, bulkTransferList);

                if (bulkTransferRequestResponse != null) {
                    if (bulkTransferRequestResponse.getBody().getApplyNo() != null) {
                        String applyNo = bulkTransferRequestResponse.getBody().getApplyNo();
                        // 依頼完了
                        for (BulkTransferToUpd bulkTransferToUpd : bulkTransferIDList) {
                            fiatWithdrawalService.updateApplyNo(
                                    bulkTransferToUpd.getId(),
                                    applyNo,
                                    bulkTransferToUpd.getItemId());
                        }
                    }
                }
            }
        }

        log.info(
                " ==============================BulkTransferExecutor end==============================");
        log.info("総合振込は終了しました。");
    }

    /**
     * 総合振込依頼結果と状況照会
     *
     * @throws Exception
     */
    private void bulkTransferResultAndStatus() throws Exception {
        List<String> applyNoList = new ArrayList<>();
        List<FiatWithdrawal> bulkTransferResultTargets =
                fiatWithdrawalService.findBulkTransferResultTarget();
        String compareApplyNo = "";
        for (FiatWithdrawal fiatWithdrawal : bulkTransferResultTargets) {
            if (!compareApplyNo.equals(fiatWithdrawal.getApplyNo())) {
                compareApplyNo = fiatWithdrawal.getApplyNo();
                applyNoList.add(fiatWithdrawal.getApplyNo());
            }
        }

        // 総合振込依頼結果照会
        for (String applyNo : applyNoList) {
            String resultCode = gmoService.getBulktransferRequestResult(applyNo);

            if ("8".equals(resultCode)) {
                // 依頼が期限切の場合、受付番号をクリア、次回の振込依頼を待ち
                List<FiatWithdrawal> fiatWithdrawalsByApplyNo =
                        fiatWithdrawalService.findAllByApplyNo(applyNo);
                for (FiatWithdrawal clearInfo : fiatWithdrawalsByApplyNo) {
                    fiatWithdrawalService.updateApplyNo(
                            clearInfo.getId(), null, StringUtil.EMPTY_STRING);
                }
            }
        }
    }

    /**
     * 待機中と受付完了の出金依頼を取得
     *
     * @param bulkTransferList API用総合振込明細情報
     * @param waittingList ウェディングリスト
     * @param bulkTransferIDList 出金IDリスト
     * @return 合計金額
     * @throws GmoRequestException
     */
    private BigDecimal getBulkTransferList(
            List<BulkTransfer> bulkTransferList,
            List<Long> waittingList,
            List<BulkTransferToUpd> bulkTransferIDList)
            throws GmoRequestException {

        // 振込限度額を取得
        BigDecimal transferLimitAmount = new BigDecimal(gmoService.getTransferLimitAmount());
        // 合計金額
        BigDecimal totalAmount = new BigDecimal(0);
        BigDecimal compareAmount = new BigDecimal(0);
        List<FiatWithdrawal> fiatWithdrawalList = fiatWithdrawalService.findBulkTransferTarget();
        int itemId = 1;
        if (fiatWithdrawalList != null) {
            for (FiatWithdrawal fiatWithdrawal : fiatWithdrawalList) {
                // 振込金額
                BigDecimal transferAmount =
                        fiatWithdrawal.getAmount().subtract(fiatWithdrawal.getFee());
                compareAmount = totalAmount.add(transferAmount);
                // 振込限度額を超える場合
                if (transferLimitAmount.compareTo(compareAmount) < 0) {
                    waittingList.add(fiatWithdrawal.getId());
                    continue;
                }

                totalAmount = compareAmount;
                BulkTransfer bulkTransfer = new BulkTransfer();
                // 明細番号
                bulkTransfer.setItemId(String.valueOf(itemId));
                // 被仕向金融機関番号
                Integer bankCode = fiatWithdrawal.getBankAccount().getBank().getBankCode();
                bulkTransfer.setBeneficiaryBankCode(String.format("%04d", bankCode));
                // 被仕向支店番号
                Integer branchCode = fiatWithdrawal.getBankAccount().getBank().getBranchCode();
                bulkTransfer.setBeneficiaryBranchCode(String.format("%03d", branchCode));
                // 科目コード 1：普通、2：当座、4：貯蓄、9：その他
                bulkTransfer.setAccountTypeCode("1");
                // 口座番号
                bulkTransfer.setAccountNumber(
                        String.format(
                                "%07d",
                                Integer.valueOf(
                                        fiatWithdrawal.getBankAccount().getAccountNumber())));
                // 受取人名
                Transliterator fullToHalf = Transliterator.getInstance("Fullwidth-Halfwidth");
                String halfAccountName =
                        fullToHalf.transliterate(fiatWithdrawal.getBankAccount().getAccountName());
                bulkTransfer.setBeneficiaryName(StringUtils.substring(halfAccountName, 0, 30));
                // 振込金額
                bulkTransfer.setTransferAmount(transferAmount.stripTrailingZeros().toPlainString());

                bulkTransferList.add(bulkTransfer);
                BulkTransferToUpd bulkTransferToUpd = new BulkTransferToUpd();
                bulkTransferToUpd.setId(fiatWithdrawal.getId());
                bulkTransferToUpd.setItemId(String.valueOf(itemId));
                bulkTransferIDList.add(bulkTransferToUpd);
                itemId++;
            }
        }

        return totalAmount;
    }
}
