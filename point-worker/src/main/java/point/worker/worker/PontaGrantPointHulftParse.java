package point.worker.worker;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import point.common.component.CustomTransactionManager;
import point.common.entity.PontaHulftTaskStatus;
import point.common.entity.Symbol;
import point.common.service.PointTransferService;
import point.common.service.PontaHufltTaskStatusService;
import point.common.util.JsonUtil;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class PontaGrantPointHulftParse extends Worker {

    @Value("${hulft.rcvdata}")
    private String hulftRcvdata;

    @Value("${hulft.file_name_coll}")
    private String fileName;

    private static final Charset SHIFTJIS = Charset.forName("Shift_JIS");
    private static String header = "";
    private static String footer = "";
    private static final String SUCCESS_CODE_N = "N000000000";
    private static final String SUCCESS_CODE_B = "B";
    private static final String SUCCESS_CODE_M = "M";
    private final PointTransferService pointTransferService;
    private final CustomTransactionManager customTransactionManager;
    private static final List<Integer> INDEXES =
            List.of(
                    0, 4, 7, 8, 9, 19, 20, 40, 48, 54, 58, 61, 65, 68, 71, 74, 82, 90, 96, 98, 104,
                    110, 122, 135, 143, 163, 164, 166, 167, 175, 176, 178, 179, 187, 188, 190, 191,
                    199, 200, 208, 209, 214, 215, 223, 224, 232, 240, 241, 242, 250, 258, 264, 284,
                    287, 290, 298, 306, 312, 314, 320, 326, 338, 351, 359, 367, 374, 375, 377, 384,
                    385, 387, 394, 395, 397, 404, 405, 407, 414, 415, 417, 418, 458, 478);
    private final PontaHufltTaskStatusService pontaHufltTaskStatusService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("-----------------Hulft rcvdata Start ----------------");
        List<String> details = extractDetails(hulftRcvdata, fileName);
        PontaHulftTaskStatus pontaHufltTaskStatus =
                pontaHufltTaskStatusService.findByCondition(
                        PontaHulftTaskStatus.ParsingStatus.PENDING);
        if (details == null || details.isEmpty()) {
            if (pontaHufltTaskStatus != null) {
                pontaHufltTaskStatus.setErrorMessage(null);
                pontaHufltTaskStatus.setParsingEndTime(new Date());
                pontaHufltTaskStatus.setParsingStatus(PontaHulftTaskStatus.ParsingStatus.SUCCESS);
                pontaHufltTaskStatusService.save(pontaHufltTaskStatus);
            }
            deleteSourceFile(hulftRcvdata + fileName);
            return;
        }
        if (pontaHufltTaskStatus == null) {
            log.info("---------------Hulft rcvdata End status is null ----------------");
            return;
        } else if (pontaHufltTaskStatus.getSendContent() == null) {
            log.info("---------------Hulft rcvdata End content is null ----------------");
            return;
        }
        log.info("header:{}", header);
        List<Map<String, String>> resultMpaList = new ArrayList<>();
        try {
            log.info("details:{}", details);
            log.info("details size:{}", details.size());
            for (String detail : details) {
                log.info("detail:{}", detail);
                Map<String, String> dataMap = getStringStringMap(detail);
                resultMpaList.add(dataMap);
            }
            customTransactionManager.execute(
                    entityManager -> {
                        for (Map<String, String> stringStringMap : resultMpaList) {
                            String requestStatus =
                                    stringStringMap.get("field9"); // 実行ステータス ・N000000000　：正常
                            // ◆正常データ
                            // ・B:バッチデータのみでポイント反映
                            // ・M:リアルデータとバッチデータで照合済
                            // ※Gメンバ0ポイント利用の場合、
                            // リアルデータ＋バッチデータ連携時、バッチデータのみ連携時
                            // ともに「B」がセットされる
                            // ◆エラーデータ
                            // ・E:エラー（共通FMTエラーを除く実施可否でのエラー）
                            String collationType = stringStringMap.get("field19");
                            String partnerMemberId = stringStringMap.get("field143"); // 会員ID
                            String tradeNUmber = stringStringMap.get("field135"); // 取引番号 上り電文と同様
                            log.info("Hulft data: {}", JsonUtil.encode(stringStringMap));
                            if (SUCCESS_CODE_N.equals(requestStatus)
                                    && SUCCESS_CODE_B.equals(collationType)) {
                                pointTransferService.transferOperationToPontaSuccess(
                                        partnerMemberId, tradeNUmber, entityManager);
                            } else if (SUCCESS_CODE_N.equals(requestStatus)
                                    && SUCCESS_CODE_M.equals(collationType)) {
                                pointTransferService.transferOperationToPontaSuccess_m(
                                        partnerMemberId, tradeNUmber, entityManager);
                            } else {
                                pointTransferService.transferOperationToPontaFailed(
                                        partnerMemberId, tradeNUmber, requestStatus, entityManager);
                            }
                        }
                        pontaHufltTaskStatus.setErrorMessage(null);
                        pontaHufltTaskStatus.setParsingStatus(
                                PontaHulftTaskStatus.ParsingStatus.SUCCESS);
                        pontaHufltTaskStatusService.save(pontaHufltTaskStatus, entityManager);
                        deleteSourceFile(hulftRcvdata + fileName);
                    });
        } catch (Exception e) {
            log.error("Error occurred while parsing the file: {}", e.getMessage(), e);
            pontaHufltTaskStatus.setParsingStatus(PontaHulftTaskStatus.ParsingStatus.FAILED);
            pontaHufltTaskStatus.setErrorMessage(
                    "Failed to parse the file in HULFT. Check the detailed information in the point_transfer_status_history table.");
            pontaHufltTaskStatusService.save(pontaHufltTaskStatus);
        }
        log.info("footer:{}", footer);
        log.info("-----------------Hulft Completed  ----------------");
    }

    private static Map<String, String> getStringStringMap(String detail) {
        Map<String, String> dataMap = new LinkedHashMap<>();
        for (int i = 0; i < INDEXES.size(); i++) {
            int start = INDEXES.get(i);
            int end = (i + 1 < INDEXES.size()) ? INDEXES.get(i + 1) : detail.length();
            if (start < detail.length() && end <= detail.length()) {
                String substring = detail.substring(start, end);
                dataMap.put("field" + start, substring);
            }
        }
        return dataMap;
    }

    public static List<String> extractDetails(String filePath, String fileName) {
        List<String> details = new ArrayList<>();
        File file = new File(filePath + fileName);
        log.info(
                "File exists: {}, Readable: {}, Writable: {}",
                file.exists(),
                file.canRead(),
                file.canWrite());
        if (!file.exists()) {
            log.info("File does not exist. Exiting...");
            return null;
        }
        Path path = Paths.get(filePath + fileName);

        try (BufferedReader br = Files.newBufferedReader(path, SHIFTJIS)) {
            String line;
            boolean isFirstLine = true;
            while ((line = br.readLine()) != null) {
                if (isFirstLine) {
                    header = line;
                    isFirstLine = false;
                    continue;
                }
                if (line.startsWith("000800103")) {
                    footer = line;
                    break;
                }
                if (!line.trim().isEmpty()) {
                    details.add(line);
                }
            }
            if (header != null && footer != null && details.isEmpty()) {
                log.info("Valid file structure but no business data");
                return null;
            }
        } catch (IOException e) {
            log.warn("Hulft read IOException:{}", e.getMessage());
        }
        return details;
    }

    private static void deleteSourceFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.deleteIfExists(path)) {
                log.info("Source file deleted successfully: {}", filePath);
            } else {
                log.warn("Source file not found: {}", filePath);
            }
        } catch (Exception e) {
            log.error("Failed to delete the source file: {}", e.getMessage(), e);
        }
    }
}
