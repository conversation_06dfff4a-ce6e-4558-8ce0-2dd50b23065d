package point.worker.worker;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;
import point.common.entity.SameIpUser;
import point.common.entity.Symbol;
import point.common.entity.UserLoginInfo;
import point.common.service.SameIpUserService;
import point.common.service.SystemConfigService;
import point.common.service.UserLoginInfoService;
import point.common.util.DateUnit;
import point.worker.component.Worker;

@Component
@RequiredArgsConstructor
@Slf4j
public class SameIpChecker extends Worker {

    private static final String SAME_IP_CHECK_SPAN_HOURS_KEY = "SAME_IP_CHECK_SPAN_HOURS";
    private static final String SAME_IP_THRESHOLD_COUNT_KEY = "SAME_IP_THRESHOLD_COUNT";

    private final UserLoginInfoService userLoginInfoService;
    private final SystemConfigService systemConfigService;
    private final SameIpUserService sameIpUserService;

    private void sendMessage(final SameIpUser sameIpUser) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("same ip. ipAddress: " + sameIpUser.getIpAddress() + ". ");
        stringBuilder.append("userIds: " + sameIpUser.getUserIds());
        log.info(stringBuilder.toString());
    }

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        // 現在時刻を基本日時とする
        final var date = System.currentTimeMillis();
        final var targetDate = new Date(date);
        execute(targetDate);
    }

    void execute(final Date targetDate) {
        final var readDto = read(targetDate);
        final var writeDtos = process(readDto);
        write(writeDtos);
    }

    ReadData read(final Date targetDate) {
        final var sameIpCheckSpanConfig =
                systemConfigService.findByCondition(null, null, SAME_IP_CHECK_SPAN_HOURS_KEY);
        final var sameIpCheckSpan = Integer.parseInt(sameIpCheckSpanConfig.getValue());

        final var sameIpThresholdCountConfig =
                systemConfigService.findByCondition(null, null, SAME_IP_THRESHOLD_COUNT_KEY);
        final var sameIpThresholdCount = Integer.parseInt(sameIpThresholdCountConfig.getValue());

        final var fromDate =
                new Date(targetDate.getTime() - DateUnit.HOUR.getMillis() * sameIpCheckSpan);
        log.info("fromDate:{}", DateFormatUtils.format(fromDate, "yyyy-MM-dd HH:mm:ss"));
        log.info("targetDate:{}", DateFormatUtils.format(targetDate, "yyyy-MM-dd HH:mm:ss"));
        final var userLoginInfos = userLoginInfoService.findByCondition(fromDate, targetDate);

        return new ReadData(userLoginInfos, sameIpCheckSpan, sameIpThresholdCount);
    }

    record ReadData(
            List<UserLoginInfo> userLoginInfos,
            Integer sameIpCheckSpanHours,
            Integer sameIpThresholdCount) {}

    List<SameIpUser> process(final ReadData readDto) {
        final var resultEntities = new ArrayList<SameIpUser>();

        final var userLoginInfoGroupingIpAddress =
                readDto.userLoginInfos.stream()
                        .filter(userLoginInfo -> !userLoginInfo.getIpAddress().isEmpty())
                        .collect(Collectors.groupingBy(UserLoginInfo::getIpAddress));

        for (var userLoginInfoEntry : userLoginInfoGroupingIpAddress.entrySet()) {
            final var removedDuplicationUserIds =
                    userLoginInfoEntry.getValue().stream()
                            .map(UserLoginInfo::getUserId)
                            .distinct()
                            .toList();

            if (removedDuplicationUserIds.size() >= readDto.sameIpThresholdCount) {
                final var sameIpUser = new SameIpUser();
                sameIpUser.setUserIds(ArrayUtils.toString(removedDuplicationUserIds));
                sameIpUser.setIpAddress(userLoginInfoEntry.getKey());
                resultEntities.add(sameIpUser);
            }
        }
        return resultEntities;
    }

    void write(final List<SameIpUser> writeDtos) {
        for (var writeDto : writeDtos) {
            sendMessage(writeDto);
            sameIpUserService.save(writeDto);
        }
    }
}
