package point.worker.worker;

import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import point.common.constant.TradeType;
import point.common.entity.Symbol;
import point.common.service.InvestmentPurposeDeviationUserService;
import point.worker.component.Worker;

@Component
@RequiredArgsConstructor
public class InvestmentPurposeDeviationChecker extends Worker {

    private final InvestmentPurposeDeviationUserService investmentPurposeDeviationUserService;

    @Override
    public void execute(Symbol symbolData, Map<String, Object> params) throws Exception {
        Long now = System.currentTimeMillis();

        // 販売所をチェック
        List<Long> posSavedUserId =
                investmentPurposeDeviationUserService.execute(TradeType.INVEST, now, null);

        investmentPurposeDeviationUserService.execute(null, now, posSavedUserId);
    }
}
