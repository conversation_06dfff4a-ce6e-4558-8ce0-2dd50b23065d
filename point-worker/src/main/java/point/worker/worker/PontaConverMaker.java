package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.PontaConvertWorkerService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class PontaConverMaker extends Worker {

    private final PontaConvertWorkerService pontaConvertWorkerService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "===========================PONTA CONVERT WORKER 開始/PontaConverMaker start=======================================");
        pontaConvertWorkerService.pontaConverSync();
        log.info(
                "===========================PONTA CONVERT WORKER 終了/PontaConverMaker finish=======================================");
    }
}
