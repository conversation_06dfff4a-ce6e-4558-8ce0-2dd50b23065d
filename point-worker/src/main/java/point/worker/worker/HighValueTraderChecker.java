package point.worker.worker;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.constant.CurrencyPair;
import point.common.constant.OrderType;
import point.common.constant.TradeType;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.HighValueTrader;
import point.common.entity.Symbol;
import point.common.service.CurrencyPairConfigService;
import point.common.service.HighValueTraderService;
import point.common.service.OrderbookService;
import point.pos.entity.PosOrder;
import point.pos.entity.PosTrade;
import point.pos.service.PosOrderService;
import point.pos.service.PosTradeService;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class HighValueTraderChecker extends Worker {

    private final OrderbookService orderbookService;
    private final HighValueTraderService highValueTraderService;
    private final CurrencyPairConfigService currencyPairConfigService;
    private final PosOrderService posOrderService;
    private final PosTradeService posTradeService;

    private void sendMessage(long userId, int limitOrders, int marketTrades, TradeType tradeType) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("highValueTrader. userId: " + userId + ". ");
        stringBuilder.append(
                "limitOrders: " + limitOrders + ", marketTrades: " + marketTrades + ", ");
        stringBuilder.append("trade_type: " + tradeType.toString());
        log.info(stringBuilder.toString());
    }

    private void save(
            long userId,
            int limitOrders,
            int marketTrades,
            TradeType tradeType,
            CurrencyPair currencyPair) {
        HighValueTrader highValueTrader = new HighValueTrader();
        highValueTrader.setUserId(userId);
        highValueTrader.setTradeType(tradeType);
        highValueTrader.setCurrencyPair(currencyPair);
        highValueTrader.setPosOrderLimits(limitOrders);
        highValueTrader.setPosTradeMarkets(marketTrades);
        highValueTraderService.save(highValueTrader);
    }

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        CurrencyPairConfig currencyPairConfig =
                currencyPairConfigService.findByCondition(
                        symbol.getTradeType(), symbol.getCurrencyPair());

        // チェック範囲時間
        int checkSpanHours = currencyPairConfig.getHighValueTraderCheckSpanHours();
        // 指定額以上の指値注文のしきい値
        BigDecimal posOrderLimitAmountThreshold =
                currencyPairConfig.getHighValueTraderPosOrderLimitAmountThreshold();
        // 指定額以上の成行注文約定のしきい値
        BigDecimal posTradeMarketAmountThreshold =
                currencyPairConfig.getHighValueTraderPosTradeMarketAmountThreshold();
        // 回数のしきい値
        int countThresHold = currencyPairConfig.getHighValueTraderCountThreshold();

        Calendar calendar = Calendar.getInstance();
        Date dateTo = calendar.getTime();
        // dateFrom:checkSpanHour時間前
        calendar.add(Calendar.HOUR_OF_DAY, checkSpanHours * -1);
        Set<Long> userIdSet = new HashSet<>();
        Map<Long, Integer> userIdAndLimitOrdersMap = new HashMap<>();
        Map<Long, Integer> userIdAndLimitOrdersHistoryMap = new HashMap<>();
        Map<Long, Integer> userIdAndMarketTradesMap = new HashMap<>();
        Map<Long, Integer> userIdAndMarketTradesHistoryMap = new HashMap<>();
        BigDecimal conversionPrice =
                orderbookService.getBestBidOfQuoteJpy(symbol.getCurrencyPair().getQuoteCurrency());

        log.info(
                "HighValueTraderChecker, targetFrom:"
                        + calendar.getTime()
                        + ", targetTo:"
                        + dateTo);

        // 販売所の場合
        // テーブル「pos_order」からデータを取得する
        List<PosOrder> posOrders =
                posOrderService.findAllByCondition(
                        symbol.getId(),
                        null,
                        null,
                        null,
                        null,
                        calendar.getTime().getTime(),
                        dateTo.getTime(),
                        null,
                        OrderType.MARKET,
                        null);
        checkPosOrders(
                posOrders,
                userIdAndLimitOrdersMap,
                userIdSet,
                conversionPrice,
                posOrderLimitAmountThreshold);

        // テーブル「pos_trade」からデータを取得する
        List<PosTrade> posTrades =
                posTradeService.findAllByCondition(
                        symbol.getId(),
                        null,
                        null,
                        null,
                        null,
                        calendar.getTime().getTime(),
                        dateTo.getTime(),
                        null,
                        null,
                        OrderType.MARKET);
        checkPosTrades(
                posTrades,
                userIdAndMarketTradesMap,
                userIdSet,
                conversionPrice,
                posTradeMarketAmountThreshold);

        // チェック範囲が48時間より広い場合、redshiftもチェック
        if (checkSpanHours > 48) {
            List<PosOrder> posOrdersFromHistory =
                    posOrderService.findAllFromHistory(
                            symbol,
                            null,
                            null,
                            null,
                            null,
                            calendar.getTime(),
                            dateTo,
                            null,
                            OrderType.MARKET,
                            null,
                            null,
                            null);
            checkPosOrders(
                    posOrdersFromHistory,
                    userIdAndLimitOrdersHistoryMap,
                    userIdSet,
                    conversionPrice,
                    posOrderLimitAmountThreshold);

            List<PosTrade> posTradesFromHistory =
                    posTradeService.findAllFromHistory(
                            symbol,
                            null,
                            null,
                            null,
                            null,
                            null,
                            OrderType.MARKET,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            calendar.getTime(),
                            dateTo);
            checkPosTrades(
                    posTradesFromHistory,
                    userIdAndMarketTradesHistoryMap,
                    userIdSet,
                    conversionPrice,
                    posTradeMarketAmountThreshold);
        }

        userIdSet.forEach(
                userId -> {
                    int limitOrders =
                            userIdAndLimitOrdersMap.getOrDefault(userId, 0)
                                    + userIdAndLimitOrdersHistoryMap.getOrDefault(userId, 0);
                    int marketTrades =
                            userIdAndMarketTradesMap.getOrDefault(userId, 0)
                                    + userIdAndMarketTradesHistoryMap.getOrDefault(userId, 0);

                    if (limitOrders + marketTrades >= countThresHold) {
                        sendMessage(userId, limitOrders, marketTrades, symbol.getTradeType());
                        save(
                                userId,
                                limitOrders,
                                marketTrades,
                                symbol.getTradeType(),
                                symbol.getCurrencyPair());
                    }
                });
    }

    /*
     * 販売所用のPosOrderのチェック
     *
     */
    private void checkPosOrders(
            List<PosOrder> posOrders,
            Map<Long, Integer> userIdAndLimitOrdersMap,
            Set<Long> userIdSet,
            BigDecimal conversionPrice,
            BigDecimal posOrderLimitAmountThreshold) {
        for (PosOrder posOrder : posOrders) {
            // exist userId in map
            if (userIdAndLimitOrdersMap.containsKey(posOrder.getUserId())) {
                continue;
            }
            userIdSet.add(posOrder.getUserId());

            int limitOrders = 0;
            for (PosOrder targetPosOrder : posOrders) {
                if (!posOrder.getUserId().equals(targetPosOrder.getUserId())) {
                    continue;
                }
                if (targetPosOrder
                                .getPrice()
                                .multiply(targetPosOrder.getAmount())
                                .multiply(conversionPrice)
                                .compareTo(posOrderLimitAmountThreshold)
                        >= 0) {
                    limitOrders++;
                }
            }
            userIdAndLimitOrdersMap.put(posOrder.getUserId(), limitOrders);
        }
    }

    /*
     * 販売所用のPosTradeのチェック
     *
     */
    private void checkPosTrades(
            List<PosTrade> posTrades,
            Map<Long, Integer> userIdAndMarketTradesMap,
            Set<Long> userIdSet,
            BigDecimal conversionPrice,
            BigDecimal posTradeMarketAmountThreshold) {
        for (PosTrade posTrade : posTrades) {
            if (userIdAndMarketTradesMap.containsKey(posTrade.getUserId())) {
                continue;
            }
            userIdSet.add(posTrade.getUserId());

            int marketTrades = 0;
            for (PosTrade targetPosTrade : posTrades) {
                if (!posTrade.getUserId().equals(targetPosTrade.getUserId())) {
                    continue;
                }
                if (targetPosTrade
                                .getAssetAmount()
                                .multiply(conversionPrice)
                                .compareTo(posTradeMarketAmountThreshold)
                        >= 0) {
                    marketTrades++;
                }
            }
            userIdAndMarketTradesMap.put(posTrade.getUserId(), marketTrades);
        }
    }
}
