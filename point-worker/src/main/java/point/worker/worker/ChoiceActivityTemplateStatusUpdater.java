package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.ChoiceActivityTemplateService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class ChoiceActivityTemplateStatusUpdater extends Worker {

    private final ChoiceActivityTemplateService choiceActivityTemplateService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("start to refresh status for choice_activity_template");
        choiceActivityTemplateService.refreshStatus();
        log.info("finished to refresh status for choice_activity_template");
    }
}
