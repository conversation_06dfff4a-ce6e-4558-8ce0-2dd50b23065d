package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.OnetimeBankAccountService;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class Issue<PERSON><PERSON>he<PERSON> extends Worker {

    private final OnetimeBankAccountService onetimeBankAccountService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("create virtual account checker started ");
        onetimeBankAccountService.issueMigrationUserVirtualAccount();
        log.info("create virtual account checker finished ");
    }
}
