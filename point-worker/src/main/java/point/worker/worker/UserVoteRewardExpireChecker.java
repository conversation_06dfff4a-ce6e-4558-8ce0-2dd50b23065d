package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.UserVoteRewardService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class UserVoteRewardExpireChecker extends Worker {

    private final UserVoteRewardService userVoteRewardService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("check vote reward expire started ");
        userVoteRewardService.voteRewardExpiry();
        log.info("check vote reward expire ended ");
    }
}
