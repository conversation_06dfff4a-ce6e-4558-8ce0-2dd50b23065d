package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.pos.service.PosTradeService;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class PosTradeArchiver extends Worker {

    private final PosTradeService posTradeService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) {
        log.info(
                "posTradeServiceArchiver start execute  symbolID:{} tradeType:{}",
                symbol.getId(),
                symbol.getTradeType());
        posTradeService.archive(symbol);
        log.info(
                "posTradeServiceArchiver end execute  symbolID:{} tradeType:{}",
                symbol.getId(),
                symbol.getTradeType());
    }
}
