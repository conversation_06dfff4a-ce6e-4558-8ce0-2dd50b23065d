package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.QuizQuestionService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class QuizQuestionPublishUpdater extends Worker {

    private final QuizQuestionService quizQuestionAppService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("publish quiz question worker started ");
        quizQuestionAppService.randomQuizQuestionDaily();
        log.info("publish quiz question worker finished ");
    }
}
