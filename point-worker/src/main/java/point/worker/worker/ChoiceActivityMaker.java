package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.ChoiceActivityTemplateService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class ChoiceActivityMaker extends Worker {

    private final ChoiceActivityTemplateService choiceActivityTemplateService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "===========================CHOICE ACTIVITY MAKE BY ACTIVITY TEMPLATE 開始/ChoiceActivityMaker start=======================================");
        choiceActivityTemplateService.setChoiceActivityByTemplate();
        log.info(
                "===========================CHOICE ACTIVITY MAKE BY ACTIVITY TEMPLATE 終了/ChoiceActivityMaker finish=======================================");
    }
}
