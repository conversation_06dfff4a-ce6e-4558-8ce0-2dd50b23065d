package point.worker.worker;

import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.UserSummaryService;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.worker.component.Worker;

@RequiredArgsConstructor
@Component
public class UserSummaryCalculator extends Worker {

    private final UserSummaryService userSummaryService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        // 起動日時の取得
        Date today = new Date();
        // 処理対象日の取得
        Date yesterday = new Date(today.getTime() - 60 * 60 * 24 * 1000);
        String targetDay = FormatUtil.formatJst(yesterday, FormatPattern.YYYYMMDD);
        // 計算処理の呼び出し
        userSummaryService.calculate(targetDay);
    }
}
