package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.AntiSocialCheckService;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserAntiSocialChecker extends Worker {

    private final AntiSocialCheckService antiSocialCheckService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                " ==============================UserAntiSocialChecker start==============================");
        antiSocialCheckService.antiSocialCheck();
        log.info(
                " ==============================UserAntiSocialChecker end==============================");
    }
}
