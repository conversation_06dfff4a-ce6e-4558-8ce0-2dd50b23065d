package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.pos.service.PosOrderService;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class PosOrderArchiver extends Worker {

    private final PosOrderService posOrderService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) {
        log.info(
                "posOrderServiceArchiver start execute  symbolID:{} tradeType:{}",
                symbol.getId(),
                symbol.getTradeType());
        posOrderService.archive(symbol);
        log.info(
                "posOrderServiceArchiver end execute  symbolID:{} tradeType:{}",
                symbol.getId(),
                symbol.getTradeType());
    }
}
