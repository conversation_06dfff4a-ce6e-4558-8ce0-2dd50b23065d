package point.worker.worker;

import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import point.common.constant.TradeType;
import point.common.entity.Symbol;
import point.common.service.AssetSummaryService;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.worker.component.Worker;

@RequiredArgsConstructor
@Component
public class AssetSummaryRecalculator extends Worker {

    private final AssetSummaryService assetSummaryService;
    private static final Logger log = LoggerFactory.getLogger(AssetSummaryRecalculator.class);

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {

        String targetAtStr = (String) params.get("targetAt");
        if (targetAtStr == null) {
            log.info("targetAt is null");
            return;
        }
        // 再計算対象日の取得（9/16 0時の計算を再計算 -> "20210916"指定 -> targetAt = 9/15 15:00）
        Date targetAt = FormatUtil.parse(targetAtStr, FormatPattern.YYYYMMDD);
        Date today = new DateTime().withTime(0, 0, 0, 0).toDate();
        // 前日まで繰り返し計算処理の呼び出し
        while (targetAt.before(today)) {
            Date next = new DateTime(targetAt).plusDays(1).toDate();
            Date before = new DateTime(targetAt).minusDays(1).toDate();
            log.info(
                    "assetSummaryRecalcTarget:{}, delTargetAt:{} - {}",
                    FormatUtil.format(targetAt, FormatPattern.YYYYMMDD),
                    FormatUtil.format(before, FormatPattern.YYYYMMDDHHMM),
                    FormatUtil.format(targetAt, FormatPattern.YYYYMMDDHHMM));

            // 再計算対象日のレコード削除
            assetSummaryService
                    .findByCondition(null, null, before, targetAt)
                    .forEach(assetSummaryService::delete);

            assetSummaryService.calculate(
                    FormatUtil.formatJst(targetAt, FormatPattern.YYYYMMDD), TradeType.INVEST);
            targetAt = next;
        }
    }
}
