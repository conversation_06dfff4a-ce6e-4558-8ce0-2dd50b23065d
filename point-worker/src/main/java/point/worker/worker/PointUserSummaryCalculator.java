package point.worker.worker;

import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.util.FormatUtil;
import point.operate.service.PointUserSummaryService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class PointUserSummaryCalculator extends Worker {

    private final PointUserSummaryService pointUserSummaryService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("===========================POINT USER SUMMARY CALCULATE 開始/PointUserSummaryCalculator start=======================================");
        // 起動日時の取得
        Date today = new Date();
        // 処理対象日の取得
        Date yesterday = new Date(today.getTime() - 60 * 60 * 24 * 1000);
        String targetDay = FormatUtil.formatJst(yesterday, FormatUtil.FormatPattern.YYYYMMDD);
        // 計算処理の呼び出し
        pointUserSummaryService.calculate(targetDay);
        log.info("===========================POINT USER SUMMARY CALCULATE 終了/PointUserSummaryCalculator finish=======================================");
    }
}
