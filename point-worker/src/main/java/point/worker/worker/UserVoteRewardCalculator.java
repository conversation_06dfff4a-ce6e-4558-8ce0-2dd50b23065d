package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.UserVoteRewardCalculatorService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class UserVoteRewardCalculator extends Worker {

    private final UserVoteRewardCalculatorService userVoteRewardService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info("calculate vote reward worker started ");
        long startTime = System.currentTimeMillis();
        userVoteRewardService.calculateVotePower();
        long endTime = System.currentTimeMillis();
        log.info("[WORKER] UserVoteRewardCalculator: total exec time is {}", endTime - startTime);
        log.info("calculate vote reward worker ended ");
    }
}
