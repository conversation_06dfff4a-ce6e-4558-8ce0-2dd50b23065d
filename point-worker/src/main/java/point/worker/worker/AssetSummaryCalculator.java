package point.worker.worker;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import point.common.constant.TradeType;
import point.common.entity.Symbol;
import point.common.service.AssetSummaryService;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.worker.component.Worker;

@RequiredArgsConstructor
@Component
public class AssetSummaryCalculator extends Worker {

    private final AssetSummaryService assetSummaryService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        // 起動日時の取得
        Date today = new Date();
        // 処理対象日の取得
        Date yesterday = new Date(today.getTime() - TimeUnit.DAYS.toMillis(1));
        String targetDay = FormatUtil.formatJst(yesterday, FormatPattern.YYYYMMDD);
        // 計算処理の呼び出し
        assetSummaryService.calculate(targetDay, TradeType.INVEST);
    }
}
