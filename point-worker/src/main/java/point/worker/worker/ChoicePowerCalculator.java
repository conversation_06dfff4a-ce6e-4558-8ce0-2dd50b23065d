package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.ChoicePowerTransferService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class ChoicePowerCalculator extends Worker {

    private final ChoicePowerTransferService choicePowerTransferService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "===========================CHOICE POWER SET BY RULE 開始/ChoicePowerCalculator start=======================================");
        choicePowerTransferService.setChoicePowerTransferByRule();
        log.info(
                "===========================CHOICE POWER SET BY RULE 終了/ChoicePowerCalculator finish=======================================");
    }
}
