package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.UserEkycBpoResultService;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserEkycBpoResultUpdater extends Worker {

    private final UserEkycBpoResultService service;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                " ==============================userEkycBpoResult start==============================");
        service.fetchBpoResultWithUpdate();
        log.info(
                " ==============================userEkycBpoResult end==============================");
    }
}
