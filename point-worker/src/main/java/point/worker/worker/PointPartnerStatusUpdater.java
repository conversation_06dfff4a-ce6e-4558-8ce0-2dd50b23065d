package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.PointPartnerService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class PointPartnerStatusUpdater extends Worker {

    private final PointPartnerService pointPartnerService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "===========================事業者STATUS UPDATE TO ABOLISH 開始/PointPartnerStatusUpdater start=======================================");
        pointPartnerService.updatePointPartnerStatus();
        log.info(
                "===========================事業者STATUS UPDATE TO ABOLISH 終了/PointPartnerStatusUpdater finish=======================================");
    }
}
