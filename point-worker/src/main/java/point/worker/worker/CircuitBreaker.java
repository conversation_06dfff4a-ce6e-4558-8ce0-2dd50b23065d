package point.worker.worker;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;
import point.common.component.CustomLogger;
import point.common.constant.CandlestickType;
import point.common.entity.Candlestick;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.Symbol;
import point.common.service.CurrencyPairConfigService;
import point.common.util.CalculatorUtil;
import point.common.util.DateUnit;
import point.common.util.JsonUtil;
import point.pos.service.PosCandlestickService;
import point.worker.component.Worker;

@RequiredArgsConstructor
@Component
public class CircuitBreaker extends Worker {

    private final CurrencyPairConfigService currencyPairConfigService;
    private static final CustomLogger log = new CustomLogger(CircuitBreaker.class.getName());
    private final PosCandlestickService posCandlestickService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {

        // 現在時分の00秒を取得
        Date date = new Date();
        long targetAt = CandlestickType.PT1M.getTargetAt(date).getTime();
        CurrencyPairConfig currencyPairConfig =
                currencyPairConfigService.findByCondition(
                        symbol.getTradeType(), symbol.getCurrencyPair());
        log.info(getClass().getName(), "currencyPairConfig:" + JsonUtil.encode(currencyPairConfig));
        // 最新の1分足を取得(isAscending=false)
        Candlestick latestCandlestick =
                posCandlestickService.findOneByCondition(
                        symbol.getId(), CandlestickType.PT1M, targetAt, null, false);

        // 存在なし時はスキップ (生成中(!isFixed)はOK)
        if (latestCandlestick == null) {
            return;
        }
        log.info(getClass().getName(), "latestCandlestick:" + JsonUtil.encode(latestCandlestick));
        // チェックタイム分前の最古の1分足openを取得(isAscending=true)
        Long circuitBreakCheckTimeSpan = currencyPairConfig.getCircuitBreakCheckTimespan();

        // 1分未満・設定無しはスキップ
        if (circuitBreakCheckTimeSpan == null || circuitBreakCheckTimeSpan < 1) {
            return;
        }

        log.info(
                getClass().getName(),
                "circuitBreakCheckTimeSpan:"
                        + DateFormatUtils.format(circuitBreakCheckTimeSpan, "yyyy-MM-dd HH:mm:ss"));

        long previousTargetAt = targetAt - DateUnit.MINUTE.getMillis() * circuitBreakCheckTimeSpan;

        log.info(
                getClass().getName(),
                "previousTargetAt:"
                        + DateFormatUtils.format(previousTargetAt, "yyyy-MM-dd HH:mm:ss"));

        Candlestick previousCandlestick =
                posCandlestickService.findOneByCondition(
                        symbol.getId(), CandlestickType.PT1M, previousTargetAt, null, true);

        if (previousCandlestick == null) {
            return;
        }

        log.info(
                getClass().getName(),
                "previousCandlestick:" + JsonUtil.encode(previousCandlestick));

        // 変化率分母zeroチェック(基準価格0のときはスキップ)
        if (previousCandlestick.getOpen().compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 変化率算出(差分絶対値 / x分以内の最古価格)
        BigDecimal diffRate =
                CalculatorUtil.divide(
                        latestCandlestick.getOpen().subtract(previousCandlestick.getOpen()).abs(),
                        previousCandlestick.getOpen());

        log.info(
                getClass().getName(),
                "latestCandlestickOpen:"
                        + latestCandlestick
                                .getOpen()
                                .subtract(previousCandlestick.getOpen())
                                .abs()
                                .toPlainString()
                        + ", previousCandlestickOpen:"
                        + previousCandlestick.getOpen().toPlainString());
        log.info(getClass().getName(), "diffRate:" + diffRate.toPlainString());

        BigDecimal circuitBreakPercent = currencyPairConfig.getCircuitBreakPercent();

        // 0%以下・設定無しはスキップ
        if (circuitBreakPercent == null || circuitBreakPercent.signum() < 1) {
            return;
        }
        log.info(getClass().getName(), "circuitBreakPercent=" + circuitBreakPercent);
        log.info(
                getClass().getName(),
                "CalculatorUtil.calculatePercentage(circuitBreakPercent):"
                        + CalculatorUtil.calculatePercentage(circuitBreakPercent));
        // 異常検知
        if (diffRate.compareTo(CalculatorUtil.calculatePercentage(circuitBreakPercent)) > 0) {

            // ・x分以内の最新最古の1分足open同士を比較する
            // ・1分以内の高安の突発変動は無視される（確定値のみで定点観測)

            // x分前に約定がない場合は、該当の最古の1分足価格はそれ以前の最後に約定した価格を引き継ぐため、x分前より前の最後に約定した価格を基準に検知

            // 最新の検知時刻を更新
            // ※検知時刻をもとに成行注文のみ停止(注文処理で判定)
            log.info(
                    getClass().getName(),
                    "circuitBreakUpdatedAt:" + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss"));
            currencyPairConfig.setCircuitBreakUpdatedAt(date);
            currencyPairConfigService.save(currencyPairConfig);

            log.info(
                    getClass().getName(),
                    "Circuit Breaker symbol: "
                            + JsonUtil.encode(symbol)
                            + ", circuitBreakCheckTimeSpan: "
                            + JsonUtil.encode(circuitBreakCheckTimeSpan)
                            + ", latestCandlestick: "
                            + JsonUtil.encode(latestCandlestick)
                            + ", previousCandlestick: "
                            + JsonUtil.encode(previousCandlestick)
                            + ", diffRate: "
                            + JsonUtil.encode(diffRate)
                            + ", cirCuitBreakPercent/100: "
                            + JsonUtil.encode(
                                    CalculatorUtil.calculatePercentage(circuitBreakPercent))
                            + ", circuitBreakStopTimespan: "
                            + JsonUtil.encode(currencyPairConfig.getCircuitBreakStopTimespan())
                            + ", circuitBreakUpdatedAt: "
                            + JsonUtil.encode(currencyPairConfig.getCircuitBreakUpdatedAt()));
        }
    }
}
