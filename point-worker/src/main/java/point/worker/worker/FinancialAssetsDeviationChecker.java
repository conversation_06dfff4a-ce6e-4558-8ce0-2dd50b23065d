package point.worker.worker;

import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import point.common.constant.TradeType;
import point.common.entity.Symbol;
import point.common.service.FinancialAssetsDeviationUserService;
import point.worker.component.Worker;

@Component
@RequiredArgsConstructor
public class FinancialAssetsDeviationChecker extends Worker {

    private final FinancialAssetsDeviationUserService financialAssetsDeviationUserService;

    @Override
    public void execute(Symbol symbolData, Map<String, Object> params) throws Exception {
        Long now = System.currentTimeMillis();

        // 販売所をチェック
        List<Long> posSavedUserId =
                financialAssetsDeviationUserService.execute(TradeType.INVEST, now, null);

        financialAssetsDeviationUserService.execute(null, now, posSavedUserId);
    }
}
