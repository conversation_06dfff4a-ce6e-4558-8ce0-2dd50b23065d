package point.worker.worker;

import java.time.Instant;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.auth.model.TokenResponse;
import point.common.config.GmoAuthorizationTokenConfiguration;
import point.common.config.GmoConfig;
import point.common.entity.Symbol;
import point.common.service.GmoService;
import point.worker.component.Worker;

@Slf4j
@Component
@RequiredArgsConstructor
public class GmoTokenUpdater extends Worker {

    private final GmoService gmoService;
    private final GmoConfig gmoConfig;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "===========================gmo token updater start=======================================");
        GmoAuthorizationTokenConfiguration authConfig = gmoService.getGmoAuthConfig();
        Long expireAt = authConfig.getExpireAt();
        String accessToken = authConfig.getAccessToken();
        log.info("worker gmo old accessToken: {}", accessToken);
        Long current = Instant.now().toEpochMilli();
        if (current > expireAt
                || (current < expireAt
                        && (expireAt - current < gmoConfig.getRefreshTokenInterval()))) {
            log.info("access token has expired,call refresh token manually");
            TokenResponse response = gmoService.refreshToken();
            accessToken = response.getAccessToken();
        }
        log.info("worker gmo new accessToken: {}", accessToken);
        log.info(
                "===========================gmo token updater end=========================================");
    }
}
