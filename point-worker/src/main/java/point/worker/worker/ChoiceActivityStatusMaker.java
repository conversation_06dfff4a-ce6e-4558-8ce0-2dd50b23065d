package point.worker.worker;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.ChoiceActivityService;
import point.worker.component.Worker;

@Slf4j
@RequiredArgsConstructor
@Component
public class ChoiceActivityStatusMaker extends Worker {

    private final ChoiceActivityService choiceActivityService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        log.info(
                "===========================CHOICE ACTIVITY UPDATE STATUS 開始/ChoiceActivityStatusMaker start=======================================");
        choiceActivityService.updateChoiceActivityStatus();
        log.info(
                "===========================CHOICE ACTIVITY UPDATE STATUS 終了/ChoiceActivityStatusMaker finish=======================================");
    }
}
