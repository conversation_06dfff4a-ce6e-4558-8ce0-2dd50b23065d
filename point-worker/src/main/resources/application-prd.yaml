cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
aws:
  s3:
    year-report-bucket:
      name: year-report.bs-point-prd
common:
  log:
    console:
      appender: CONSOLE_JSON
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: backseat-service.com
    environment: prd
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false

ponta:
  webIf-Url: https://onlinesx01.lp.loyalty.co.jp:8443/webif/bizinvoker

vote-reward:
  expire-unit: YEAR
  expire-date: 1