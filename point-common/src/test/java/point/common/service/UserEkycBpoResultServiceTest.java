package point.common.service;

import static org.mockito.Mockito.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import point.common.component.EkycProxy;
import point.common.component.SesManager;
import point.common.config.EKycConfig;
import point.common.constant.KycStatus;
import point.common.constant.MailNoreplyType;
import point.common.entity.MailNoreply;
import point.common.entity.User;
import point.common.entity.UserEkyc;
import point.common.exception.CustomException;
import point.common.repos.UserEkycRepository;
import point.common.repos.UserRepository;

class UserEkycBpoResultServiceTest {

    @Mock private UserEkycService mockUserEkycService;
    @Mock private UserEkycStatusChangeHistoryService mockUserEkycStatusChangeHistoryService;
    @Mock private UserEkycRequestHistoryService mockUserEkycRequestHistoryService;
    @Mock private UserEkycRepository mockRepository;
    @Mock private UserRepository mockUserRepository;
    @Mock private EKycConfig mockEKycConfig;
    @Mock private SesManager mockSesManager;
    @Mock private MailNoreplyService mockMailNoreplyService;
    @InjectMocks private UserEkycBpoResultService userEkycBpoResultServiceUnderTest;
    private AutoCloseable autoCloseable;

    @BeforeEach
    void setUp() {
        this.autoCloseable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    public void close() throws Exception {
        this.autoCloseable.close();
    }

    @Test
    void testInit() {
        final MailNoreply mailNoreply = new MailNoreply();
        mailNoreply.setId(0L);
        mailNoreply.setMailNoreplyType(MailNoreplyType.EKYC_BPO_FAILED_ERROR);
        mailNoreply.setFromAddress("fromAddress");
        mailNoreply.setTitle("title");
        mailNoreply.setContents("mailContent");
        when(mockMailNoreplyService.findOne(MailNoreplyType.EKYC_BPO_FAILED_ERROR))
                .thenReturn(mailNoreply);

        // Run the test
        // userEkycBpoResultServiceUnderTest.init();

        // Verify the results
        verify(mockMailNoreplyService, times(1)).findOne(MailNoreplyType.EKYC_BPO_FAILED_ERROR);
    }

    @Test
    void testFindTopByStatusInOrderByIdDescReturnEmpty() throws CustomException {
        when(mockRepository.findTopByStatusInOrderByIdDesc(anyList()))
                .thenReturn(Collections.emptyList());
        // Run the test
        userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();
        verify(mockUserEkycService, times(0)).getSignatureHeader(anyString());
        verify(mockUserEkycService, times(0)).getAuthorizationToken();
    }

    @Test
    void testUserEkycApplicantIdIsEmpty() throws CustomException {

        final List<UserEkyc> userEkycs =
                List.of(
                        new UserEkyc(
                                "docType",
                                "url",
                                KycStatus.DOCUMENT_CONFIRMING,
                                "referenceId",
                                null,
                                0L));
        when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);

        // Run the test
        userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();
        verify(mockUserEkycService, times(0)).hasValidEkycUrl(any(UserEkyc.class));
        verify(mockUserEkycService, times(0)).getSignatureHeader(anyString());
        verify(mockUserEkycService, times(0)).getAuthorizationToken();
    }

    @Test
    void testEkycUrlExpired() throws CustomException {

        final List<UserEkyc> userEkycs =
                List.of(
                        new UserEkyc(
                                "docType",
                                "url",
                                KycStatus.DOCUMENT_RECEIVED,
                                "referenceId",
                                10086L,
                                0L));
        when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
        when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.FALSE);

        // Run the test
        userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();
        verify(mockUserEkycService, times(1)).hasValidEkycUrl(any(UserEkyc.class));
        verify(mockUserEkycService, times(0)).getSignatureHeader(anyString());
        verify(mockUserEkycService, times(0)).getAuthorizationToken();
    }

    @Test
    void testBpoResultIsNull() throws Exception {

        final List<UserEkyc> userEkycs =
                List.of(
                        new UserEkyc(
                                "docType",
                                "url",
                                KycStatus.DOCUMENT_RECEIVED,
                                "referenceId",
                                10086L,
                                0L));
        when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
        when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            String returnPayload = StringUtils.EMPTY;
            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(1)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(0)).findById(anyLong());
            verify(mockRepository, times(0)).findById(anyLong());
            verify(mockUserRepository, times(0)).save(any(User.class));
            verify(mockRepository, times(0)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(0))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(0))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultCompletedWithResultFieldNotInMatchOrUnMatch() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            final List<UserEkyc> userEkycs =
                    List.of(
                            new UserEkyc(
                                    "docType",
                                    "url",
                                    KycStatus.DOCUMENT_RECEIVED,
                                    "referenceId",
                                    10086L,
                                    0L));
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"completed\",\"result_without_face_match\":\"unmatch\",\"result\":\"absent\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}],\"ekyc_result\":{\"front_match\":\"match\",\"thickness_match\":\"match\",\"live_match\":\"match\",\"ekyc_end_at\":\"2022-11-01T09:50:52+09:00\"}}";

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(1)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(0)).findById(anyLong());
            verify(mockRepository, times(0)).findById(anyLong());
            verify(mockUserRepository, times(0)).save(any(User.class));
            verify(mockRepository, times(0)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(0))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(0))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultCompletedAndResultFiledEqualMatch() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            UserEkyc userEkycResult =
                    new UserEkyc(
                            "docType",
                            "url",
                            KycStatus.DOCUMENT_RECEIVED,
                            "referenceId",
                            10086L,
                            1L);
            userEkycResult.setId(1L);
            final List<UserEkyc> userEkycs = List.of(userEkycResult);
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"completed\",\"result_without_face_match\":\"unmatch\",\"result\":\"match\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}],\"ekyc_result\":{\"front_match\":\"match\",\"thickness_match\":\"match\",\"live_match\":\"match\",\"ekyc_end_at\":\"2022-11-01T09:50:52+09:00\"}}";

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            User user = new User();
            user.setId(1L);
            user.setKycStatus(KycStatus.DOCUMENT_RECEIVED);
            UserEkyc userEkyc = new UserEkyc();
            userEkyc.setUserId(user.getId());
            userEkyc.setStatus(KycStatus.DOCUMENT_RECEIVED);

            when(mockUserRepository.findById(anyLong())).thenReturn(Optional.of(user));
            when(mockRepository.findById(anyLong())).thenReturn(Optional.of(userEkyc));

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(1)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(1)).findById(anyLong());
            verify(mockRepository, times(1)).findById(anyLong());
            verify(mockUserRepository, times(1)).save(any(User.class));
            verify(mockRepository, times(1)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(2))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(0))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultCompletedAndResultFiledEqualUnMatch() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            UserEkyc userEkycResult =
                    new UserEkyc(
                            "docType",
                            "url",
                            KycStatus.DOCUMENT_RECEIVED,
                            "referenceId",
                            10086L,
                            1L);
            userEkycResult.setId(1L);
            final List<UserEkyc> userEkycs = List.of(userEkycResult);
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"completed\",\"result_without_face_match\":\"unmatch\",\"result\":\"unmatch\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}],\"ekyc_result\":{\"front_match\":\"match\",\"thickness_match\":\"match\",\"live_match\":\"match\",\"ekyc_end_at\":\"2022-11-01T09:50:52+09:00\"}}";

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            User user = new User();
            user.setId(1L);
            user.setKycStatus(KycStatus.DOCUMENT_RECEIVED);
            user.setEmail("<EMAIL>");
            UserEkyc userEkyc = new UserEkyc();
            userEkyc.setUserId(user.getId());
            userEkyc.setStatus(KycStatus.DOCUMENT_RECEIVED);

            when(mockUserRepository.findById(anyLong())).thenReturn(Optional.of(user));
            when(mockRepository.findById(anyLong())).thenReturn(Optional.of(userEkyc));

            this.testInit();
            when(mockSesManager.send(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(Boolean.TRUE);

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(1)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(2)).findById(anyLong());
            verify(mockRepository, times(1)).findById(anyLong());
            verify(mockUserRepository, times(1)).save(any(User.class));
            verify(mockRepository, times(1)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(2))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(1))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultApplyingAndEkycUrlExpired() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            UserEkyc userEkycResult =
                    new UserEkyc(
                            "docType",
                            "url",
                            KycStatus.DOCUMENT_CONFIRMING,
                            "referenceId",
                            10086L,
                            1L);
            userEkycResult.setId(1L);
            final List<UserEkyc> userEkycs = List.of(userEkycResult);
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class)))
                    .thenReturn(Boolean.FALSE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"applying\",\"result_without_face_match\":\"unmatch\",\"result\":\"unmatch\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}],\"ekyc_result\":{\"front_match\":\"match\",\"thickness_match\":\"match\",\"live_match\":\"match\",\"ekyc_end_at\":\"2022-11-01T09:50:52+09:00\"}}";

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            User user = new User();
            user.setId(1L);
            user.setKycStatus(KycStatus.DOCUMENT_RECEIVED);
            user.setEmail("<EMAIL>");
            UserEkyc userEkyc = new UserEkyc();
            userEkyc.setUserId(user.getId());
            userEkyc.setStatus(KycStatus.DOCUMENT_RECEIVED);

            when(mockUserRepository.findById(anyLong())).thenReturn(Optional.of(user));
            when(mockRepository.findById(anyLong())).thenReturn(Optional.of(userEkyc));

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(1)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(1)).findById(anyLong());
            verify(mockRepository, times(1)).findById(anyLong());
            verify(mockUserRepository, times(1)).save(any(User.class));
            verify(mockRepository, times(1)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(1))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(0))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultApplyingAndEmptyEkycResult() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            UserEkyc userEkycResult =
                    new UserEkyc(
                            "docType",
                            "url",
                            KycStatus.DOCUMENT_RECEIVED,
                            "referenceId",
                            10086L,
                            1L);
            userEkycResult.setId(1L);
            final List<UserEkyc> userEkycs = List.of(userEkycResult);
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"applying\",\"result_without_face_match\":\"unmatch\",\"result\":\"unmatch\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}]}";

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(2)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(0)).findById(anyLong());
            verify(mockRepository, times(0)).findById(anyLong());
            verify(mockUserRepository, times(0)).save(any(User.class));
            verify(mockRepository, times(0)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(0))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(0))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultApplyingAndEmptyEkycResultSuccess() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            UserEkyc userEkycResult =
                    new UserEkyc(
                            "docType",
                            "url",
                            KycStatus.DOCUMENT_RECEIVED,
                            "referenceId",
                            10086L,
                            1L);
            userEkycResult.setId(1L);
            final List<UserEkyc> userEkycs = List.of(userEkycResult);
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"applying\",\"result_without_face_match\":\"unmatch\",\"result\":\"unmatch\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}],\"ekyc_result\":{\"front_match\":\"match\",\"thickness_match\":\"match\",\"live_match\":\"match\",\"ekyc_end_at\":\"2022-11-01T09:50:52+09:00\"}}";

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            User user = new User();
            user.setId(1L);
            user.setKycStatus(KycStatus.DOCUMENT_RECEIVED);
            user.setEmail("<EMAIL>");
            UserEkyc userEkyc = new UserEkyc();
            userEkyc.setUserId(user.getId());
            userEkyc.setStatus(KycStatus.DOCUMENT_RECEIVED);

            when(mockUserRepository.findById(anyLong())).thenReturn(Optional.of(user));
            when(mockRepository.findById(anyLong())).thenReturn(Optional.of(userEkyc));

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(2)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(1)).findById(anyLong());
            verify(mockRepository, times(1)).findById(anyLong());
            verify(mockUserRepository, times(1)).save(any(User.class));
            verify(mockRepository, times(1)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(1))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(0))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultApplyingAndEmptyEkycResultFailed() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            UserEkyc userEkycResult =
                    new UserEkyc(
                            "docType",
                            "url",
                            KycStatus.DOCUMENT_RECEIVED,
                            "referenceId",
                            10086L,
                            1L);
            userEkycResult.setId(1L);
            final List<UserEkyc> userEkycs = List.of(userEkycResult);
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"applying\",\"result_without_face_match\":\"unmatch\",\"result\":\"unmatch\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}],\"ekyc_result\":{\"front_match\":\"unmatch\",\"thickness_match\":\"unmatch\",\"live_match\":\"unmatch\",\"ekyc_end_at\":\"2022-11-01T09:50:52+09:00\"}}";

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            User user = new User();
            user.setId(1L);
            user.setKycStatus(KycStatus.DOCUMENT_RECEIVED);
            user.setEmail("<EMAIL>");
            UserEkyc userEkyc = new UserEkyc();
            userEkyc.setUserId(user.getId());
            userEkyc.setStatus(KycStatus.DOCUMENT_RECEIVED);

            when(mockUserRepository.findById(anyLong())).thenReturn(Optional.of(user));
            when(mockRepository.findById(anyLong())).thenReturn(Optional.of(userEkyc));

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(2)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(1)).findById(anyLong());
            verify(mockRepository, times(1)).findById(anyLong());
            verify(mockUserRepository, times(1)).save(any(User.class));
            verify(mockRepository, times(1)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(1))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(0))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }

    @Test
    void testBpoResultCompletedAndResultFiledUnMatchAndMultipleErrorCode() throws Exception {

        try (MockedStatic<EkycProxy> mocked = mockStatic(EkycProxy.class)) {
            UserEkyc userEkycResult =
                    new UserEkyc(
                            "docType",
                            "url",
                            KycStatus.DOCUMENT_RECEIVED,
                            "referenceId",
                            10086L,
                            1L);
            userEkycResult.setId(1L);
            final List<UserEkyc> userEkycs = List.of(userEkycResult);
            when(mockRepository.findTopByStatusInOrderByIdDesc(anyList())).thenReturn(userEkycs);
            when(mockUserEkycService.hasValidEkycUrl(any(UserEkyc.class))).thenReturn(Boolean.TRUE);

            String returnPayload =
                    "{\"applicant_id\":1888487,\"reference_id\":\"103T1667263124338\",\"document_type\":\"2\",\"require_face_match\":true,\"accepted_at\":\"2022-11-01T09:38:44+09:00\",\"status\":\"completed\",\"result_without_face_match\":\"unmatch\",\"result\":\"unmatch\",\"completed_at\":\"2022-11-01T10:12:03+09:00\",\"verification_errors\":[{\"error_code\":\"Q9\",\"error_message\":\"住所が確認書類と一致しない\"}, {\"error_code\":\"Q1\",\"error_message\":\"ご提出いただきました本人確認書類と顔写真が一致しなかったため、本人確認が取れませんでした。改めて、本人確認書類と顔写真を撮影いただきご提出ください。\"}],\"ekyc_result\":{\"front_match\":\"match\",\"thickness_match\":\"match\",\"live_match\":\"match\",\"ekyc_end_at\":\"2022-11-01T09:50:52+09:00\"}}";
            System.out.println(returnPayload);

            when(mockEKycConfig.getBpoResultUrl()).thenReturn(StringUtils.EMPTY);
            mocked.when(() -> EkycProxy.reqApi(anyString(), anyList(), anyMap()))
                    .thenReturn(returnPayload);

            User user = new User();
            user.setId(1L);
            user.setKycStatus(KycStatus.DOCUMENT_RECEIVED);
            user.setEmail("<EMAIL>");
            UserEkyc userEkyc = new UserEkyc();
            userEkyc.setUserId(user.getId());
            userEkyc.setStatus(KycStatus.DOCUMENT_RECEIVED);

            when(mockUserRepository.findById(anyLong())).thenReturn(Optional.of(user));
            when(mockRepository.findById(anyLong())).thenReturn(Optional.of(userEkyc));

            this.testInit();
            when(mockSesManager.send(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(Boolean.TRUE);

            // Run the test
            userEkycBpoResultServiceUnderTest.fetchBpoResultWithUpdate();

            // verify
            verify(mockUserEkycService, times(1)).hasValidEkycUrl(any(UserEkyc.class));
            verify(mockUserEkycService, times(1)).getSignatureHeader(anyString());
            verify(mockUserEkycService, times(1)).getAuthorizationToken();
            verify(mockUserRepository, times(2)).findById(anyLong());
            verify(mockRepository, times(1)).findById(anyLong());
            verify(mockUserRepository, times(1)).save(any(User.class));
            verify(mockRepository, times(1)).save(any(UserEkyc.class));
            verify(mockUserEkycStatusChangeHistoryService, times(2))
                    .create(
                            anyLong(),
                            anyString(),
                            anyLong(),
                            any(KycStatus.class),
                            any(KycStatus.class),
                            anyString());
            verify(mockSesManager, times(1))
                    .send(anyString(), anyString(), anyString(), anyString());
        }
    }
}
