package point.common.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import point.common.config.SNSConfig;
import point.common.http.HttpCallback;
import point.common.http.HttpManager;
import point.common.model.request.SNSRequestForm;
import point.common.model.response.SNSResponse;

@ExtendWith(MockitoExtension.class)
class SNSServiceTest {

    @Mock private SNSConfig mockSnsConfig;
    @Mock private HttpManager<SNSResponse> mockHttpManager;

    private SNSService snsServiceUnderTest;

    @BeforeEach
    void setUp() {
        snsServiceUnderTest = new SNSService(mockSnsConfig, mockHttpManager);
    }

    @Test
    void testPostSenSNS() throws Exception {
        final Map<String, Object> objectMap = Map.ofEntries(Map.entry("value", "value"));
        when(mockSnsConfig.createEntityMap(any(SNSRequestForm.class))).thenReturn(objectMap);
        when(mockSnsConfig.getHost()).thenReturn("host");
        final Map<String, String> stringStringMap = Map.ofEntries(Map.entry("value", "value"));
        when(mockSnsConfig.createHeaderMap()).thenReturn(stringStringMap);
        final SNSResponse snsResponse = new SNSResponse();
        snsResponse.setDelivery_order_id(22222L);
        snsResponse.setAccepted_at("20241115");
        when(mockHttpManager.doPostJson(
                        eq("host"),
                        eq(Map.ofEntries(Map.entry("value", "value"))),
                        eq(Map.ofEntries(Map.entry("value", "value"))),
                        any(HttpCallback.class),
                        eq(SNSResponse.class),
                        any(Object.class)))
                .thenReturn(snsResponse);
        final Optional<SNSResponse> result =
                snsServiceUnderTest.postSenSNS("message", "090123456789", "11232313");
        assertEquals(Optional.empty(), result);
        verify(mockHttpManager, times(1))
                .doPostJson(
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any());
    }
}
