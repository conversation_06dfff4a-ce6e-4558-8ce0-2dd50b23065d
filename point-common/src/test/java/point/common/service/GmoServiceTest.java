package point.common.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.initMocks;

import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import point.common.auth.model.TokenResponse;
import point.common.component.RedisManager;
import point.common.config.GmoConfig;
import point.common.config.SpringConfig;
import point.common.constant.CommonConstants;
import point.common.entity.AppConfiguration;
import point.common.exception.GmoRequestException;
import point.common.model.response.Account;
import point.common.model.response.AccountsResponse;
import point.common.model.response.VaIssueResponse;
import point.common.repos.AppConfigurationRepository;

class GmoServiceTest {

    @Mock private GmoConfig gmoConfig;

    @Mock private AppConfigurationRepository appConfigurationRepository;
    @Mock private RedisManager redisManager;
    @Mock private RestTemplate restTemplate;
    @Mock SpringConfig springConfig;

    private GmoService gmoServiceTest;

    @BeforeEach
    void setUp() {
        initMocks(this);
        gmoServiceTest = new GmoService();
        gmoServiceTest.restOperations = restTemplate;
        gmoServiceTest.gmoConfig = gmoConfig;
        gmoServiceTest.appConfigurationRepository = appConfigurationRepository;
        gmoServiceTest.redisManager = redisManager;
        gmoServiceTest.springConfig = springConfig;
        gmoServiceTest.hostExternal = "";
        AppConfiguration appConfiguration = new AppConfiguration();
        appConfiguration.setValue(
                "{\"accessToken\":\"ncRvV1WQlQVhjgcTBfmy2RYqERf1FiHz\",\"refreshToken\":\"7u328xjlyOZ1j9NeCZ0nLaDxIfu58BoJ\",\"scope\":\"private:bulk-transfer private:transfer private:virtual-account private:account\",\"tokenType\":\"Bearer\",\"expiresIn\":\"***********\",\"sessionId\":\"b4a6b543-675f-4294-ac52-b33f3b9bcad0\",\"hashKey\":\"NOJqkIdjZ\",\"state\":\"a5ce6eab6ddb7f7782b342a57570436bf5011b2710f7dadfa260ce7b804f47be\",\"expireAt\":*************}");
        when(appConfigurationRepository.findFirstByKey(CommonConstants.authorizationTokenKey))
                .thenReturn(appConfiguration);
        when(gmoConfig.getStgBaseEndpoint()).thenReturn("https://stg-api.gmo-aozora.com/ganb/api");
        when(gmoConfig.getAuthorizationToken()).thenReturn("/auth/v1/token");
    }

    @Test
    void callBackNullResponse() {
        when(restTemplate.postForObject(any(), any(), any())).thenReturn(null);
        assertThrows(
                GmoRequestException.class,
                () ->
                        gmoServiceTest.callBack(
                                "test",
                                "81db6d9dde21898820e8148811bac424364f923bd8ec1d4fc97d8337dc5bb440",
                                "",
                                ""));
    }

    @Test
    void callBack() throws GmoRequestException {
        TokenResponse response = new TokenResponse();
        response.setAccessToken("aaa");
        response.setRefreshToken("ccc");
        response.setExpiresIn("999");
        response.setTokenType("a");
        response.setScope("b");
        when(restTemplate.postForObject(any(), any(), any())).thenReturn(response);
        gmoServiceTest.callBack(
                "test", "81db6d9dde21898820e8148811bac424364f923bd8ec1d4fc97d8337dc5bb440", "", "");
        verify(restTemplate, times(1)).postForObject(any(), any(), any());
    }

    @Test
    void accounts_cached() throws GmoRequestException {
        when(gmoConfig.getAccountsUri()).thenReturn("/corporation/v1/accounts");
        when(redisManager.get(CommonConstants.GMO_CORPORATE_KEY)).thenReturn("123");
        AccountsResponse response = new AccountsResponse();
        when(restTemplate.exchange(
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        (Class<Object>) any()))
                .thenReturn(ResponseEntity.status(HttpStatus.OK).body(response));
        assertEquals("123", gmoServiceTest.accounts("test"));
        verify(restTemplate, times(0))
                .exchange(
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        (Class<Object>) any());
    }

    @Test
    void accounts_no_cached() throws GmoRequestException {
        when(gmoConfig.getAccountsUri()).thenReturn("/corporation/v1/accounts");
        when(redisManager.get(CommonConstants.GMO_CORPORATE_KEY)).thenReturn(null);
        AccountsResponse response = new AccountsResponse();
        List<Account> accounts = new ArrayList<>();
        Account account = new Account();
        account.setAccountId("************");
        account.setAccountName("株式会社ｃｏｉｎｂｏｏｋ　顧客口");
        account.setAccountNumber("1014029");
        accounts.add(account);
        response.setAccounts(accounts);
        when(springConfig.isStg()).thenReturn(true);
        when(restTemplate.exchange(
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        (Class<Object>) any()))
                .thenReturn(ResponseEntity.status(HttpStatus.OK).body(response));
        assertEquals("************", gmoServiceTest.accounts("YjnImHdy3eGg1vi08YYRRj2e6jOsd0hm"));
        verify(restTemplate, times(1))
                .exchange(
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        (Class<Object>) any());
    }

    @Test
    void accounts_prd_cached() throws GmoRequestException {
        when(gmoConfig.getAccountsUri()).thenReturn("/corporation/v1/accounts");
        when(redisManager.get(CommonConstants.GMO_CORPORATE_KEY)).thenReturn(null);
        AccountsResponse response = new AccountsResponse();
        List<Account> accounts = new ArrayList<>();
        Account account = new Account();
        account.setAccountId("************");
        account.setAccountName("株式会社ｃｏｉｎｂｏｏｋ　顧客口");
        account.setAccountNumber("1149414");
        accounts.add(account);
        response.setAccounts(accounts);
        when(springConfig.isPrd()).thenReturn(true);
        when(restTemplate.exchange(
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        (Class<Object>) any()))
                .thenReturn(ResponseEntity.status(HttpStatus.OK).body(response));
        assertEquals("************", gmoServiceTest.accounts("YjnImHdy3eGg1vi08YYRRj2e6jOsd0hm"));
        verify(restTemplate, times(1))
                .exchange(
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.any(),
                        (Class<Object>) any());
    }

    @Test
    void refresh_token_null() {
        when(gmoConfig.getClientId()).thenReturn("Av1khkZhhRXB8ppG");
        when(gmoConfig.getSecret())
                .thenReturn("8PBpEKucB4IktxrZngK99wpNSxvFWOcpamGNZSBrKnKjt9Q8Iqs");
        when(gmoConfig.getAuthorizationToken()).thenReturn("/auth/v1/token");
        when(restTemplate.postForObject(any(), any(), any())).thenReturn(null);
        assertThrows(GmoRequestException.class, () -> gmoServiceTest.refreshToken());
    }

    @Test
    void refresh_token() throws GmoRequestException {
        when(gmoConfig.getClientId()).thenReturn("Av1khkZhhRXB8ppG");
        when(gmoConfig.getSecret())
                .thenReturn("8PBpEKucB4IktxrZngK99wpNSxvFWOcpamGNZSBrKnKjt9Q8Iqs");
        when(gmoConfig.getAuthorizationToken()).thenReturn("/auth/v1/token");
        TokenResponse response = new TokenResponse();
        response.setAccessToken("123");
        response.setRefreshToken("456");
        response.setTokenType("1");
        response.setScope("2");
        response.setExpiresIn("999");
        when(restTemplate.postForObject(any(), any(), any())).thenReturn(response);
        gmoServiceTest.refreshToken();
        verify(restTemplate, times(1)).postForObject(any(), any(), any());
    }

    @Test
    void issue() throws GmoRequestException {
        when(redisManager.get(CommonConstants.GMO_CORPORATE_KEY)).thenReturn("123");
        when(gmoConfig.getClientId()).thenReturn("Av1khkZhhRXB8ppG");
        when(gmoConfig.getSecret())
                .thenReturn("8PBpEKucB4IktxrZngK99wpNSxvFWOcpamGNZSBrKnKjt9Q8Iqs");
        when(gmoConfig.getIssueAccountUri()).thenReturn("/corporation/v1/va/issue");
        VaIssueResponse response = new VaIssueResponse();
        response.setVaTypeName("aa");
        when(restTemplate.postForEntity(any(), any(), any()))
                .thenReturn(ResponseEntity.status(HttpStatus.OK).body(response));
        gmoServiceTest.issueAccount("2");
        verify(restTemplate, times(1)).postForEntity(any(), any(), any());
    }
}
