package point.common.service;

import static org.junit.jupiter.api.Assertions.*;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import org.junit.jupiter.api.Test;
import point.common.util.DateUnit;

class DateUtilTest {

    @Test
    void test() {
        final var baseDate = new Date(1675350000000L);
        assertEquals("2023-02-02 15:00:00", dateToString(baseDate));

        final var fromTo = DateUnit.createFromTo(1675350000000L);
        final var from = fromTo.getLeft();
        assertEquals("2023-02-02 15:00:00", dateToString(from));

        final var to = fromTo.getRight();
        assertEquals("2023-02-03 15:00:00", dateToString(to));
    }

    @Test
    void test2() {
        final var fromTo = DateUnit.createFromTo(1675350010000L);
        final var from = fromTo.getLeft();
        assertEquals("2023-02-02 15:00:00", dateToString(from));

        final var to = fromTo.getRight();
        assertEquals("2023-02-03 15:00:00", dateToString(to));
    }

    String dateToString(Date date) {
        final var baseZdt = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.of("UTC"));
        return baseZdt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
