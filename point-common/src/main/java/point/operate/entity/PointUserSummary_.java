package point.operate.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.entity.AbstractEntity_;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PointUserSummary.class)
public abstract class PointUserSummary_ extends AbstractEntity_ {

    public static volatile SingularAttribute<PointUserSummary, Date> targetAt;
    public static volatile SingularAttribute<PointUserSummary, Long> users;
    public static volatile SingularAttribute<PointUserSummary, Long> pointUserPartnerActive;
    public static volatile SingularAttribute<PointUserSummary, Long> pointUserPartnerDate;

    public static final String TARGET_AT = "targetAt";
    public static final String USERS = "users";
    public static final String POINT_USER_PARTNER_ACTIVE = "pointUserPartnerActive";
    public static final String POINT_USER_PARTNER_DATE = "pointUserPartnerDate";
}
