package point.operate.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.OrderStatus;
import point.common.entity.AbstractEntity_;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(OperateCoverOrder.class)
public abstract class OperateCoverOrder_ extends AbstractEntity_ {
    public static volatile SingularAttribute<OperateCoverOrder, Long> symbolId;
    public static volatile SingularAttribute<OperateCoverOrder, Long> aggregatedOrderId;
    public static volatile SingularAttribute<OperateCoverOrder, String> orderSide;
    public static volatile SingularAttribute<OperateCoverOrder, String> orderType;
    public static volatile SingularAttribute<OperateCoverOrder, BigDecimal> price;
    public static volatile SingularAttribute<OperateCoverOrder, BigDecimal> usdtPrice;
    public static volatile SingularAttribute<OperateCoverOrder, BigDecimal> amount;
    public static volatile SingularAttribute<OperateCoverOrder, BigDecimal> remainingAmount;
    public static volatile SingularAttribute<OperateCoverOrder, BigDecimal> fee;
    public static volatile SingularAttribute<OperateCoverOrder, String> mm;
    public static volatile SingularAttribute<OperateCoverOrder, String> mmOrderId;
    public static volatile SingularAttribute<OperateCoverOrder, OrderStatus> orderStatus;
    public static volatile SingularAttribute<OperateCoverOrder, Boolean> isOffset;

    public static final String SYMBOL_ID = "symbolId";
    public static final String AGGREGATED_ORDER_ID = "aggregatedOrderId";
    public static final String ORDER_SIDE = "orderSide";
    public static final String ORDER_TYPE = "orderType";
    public static final String PRICE = "price";
    public static final String USDT_PRICE = "usdtPrice";
    public static final String AMOUNT = "amount";
    public static final String REMAINING_AMOUNT = "remainingAmount";
    public static final String FEE = "fee";
    public static final String MM = "mm";
    public static final String MM_ORDER_ID = "mmOrderId";
    public static final String ORDER_STATUS = "orderStatus";
    public static final String IS_OFFSET = "isOffset";
}
