package point.operate.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.entity.AbstractEntity_;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(OperateAggregatedOrder.class)
public abstract class OperateAggregatedOrder_ extends AbstractEntity_ {

    public static volatile SingularAttribute<OperateAggregatedOrder, Long> symbolId;
    public static volatile SingularAttribute<OperateAggregatedOrder, String> orderSide;
    public static volatile SingularAttribute<OperateAggregatedOrder, String> orderType;
    public static volatile SingularAttribute<OperateAggregatedOrder, BigDecimal> amount;

    public static final String SYMBOL_ID = "symbolId";
    public static final String ORDER_SIDE = "orderSide";
    public static final String ORDER_TYPE = "orderType";
    public static final String AMOUNT = "amount";
}
