package point.operate.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Exchange;
import point.common.constant.OrderSide;
import point.common.constant.OrderStatus;
import point.common.constant.OrderType;
import point.common.entity.AbstractEntity;

@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "operate_cover_order")
public class OperateCoverOrder extends AbstractEntity {

    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    @Column(name = "aggregated_order_id", nullable = false)
    private Long aggregatedOrderId;

    @Column(name = "order_side", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderSide orderSide;

    @Column(name = "order_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @Column(name = "price", nullable = false)
    private BigDecimal price;

    @Column(name = "usdt_price")
    private BigDecimal usdtPrice;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Column(name = "remaining_amount", nullable = false)
    private BigDecimal remainingAmount;

    @Column(name = "fee", nullable = false)
    private BigDecimal fee;

    @Column(name = "mm", nullable = false)
    @Enumerated(EnumType.STRING)
    private Exchange mm;

    @Column(name = "mm_order_id", nullable = false)
    private String mmOrderId;

    @Column(name = "order_status", length = 128)
    @Enumerated(EnumType.STRING)
    private OrderStatus orderStatus;

    @Column(name = "is_offset")
    private boolean isOffset;

    @Column(name = "client_id")
    private String clientId;

    public OperateCoverOrder setAnotherProperties(
            Long symbolId,
            Long aggregatedOrderId,
            OrderSide orderSide,
            OrderType orderType,
            BigDecimal price,
            BigDecimal usdtPrice,
            BigDecimal amount,
            BigDecimal remainingAmount,
            BigDecimal fee,
            Exchange mm,
            String mmOrderId,
            OrderStatus orderStatus) {
        this.symbolId = symbolId;
        this.aggregatedOrderId = aggregatedOrderId;
        this.orderSide = orderSide;
        this.orderType = orderType;
        this.price = price;
        this.usdtPrice = usdtPrice;
        this.amount = amount;
        this.remainingAmount = remainingAmount;
        this.fee = fee;
        this.mm = mm;
        this.mmOrderId = mmOrderId;
        this.orderStatus = orderStatus;
        this.isOffset = false;
        return this;
    }
}
