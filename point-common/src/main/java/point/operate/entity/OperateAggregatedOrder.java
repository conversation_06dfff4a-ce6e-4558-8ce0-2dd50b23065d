package point.operate.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.entity.AbstractEntity;

@Getter
@Setter
@Entity
@Table(name = "operate_aggregated_order")
@NoArgsConstructor
public class OperateAggregatedOrder extends AbstractEntity {

    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    @Column(name = "order_side", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderSide orderSide;

    @Column(name = "order_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @Column(name = "amount", nullable = false, precision = 34, scale = 20)
    private BigDecimal amount;

    public OperateAggregatedOrder setAnotherProperties(
            Long symbolId, OrderSide orderSide, OrderType orderType, BigDecimal amount) {
        this.symbolId = symbolId;
        this.orderSide = orderSide;
        this.orderType = orderType;
        this.amount = amount;
        return this;
    }
}
