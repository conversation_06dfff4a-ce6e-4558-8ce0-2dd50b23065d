package point.operate.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.entity.AbstractEntity;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "point_user_summary")
@ToString(callSuper = true, doNotUseGetters = true)
public class PointUserSummary extends AbstractEntity {

    private static final long serialVersionUID = -8778937891595714900L;

    @Column(name = "target_at", nullable = false)
    private Date targetAt;

    @Column(name = "users", nullable = false)
    private Long users = 0L;

    @Column(name = "point_user_partner_active", nullable = false)
    private Long pointUserPartnerActive = 0L;

    @Column(name = "point_user_partner_date", nullable = false)
    private Long pointUserPartnerDate = 0L;

    public PointUserSummary(Date targetAt) {
        this.targetAt = targetAt;
    }
}
