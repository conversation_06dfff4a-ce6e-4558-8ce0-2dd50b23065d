package point.operate.predicate;

import java.math.BigDecimal;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.Exchange;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.predicate.EntityPredicate;
import point.operate.entity.OperateCoverOrder;
import point.operate.entity.OperateCoverOrder_;

@Component
public class OperateCoverOrderPredicate extends EntityPredicate<OperateCoverOrder> {

    public Predicate equalSymbol(
            CriteriaBuilder criteriaBuilder, Root<OperateCoverOrder> root, Long symbolId) {
        return criteriaBuilder.equal(root.get("symbolId"), symbolId);
    }

    public Predicate greaterThanOrEqualToRemainingAmount(
            CriteriaBuilder criteriaBuilder,
            Root<OperateCoverOrder> root,
            BigDecimal greaterThanRemainingAmount) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get("remainingAmount"), greaterThanRemainingAmount);
    }

    public Predicate lessThanRemainingAmount(
            CriteriaBuilder criteriaBuilder,
            Root<OperateCoverOrder> root,
            BigDecimal remainingAmount) {
        return criteriaBuilder.lessThanOrEqualTo(root.get("remainingAmount"), remainingAmount);
    }

    public Predicate equalOrderType(
            CriteriaBuilder criteriaBuilder, Root<OperateCoverOrder> root, OrderType orderType) {
        return criteriaBuilder.equal(root.get("orderType"), orderType);
    }

    public Predicate inOrderType(Root<OperateCoverOrder> root, OrderType[] orderTypes) {
        return root.get(OperateCoverOrder_.orderType).in((Object[]) orderTypes);
    }

    public Predicate notInOrderType(
            CriteriaBuilder criteriaBuilder,
            Root<OperateCoverOrder> root,
            OrderType[] exceptOrderTypes) {
        return criteriaBuilder.not(inOrderType(root, exceptOrderTypes));
    }

    public Predicate equalOrderSide(
            CriteriaBuilder criteriaBuilder, Root<OperateCoverOrder> root, OrderSide orderSide) {
        return criteriaBuilder.equal(root.get(OperateCoverOrder_.orderSide), orderSide);
    }

    public Predicate equalExchange(
            CriteriaBuilder criteriaBuilder, Root<OperateCoverOrder> root, Exchange exchange) {
        return criteriaBuilder.equal(root.get(OperateCoverOrder_.MM), exchange);
    }
}
