package point.operate.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.predicate.EntityPredicate;
import point.operate.entity.PointUserSummary;
import point.operate.entity.PointUserSummary_;

@Component
public class PointUserSummaryPredicate extends EntityPredicate<PointUserSummary> {

    public Predicate greaterThanOrEqualToTargetAt(
            CriteriaBuilder criteriaBuilder, Root<PointUserSummary> root, Date targetAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(PointUserSummary_.targetAt), targetAt);
    }

    public Predicate lessThanTargetAt(
            CriteriaBuilder criteriaBuilder, Root<PointUserSummary> root, Date targetAt) {
        return criteriaBuilder.lessThan(root.get(PointUserSummary_.targetAt), targetAt);
    }

    public Predicate equalUsers(
            CriteriaBuilder criteriaBuilder, Root<PointUserSummary> root, Long users) {
        return criteriaBuilder.equal(root.get(PointUserSummary_.users), users);
    }

    public Predicate equalPointUserPartnerActive(
            CriteriaBuilder criteriaBuilder,
            Root<PointUserSummary> root,
            Long pointUserPartnerActive) {
        return criteriaBuilder.equal(
                root.get(PointUserSummary_.pointUserPartnerActive), pointUserPartnerActive);
    }

    public Predicate equalPointUserPartnerDate(
            CriteriaBuilder criteriaBuilder,
            Root<PointUserSummary> root,
            Long pointUserPartnerDate) {
        return criteriaBuilder.equal(
                root.get(PointUserSummary_.pointUserPartnerDate), pointUserPartnerDate);
    }
}
