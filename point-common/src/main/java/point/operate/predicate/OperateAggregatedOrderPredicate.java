package point.operate.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.predicate.EntityPredicate;
import point.operate.entity.OperateAggregatedOrder;
import point.operate.entity.OperateAggregatedOrder_;

@Component
public class OperateAggregatedOrderPredicate extends EntityPredicate<OperateAggregatedOrder> {

    public Predicate equalSymbolId(
            CriteriaBuilder criteriaBuilder, Root<OperateAggregatedOrder> root, Long symbolId) {
        return criteriaBuilder.equal(root.get(OperateAggregatedOrder_.symbolId), symbolId);
    }

    public Predicate equalOrderSide(
            CriteriaBuilder criteriaBuilder,
            Root<OperateAggregatedOrder> root,
            OrderSide orderSide) {
        return criteriaBuilder.equal(root.get(OperateAggregatedOrder_.orderSide), orderSide);
    }

    public Predicate equalOrderType(
            CriteriaBuilder criteriaBuilder,
            Root<OperateAggregatedOrder> root,
            OrderType orderType) {
        return criteriaBuilder.equal(root.get(OperateAggregatedOrder_.orderType), orderType);
    }
}
