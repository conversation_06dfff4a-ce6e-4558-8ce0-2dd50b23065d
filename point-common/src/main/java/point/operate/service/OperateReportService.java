package point.operate.service;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPRow;
import com.itextpdf.text.pdf.PdfPTable;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.model.response.ModelTableSource;
import point.common.model.response.ReportData;
import point.common.service.*;
import point.common.util.CurrencyUtils;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.pos.entity.PosTrade;
import point.pos.service.PosTradeService;

@RequiredArgsConstructor
@Service
public class OperateReportService {
    private final SymbolService symbolService;
    private final CurrencyConfigService currencyConfigService;
    ;
    private final AssetSummaryService assetSummaryService;
    private final SystemConfigService systemConfigService;
    private final FiatDepositService fiatDepositService;
    private final FiatWithdrawalService fiatWithDrawalService;
    private final PosTradeService posTradeService;

    public static final String REPORT_ID_PREFIX_DEPOSIT = "DEP";
    public static final String REPORT_ID_PREFIX_WITHDRAWAL = "WTH";

    public ReportData createReportData(Long userId, String firstDay, String lastDay)
            throws CustomException {
        long dateFrom = FormatUtil.parseJst(firstDay, FormatPattern.YYYYMMDD).getTime(); // 初日の00:00
        long dateTo =
                FormatUtil.parseJst(lastDay, FormatPattern.YYYYMMDD).getTime()
                        + 60 * 60 * 24 * 1000; // 最終日翌日の00:00
        ReportData reportData = new ReportData();

        // marketMakerPortalIdのリストを取得(自己・媒介判定に使用)
        List<Long> marketMakerUserIdList = new ArrayList<Long>();
        Arrays.asList(
                        systemConfigService
                                .findByCondition(null, null, "marketMakerUserId")
                                .getValue()
                                .split(","))
                .forEach(
                        marketMakerUserId ->
                                marketMakerUserIdList.add(Long.valueOf(marketMakerUserId)));

        reportData.setUserId(userId);
        reportData.setCreatedDay(FormatUtil.formatJst(new Date(), FormatPattern.YYYY_MM_DD_SLASH));
        reportData.setFirstDay(
                firstDay.substring(0, 4)
                        + "/"
                        + firstDay.substring(4, 6)
                        + "/"
                        + firstDay.substring(6, 8));
        reportData.setLastDay(
                lastDay.substring(0, 4)
                        + "/"
                        + lastDay.substring(4, 6)
                        + "/"
                        + lastDay.substring(6, 8));

        // add assets
        List<ReportData.AssetReport> assetReportList = new ArrayList<ReportData.AssetReport>();

        final var currencyConfigs =
                currencyConfigService.findAllByCondition(TradeType.OPERATE, null).stream()
                        .filter(CurrencyConfig::isEnabled)
                        .toList();
        for (CurrencyConfig config : currencyConfigs) {
            Currency currency = config.getCurrency();
            ReportData.AssetReport assetReport = new ReportData.AssetReport();
            AssetSummary assetSnapshot = getAssetSnapshot(userId, currency, lastDay);

            // スナップショットの不存在をを許容するのは、新規通貨の過去分のみ（過去分でも前日の場合は許容しない）
            if (assetSnapshot == null) {
                assetReport.setAmount(BigDecimal.ZERO.toPlainString());
            } else {
                assetReport.setAmount(
                        currency.getScaledAmount(
                                        assetSnapshot.getCurrentAmount(), RoundingMode.FLOOR)
                                .toPlainString());
            }
            CurrencyUtils.setCurrencyToAssetReport(assetReport, currency);
            assetReportList.add(assetReport);
        }
        reportData.setAssetReportList(
                assetReportList.stream()
                        .sorted(Comparator.comparing(ReportData.AssetReport::getCurrencyIndex))
                        .collect(Collectors.toList()));
        // add pos trades
        List<ReportData.PosTradeReport> posTradeReportList = new ArrayList<>();
        symbolService
                .findByCondition(TradeType.OPERATE)
                .forEach(
                        symbol -> {
                            // get master pos_trade data
                            posTradeService
                                    .findByCondition(
                                            symbol.getId(),
                                            userId,
                                            new Date(dateFrom),
                                            new Date(dateTo))
                                    .forEach(
                                            posTrade -> {
                                                posTradeReportList.add(
                                                        createPosTradeReportFromTrade(
                                                                symbol, posTrade));
                                            });
                            // get historical pos_trade data
                            posTradeService
                                    .findFromPosTradeHistory(
                                            symbol,
                                            userId,
                                            null,
                                            null,
                                            null,
                                            new Date(dateFrom),
                                            new Date(dateTo),
                                            0,
                                            Integer.MAX_VALUE)
                                    .forEach(
                                            posTrade -> {
                                                posTradeReportList.add(
                                                        createPosTradeReportFromTrade(
                                                                symbol, posTrade));
                                            });
                        });
        reportData.setPosTradeReportList(
                posTradeReportList.stream()
                        .sorted(Comparator.comparing(ReportData.PosTradeReport::getDateTime))
                        .collect(Collectors.toList()));

        // add deposit
        List<ReportData.DepositWithdrawalReport> dwReportList = new ArrayList<>();
        fiatDepositService
                .findAllByCondition(userId, null, null, dateFrom, dateTo, FiatDepositStatus.DONE)
                .forEach(
                        fiatDeposit -> {
                            dwReportList.add(createDwReportFromDeposit(fiatDeposit));
                        });
        fiatWithDrawalService
                .findAllByCondition(userId, null, null, dateFrom, dateTo, FiatWithdrawalStatus.DONE)
                .forEach(
                        fiatWithDrawal -> {
                            dwReportList.add(createDwReportFromWithdrawal(fiatWithDrawal));
                        });
        reportData.setDepositWithdrawalReportList(
                dwReportList.stream()
                        .sorted(
                                Comparator.comparing(
                                                ReportData.DepositWithdrawalReport::getDateTime)
                                        .thenComparing(
                                                ReportData.DepositWithdrawalReport
                                                        ::getCurrencyIndex)
                                        .thenComparing(
                                                ReportData.DepositWithdrawalReport::getSortIndex))
                        .collect(Collectors.toList()));

        BigDecimal transactionFees =
                posTradeReportList.stream()
                        .map(ReportData.PosTradeReport::getFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        reportData.setTransactionFees(transactionFees);
        // 入出金手数料sum
        BigDecimal depositWithdrawalFees = BigDecimal.ZERO;
        List<ReportData.DepositWithdrawalReport> commissionJpyConvertFeeList =
                dwReportList.stream().filter(e -> e.getCommissionJpyConvert() != null).toList();
        // 暗号資産日本円換算額Sum
        depositWithdrawalFees =
                commissionJpyConvertFeeList.stream()
                        .map(ReportData.DepositWithdrawalReport::getCommissionJpyConvert)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        reportData.setDepositWithdrawalFees(depositWithdrawalFees);
        // 合計
        reportData.setTotal(transactionFees.add(depositWithdrawalFees));
        // 内消費税
        BigDecimal consumptionTaxIncluded =
                reportData
                        .getTotal()
                        .subtract(
                                reportData
                                        .getTotal()
                                        .divide(BigDecimal.valueOf(1.1), 2, RoundingMode.HALF_UP))
                        .setScale(0, RoundingMode.DOWN);
        reportData.setConsumptionTaxIncluded(consumptionTaxIncluded);

        return reportData;
    }

    // add pos_trade data
    private ReportData.PosTradeReport createPosTradeReportFromTrade(
            Symbol symbol, PosTrade posTrade) {
        ReportData.PosTradeReport posTradeReport = new ReportData.PosTradeReport();
        posTradeReport.setId(String.format("%08d", posTrade.getId()));
        CurrencyUtils.setSymbolToPosTradeReport(posTradeReport, symbol.getCurrencyPair());
        posTradeReport.setAmount(posTrade.getAmount());
        posTradeReport.setDateTime(formatReportDate(posTrade.getCreatedAt()));
        posTradeReport.setFee(posTrade.getFee());
        posTradeReport.setOrderSide(posTrade.getOrderSide());
        posTradeReport.setPaidAmount(posTrade.getAssetAmount());
        posTradeReport.setPrice(posTrade.getPrice());
        posTradeReport.setConsumptionTaxAmount(
                posTrade.getFee()
                        .subtract(
                                posTrade.getFee()
                                        .divide(BigDecimal.valueOf(1.1), 2, RoundingMode.HALF_UP))
                        .setScale(0, RoundingMode.DOWN)); // 消費税額
        return posTradeReport;
    }

    private ReportData.DepositWithdrawalReport createDwReportFromDeposit(FiatDeposit fiatDeposit) {
        ReportData.DepositWithdrawalReport dwReport = new ReportData.DepositWithdrawalReport();
        dwReport.setId(
                createReportId(
                        fiatDeposit.getId(), PosConstants.CURRENCY_JYP, REPORT_ID_PREFIX_DEPOSIT));
        dwReport.setCurrency(PosConstants.CURRENCY_JYP);
        dwReport.setDepositAmount(fiatDeposit.getAmount().subtract(fiatDeposit.getFee()));
        dwReport.setDateTime(formatReportDate(fiatDeposit.getUpdatedAt()));
        dwReport.setWithdrawalAmount(null);
        dwReport.setFee(fiatDeposit.getFee());
        dwReport.setSortIndex(0);
        dwReport.setCommissionJpyConvert(fiatDeposit.getFee()); // 手数料金額（日本円換算額）税込
        // 消費税額
        dwReport.setConsumptionTaxAmount(
                dwReport.getCommissionJpyConvert()
                        .subtract(
                                dwReport.getCommissionJpyConvert()
                                        .divide(BigDecimal.valueOf(1.1), 2, RoundingMode.HALF_UP))
                        .setScale(0, RoundingMode.DOWN));
        return dwReport;
    }

    private ReportData.DepositWithdrawalReport createDwReportFromWithdrawal(
            FiatWithdrawal fiatWithdrawal) {
        ReportData.DepositWithdrawalReport dwReport = new ReportData.DepositWithdrawalReport();
        dwReport.setId(
                createReportId(
                        fiatWithdrawal.getId(),
                        PosConstants.CURRENCY_JYP,
                        REPORT_ID_PREFIX_WITHDRAWAL));
        dwReport.setCurrency(PosConstants.CURRENCY_JYP);
        dwReport.setDepositAmount(null);
        dwReport.setDateTime(formatReportDate(fiatWithdrawal.getUpdatedAt()));
        dwReport.setWithdrawalAmount(fiatWithdrawal.getAmount().subtract(fiatWithdrawal.getFee()));
        dwReport.setFee(fiatWithdrawal.getFee());
        dwReport.setSortIndex(2);
        dwReport.setCommissionJpyConvert(fiatWithdrawal.getFee()); // 手数料金額（日本円換算額）税込
        // 消費税額
        dwReport.setConsumptionTaxAmount(
                dwReport.getCommissionJpyConvert()
                        .subtract(
                                dwReport.getCommissionJpyConvert()
                                        .divide(BigDecimal.valueOf(1.1), 2, RoundingMode.HALF_UP))
                        .setScale(0, RoundingMode.DOWN));
        return dwReport;
    }

    private String createReportId(Long id, String currency, String prefix) {
        return new StringBuilder()
                .append(prefix)
                .append("_")
                .append(currency)
                .append("_")
                .append(String.format("%08d", id))
                .toString();
    }

    private AssetSummary getAssetSnapshot(Long userId, Currency currency, String yyyyMMdd) {
        // 日付範囲の指定
        Date targetFromTemp =
                FormatUtil.parseJst(yyyyMMdd, FormatPattern.YYYYMMDD); // 対象日 たとえば20231015 00:00:00
        Date targetFrom =
                new Date(
                        targetFromTemp.getTime()
                                - 1000 * 3600 * 9); // 前日のUTC変換後Date Wed Oct 14 15:00:00 GMT 2023
        Date targetTo =
                new Date(targetFromTemp.getTime() + 1000); // 変換後Date Wed Oct 14 15:00:01 GMT 2023

        List<AssetSummary> assetSummaryList =
                assetSummaryService.findByCondition(userId, currency, targetFrom, targetTo);
        if (assetSummaryList == null || assetSummaryList.isEmpty()) {
            return null;
        } else {
            return assetSummaryList.get(0);
        }
    }

    // 日付変換フォーマットを統一化
    private String formatReportDate(Date date) {
        return FormatUtil.formatJst(date, FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH);
    }

    public void createdPDFContent(
            Document document, Long userId, String firstDay, String lastDay, boolean isBatch)
            throws CustomException, DocumentException, IOException {
        ClassPathResource cpr = new ClassPathResource("font/MS-PGothic.ttf");
        Font font =
                new Font(
                        BaseFont.createFont(
                                cpr.getPath(), BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED),
                        11);
        Map<String, String> paramsMap = createPDFParamsMap(userId, firstDay, lastDay);
        ModelTableSource mts = createPDFTable(userId, firstDay, lastDay, isBatch);
        PdfPCell blankCell = new PdfPCell();
        blankCell.disableBorderSide(15);
        PdfPTable informationTable = new PdfPTable(4);
        int[] informationTableColWidths = new int[] {1, 4, 1, 4};
        informationTable.setWidths(informationTableColWidths);
        informationTable.setWidthPercentage(100);
        informationTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        PdfPCell title = new PdfPCell(new Paragraph("【タイトル】", font));
        PdfPCell titleContent = new PdfPCell(new Paragraph("年間取引報告書兼残高報告書", font));
        PdfPCell operator = new PdfPCell(new Paragraph("【運営者】", font));
        PdfPCell operatorContent = new PdfPCell(new Paragraph("株式会社 backseat", font));
        informationTable.addCell(title);
        informationTable.addCell(titleContent);
        informationTable.addCell(operator);
        informationTable.addCell(operatorContent);

        PdfPCell createdDay = new PdfPCell(new Paragraph("【作成日】", font));
        PdfPCell createdDayContent = new PdfPCell(new Paragraph(paramsMap.get("createdDay"), font));
        PdfPCell address = new PdfPCell(new Paragraph("【住所】", font));
        PdfPCell addressContent1 = new PdfPCell(new Paragraph("〒107-0052", font));
        informationTable.addCell(createdDay);
        informationTable.addCell(createdDayContent);
        informationTable.addCell(address);
        informationTable.addCell(addressContent1);
        PdfPCell addressContent2 = new PdfPCell(new Paragraph("東京都港区赤坂2-18-14 赤坂STビル2階", font));
        PdfPCell addressContent3 =
                new PdfPCell(new Paragraph("登録番号： 暗号資産交換業者 関東財務局長 第00026号", font));
        PdfPCell addressContent4 = new PdfPCell(new Paragraph("加入協会： 一般社団法人日本暗号資産取引業協会", font));
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(addressContent2);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(addressContent3);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(addressContent4);

        // 空行
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);

        PdfPCell userName = new PdfPCell(new Paragraph("【氏名（法人）】", font));
        PdfPCell userNameContent =
                new PdfPCell(new Paragraph((String) paramsMap.get("userId"), font));
        informationTable.addCell(userName);
        informationTable.addCell(userNameContent);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);

        PdfPCell targetDate = new PdfPCell(new Paragraph("【対象期間】", font));
        PdfPCell targetDateContent =
                new PdfPCell(new Paragraph((String) paramsMap.get("targetDate"), font));
        informationTable.addCell(targetDate);
        informationTable.addCell(targetDateContent);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);

        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);
        informationTable.addCell(blankCell);

        // セルの枠を削除
        for (PdfPRow row : informationTable.getRows()) {
            for (PdfPCell cell : row.getCells()) {
                cell.setMinimumHeight(18f);
                cell.disableBorderSide(15);
            }
        }

        // テーブルタイトル
        PdfPCell tableTitle = new PdfPCell(new Paragraph("【取引所等】", font));
        tableTitle.setFixedHeight(20f);
        tableTitle.disableBorderSide(15);
        tableTitle.setColspan(4);
        informationTable.addCell(tableTitle);

        // 通貨名,年始数量,年末数量
        PdfPTable tablePrt1 = new PdfPTable(3);
        // テーブルヘッダ設定
        tablePrt1.setHeaderRows(1);
        int[] tablePrt1ColWidths = new int[] {6, 15, 15};
        tablePrt1.setWidths(tablePrt1ColWidths);
        tablePrt1.setWidthPercentage(45);
        tablePrt1.setHorizontalAlignment(Element.ALIGN_LEFT);
        PdfPCell prt1Currency = new PdfPCell(new Paragraph("通貨名", font));
        PdfPCell yearBeginAmount = new PdfPCell(new Paragraph("年始数量", font));
        PdfPCell yearEndAmount = new PdfPCell(new Paragraph("年末数量", font));

        prt1Currency.setBackgroundColor(new BaseColor(255, 165, 0));
        yearBeginAmount.setBackgroundColor(new BaseColor(255, 165, 0));
        yearEndAmount.setBackgroundColor(new BaseColor(255, 165, 0));

        tablePrt1.addCell(prt1Currency);
        tablePrt1.addCell(yearBeginAmount);
        tablePrt1.addCell(yearEndAmount);
        for (Map<String, String> prt1Map : mts.getReportList_prt1()) {
            PdfPCell prt1CurrencyContent =
                    new PdfPCell(new Paragraph(prt1Map.get("currency"), font));
            PdfPCell yearBeginAmountContent =
                    new PdfPCell(new Paragraph(prt1Map.get("yearBeginAmount"), font));
            PdfPCell yearEndAmountContent =
                    new PdfPCell(new Paragraph(prt1Map.get("yearEndAmount"), font));

            tablePrt1.addCell(prt1CurrencyContent);
            tablePrt1.addCell(yearBeginAmountContent);
            tablePrt1.addCell(yearEndAmountContent);
        }
        // セルのプロパティを設定
        for (PdfPRow row : tablePrt1.getRows()) {
            for (PdfPCell cell : row.getCells()) {
                cell.setPaddingLeft(5f);
                cell.setPaddingRight(5f);
                cell.setPaddingTop(3f);
                cell.setPaddingBottom(7f);
            }
        }
        PdfPCell blankprt1 = new PdfPCell();
        blankprt1.setFixedHeight(40);
        blankprt1.setColspan(6);
        blankprt1.disableBorderSide(15);
        tablePrt1.addCell(blankprt1);

        // 通貨名,年中購入数量(*1),年中購入金額(*2),年中売却数量(*3)，年中売却金額(*4),年中移入数量(*5)
        PdfPTable tablePrt2 = new PdfPTable(6);
        // テーブルヘッダ設定
        tablePrt2.setHeaderRows(1);
        int[] tablePrt2ColWidths = new int[] {6, 15, 15, 15, 15, 15};
        tablePrt2.setWidths(tablePrt2ColWidths);
        tablePrt2.setWidthPercentage(100);
        PdfPCell prt2Currency = new PdfPCell(new Paragraph("通貨名", font));
        PdfPCell yearBuyAmount = new PdfPCell(new Paragraph("年中購入数量(*1)", font));
        PdfPCell yearBuyAsset = new PdfPCell(new Paragraph("年中購入金額(*2)", font));
        PdfPCell yearSellAmount = new PdfPCell(new Paragraph("年中売却数量(*3)", font));
        PdfPCell yearSellAsset = new PdfPCell(new Paragraph("年中売却金額(*4)", font));
        PdfPCell moveInAmount = new PdfPCell(new Paragraph("年中移入数量(*5)", font));

        prt2Currency.setBackgroundColor(new BaseColor(255, 165, 0));
        yearBuyAmount.setBackgroundColor(new BaseColor(255, 165, 0));
        yearBuyAsset.setBackgroundColor(new BaseColor(255, 165, 0));
        yearSellAmount.setBackgroundColor(new BaseColor(255, 165, 0));
        yearSellAsset.setBackgroundColor(new BaseColor(255, 165, 0));
        moveInAmount.setBackgroundColor(new BaseColor(255, 165, 0));

        tablePrt2.addCell(prt2Currency);
        tablePrt2.addCell(yearBuyAmount);
        tablePrt2.addCell(yearBuyAsset);
        tablePrt2.addCell(yearSellAmount);
        tablePrt2.addCell(yearSellAsset);
        tablePrt2.addCell(moveInAmount);

        for (Map<String, String> prt2Map : mts.getReportList_prt2()) {
            PdfPCell prt2CurrencyContent =
                    new PdfPCell(new Paragraph(prt2Map.get("currency"), font));
            PdfPCell yearBuyAmountContent =
                    new PdfPCell(new Paragraph(prt2Map.get("yearBuyAmount"), font));
            PdfPCell yearBuyAssetContent =
                    new PdfPCell(new Paragraph(prt2Map.get("yearBuyAsset"), font));
            PdfPCell yearSellAmountContent =
                    new PdfPCell(new Paragraph(prt2Map.get("yearSellAmount"), font));
            PdfPCell yearSellAssetContent =
                    new PdfPCell(new Paragraph(prt2Map.get("yearSellAsset"), font));
            PdfPCell moveInAmountContent =
                    new PdfPCell(new Paragraph(prt2Map.get("moveInAmount"), font));

            tablePrt2.addCell(prt2CurrencyContent);
            tablePrt2.addCell(yearBuyAmountContent);
            tablePrt2.addCell(yearBuyAssetContent);
            tablePrt2.addCell(yearSellAmountContent);
            tablePrt2.addCell(yearSellAssetContent);
            tablePrt2.addCell(moveInAmountContent);
        }
        // セルのプロパティを設定
        for (PdfPRow row : tablePrt2.getRows()) {
            for (PdfPCell cell : row.getCells()) {
                cell.setPaddingLeft(5f);
                cell.setPaddingRight(5f);
                cell.setPaddingTop(3f);
                cell.setPaddingBottom(7f);
            }
        }
        PdfPCell blankprt2 = new PdfPCell();
        blankprt2.setFixedHeight(40);
        blankprt2.setColspan(6);
        blankprt2.disableBorderSide(15);
        tablePrt2.addCell(blankprt2);

        // 通貨名,年中移出数量(*6),ステーキング報酬数量,ステーキング報酬金額(*7)，支払手数料(数量)(*8),支払手数料(円換算)(*9)
        PdfPTable tablePrt3 = new PdfPTable(6);
        // テーブルヘッダ設定
        tablePrt3.setHeaderRows(1);
        int[] tablePrt3ColWidths = new int[] {6, 15, 15, 15, 15, 15};
        tablePrt3.setWidths(tablePrt3ColWidths);
        tablePrt3.setWidthPercentage(100);
        PdfPCell prt3Currency = new PdfPCell(new Paragraph("通貨名", font));
        PdfPCell moveOutAmount = new PdfPCell(new Paragraph("年中移出数量(*6)", font));
        PdfPCell feeAmount = new PdfPCell(new Paragraph("支払手数料(数量)(*8)", font));
        PdfPCell feeAsset = new PdfPCell(new Paragraph("支払手数料(円換算)(*9)", font));

        prt3Currency.setBackgroundColor(new BaseColor(255, 165, 0));
        moveOutAmount.setBackgroundColor(new BaseColor(255, 165, 0));
        feeAmount.setBackgroundColor(new BaseColor(255, 165, 0));
        feeAsset.setBackgroundColor(new BaseColor(255, 165, 0));
        tablePrt3.addCell(prt3Currency);
        tablePrt3.addCell(moveOutAmount);
        feeAmount.disableBorderSide(4);
        tablePrt3.addCell(feeAmount);
        tablePrt3.addCell(feeAsset);

        for (Map<String, String> prt3Map : mts.getReportList_prt3()) {
            PdfPCell prt3CurrencyContent =
                    new PdfPCell(new Paragraph(prt3Map.get("currency"), font));
            PdfPCell moveOutAmountContent =
                    new PdfPCell(new Paragraph(prt3Map.get("moveOutAmount"), font));
            PdfPCell feeAmountContent = new PdfPCell(new Paragraph(prt3Map.get("feeAmount"), font));
            PdfPCell feeAssetContent = new PdfPCell(new Paragraph(prt3Map.get("feeAsset"), font));

            tablePrt3.addCell(prt3CurrencyContent);
            tablePrt3.addCell(moveOutAmountContent);
            feeAmountContent.disableBorderSide(4);
            tablePrt3.addCell(feeAmountContent);
            tablePrt3.addCell(feeAssetContent);
        }
        // セルのプロパティを設定
        for (PdfPRow row : tablePrt3.getRows()) {
            for (PdfPCell cell : row.getCells()) {
                cell.setPaddingLeft(5f);
                cell.setPaddingRight(5f);
                cell.setPaddingTop(3f);
                cell.setPaddingBottom(7f);
            }
        }
        PdfPCell blankprt3 = new PdfPCell();
        blankprt3.setFixedHeight(0);
        blankprt3.setColspan(6);
        blankprt3.disableBorderSide(15);
        tablePrt3.addCell(blankprt3);

        Paragraph comment =
                new Paragraph(
                        "(*1)　年中購入数量：取得した暗号資産の数量\r\n"
                                + "(*2)　年中購入金額：購入数量を取得時のレートで円換算した金額\r\n"
                                + "(*3)　年中売却数量：支払った暗号資産の数量\r\n"
                                + "(*4)　年中売却金額：売却数量を支払い時のレートで円換算した金額\r\n"
                                + "(*5)　年中移入数量：その年に購入以外で口座に受け入れた暗号資産の数量・各種キャンペーンボーナスの数量・日本円入金・暗号資産売却により獲得した日本円\r\n"
                                + "(*6)　年中移出数量：その年に売却以外で口座から払い出した暗号資産の数量・日本円出金・暗号資産購入により支出した日本円\r\n"
                                + "(*7)　ステーキング報酬金額：１年間に受け取った各ステーキング報酬数量に受取日の終値で円換算した金額\r\n"
                                + "(*8)　支払手数料(数量)は、「移出数量」のうち手数料としてお支払いいただいた暗号資産の数量のみを記載しております。\r\n"
                                + "(*9)　支払手数料(円換算) のうちJPYの欄の金額は、その年の発生した日本円の手数料を表します。\r\n"
                                + "　　　 暗号資産の欄の数量は、支払手数料としてお支払いいただいた暗号資産の数量に支払い時のレートで円換算した金額となります。\r\n\r\n"
                                + "【本報告書に関するご注意】\r\n"
                                + "1.　本書は、資金決済に関する法律第６３条の１０及び暗号資産交換業者に関する内閣府令第２２条第６項に基づき交付されるものです。\r\n"
                                + "必ず内容をご確認いただき、万一疑義がございましたら、速やかにご連絡ください。本書発行後５日以内にご連絡がない場合には、ご承認いただいたものとして取り扱わせていただきます。\r\n\r\n"
                                + "2.　本報告書は参考情報となります。暗号資産の税務上の取り扱い（ステーキングの報酬、キャンペーンにより付与された暗号資産の取り扱いなどを含む）はお客様の状況により異なりますので、ご不明な点等は最寄りの税務署又は税理等の専門家にお問い合わせください。\r\n\r\n"
                                + "3.　当社以外の暗号資産に係るサービスをご利用になった場合や、他の取引所などから入金している場合、取得単価が不明なため当社で損益を確定することができません。お客様ご自身で取引報告書等を基に、他社のお取引と合わせて損益計算を行っていただきますようお願いいたします。",
                        font);
        comment.setLeading(15f);

        // keep table on one page
        tablePrt2.setKeepTogether(true);
        tablePrt3.setKeepTogether(true);
        // add content to pdf
        document.add(informationTable);
        document.add(tablePrt1);
        document.add(tablePrt2);
        document.add(tablePrt3);
        // display comment on new page
        document.newPage();
        document.add(comment);
    }

    private Map<String, String> createPDFParamsMap(Long userId, String firstDay, String lastDay) {
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put(
                "createdDay", FormatUtil.formatJst(new Date(), FormatPattern.YYYY_MM_DD_SLASH));
        paramsMap.put("userId", userId.toString());
        paramsMap.put(
                "targetDate",
                firstDay.substring(0, 4)
                        + "/"
                        + firstDay.substring(4, 6)
                        + "/"
                        + firstDay.substring(6, 8)
                        + "～"
                        + lastDay.substring(0, 4)
                        + "/"
                        + lastDay.substring(4, 6)
                        + "/"
                        + lastDay.substring(6, 8));
        return paramsMap;
    }

    private ModelTableSource createPDFTable(
            Long userId, String firstDay, String lastDay, boolean isBatch) throws CustomException {

        Long dateFrom = FormatUtil.parseJst(firstDay, FormatPattern.YYYYMMDD).getTime(); // 初日の00:00
        Long dateTo =
                FormatUtil.parseJst(lastDay, FormatPattern.YYYYMMDD).getTime()
                        + 60 * 60 * 24 * 1000l; // 最終日翌日の00:00
        // 通貨取得
        List<CurrencyConfig> currencyConfigs =
                currencyConfigService.findAllByCondition(TradeType.OPERATE, null, true);
        List<Map<String, String>> reportListPrt1Map = new ArrayList<>();
        List<Map<String, String>> reportListPrt2Map = new ArrayList<>();
        List<Map<String, String>> reportListPrt3Map = new ArrayList<>();
        // 数量初期化
        String yearBuyAmount = "0";
        String yearBuyAsset = "0";
        String yearSellAmount = "0";
        String yearSellAsset = "0";
        String moveInAmount = "0";
        String moveOutAmount = "0";
        String feeAmount = "0";
        String feeAsset = "0";

        for (CurrencyConfig currency : currencyConfigs) {
            List<AssetSummary> assetSumList =
                    assetSummaryService.findByCondition(
                            userId, currency.getCurrency(), new Date(dateFrom), new Date(dateTo));
            String firstDayForAssetSnapshot =
                    FormatUtil.formatJst(
                            new Date(dateFrom - 60 * 60 * 24 * 1000l), FormatPattern.YYYYMMDD);
            AssetSummary assetSummaryFirstDay =
                    getAssetSnapshot(userId, currency.getCurrency(), firstDayForAssetSnapshot);
            AssetSummary assetSummaryLastDay =
                    getAssetSnapshot(userId, currency.getCurrency(), lastDay);
            if (assetSummaryLastDay == null && currency.isEnabled() && isBatch) {
                throw new CustomException(ErrorCode.REQUEST_ERROR_YEARLY_REPORT_CREATE_FAILED);
            }
            if (ObjectUtils.isEmpty(assetSumList)) {
                continue;
            }

            Map<String, String> map = new HashMap<>();
            CurrencyUtils.putCurrencyToMap(map, currency.getCurrency());
            map.put(
                    "yearBeginAmount",
                    assetSummaryFirstDay != null
                            ? commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    assetSummaryFirstDay.getCurrentAmount(),
                                                    RoundingMode.FLOOR))
                            : "0");
            map.put(
                    "yearEndAmount",
                    assetSummaryLastDay != null
                            ? commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    assetSummaryLastDay.getCurrentAmount(),
                                                    RoundingMode.FLOOR))
                            : "0");
            reportListPrt1Map.add(map);

            if (assetSumList != null) {
                BigDecimal depositAmount = BigDecimal.ZERO;
                BigDecimal depositAmountJpy = BigDecimal.ZERO;
                // 入金手数料は常に「0」である
                BigDecimal depositFee = BigDecimal.ZERO;
                BigDecimal depositFeeJpy = BigDecimal.ZERO;
                BigDecimal withdrawalAmount = BigDecimal.ZERO;
                BigDecimal withdrawalAmountJpy = BigDecimal.ZERO;
                BigDecimal withdrawalFee = BigDecimal.ZERO;
                BigDecimal withdrawalFeeJpy = BigDecimal.ZERO;

                // 販売

                BigDecimal posTradeBuyAmountJpy =
                        assetSumList.stream()
                                .map(AssetSummary::getPosTradeBuyAmountJpy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal posTradeSellAmountJpy =
                        assetSumList.stream()
                                .map(AssetSummary::getPosTradeSellAmountJpy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal posTradeFeeJpy =
                        assetSumList.stream()
                                .map(AssetSummary::getPosTradeFeeJpy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                // ステーキング
                if (Currency.JPY.equals(currency.getCurrency())) {
                    depositAmountJpy =
                            assetSumList.stream()
                                    .map(AssetSummary::getDepositAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    withdrawalAmountJpy =
                            assetSumList.stream()
                                    .map(AssetSummary::getWithdrawalAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    depositFeeJpy =
                            assetSumList.stream()
                                    .map(AssetSummary::getDepositFeeJpy)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    withdrawalFeeJpy =
                            assetSumList.stream()
                                    .map(AssetSummary::getWithdrawalFeeJpy)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    yearBuyAmount = "-";
                    yearBuyAsset = "-";
                    yearSellAmount = "-";
                    yearSellAsset = "-";
                    moveInAmount =
                            commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    depositAmountJpy.add(posTradeBuyAmountJpy),
                                                    RoundingMode.FLOOR));
                    moveOutAmount =
                            commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    withdrawalAmountJpy
                                                            .add(depositFeeJpy)
                                                            .add(posTradeSellAmountJpy)
                                                            .add(posTradeFeeJpy),
                                                    RoundingMode.FLOOR));
                    feeAmount = "-";
                    feeAsset =
                            commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    withdrawalFeeJpy
                                                            .add(depositFeeJpy)
                                                            .add(posTradeFeeJpy),
                                                    RoundingMode.FLOOR));
                } else {

                    moveInAmount =
                            commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(depositAmount, RoundingMode.FLOOR));
                    moveOutAmount =
                            commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    withdrawalAmount.add(depositFee),
                                                    RoundingMode.FLOOR));
                    feeAmount =
                            commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    withdrawalFee.add(depositFee),
                                                    RoundingMode.FLOOR));
                    feeAsset =
                            commaSeparatedDecimal(
                                    currency.getCurrency()
                                            .getScaledAmount(
                                                    withdrawalFeeJpy.add(depositFeeJpy),
                                                    RoundingMode.FLOOR));
                }
            }

            Map<String, String> map2 = new HashMap<>();
            CurrencyUtils.putCurrencyToMap(map2, currency.getCurrency());
            map2.put("yearBuyAmount", yearBuyAmount);
            map2.put("yearBuyAsset", yearBuyAsset);
            map2.put("yearSellAmount", yearSellAmount);
            map2.put("yearSellAsset", yearSellAsset);
            map2.put("moveInAmount", moveInAmount);
            reportListPrt2Map.add(map2);

            Map<String, String> map3 = new HashMap<>();
            CurrencyUtils.putCurrencyToMap(map3, currency.getCurrency());
            map3.put("moveOutAmount", moveOutAmount);
            map3.put("feeAmount", feeAmount);
            map3.put("feeAsset", feeAsset);
            reportListPrt3Map.add(map3);
        }
        ModelTableSource mts = new ModelTableSource();
        mts.setReportList_prt1(reportListPrt1Map);
        mts.setReportList_prt2(reportListPrt2Map);
        mts.setReportList_prt3(reportListPrt3Map);
        return mts;
    }

    private String commaSeparatedDecimal(BigDecimal decimal) {
        if (decimal == null) {
            return "";
        }
        int scale = decimal.stripTrailingZeros().scale();
        String format = "#,###";
        if (scale > 0) {
            format += "." + StringUtils.repeat("#", scale);
        }
        DecimalFormat df = new DecimalFormat(format);
        return df.format(decimal);
    }
}
