package point.operate.service;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import point.common.component.QueryExecutorReturner;
import point.common.component.RedisManager;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.CoverOrderConfig;
import point.common.entity.Symbol;
import point.common.exception.CustomException;
import point.common.http.whalefin.CreateOrderResult;
import point.common.http.whalefin.GetOrderResult;
import point.common.http.whalefin.RequestOrder;
import point.common.http.whalefin.WhaleFinClient;
import point.common.service.CoverOrderConfigService;
import point.common.service.EntityService;
import point.common.service.OperateInternalOffsetService;
import point.common.util.JsonUtil;
import point.operate.entity.OperateAggregatedOrder;
import point.operate.entity.OperateCoverOrder;
import point.operate.entity.OperateCoverOrder_;
import point.operate.predicate.OperateCoverOrderPredicate;

@Slf4j
@RequiredArgsConstructor
public class OperateCoverOrderService
        extends EntityService<OperateCoverOrder, OperateCoverOrderPredicate> {

    @Value("${point-pos.base-trade.amber.api-path}")
    private String createOrderPath;

    @Value("${point-pos.base-trade.amber.get-order-api-path}")
    private String getOrderPath;

    private final WhaleFinClient whaleFinClient;

    private final OperateAggregatedOrderService orderAggregatedOrderService;

    private final CoverOrderConfigService coverOrderConfigService;

    private final RedisManager redisManager;

    private final OperateInternalOffsetService operateInternalOffsetService;

    private static String getLockKeyAmount(OrderSide orderSide, Long symbolId, Currency currency) {
        return "lock:amount:" + orderSide + ":" + symbolId + ":" + currency.getName();
    }

    @Override
    public Class<OperateCoverOrder> getEntityClass() {
        return OperateCoverOrder.class;
    }

    public void execute(Symbol symbol, OrderSide orderSide) throws Exception {
        // Setting logging context
        MDC.put("symbolId", String.valueOf(symbol.getId()));
        MDC.put("orderSide", orderSide.name());

        log.info(
                "[CoverOrder] Start processing for symbolId:{}, orderSide:{}",
                symbol.getId(),
                orderSide);

        // -- start handle pending orders
        if (this.hasPendingCoverOrders(symbol, orderSide)) {
            log.info(
                    "[CoverOrder] Pending orders exist for symbolId:{}, orderSide: {}, skipping execution",
                    symbol.getId(),
                    orderSide);
            return;
        }
        // -- end handle pending orders

        // -- start offsetting orders
        try {
            log.info("[Offset] Start processing for symbolId:{}", symbol.getId());
            operateInternalOffsetService.offsetOrders(symbol.getId(), this);
            log.info("[Offset] Completed processing for symbolId:{}", symbol.getId());
        } catch (CustomException e) {
            log.error("[Offset] Failed for symbolId:{}, error:{}", symbol.getId(), e.getMessage());
            return;
        }
        // -- end offsetting orders

        // -- start send order to exchange
        // Find aggregated order by symbol and order side
        OperateAggregatedOrder operateAggregatedOrder =
                orderAggregatedOrderService.findAllByCondition(
                        symbol.getId(), orderSide, OrderType.MARKET);

        // Check if aggregated order is valid
        if (ObjectUtils.isEmpty(operateAggregatedOrder)
                || operateAggregatedOrder.getAmount().compareTo(BigDecimal.ZERO)
                        == NumberUtils.INTEGER_ZERO) {
            log.info(
                    "[CoverOrder] No valid aggregated order found for symbolId:{}, amount:{}",
                    symbol.getId(),
                    operateAggregatedOrder != null && operateAggregatedOrder.getAmount() != null
                            ? operateAggregatedOrder.getAmount().toPlainString()
                            : "null");
            return;
        }

        CoverOrderConfig coverOrderConfig =
                coverOrderConfigService.findOne(
                        symbol.getId(), Exchange.AMBER, Boolean.TRUE, symbol.getTradeType());

        if (ObjectUtils.isEmpty(coverOrderConfig)) {
            log.warn("[CoverOrder] Missing configuration for symbolId:{}", symbol.getId());
            return;
        }

        BigDecimal orderAmount = operateAggregatedOrder.getAmount();
        if (orderAmount.compareTo(coverOrderConfig.getMinOrderAmount()) < 0) {
            log.info(
                    "[CoverOrder] Amount below minimum for symbolId:{}, amount:{}, minRequired:{}",
                    symbol.getId(),
                    orderAmount.toPlainString(),
                    coverOrderConfig.getMinOrderAmount().toPlainString());
            return;
        }

        try {
            String clientId = UUID.randomUUID().toString();
            BigDecimal scaledAmount = symbol.getCurrencyPair().getWhalefinScaledAmount(orderAmount);

            RequestOrder createPosOrder =
                    new RequestOrder(
                            operateAggregatedOrder.getOrderSide().getName(),
                            operateAggregatedOrder.getOrderType().getName(),
                            clientId,
                            symbol.getCurrencyPairName(),
                            scaledAmount.toString());
            String payload = JsonUtil.encode(createPosOrder);
            if (!StringUtils.hasLength(payload)) {
                log.error("[CoverOrder] Failed to serialize order for symbolId:{}", symbol.getId());
                return;
            }

            Optional<CreateOrderResult> createOrderResult =
                    whaleFinClient.createOrder(clientId, this.createOrderPath, payload);
            if (createOrderResult.isPresent()) {
                log.info(
                        "[CoverOrder] Successfully submitted for symbolId:{}, clientId:{}",
                        symbol.getId(),
                        clientId);
                this.createOrder(
                        symbol,
                        orderSide,
                        createOrderResult.get(),
                        operateAggregatedOrder,
                        clientId);
            } else {
                log.warn(
                        "[CoverOrder] Submission failed for symbolId:{}, clientId:{}",
                        symbol.getId(),
                        clientId);
            }
        } catch (Exception e) {
            log.error(
                    "[CoverOrder] Processing failed for symbolId:{}, error:{}",
                    symbol.getId(),
                    e.getMessage(),
                    e);
        } finally {
            MDC.clear();
        }
        // -- end order submission to exchange
    }

    public List<OperateCoverOrder> findByOrderStatusWithOrderSideAndSymbolId(
            List<OrderStatus> status, OrderSide orderSide, Long symbolId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<OperateCoverOrder> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                List.of(
                                        criteriaBuilder.equal(
                                                root.get(OperateCoverOrder_.ORDER_STATUS), status),
                                        criteriaBuilder.equal(
                                                root.get(OperateCoverOrder_.ORDER_SIDE), orderSide),
                                        criteriaBuilder.equal(
                                                root.get(OperateCoverOrder_.SYMBOL_ID), symbolId)),
                                criteriaBuilder.asc(root.get(OperateCoverOrder_.createdAt)));
                    }
                });
    }

    private boolean hasPendingCoverOrders(Symbol symbol, OrderSide orderSide) {
        List<OrderStatus> waiting = List.of(OrderStatus.WAITING);
        List<OperateCoverOrder> waitingOrders =
                this.findByOrderStatusWithOrderSideAndSymbolId(waiting, orderSide, symbol.getId());
        if (CollectionUtils.isEmpty(waitingOrders)) {
            return false;
        }

        waitingOrders.forEach(
                order -> {
                    try {
                        this.processWaitingOrder(symbol, orderSide, order);
                    } catch (Exception e) {
                        log.error("[PendingOrder] Process failed for order:{}", order.getId(), e);
                    }
                });

        waitingOrders =
                this.findByOrderStatusWithOrderSideAndSymbolId(waiting, orderSide, symbol.getId());
        if (CollectionUtils.isEmpty(waitingOrders)) {
            log.info("After processing, no pending cover orders found");
            return false;
        }
        log.info("Pending cover orders still exist, waitingOrderSize: {}", waitingOrders.size());
        return true;
    }

    private void processWaitingOrder(Symbol symbol, OrderSide orderSide, OperateCoverOrder order)
            throws Exception {
        if (!StringUtils.hasLength(order.getClientId())) {
            log.warn("[PendingOrder] Invalid clientId for orderId:{}", order.getId());
            return;
        }

        Optional<GetOrderResult> getOrderResult =
                whaleFinClient.getOrder(
                        order.getClientId(),
                        this.getOrderPath + order.getClientId(),
                        Map.of("category", "SPOT"));
        if (getOrderResult.isEmpty()) {
            log.info("[PendingOrder] No result for orderId:{}", order.getId());
            return;
        }

        GetOrderResult orderResult = getOrderResult.get();
        OrderStatus newStatus = this.getOrderStatus(orderResult.getStatus());

        if (!order.getOrderStatus().equals(newStatus)) {
            boolean isTerminal = this.isTerminalStatus(newStatus);
            this.updateOrderStatus(
                    symbol, orderSide, order, newStatus, isTerminal ? orderResult : null);
        } else {
            log.info(
                    "[PendingOrder] Status unchanged for orderId: {}, beforeStatus: {}, afterStatus: {}",
                    order.getId(),
                    order.getOrderStatus(),
                    newStatus);
        }

        if (OrderStatus.UNFILLED.equals(newStatus)) {
            log.error(
                    "[PendingOrder] Order is unfilled: {}, beforeStatus: {}, afterStatus: {}",
                    order.getId(),
                    order.getOrderStatus(),
                    newStatus);
        }

        if (OrderStatus.CANCELED_UNFILLED.equals(newStatus)) {
            log.info(
                    "[PendingOrder] Order is canceled: {}, beforeStatus: {}, afterStatus: {}",
                    order.getId(),
                    order.getOrderStatus(),
                    newStatus);
        }
    }

    private boolean isTerminalStatus(OrderStatus status) {
        return OrderStatus.FULLY_FILLED.equals(status)
                || OrderStatus.PARTIALLY_FILLED.equals(status);
    }

    private void updateOrderStatus(
            Symbol symbol,
            OrderSide orderSide,
            OperateCoverOrder order,
            OrderStatus orderStatus,
            GetOrderResult orderResult)
            throws Exception {
        log.info("[OrderStatus] Updating orderId:{}, newStatus:{}", order.getId(), orderStatus);

        String lockKey =
                getLockKeyAmount(
                        orderSide, symbol.getId(), symbol.getCurrencyPair().getQuoteCurrency());

        if (!redisManager.executeWithLock(
                lockKey,
                RedisManager.LockParams.EXECUTE_ORDER,
                () ->
                        customTransactionManager.execute(
                                entityManager -> {
                                    OperateCoverOrder operateCoverOrder =
                                            this.findOne(order.getId());
                                    if (operateCoverOrder.getOrderStatus().equals(orderStatus)) {
                                        // The order has already been updated, no need to proceed
                                        return;
                                    }

                                    order.setOrderStatus(orderStatus);
                                    this.save(order, entityManager);
                                    log.info("[OrderStatus] Saved orderId:{}", order.getId());

                                    if (Objects.nonNull(orderResult)) {
                                        OperateAggregatedOrder aggregatedOrder =
                                                orderAggregatedOrderService.findOne(
                                                        order.getAggregatedOrderId(),
                                                        entityManager);
                                        OrderMetrics orderMetrics =
                                                this.calculateOrderMetrics(
                                                        orderResult,
                                                        symbol,
                                                        aggregatedOrder.getAmount());
                                        this.updateAggregatedOrderAmount(
                                                entityManager,
                                                symbol,
                                                orderMetrics,
                                                order.getAggregatedOrderId());
                                    }
                                }))) {
            log.error("[Lock] Failed to acquire for key:{}", lockKey);
            throw new CustomException(ErrorCode.LOCK_KEY, "Failed to acquire distributed lock");
        }
    }

    /**
     * Updates the aggregated order amount after processing filled quantity
     *
     * @param entityManager The EntityManager for database operations
     * @param symbol The trading symbol associated with the order
     * @param orderMetrics The metrics of the order including filled quantity
     * @param aggregatedOrderId The ID of the aggregated order to update
     * @throws Exception if any operation fails
     */
    private void updateAggregatedOrderAmount(
            EntityManager entityManager,
            Symbol symbol,
            OrderMetrics orderMetrics,
            Long aggregatedOrderId)
            throws Exception {

        // 1. Retrieve the aggregated order
        OperateAggregatedOrder aggregatedOrder =
                orderAggregatedOrderService.findOne(aggregatedOrderId, entityManager);

        // 2. Convert quantities to operational scale
        BigDecimal updatedAmount =
                orderMetrics.operateScaledAmount.subtract(orderMetrics.filledQuantityAmount);

        // 3. Log the amount changes
        log.info(
                "[OrderUpdate] symbolId:{} | Current:{}, Filled:{}, Updated:{}, ExchangeOrder:{}",
                symbol.getId(),
                orderMetrics.operateScaledAmount.toPlainString(),
                orderMetrics.filledQuantityAmount.toPlainString(),
                updatedAmount.toPlainString(),
                orderMetrics.id);

        // 4. Update and persist the changes
        aggregatedOrder.setAmount(updatedAmount);
        orderAggregatedOrderService.save(aggregatedOrder, entityManager);
    }

    private void createOrder(
            Symbol symbol,
            OrderSide orderSide,
            CreateOrderResult result,
            OperateAggregatedOrder aggregatedOrder,
            String clientId)
            throws Exception {
        String lockKey =
                getLockKeyAmount(
                        orderSide, symbol.getId(), symbol.getCurrencyPair().getQuoteCurrency());

        if (!acquireLockAndProcessOrder(
                symbol,
                lockKey,
                result,
                aggregatedOrder.getId(),
                aggregatedOrder.getAmount(),
                clientId)) {
            handleLockFailure(lockKey);
        }
    }

    private boolean acquireLockAndProcessOrder(
            Symbol symbol,
            String lockKey,
            CreateOrderResult result,
            Long aggregatedOrderId,
            BigDecimal aggregatedOrderAmount,
            String clientId)
            throws Exception {
        return redisManager.executeWithLock(
                lockKey,
                RedisManager.LockParams.EXECUTE_ORDER,
                () ->
                        processOrderInTransaction(
                                result,
                                symbol,
                                aggregatedOrderId,
                                aggregatedOrderAmount,
                                clientId));
    }

    private void processOrderInTransaction(
            CreateOrderResult result,
            Symbol symbol,
            Long aggregatedOrderId,
            BigDecimal aggregatedOrderAmount,
            String clientId)
            throws Exception {
        customTransactionManager.execute(
                entityManager -> {
                    try {
                        OrderMetrics metrics =
                                this.calculateOrderMetrics(result, symbol, aggregatedOrderAmount);
                        this.logOrderMetrics(metrics, result.getId());

                        OperateCoverOrder coverOrder =
                                this.createCoverOrder(
                                        result,
                                        metrics,
                                        symbol.getId(),
                                        aggregatedOrderId,
                                        clientId);
                        this.save(coverOrder, entityManager);
                        log.info(
                                "Successfully saved cover order - orderId:[{}] symbolId:[{}] status:[{}]",
                                result.getId(),
                                symbol.getId(),
                                result.getStatus());

                        this.updateAggregatedOrder(
                                entityManager, symbol, result, metrics, aggregatedOrderId);
                    } catch (Exception e) {
                        log.error(
                                "Error processing order transaction - orderId:[{}] symbolId:[{}] error:[{}]",
                                result.getId(),
                                symbol.getId(),
                                e.getMessage(),
                                e);
                        throw e;
                    }
                });
    }

    private OrderMetrics calculateOrderMetrics(
            Object orderResult, Symbol symbol, BigDecimal aggregatedOrderAmount) {

        // Extract common fields from either result type
        String quantity;
        String filledQuantity;
        String price;
        String id;

        if (orderResult instanceof CreateOrderResult result) {
            id = result.getId();
            quantity = result.getQuantity();
            filledQuantity = result.getFilledQuantity();
            price = (result.getFiatReference() == null) ? "0" : result.getFiatReference().getJpy();
        } else if (orderResult instanceof GetOrderResult result) {
            id = result.getId();
            quantity = result.getQuantity();
            filledQuantity = result.getFilledQuantity();
            price = (result.getFiatReference() == null) ? "0" : result.getFiatReference().getJpy();
        } else {
            throw new IllegalArgumentException(
                    "Unsupported order result type: " + orderResult.getClass());
        }

        // Common calculation logic
        BigDecimal quantityAmount =
                symbol.getCurrencyPair().getWhalefinScaledAmount(new BigDecimal(quantity));
        BigDecimal filledQuantityAmount =
                symbol.getCurrencyPair().getWhalefinScaledAmount(new BigDecimal(filledQuantity));
        BigDecimal remainingAmount = quantityAmount.subtract(filledQuantityAmount);
        BigDecimal averagePrice = new BigDecimal(price);
        BigDecimal operateScaledAmount =
                symbol.getCurrencyPair().getOperateScaledAmount(aggregatedOrderAmount);

        return new OrderMetrics(
                id,
                quantityAmount,
                filledQuantityAmount,
                remainingAmount,
                averagePrice,
                operateScaledAmount);
    }

    private void logOrderMetrics(OrderMetrics metrics, String orderId) {
        log.info(
                "Order metrics for {} - quantity:[{}] filled:[{}] remaining:[{}] avgPrice:[{}]",
                orderId,
                metrics.quantityAmount.toPlainString(),
                metrics.filledQuantityAmount.toPlainString(),
                metrics.remainingAmount.toPlainString(),
                metrics.averagePrice.toPlainString());
    }

    private OperateCoverOrder createCoverOrder(
            CreateOrderResult result,
            OrderMetrics metrics,
            Long symbolId,
            Long aggregatedOrderId,
            String clientId) {
        OperateCoverOrder coverOrder = newEntity();
        coverOrder.setAnotherProperties(
                symbolId,
                aggregatedOrderId,
                OrderSide.valueOf(result.getDirection()),
                OrderType.valueOf(result.getType()),
                metrics.averagePrice,
                new BigDecimal(result.getFilledPrice()),
                metrics.filledQuantityAmount,
                metrics.remainingAmount,
                new BigDecimal(result.getFee()),
                Exchange.AMBER,
                result.getId(),
                this.getOrderStatus(result.getStatus()));
        coverOrder.setClientId(clientId);
        return coverOrder;
    }

    private void updateAggregatedOrder(
            EntityManager entityManager,
            Symbol symbol,
            CreateOrderResult result,
            OrderMetrics metrics,
            Long aggregatedOrderId)
            throws Exception {

        OrderStatus orderStatus = this.getOrderStatus(result.getStatus());
        if (this.isTerminalStatus(orderStatus)) {
            this.updateAggregatedOrderAmount(entityManager, symbol, metrics, aggregatedOrderId);
            log.info(
                    """
                    [AggregatedOrder] Successfully updated - \
                    exchangeOrderId:{}, symbol:{}, \
                    originalAmount:{}, filled:{}
                    """,
                    result.getId(),
                    symbol.getCurrencyPairName(),
                    metrics.operateScaledAmount,
                    metrics.filledQuantityAmount);
        } else {
            log.info(
                    """
                        [Non-Terminal Status] Skipping amount update - \
                        exchangeOrderId:{}, status:{}
                        """,
                    result.getId(),
                    orderStatus);
        }
    }

    private void handleLockFailure(String lockKey) throws CustomException {
        log.error("Failed to acquire lock for order processing - lockKey:[{}]", lockKey);
        throw new CustomException(
                ErrorCode.LOCK_KEY, "Failed to acquire distributed lock see logs for details");
    }

    // Helper class to hold order metrics
    private static class OrderMetrics {
        final BigDecimal quantityAmount;
        final BigDecimal filledQuantityAmount;
        final BigDecimal remainingAmount;
        final BigDecimal averagePrice;
        final BigDecimal operateScaledAmount;
        final String id;

        OrderMetrics(
                String id,
                BigDecimal quantityAmount,
                BigDecimal filledQuantityAmount,
                BigDecimal remainingAmount,
                BigDecimal averagePrice,
                BigDecimal operateScaledAmount) {
            this.id = id;
            this.quantityAmount = quantityAmount;
            this.filledQuantityAmount = filledQuantityAmount;
            this.remainingAmount = remainingAmount;
            this.averagePrice = averagePrice;
            this.operateScaledAmount = operateScaledAmount;
        }
    }

    private OrderStatus getOrderStatus(String orderStatus) {
        return switch (orderStatus) {
            case "PENDING", "EXECUTING" -> OrderStatus.WAITING;
            case "COMPLETED" -> OrderStatus.FULLY_FILLED;
            case "PART_CANCELED" -> OrderStatus.PARTIALLY_FILLED;
            case "CANCELED" -> OrderStatus.CANCELED_UNFILLED;
            default -> OrderStatus.UNFILLED;
        };
    }
}
