package point.operate.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.service.EntityService;
import point.operate.entity.OperateAggregatedOrder;
import point.operate.predicate.OperateAggregatedOrderPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class OperateAggregatedOrderService
        extends EntityService<OperateAggregatedOrder, OperateAggregatedOrderPredicate> {
    @Override
    public Class<OperateAggregatedOrder> getEntityClass() {
        return OperateAggregatedOrder.class;
    }

    public OperateAggregatedOrder findAllByCondition(
            Long symbolId, OrderSide orderSide, OrderType orderType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<OperateAggregatedOrder, OperateAggregatedOrder>() {
                    @Override
                    public OperateAggregatedOrder query() {
                        List<Predicate> predicates =
                                getPredicateList(
                                        criteriaBuilder, root, symbolId, orderSide, orderType);
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    private List<Predicate> getPredicateList(
            CriteriaBuilder criteriaBuilder,
            Root<OperateAggregatedOrder> root,
            Long symbolId,
            OrderSide orderSide,
            OrderType orderType) {
        List<Predicate> predicates = new ArrayList<>();
        if (symbolId != null) {
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
        }
        if (orderSide != null) {
            predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
        }
        if (orderType != null) {
            predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
        }
        return predicates;
    }
}
