package point.operate.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ErrorCode;
import point.common.entity.PointUser;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.service.EntityService;
import point.common.service.PointUserService;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.operate.entity.PointUserSummary;
import point.operate.entity.PointUserSummary_;
import point.operate.predicate.PointUserSummaryPredicate;

@Service
@Slf4j
@RequiredArgsConstructor
public class PointUserSummaryService
        extends EntityService<PointUserSummary, PointUserSummaryPredicate> {

    private final PointUserService pointUserService;

    @Override
    public Class<PointUserSummary> getEntityClass() {
        return PointUserSummary.class;
    }

    public PageData<PointUserSummary> findByTargetAtPageData(
            Date targetAtFrom, Date targetAtTo, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                targetAtFrom,
                                                targetAtTo,
                                                null));
                            }
                        });
        return new PageData<PointUserSummary>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PointUserSummary, List<PointUserSummary>>() {
                            @Override
                            public List<PointUserSummary> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                targetAtFrom,
                                                targetAtTo,
                                                null);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(PointUserSummary_.id)));
                            }
                        }));
    }

    public void calculate(String targetDay) throws CustomException {
        // 日付チェックとtargetAtの設定
        // targetAtは対象日の00:00を示す
        Date targetAt = FormatUtil.parseJst(targetDay, FormatPattern.YYYYMMDD);
        if (targetAt == null) {
            log.error("invalid target date string: targetDay=" + targetDay);
            throw new CustomException(ErrorCode.COMMON_ERROR_INVALID_DATE_STRING);
        }
        List<PointUser> pointUsers = pointUserService.findAll();
        PointUserSummary userSummary = new PointUserSummary(targetAt);
        userSummary.setUsers((long) pointUsers.size());
        userSummary.setPointUserPartnerActive(
                pointUsers.stream().filter(user -> user.getPointPartner().isActive()).count());
        userSummary.setPointUserPartnerDate(
                pointUsers.stream().filter(user -> user.getPointPartner().isValidate()).count());
        this.save(userSummary);
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PointUserSummary> root,
            Date targetAtFrom,
            Date targetAtTo,
            Long users) {
        List<Predicate> predicates = new ArrayList<>();

        if (targetAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, targetAtFrom));
        }

        if (targetAtTo != null) {
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetAtTo));
        }

        if (users != null) {
            predicates.add(predicate.equalUsers(criteriaBuilder, root, users));
        }
        return predicates;
    }
}
