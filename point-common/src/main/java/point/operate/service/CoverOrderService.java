package point.operate.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.CurrencyPair;
import point.common.constant.Exchange;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.model.response.PageData;
import point.common.service.EntityService;
import point.operate.entity.OperateCoverOrder;
import point.operate.entity.OperateCoverOrder_;
import point.operate.predicate.OperateCoverOrderPredicate;

@Service
@RequiredArgsConstructor
public class CoverOrderService
        extends EntityService<OperateCoverOrder, OperateCoverOrderPredicate> {

    @Override
    public Class<OperateCoverOrder> getEntityClass() {
        return OperateCoverOrder.class;
    }

    public PageData<OperateCoverOrder> findByConditionPageData(
            CurrencyPair currencyPair,
            Long symbolId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            OrderType orderType,
            OrderSide orderSide,
            BigDecimal greaterThanRemainingAmount,
            BigDecimal lessThanRemainingAmount,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                currencyPair,
                                                symbolId,
                                                orderType,
                                                null,
                                                null,
                                                orderSide,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo,
                                                greaterThanRemainingAmount,
                                                lessThanRemainingAmount));
                            }
                        });

        return new PageData<OperateCoverOrder>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<OperateCoverOrder, List<OperateCoverOrder>>() {
                            @Override
                            public List<OperateCoverOrder> query() {
                                List<Predicate> predicates =
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                currencyPair,
                                                symbolId,
                                                orderType,
                                                null,
                                                null,
                                                orderSide,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo,
                                                greaterThanRemainingAmount,
                                                lessThanRemainingAmount);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(OperateCoverOrder_.id)));
                            }
                        }));
    }

    private List<Predicate> createPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<OperateCoverOrder> root,
            CurrencyPair currencyPair,
            Long symbolId,
            OrderType orderType,
            OrderType[] orderTypes,
            OrderType[] exceptOrderTypes,
            OrderSide orderSide,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            BigDecimal greaterThanRemainingAmount,
            BigDecimal lessThanRemainingAmount) {
        List<Predicate> predicates =
                createPredicates(
                        criteriaBuilder,
                        root,
                        symbolId,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        orderSide);

        // https://backseat-inc.atlassian.net/browse/OFLM-1613
        predicates.add(predicate.equalExchange(criteriaBuilder, root, Exchange.AMBER));

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {
            if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
            }

            if (idTo != null) {
                predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
            }

            if (dateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToCreatedAt(
                                criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
                predicates.add(
                        predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            if (greaterThanRemainingAmount != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToRemainingAmount(
                                criteriaBuilder, root, greaterThanRemainingAmount));
            }
            if (lessThanRemainingAmount != null) {
                predicates.add(
                        predicate.lessThanRemainingAmount(
                                criteriaBuilder, root, lessThanRemainingAmount));
            }
        }
        return predicates;
    }

    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<OperateCoverOrder> root,
            Long symbolId,
            OrderType orderType,
            OrderType[] orderTypes,
            OrderType[] exceptOrderTypes,
            OrderSide orderSide) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalSymbol(criteriaBuilder, root, symbolId));
        if (orderType != null) {
            predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
        } else if (!ArrayUtils.isEmpty(orderTypes)) {
            predicates.add(predicate.inOrderType(root, orderTypes));
        } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
            predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
        }
        if (orderSide != null) {
            predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
        }
        return predicates;
    }
}
