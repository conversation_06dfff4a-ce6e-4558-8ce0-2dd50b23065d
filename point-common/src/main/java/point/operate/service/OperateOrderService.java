package point.operate.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.*;
import point.common.component.RedisManager.LockParams;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.GameException;
import point.common.model.request.PosOrderForm;
import point.common.model.response.CurrencyConfigData;
import point.common.model.response.PageData;
import point.common.service.*;
import point.common.util.FormatUtil;
import point.common.util.JsonUtil;
import point.operate.entity.OperateAggregatedOrder;
import point.pos.entity.PosOrder;
import point.pos.entity.PosTrade;
import point.pos.model.PosBestPriceData;
import point.pos.model.PosOrderRowMapper;
import point.pos.service.PosOrderHistoryService;
import point.pos.service.PosTradeService;

@Slf4j
@Service
@RequiredArgsConstructor
public class OperateOrderService extends PosOrderHistoryService {

    private final CurrencyPairConfigService currencyPairConfigService;

    private final CurrencyConfigService currencyConfigService;

    private final AssetService assetService;

    private final PosTradeService posTradeService;

    private final SymbolService symbolService;

    private final MailNoreplyService mailNoreplyService;

    private final RedisManager redisManager;

    private final HistoricalTransactionManager historicalTransactionManager;

    private final OrderbookService orderbookService;

    private final OperateAggregatedOrderService operateAggregatedOrderService;

    private final BcCurrencyConfigService bcCurrencyConfigService;

    private final BcOrderCurrencySplitService bcOrderCurrencySplitService;

    private final ChoicePowerSyncService choicePowerSyncService;

    private final UserMonsterInfoService userMonsterInfoService;

    private final MonsterExperienceService monsterExperienceService;
    private final UserCropStatusService userCropStatusService;

    private static final int AMOUNT_SCALE = 18;

    @Value("${point-pos.price-range-percent:0.0005}")
    private BigDecimal priceRangePercent;

    @Override
    public Class<PosOrder> getEntityClass() {
        return PosOrder.class;
    }

    @Override
    public Class<PosOrderRowMapper> getRowMapperClass() {
        return PosOrderRowMapper.class;
    }

    private static String getLockKey(Long userId, Currency currency) {
        return "lock:asset:" + userId + ":" + currency;
    }

    private static String getLockKeyAmount(OrderSide orderSide, Long symbolId, Currency currency) {
        return "lock:amount:" + orderSide + ":" + symbolId + ":" + currency.getName();
    }

    public PosOrder order(
            Symbol symbol, PointUser user, PosOrderForm form, OrderChannel orderChannel)
            throws Exception {
        return this.marketOrder(symbol, user, form, orderChannel);
    }

    private PosOrder marketOrder(
            Symbol symbol, PointUser user, PosOrderForm form, OrderChannel orderChannel)
            throws Exception {
        log.info("operateOrderLog,symbolId,{},limitOperateOrder_start", symbol.getId());

        CurrencyPairConfig currencyPairConfig =
                currencyPairConfigService.findByCondition(
                        symbol.getTradeType(), symbol.getCurrencyPair());

        if (currencyPairConfig == null) {
            log.warn("operateOrderLog, CurrencyPairConfig is null");
            throw new GameException(
                    ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND,
                    ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND.getMessage());
        }

        Currency baseCurrency = symbol.getCurrencyPair().getBaseCurrency();
        Currency quoteCurrency = symbol.getCurrencyPair().getQuoteCurrency();

        CurrencyConfig baseCurrencyConfig =
                currencyConfigService.findByCurrency(baseCurrency, TradeType.OPERATE);
        CurrencyConfig quoteCurrencyConfig =
                currencyConfigService.findByCurrency(quoteCurrency, TradeType.OPERATE);

        if (baseCurrencyConfig == null || quoteCurrencyConfig == null) {
            log.warn(
                    "pointUserId: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND,
                    ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND.getMessage());
        }

        if (!currencyPairConfig.isTradable() || !currencyPairConfig.isEnabled()) {

            log.warn(
                    "pointUserId: {}, tradable: {}, enabled: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(currencyPairConfig.isTradable()),
                    JsonUtil.encode(currencyPairConfig.isEnabled()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_ORDER_IS_INACTIVE_POS,
                    ErrorCode.ORDER_ERROR_ORDER_IS_INACTIVE_POS.getMessage());
        }

        OrderSide orderSide = OrderSide.valueOf(form.getOrderSide());
        OrderType orderType = OrderType.valueOf(form.getOrderType());
        // 発注価格
        if (!validatePrice(symbol, orderSide, form.getPrice())) {
            log.warn(
                    "pointUserId: {}, orderSide: {}, price: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(form.getOrderSide()),
                    JsonUtil.encode(form.getPrice()));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_INVALID_PRICE_RANGE,
                    ErrorCode.ORDER_ERROR_INVALID_PRICE_RANGE.getMessage());
        }

        BigDecimal amount = form.getAmount().setScale(AMOUNT_SCALE, RoundingMode.FLOOR);
        // 発注数量チェック:valid
        if (amount.signum() < 0) {
            log.warn(
                    "pointUserId: {}, amount: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(amount.toPlainString()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT.getMessage());
        }
        // 発注数量が指定桁数以内であること
        // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
        if (amount.scale() > AMOUNT_SCALE) {

            throw new GameException(
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT.getMessage());
        }
        BigDecimal price = form.getPrice();
        BigDecimal mmPrice = form.getMmPrice();
        // 発注価格チェック
        if (price == null || mmPrice == null) {
            log.warn(
                    "userId: {}, price: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(price),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.POS_ORDER_ERROR_PRICE_NOT_FOUND,
                    ErrorCode.POS_ORDER_ERROR_PRICE_NOT_FOUND.getMessage());
        }

        if (price.signum() < 1 && mmPrice.scale() < 1) {
            log.warn(
                    "userId: {}, price: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(price),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
                    ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE.getMessage());
        }

        // 発注価格が指定桁数以内であること
        // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
        if (price.scale() > symbol.getCurrencyPair().getQuotePrecision()) {
            log.warn(
                    "userId: {}, price: {}, price_scale: {}, quotePrecision: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(price),
                    JsonUtil.encode(price.scale()),
                    JsonUtil.encode(symbol.getCurrencyPair().getQuotePrecision()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_INVALID_PRICE,
                    ErrorCode.ORDER_ERROR_INVALID_PRICE.getMessage());
        }

        // 発注数量算出（通貨ペア桁数処理後）
        BigDecimal amountScaled = amount.setScale(AMOUNT_SCALE, RoundingMode.FLOOR);

        if (amountScaled.signum() < 0) {
            log.warn(
                    "userId: {}, amount: {}, amountScaled: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(amount.toPlainString()),
                    JsonUtil.encode(amountScaled),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT.getMessage());
        }

        // 注文価格チェック(通貨ぺア単位桁数処理後):valid
        BigDecimal priceScaled = currencyPairConfig.getCurrencyPair().getScaledPrice(price);

        // 発注数量チェック:valid
        if (priceScaled == null || priceScaled.signum() < 1) {
            log.warn(
                    "priceScaled: {}, userId: {}, form: {}",
                    JsonUtil.encode(priceScaled),
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
                    ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE.getMessage());
        }

        // 1注文の最小・最大注文数量チェック
        // 上限撤廃フラグ(apiより)が立っているときはスキップ
        if ((amountScaled.compareTo(currencyPairConfig.getMaxOrderAmount()) > 0
                || amountScaled.compareTo(currencyPairConfig.getMinOrderAmount()) < 0)) {
            log.warn(
                    "userId: {}, amount: {}, amountScaled: {}, maxOrderAmount: {}, minOrderAmount: {}, form: {}",
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(amount),
                    JsonUtil.encode(amountScaled),
                    JsonUtil.encode(currencyPairConfig.getMaxOrderAmount()),
                    JsonUtil.encode(currencyPairConfig.getMinOrderAmount()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_MINMAX,
                    ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_MINMAX.getMessage());
        }
        // 発注総額概算算出
        // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
        BigDecimal assetAmount = priceScaled.multiply(amountScaled);

        // 発注総額チェック:valid
        if (assetAmount.signum() < 1) {
            log.warn(
                    "assetAmount: {}, userId: {}, form: {}",
                    JsonUtil.encode(assetAmount),
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT.getMessage());
        }

        BigDecimal assetAmountScaled =
                this.calculateAssetAmount(symbol, priceScaled.multiply(amountScaled));

        if (assetAmountScaled.signum() < 1) {
            log.warn(
                    "assetAmount: {}, assetAmountScaled: {}, userId: {}, form: {}",
                    JsonUtil.encode(assetAmount),
                    JsonUtil.encode(assetAmountScaled),
                    JsonUtil.encode(user.getId()),
                    JsonUtil.encode(form));
            throw new GameException(
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
                    ErrorCode.ORDER_ERROR_INVALID_AMOUNT.getMessage());
        }
        // 买 BUY jpy  卖 SELL 币
        // 資産ロック
        return (PosOrder)
                redisManager.executeWithLock(
                        getLockKey(user.getId(), baseCurrency),
                        LockParams.ORDER,
                        () -> {
                            return redisManager.executeWithLock(
                                    getLockKey(user.getId(), quoteCurrency),
                                    LockParams.ORDER,
                                    () -> {
                                        return customTransactionManager.execute(
                                                entityManager -> {
                                                    log.info(
                                                            "ordertradelog,symbolId,{},marketOrder_transaction_start",
                                                            symbol.getId());

                                                    // 【注意】トランザクション内ではentityManager引数無しのクエリを使用しないこと(save,find等。findOneはentityManager引数必須)
                                                    // トランザクション内では、エラー時roll-backさせるためexceptionを出す(save,cancel処理含む)。try-catch使用注意
                                                    Asset quoteAsset =
                                                            assetService.findOrCreate(
                                                                    user.getId(),
                                                                    quoteCurrency,
                                                                    entityManager);
                                                    Asset baseAsset =
                                                            assetService.findOrCreate(
                                                                    user.getId(),
                                                                    baseCurrency,
                                                                    entityManager);

                                                    if (baseAsset == null || quoteAsset == null) {
                                                        log.warn(
                                                                "pointUserId: {}, symbol: {}, baseAsset: {}, quoteAsset: {}",
                                                                JsonUtil.encode(user.getId()),
                                                                JsonUtil.encode(symbol),
                                                                JsonUtil.encode(baseAsset),
                                                                JsonUtil.encode(quoteAsset));
                                                        throw new GameException(
                                                                ErrorCode
                                                                        .ORDER_ERROR_ASSET_NOT_FOUND,
                                                                ErrorCode
                                                                        .ORDER_ERROR_ASSET_NOT_FOUND
                                                                        .getMessage());
                                                    }
                                                    // 利用可能額チェック
                                                    if (orderSide.isSell()) {
                                                        // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
                                                        if (baseAsset
                                                                        .getOnhandAmount()
                                                                        .subtract(
                                                                                baseAsset
                                                                                        .getLockedAmount())
                                                                        .compareTo(amountScaled)
                                                                < 0) {
                                                            log.warn(
                                                                    "base_onhandAmount: {}, base_lockedAmount: {}, base_remainingAmount: {}, amountScaled: {}, userId: {}, form: {}",
                                                                    JsonUtil.encode(
                                                                            baseAsset
                                                                                    .getOnhandAmount()),
                                                                    JsonUtil.encode(
                                                                            baseAsset
                                                                                    .getLockedAmount()),
                                                                    JsonUtil.encode(
                                                                            baseAsset
                                                                                    .getOnhandAmount()
                                                                                    .subtract(
                                                                                            baseAsset
                                                                                                    .getLockedAmount())),
                                                                    JsonUtil.encode(amountScaled),
                                                                    JsonUtil.encode(user.getId()),
                                                                    JsonUtil.encode(form));
                                                            throw new GameException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_EXCEED_ASSET
                                                                            .getMessage());
                                                        }
                                                    } else {
                                                        if (quoteAsset
                                                                        .getOnhandAmount()
                                                                        .subtract(
                                                                                quoteAsset
                                                                                        .getLockedAmount())
                                                                        .compareTo(
                                                                                assetAmountScaled)
                                                                < 0) {
                                                            log.warn(
                                                                    "base_onhandAmount: {}, base_lockedAmount: {}, base_remainingAmount: {}, amountScaled: {}, pointUserId: {}, form: {}",
                                                                    JsonUtil.encode(
                                                                            baseAsset
                                                                                    .getOnhandAmount()),
                                                                    JsonUtil.encode(
                                                                            baseAsset
                                                                                    .getLockedAmount()),
                                                                    JsonUtil.encode(
                                                                            baseAsset
                                                                                    .getOnhandAmount()
                                                                                    .subtract(
                                                                                            baseAsset
                                                                                                    .getLockedAmount())),
                                                                    JsonUtil.encode(amountScaled),
                                                                    JsonUtil.encode(user.getId()),
                                                                    JsonUtil.encode(form));
                                                            throw new GameException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                                                    ErrorCode
                                                                            .ORDER_ERROR_AMOUNT_EXCEED_ASSET
                                                                            .getMessage());
                                                        }
                                                    }
                                                    // 1日の取引上限チェック：通貨単位
                                                    // 上限撤廃フラグが立っているときはスキップ
                                                    BigDecimal baseAmountSumDay =
                                                            getOrderTradeAmountPerDay(
                                                                    amountScaled,
                                                                    baseCurrency,
                                                                    user,
                                                                    entityManager);

                                                    if (baseAmountSumDay.compareTo(
                                                                    baseCurrencyConfig
                                                                            .getMaxOrderAmountPerDay())
                                                            > 0) {

                                                        log.warn(
                                                                "userId: {}, form: {}",
                                                                JsonUtil.encode(user.getId()),
                                                                JsonUtil.encode(form));
                                                        throw new GameException(
                                                                ErrorCode
                                                                        .ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                                                ErrorCode
                                                                        .ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY
                                                                        .getMessage());
                                                    }

                                                    BigDecimal quoteAmountSumDay =
                                                            getOrderTradeAmountPerDay(
                                                                    assetAmountScaled,
                                                                    quoteCurrency,
                                                                    user,
                                                                    entityManager);

                                                    if (quoteAmountSumDay.compareTo(
                                                                    quoteCurrencyConfig
                                                                            .getMaxOrderAmountPerDay())
                                                            > 0) {
                                                        log.warn(
                                                                "pointUserId: {}, form: {}",
                                                                JsonUtil.encode(user.getId()),
                                                                JsonUtil.encode(form));
                                                        throw new GameException(
                                                                ErrorCode
                                                                        .ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                                                ErrorCode
                                                                        .ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY
                                                                        .getMessage());
                                                    }
                                                    String currencyType =
                                                            CurrencyType.getCurrencyTypeById(
                                                                    symbol.getId());
                                                    BcCurrencyConfig currencyConfig =
                                                            bcCurrencyConfigService.findOneByActive(
                                                                    currencyType);
                                                    if (TradeType.OPERATE.equals(
                                                                    symbol.getTradeType())
                                                            && CurrencyPair.BALC_JPY.equals(
                                                                    symbol.getCurrencyPair())) {
                                                        log.info(
                                                                "currencyConfigData: {}",
                                                                JsonUtil.encode(currencyConfig));
                                                        if (currencyConfig == null) {
                                                            throw new GameException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_BC_CURRENCY_CONFIG_NOT_FOUND,
                                                                    ErrorCode
                                                                            .ORDER_ERROR_BC_CURRENCY_CONFIG_NOT_FOUND
                                                                            .getMessage());
                                                        }
                                                    } else if (TradeType.OPERATE.equals(
                                                                    symbol.getTradeType())
                                                            && CurrencyPair.ACTC_JPY.equals(
                                                                    symbol.getCurrencyPair())) {
                                                        if (currencyConfig == null) {
                                                            throw new GameException(
                                                                    ErrorCode
                                                                            .ORDER_ERROR_BC_CURRENCY_CONFIG_NOT_FOUND,
                                                                    ErrorCode
                                                                            .ORDER_ERROR_BC_CURRENCY_CONFIG_NOT_FOUND
                                                                            .getMessage());
                                                        }
                                                    }
                                                    PosTrade tempPosTrade = new PosTrade();
                                                    tempPosTrade.setProperties(
                                                            symbol.getId(),
                                                            user.getId(),
                                                            orderSide,
                                                            price,
                                                            amountScaled,
                                                            TradeAction.TAKER,
                                                            null);
                                                    tempPosTrade.setCalProperties(
                                                            BigDecimal.ZERO,
                                                            orderType,
                                                            orderChannel,
                                                            orderbookService
                                                                    .getOperateBestBidOfQuoteJpy(
                                                                            quoteCurrency),
                                                            assetAmountScaled,
                                                            UserIdType.Operate,
                                                            null, // evalProfitLossAmt
                                                            null, // evalProfitLossAmtRate
                                                            null, // avgAcqUnitPrice
                                                            BigDecimal.ZERO, // income
                                                            null // experiencePoints
                                                            );

                                                    getCalValue calValues =
                                                            getGetCalValue(
                                                                    price,
                                                                    symbol,
                                                                    user.getId(),
                                                                    orderSide,
                                                                    UserIdType.Operate,
                                                                    form.amount,
                                                                    tempPosTrade);
                                                    BigDecimal income = BigDecimal.ZERO;
                                                    long experiencePoints = 0L;
                                                    if (orderSide.isSell()) {
                                                        // Base:注文数量でロック（通貨ペア単位の桁数処理後、通貨単位の桁数処理なし)
                                                        assetService.updateWithExternalLock(
                                                                user.getId(),
                                                                symbol.getCurrencyPair()
                                                                        .getBaseCurrency(),
                                                                amountScaled.negate(),
                                                                BigDecimal.ZERO,
                                                                entityManager);
                                                        assetService.updateWithExternalLock(
                                                                user.getId(),
                                                                symbol.getCurrencyPair()
                                                                        .getQuoteCurrency(),
                                                                assetAmountScaled,
                                                                BigDecimal.ZERO,
                                                                entityManager);
                                                        // when sell, save Pos Order Trade History
                                                        // for cal monster
                                                        income =
                                                                symbol.getCurrencyPair()
                                                                        .getOperateScaledAmount(
                                                                                form.amount
                                                                                        .multiply(
                                                                                                calValues
                                                                                                        .nowPrice()
                                                                                                        .subtract(
                                                                                                                baseAsset
                                                                                                                        .getAvgAcqUnitPrice())));

                                                        // SET POWER By monster income
                                                        experiencePoints =
                                                                monsterExperienceService
                                                                        .calculateExperiencePoints(
                                                                                tempPosTrade
                                                                                        .getSymbolId(),
                                                                                tempPosTrade
                                                                                        .getIdType(),
                                                                                income);
                                                    } else {
                                                        // Quote:注文数量×板平均価格で算出した注文総額概算でロック(通貨ペア単位の桁数処理後、通貨単位の桁数処理なし)
                                                        assetService.updateWithExternalLock(
                                                                user.getId(),
                                                                symbol.getCurrencyPair()
                                                                        .getQuoteCurrency(),
                                                                assetAmountScaled.negate(),
                                                                BigDecimal.ZERO,
                                                                entityManager);
                                                        assetService
                                                                .updateAssestAmountWithExternalLock(
                                                                        user.getId(),
                                                                        symbol.getCurrencyPair()
                                                                                .getBaseCurrency(),
                                                                        amountScaled,
                                                                        BigDecimal.ZERO,
                                                                        calValues
                                                                                .evalProfitLossAmt(),
                                                                        calValues
                                                                                .evalProfitLossAmtRate(),
                                                                        calValues.avgAcqUnitPrice(),
                                                                        calValues.assetTotal(),
                                                                        entityManager);
                                                    }

                                                    // asset更新後チェック：残資産マイナスでないこと
                                                    if (baseAsset
                                                                            .getOnhandAmount()
                                                                            .subtract(
                                                                                    baseAsset
                                                                                            .getLockedAmount())
                                                                            .signum()
                                                                    < 0
                                                            || quoteAsset
                                                                            .getOnhandAmount()
                                                                            .subtract(
                                                                                    quoteAsset
                                                                                            .getLockedAmount())
                                                                            .signum()
                                                                    < 0
                                                            || baseAsset.getOnhandAmount().signum()
                                                                    < 0
                                                            || baseAsset.getLockedAmount().signum()
                                                                    < 0
                                                            || quoteAsset.getOnhandAmount().signum()
                                                                    < 0
                                                            || quoteAsset.getLockedAmount().signum()
                                                                    < 0) {
                                                        log.warn(
                                                                "baseOnhandAmount: {}, baseLockedAmount: {}, quoteOnhandAmount: {}, quoteLockedAmount: {}, pointUserId: {}, form: {}",
                                                                JsonUtil.encode(
                                                                        baseAsset
                                                                                .getOnhandAmount()),
                                                                JsonUtil.encode(
                                                                        baseAsset
                                                                                .getLockedAmount()),
                                                                JsonUtil.encode(
                                                                        quoteAsset
                                                                                .getOnhandAmount()),
                                                                JsonUtil.encode(
                                                                        quoteAsset
                                                                                .getLockedAmount()),
                                                                JsonUtil.encode(user.getId()),
                                                                JsonUtil.encode(form));
                                                        throw new GameException(
                                                                ErrorCode.ORDER_ERROR_INVALID_ASSET,
                                                                ErrorCode.ORDER_ERROR_INVALID_ASSET
                                                                        .getMessage());
                                                    }
                                                    PosOrder operateOrder = newEntity();
                                                    operateOrder.setProperties(
                                                            symbol.getId(),
                                                            user.getId(),
                                                            amountScaled);
                                                    operateOrder.setAnotherProperties(
                                                            orderSide,
                                                            orderType,
                                                            orderChannel,
                                                            price,
                                                            mmPrice,
                                                            BigDecimal.ZERO,
                                                            PosOrderStatus.FULLY_FILLED,
                                                            OrderOperator.USER,
                                                            form.notes,
                                                            UserIdType.Operate);
                                                    this.save(operateOrder, entityManager);

                                                    PosTrade posTrade = posTradeService.newEntity();
                                                    posTrade.setProperties(
                                                            symbol.getId(),
                                                            user.getId(),
                                                            orderSide,
                                                            price,
                                                            amountScaled,
                                                            TradeAction.TAKER,
                                                            operateOrder.getId());
                                                    posTrade.setCalProperties(
                                                            BigDecimal.ZERO,
                                                            operateOrder.getOrderType(),
                                                            operateOrder.getOrderChannel(),
                                                            orderbookService
                                                                    .getOperateBestBidOfQuoteJpy(
                                                                            quoteCurrency),
                                                            assetAmountScaled,
                                                            UserIdType.Operate,
                                                            orderSide.isSell()
                                                                    ? calValues.evalProfitLossAmt()
                                                                    : null,
                                                            orderSide.isSell()
                                                                    ? calValues
                                                                            .evalProfitLossAmtRate()
                                                                    : null,
                                                            orderSide.isSell()
                                                                    ? baseAsset.getAvgAcqUnitPrice()
                                                                    : null,
                                                            orderSide.isSell() ? income : null,
                                                            orderSide.isSell()
                                                                    ? experiencePoints
                                                                    : null);
                                                    posTrade.setNotes(operateOrder.getNotes());

                                                    // -- START
                                                    // https://backseat-inc.atlassian.net/browse/OFLM-1496
                                                    if (orderSide.isSell()) {
                                                        Asset asset =
                                                                assetService.findOne(
                                                                        user.getId(),
                                                                        symbol.getCurrencyPair()
                                                                                .getBaseCurrency());
                                                        Integer userCropStage =
                                                                userCropStatusService
                                                                        .findBy(
                                                                                user.getId(),
                                                                                asset.getId(),
                                                                                symbol
                                                                                        .getTradeType())
                                                                        .map(
                                                                                UserCropStatus
                                                                                        ::getGrowthStageId)
                                                                        .orElse(
                                                                                NumberUtils
                                                                                        .INTEGER_ZERO);
                                                        posTrade.setUserGrowthStageId(
                                                                userCropStage);
                                                    } else {
                                                        posTrade.setUserGrowthStageId(
                                                                NumberUtils.INTEGER_ZERO);
                                                    }
                                                    // -- END
                                                    // https://backseat-inc.atlassian.net/browse/OFLM-1496

                                                    posTradeService.save(posTrade, entityManager);
                                                    if (TradeType.OPERATE.equals(
                                                                    symbol.getTradeType())
                                                            && CurrencyPair.BALC_JPY.equals(
                                                                    symbol.getCurrencyPair())) {
                                                        handleOperateTrade(
                                                                currencyConfig,
                                                                amount,
                                                                orderSide,
                                                                operateOrder,
                                                                posTrade,
                                                                entityManager);
                                                    } else if (TradeType.OPERATE.equals(
                                                                    symbol.getTradeType())
                                                            && CurrencyPair.ACTC_JPY.equals(
                                                                    symbol.getCurrencyPair())) {
                                                        handleOperateTrade(
                                                                currencyConfig,
                                                                amount,
                                                                orderSide,
                                                                operateOrder,
                                                                posTrade,
                                                                entityManager);
                                                    }
                                                    // SET POWER WHEN BUY_SELL_TRADE
                                                    choicePowerSyncService.savePowerByRule(
                                                            user.getId(),
                                                            ChoiceActivityRuleEnum.BUY_SELL_TRADE,
                                                            posTrade.getAssetAmount() == null
                                                                    ? BigDecimal.ZERO
                                                                    : posTrade.getAssetAmount());
                                                    if (posTrade.getOrderSide().isSell()) {
                                                        userMonsterInfoService.calculateMonster(
                                                                income, posTrade);
                                                    }
                                                    return operateOrder;
                                                });
                                    });
                        });
    }

    private boolean validatePrice(Symbol symbol, OrderSide orderSide, BigDecimal inputPrice) {
        PosBestPriceData pos = orderbookService.getPos(symbol);
        BigDecimal latestPrice = orderSide.isSell() ? pos.getBestBid() : pos.getBestAsk();
        BigDecimal range = latestPrice.multiply(priceRangePercent);
        BigDecimal minAllowed = latestPrice.subtract(range);
        BigDecimal maxAllowed = latestPrice.add(range);
        return inputPrice.compareTo(minAllowed) >= 0 && inputPrice.compareTo(maxAllowed) <= 0;
    }

    /**
     * get評価損益額&評価損益額率&平均取得単価
     *
     * @param nowPrice
     * @param symbol
     * @param userId
     * @param orderSide
     * @param idType
     * @param assetTotal
     * @return
     */
    private getCalValue getGetCalValue(
            BigDecimal nowPrice,
            Symbol symbol,
            Long userId,
            OrderSide orderSide,
            UserIdType idType,
            BigDecimal assetTotal,
            PosTrade tempPosTrade) {
        BigDecimal evalProfitLossAmtRate;
        BigDecimal avgAcqUnitPrice;
        BigDecimal totalAmount;
        BigDecimal evalProfitLossAmt;
        // get history assetamount
        List<PosTrade> posTradeAmountHistoryList =
                posTradeService.getPosTradeHistoryTotals(symbol.getId(), userId, orderSide, idType);

        // get posTrade assetAmountTotal history
        BigDecimal historyAssetAmountTotal =
                posTradeAmountHistoryList.stream()
                        .map(
                                trade -> {
                                    BigDecimal tradeValue =
                                            trade.getPrice().multiply(trade.getAmount());
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? tradeValue
                                            : tradeValue.negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade assetAmountTotal:{}", historyAssetAmountTotal);

        // get posTrade amountTotal history
        BigDecimal historyAmountTotal =
                posTradeAmountHistoryList.stream()
                        .map(
                                trade -> {
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? trade.getAmount()
                                            : trade.getAmount().negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade amountTotal:{}", historyAmountTotal);

        if (historyAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
            historyAssetAmountTotal = BigDecimal.ZERO;
        }
        log.info("history asset amount total: {}", historyAssetAmountTotal);
        log.info("history amount total: {}", historyAmountTotal);

        // get posTrade
        List<PosTrade> posTradeAmountList =
                posTradeService.getPosTradeAmountTotal(symbol.getId(), userId, orderSide, idType);

        // get posTrade assetAmountTotal
        BigDecimal posTradeAssetAmountTotal =
                posTradeAmountList.stream()
                        .map(
                                trade -> {
                                    BigDecimal tradeValue =
                                            trade.getPrice().multiply(trade.getAmount());
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? tradeValue
                                            : tradeValue.negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade assetAmountTotal:{}", posTradeAssetAmountTotal);

        // get posTrade amountTotal
        BigDecimal posTradeAmountTotal =
                posTradeAmountList.stream()
                        .map(
                                trade -> {
                                    return trade.getOrderSide() == OrderSide.BUY
                                            ? trade.getAmount()
                                            : trade.getAmount().negate();
                                })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("get posTrade amountTotal:{}", posTradeAmountTotal);

        if (posTradeAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
            posTradeAssetAmountTotal = BigDecimal.ZERO;
        }

        totalAmount = historyAmountTotal.add(posTradeAmountTotal);
        // get temp postrade
        BigDecimal tempPosTradeAmount = tempPosTrade.getAmount();
        if (tempPosTradeAmount == null) {
            tempPosTradeAmount = BigDecimal.ZERO;
        }
        totalAmount = totalAmount.add(tempPosTradeAmount);

        BigDecimal tempPosTradePrice = tempPosTrade.getPrice();
        if (tempPosTradePrice == null) {
            tempPosTradePrice = BigDecimal.ZERO;
        }
        BigDecimal tempPosTradeAssetAmount = tempPosTradePrice.multiply(tempPosTradeAmount);
        if (tempPosTradeAssetAmount == null) {
            tempPosTradeAssetAmount = BigDecimal.ZERO;
        }
        assetTotal = historyAssetAmountTotal.add(posTradeAssetAmountTotal);
        assetTotal = assetTotal.add(tempPosTradeAssetAmount);

        if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            avgAcqUnitPrice = BigDecimal.ZERO;
        } else {
            // cal：(Z * Z1) + (Y * Y1) / (Z1 + Y1)
            avgAcqUnitPrice =
                    symbol.getCurrencyPair()
                            .getOperateScaledAmount(
                                    assetTotal.divide(
                                            totalAmount,
                                            symbol.getCurrencyPair().getOperateBasePrecision(),
                                            BigDecimal.ROUND_HALF_UP));
        }
        log.info("get posTrade averagePrice:{}", avgAcqUnitPrice);

        evalProfitLossAmt =
                symbol.getCurrencyPair()
                        .getOperateScaledAmount(
                                totalAmount.multiply(nowPrice.subtract(avgAcqUnitPrice)));
        log.info("get evalProfitLossAmt:{}", evalProfitLossAmt);

        evalProfitLossAmtRate =
                symbol.getCurrencyPair()
                        .getOperateScaledAmount(
                                (assetTotal.compareTo(BigDecimal.ZERO) == 0)
                                        ? BigDecimal.ZERO
                                        : evalProfitLossAmt.divide(
                                                assetTotal,
                                                symbol.getCurrencyPair().getOperateBasePrecision(),
                                                BigDecimal.ROUND_HALF_UP));
        log.info("get evalProfitLossAmtRate:{}", evalProfitLossAmtRate);
        // first buy
        if (avgAcqUnitPrice.compareTo(BigDecimal.ZERO) == 0) {
            avgAcqUnitPrice = nowPrice;
        }
        return new getCalValue(
                avgAcqUnitPrice, evalProfitLossAmt, evalProfitLossAmtRate, nowPrice, assetTotal);
    }

    private record getCalValue(
            BigDecimal avgAcqUnitPrice,
            BigDecimal evalProfitLossAmt,
            BigDecimal evalProfitLossAmtRate,
            BigDecimal nowPrice,
            BigDecimal assetTotal) {}

    private BigDecimal getOrderTradeAmountPerDay(
            BigDecimal amount, Currency curerncy, PointUser user, EntityManager entityManager) {

        if (curerncy == Currency.JPY) {
            return BigDecimal.ZERO;
        }

        // 処理概要:
        // ・発注数量(発注総額) + 当日の未約定指値の残数量 + 当日取引履歴の数量合算が1日の取引上限を超えているか判定する
        // 備考：
        // ・指値に価格変更はないのでcreated_atでも問題ない
        // ・入力amount = baseのときはamount、quoteのときはassetAmount ※桁数処理後
        // 処理内容:
        // ・JPYはチェック対象外
        // ・入力のBASE, QUOTEともに、BASE/QUOTEそれぞれ参照し、同一通貨の場合合算する
        // ・買いも売りも正で合算する
        // ・成行・指値ともカウントする
        // ・JPYはチェック対象外
        // 例：
        // ・BTC/JPY では 買い注文と売り注文でBTCの数量をカウントする。JPYは不要。(例: 1BTC買いで BTCは+1 )
        // ・ETH/BTCは 買い注文と売り注文で通貨(BTCとETH)毎に数量をカウントする。(例: 1ETH(0.04BTC)の買いで、ETHは+1、BTCは+0.04 )
        // ・ETH/BTCの場合、BASE:ETH/BTCとETH/JPYのETHと、QUOTE:ETH/BTCとBTC/JPYのBTCそれぞれの合算で上限チェックを行う

        // 日本時間の00:00以上、翌日00:00未満をUTCのミリ秒に変換(TABLE上はUTCタイムスタンプ保持)
        Date date = new Date();
        long dateFromTmp =
                date.toInstant()
                        .atZone(ZoneId.of("Asia/Tokyo"))
                        .truncatedTo(ChronoUnit.DAYS)
                        .withZoneSameInstant(ZoneId.of("UTC"))
                        .toInstant()
                        .toEpochMilli();
        long dateToTmp =
                date.toInstant()
                        .atZone(ZoneId.of("Asia/Tokyo"))
                        .plusDays(1)
                        .truncatedTo(ChronoUnit.DAYS)
                        .withZoneSameInstant(ZoneId.of("UTC"))
                        .toInstant()
                        .toEpochMilli();

        // marketMaker用はログ抑制(appのみに実装)
        Date dateFrom = new Date(dateFromTmp);
        Date dateTo = new Date(dateToTmp);

        BigDecimal amountSumDay = BigDecimal.ZERO;
        BigDecimal amountPosTrade = BigDecimal.ZERO;
        BigDecimal amountPosOrder = BigDecimal.ZERO;
        List<Symbol> symbols = symbolService.findAllByCondition(TradeType.OPERATE, entityManager);
        if (symbols != null) {
            for (Symbol symbolData : symbols) {
                // 当日取引履歴の同一通貨分サマリ
                List<PosTrade> posTrades =
                        posTradeService.findByCondition(
                                symbolData.getId(), user.getId(), dateFrom, dateTo, entityManager);
                if (posTrades != null) {
                    for (PosTrade posTrade : posTrades) {
                        if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {
                            amountPosTrade = amountPosTrade.add(posTrade.getAmount());
                        }
                        if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {
                            amountPosTrade =
                                    amountPosTrade.add(
                                            posTrade.getAmount().multiply(posTrade.getPrice()));
                        }
                    }
                }
                // 当日未約定指値の同一通貨分サマリ
                List<PosOrderStatus> orderStatuses = new ArrayList<>();
                orderStatuses.add(PosOrderStatus.WAITING);
                orderStatuses.add(PosOrderStatus.PARTIALLY_FILLED);
                List<PosOrder> operateOrders =
                        this.findByCondition(
                                symbolData.getId(),
                                user.getId(),
                                null,
                                dateFrom,
                                dateTo,
                                orderStatuses,
                                entityManager);
                if (operateOrders != null) {
                    for (PosOrder operateOrder : operateOrders) {
                        if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {
                            amountPosOrder = amountPosOrder.add(operateOrder.getRemainingAmount());
                        }
                        if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {
                            amountPosOrder =
                                    amountPosOrder.add(
                                            operateOrder
                                                    .getRemainingAmount()
                                                    .multiply(operateOrder.getPrice()));
                        }
                    }
                }
            }
        }
        // 合算(発注数量(総額) + 当日取引履歴の約定数量(総額) + 当日指値の発注数量(総額))
        return amountSumDay.add(amount).add(amountPosTrade).add(amountPosOrder);
    }

    public List<PosOrder> findByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Date dateFrom,
            Date dateTo,
            List<PosOrderStatus> orderStatuses,
            EntityManager entityManager) {
        return new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
            @Override
            public List<PosOrder> query() {
                return getResultList(
                        entityManager,
                        criteriaQuery,
                        root,
                        createPredicatesOfFindByCondition(
                                criteriaBuilder,
                                root,
                                symbolId,
                                CollectionUtils.isEmpty(orderStatuses)
                                        ? null
                                        : orderStatuses.toArray(
                                                new PosOrderStatus[orderStatuses.size()]),
                                null,
                                null,
                                null,
                                null,
                                null,
                                userId,
                                null,
                                null,
                                id,
                                null,
                                null,
                                dateFrom != null ? dateFrom.getTime() : null,
                                dateTo != null ? dateTo.getTime() : null),
                        0,
                        Integer.MAX_VALUE,
                        criteriaBuilder.asc(root.get(Order_.id)));
            }
        }.execute(getEntityClass(), entityManager);
    }

    private List<Predicate> createPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PosOrder> root,
            Long symbolId,
            PosOrderStatus[] orderStatuses,
            OrderType orderType,
            OrderType[] orderTypes,
            OrderType[] exceptOrderTypes,
            OrderSide orderSide,
            OrderChannel orderChannel,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo) {
        List<Predicate> predicates =
                createPredicates(
                        criteriaBuilder,
                        root,
                        symbolId,
                        orderStatuses,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        orderSide,
                        userId,
                        null,
                        userIds,
                        exceptUserIds);

        if (orderChannel != null) {
            predicates.add(predicate.equalOrderChannel(criteriaBuilder, root, orderChannel));
        }

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {
            if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
            }

            if (idTo != null) {
                predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
            }

            if (dateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToCreatedAt(
                                criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
                predicates.add(
                        predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }
        }

        return predicates;
    }

    // インデックス: symbol_id, order_status, order_type, order_side, user_id, covered
    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<PosOrder> root,
            Long symbolId,
            PosOrderStatus[] orderStatuses,
            OrderType orderType,
            OrderType[] orderTypes,
            OrderType[] exceptOrderTypes,
            OrderSide orderSide,
            Long userId,
            Boolean covered,
            List<Long> userIds,
            List<Long> exceptUserIds) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));

        if (!ArrayUtils.isEmpty(orderStatuses)) {
            predicates.add(predicate.inOrderStatus(root, orderStatuses));
        }

        if (orderType != null) {
            predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
        } else if (!ArrayUtils.isEmpty(orderTypes)) {
            predicates.add(predicate.inOrderType(root, orderTypes));
        } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
            predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
        }

        if (orderSide != null) {
            predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
        }

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        } else {
            // userIdsとexceptUserIdsは同時に利用される場合あり（重複時はexceptUserIdsが優先）
            if (userIds != null) {
                predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
            }

            if (exceptUserIds != null) {
                predicates.add(predicate.inExceptUserIds(criteriaBuilder, root, exceptUserIds));
            }
        }

        if (covered != null) {
            predicates.add(predicate.equalOrderCovered(criteriaBuilder, root, covered));
        }

        return predicates;
    }

    private BigDecimal calculateAssetAmount(Symbol symbol, BigDecimal amountWithoutScale) {

        // 約定総額算出
        // 約定価格×約定数量(部分約定含む)
        // 資産計算は桁数処理を行う（切り上げ）

        int assetPrecision = symbol.getCurrencyPair().getAssetPrecision();

        // 指定桁数+1桁で切り上げ
        // 指定桁数2桁のとき、4桁(指定桁数+2)で切り捨てしてから、3桁(指定桁数+1)で切り上げ
        // ケース1(未満) fee少数部 < min :0.0009 => 0.000 => 0
        // ケース2(同値) fee少数部 = min :0.001 => 0.01
        // ケース3(超過) fee少数部 > min :0.002 => 0.01

        // ケース1(未満) fee少数部 < min :0.1009 => 0.1
        // ケース2(同値) fee少数部 = min :0.101 => 0.11
        // ケース3(超過) fee少数部 > min :0.102 => 0.11

        // ケース1(未満) fee少数部 < min :123.0009 => 123
        // ケース2(同値) fee少数部 = min :123.001 => 123.01
        // ケース3(超過) fee少数部 > min :123.002 => 123.01

        // 指定桁数+2桁で切り捨てしてから 指定桁数+1桁で切り上げ
        BigDecimal assetAmountWithoutScalePlus =
                amountWithoutScale.setScale(assetPrecision + 1, RoundingMode.DOWN);
        return symbol.getCurrencyPair()
                .getScaledAsset(assetAmountWithoutScalePlus, RoundingMode.UP);
    }

    public void sendOrderMail(PosOrder operateOrder, Symbol symbol, PointUser user) {
        if (user == null) {
            return;
        }
        try {
            MailNoreply mailInfo = mailNoreplyService.findOne(MailNoreplyType.POS_ORDER);
            String mailTemplate = mailInfo.getContents();
            mailTemplate =
                    mailTemplate.replace("${instrumentId}", symbol.getCurrencyPair().getName());
            mailTemplate = mailTemplate.replace("${side}", operateOrder.getOrderSide().toString());
            mailTemplate =
                    mailTemplate.replace("${orderType}", operateOrder.getOrderType().toString());
            mailTemplate =
                    mailTemplate.replace(
                            "${size}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledAmount(operateOrder.getAmount())
                                            .toPlainString()));
            mailTemplate =
                    mailTemplate.replace(
                            "${price}",
                            FormatUtil.formatThousandsSeparator(
                                    symbol.getCurrencyPair()
                                            .getScaledPrice(operateOrder.getPrice())
                                            .toPlainString()));
            mailTemplate = mailTemplate.replace("${orderId}", operateOrder.getId().toString());
            // sesManager.send(mailInfo.getFromAddress(), user.getEmail(), mailInfo.getTitle(),
            // mailTemplate);
        } catch (Exception e) {
            log.error("sendOperateOrderMail error", e);
        }
    }

    public PageData<PosOrder> findByConditionPageData(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderSide orderSide,
            Integer number,
            Integer size,
            boolean isAscending) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                CollectionUtils.isEmpty(orderStatuses)
                                                        ? null
                                                        : orderStatuses.toArray(
                                                                new PosOrderStatus
                                                                        [orderStatuses.size()]),
                                                orderType,
                                                CollectionUtils.isEmpty(orderTypes)
                                                        ? null
                                                        : orderTypes.toArray(
                                                                new OrderType[orderTypes.size()]),
                                                CollectionUtils.isEmpty(exceptOrderTypes)
                                                        ? null
                                                        : exceptOrderTypes.toArray(
                                                                new OrderType
                                                                        [exceptOrderTypes.size()]),
                                                orderSide,
                                                null,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo));
                            }
                        });
        return new PageData<PosOrder>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<PosOrder> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                CollectionUtils.isEmpty(orderStatuses)
                                                        ? null
                                                        : orderStatuses.toArray(
                                                                new PosOrderStatus
                                                                        [orderStatuses.size()]),
                                                orderType,
                                                CollectionUtils.isEmpty(orderTypes)
                                                        ? null
                                                        : orderTypes.toArray(
                                                                new OrderType[orderTypes.size()]),
                                                CollectionUtils.isEmpty(exceptOrderTypes)
                                                        ? null
                                                        : exceptOrderTypes.toArray(
                                                                new OrderType
                                                                        [exceptOrderTypes.size()]),
                                                orderSide,
                                                null,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo),
                                        number,
                                        size,
                                        isAscending
                                                ? criteriaBuilder.asc(root.get(Order_.id))
                                                : criteriaBuilder.desc(root.get(Order_.id)));
                            }
                        }));
    }

    public List<PosOrder> findByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderSide orderSide,
            Integer number,
            Integer size,
            boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<PosOrder> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                createPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        symbolId,
                                        CollectionUtils.isEmpty(orderStatuses)
                                                ? null
                                                : orderStatuses.toArray(
                                                        new PosOrderStatus[orderStatuses.size()]),
                                        orderType,
                                        CollectionUtils.isEmpty(orderTypes)
                                                ? null
                                                : orderTypes.toArray(
                                                        new OrderType[orderTypes.size()]),
                                        CollectionUtils.isEmpty(exceptOrderTypes)
                                                ? null
                                                : exceptOrderTypes.toArray(
                                                        new OrderType[exceptOrderTypes.size()]),
                                        orderSide,
                                        null,
                                        userId,
                                        null,
                                        null,
                                        id,
                                        idFrom,
                                        idTo,
                                        dateFrom,
                                        dateTo),
                                number,
                                size,
                                isAscending
                                        ? criteriaBuilder.asc(root.get(Order_.id))
                                        : criteriaBuilder.desc(root.get(Order_.id)));
                    }
                });
    }

    public List<PosOrder> findAllByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            OrderSide orderSide) {
        return new ArrayList<PosOrder>(
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PosOrder, List<PosOrder>>() {
                            @Override
                            public List<PosOrder> query() {
                                List<Predicate> predicates =
                                        createPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                CollectionUtils.isEmpty(orderStatuses)
                                                        ? null
                                                        : orderStatuses.toArray(
                                                                new PosOrderStatus
                                                                        [orderStatuses.size()]),
                                                orderType,
                                                null,
                                                null,
                                                orderSide,
                                                null,
                                                userId,
                                                null,
                                                null,
                                                id,
                                                idFrom,
                                                idTo,
                                                dateFrom,
                                                dateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.desc(root.get(Order_.id)));
                            }
                        }));
    }

    public List<PosOrder> findAllFromHistory(
            Symbol symbol,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Date createdAtFrom,
            Date createdAtTo,
            List<PosOrderStatus> orderStatuses,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderSide orderSide) {
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

        StringBuilder sql =
                createSqlString(
                        mapSqlParameterSource,
                        symbol,
                        userId,
                        null,
                        null,
                        id,
                        idFrom,
                        idTo,
                        createdAtFrom,
                        createdAtTo,
                        orderStatuses,
                        orderSide,
                        orderType,
                        orderTypes,
                        exceptOrderTypes,
                        null);

        log.info("redshift-sql:{}", sql);
        log.info("redshift-params:{}", mapSqlParameterSource.getValues());

        return historicalTransactionManager.findFromHistory(
                sql.toString(), mapSqlParameterSource, newRowMapper());
    }

    private StringBuilder createSqlString(
            MapSqlParameterSource mapSqlParameterSource,
            Symbol symbol,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds,
            Long id,
            Long idFrom,
            Long idTo,
            Date createdAtFrom,
            Date createdAtTo,
            List<PosOrderStatus> orderStatuses,
            OrderSide orderSide,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes,
            OrderChannel orderChannel) {

        StringBuilder sql = new StringBuilder("select * from pos_order");

        sql.append(" where symbol_id = :symbol_id");
        mapSqlParameterSource.addValue("symbol_id", symbol.getId());

        if (id != null) {
            appendIdCondition(sql, mapSqlParameterSource, id);
        } else {
            appendUserConditions(sql, mapSqlParameterSource, userId, userIds, exceptUserIds);
            appendIdRangeConditions(sql, mapSqlParameterSource, idFrom, idTo);
            appendDateConditions(sql, mapSqlParameterSource, createdAtFrom, createdAtTo);
            appendOrderStatusCondition(sql, mapSqlParameterSource, orderStatuses);
            appendOrderSideCondition(sql, mapSqlParameterSource, orderSide);
            appendOrderTypeConditions(
                    sql, mapSqlParameterSource, orderType, orderTypes, exceptOrderTypes);
            appendOrderChannelCondition(sql, mapSqlParameterSource, orderChannel);
        }
        return sql;
    }

    private void appendIdCondition(
            StringBuilder sql, MapSqlParameterSource mapSqlParameterSource, Long id) {
        sql.append(" and id = :id");
        mapSqlParameterSource.addValue("id", id);
    }

    private void appendUserConditions(
            StringBuilder sql,
            MapSqlParameterSource mapSqlParameterSource,
            Long userId,
            List<Long> userIds,
            List<Long> exceptUserIds) {
        if (userId != null) {
            sql.append(" and user_id = :user_id");
            mapSqlParameterSource.addValue("user_id", userId);
        } else {
            if (userIds != null) {
                sql.append(" and user_id in (:user_ids)");
                mapSqlParameterSource.addValue("user_ids", userIds);
            }
            if (exceptUserIds != null) {
                sql.append(" and user_id not in (:except_user_ids)");
                mapSqlParameterSource.addValue("except_user_ids", exceptUserIds);
            }
        }
    }

    private void appendIdRangeConditions(
            StringBuilder sql,
            MapSqlParameterSource mapSqlParameterSource,
            Long idFrom,
            Long idTo) {
        if (idFrom != null) {
            sql.append(" and id >= :id_from");
            mapSqlParameterSource.addValue("id_from", idFrom);
        }
        if (idTo != null) {
            sql.append(" and id <= :id_to");
            mapSqlParameterSource.addValue("id_to", idTo);
        }
    }

    private void appendDateConditions(
            StringBuilder sql,
            MapSqlParameterSource mapSqlParameterSource,
            Date createdAtFrom,
            Date createdAtTo) {
        if (createdAtFrom != null) {
            sql.append(" and created_at >= :created_at_from");
            mapSqlParameterSource.addValue("created_at_from", createdAtFrom);
        }
        if (createdAtTo != null) {
            sql.append(" and created_at < :created_at_to");
            mapSqlParameterSource.addValue("created_at_to", createdAtTo);
        }
    }

    private void appendOrderStatusCondition(
            StringBuilder sql,
            MapSqlParameterSource mapSqlParameterSource,
            List<PosOrderStatus> orderStatuses) {
        if (orderStatuses != null && !orderStatuses.isEmpty()) {
            sql.append(" and order_status in (:order_statuses)");
            mapSqlParameterSource.addValue(
                    "order_statuses",
                    orderStatuses.stream().map(Enum::name).collect(Collectors.toList()));
        }
    }

    private void appendOrderSideCondition(
            StringBuilder sql, MapSqlParameterSource mapSqlParameterSource, OrderSide orderSide) {
        if (orderSide != null) {
            sql.append(" and order_side = :order_side");
            mapSqlParameterSource.addValue("order_side", orderSide.toString());
        }
    }

    private void appendOrderTypeConditions(
            StringBuilder sql,
            MapSqlParameterSource mapSqlParameterSource,
            OrderType orderType,
            List<OrderType> orderTypes,
            List<OrderType> exceptOrderTypes) {
        if (orderType != null) {
            sql.append(" and order_type = :order_type");
            mapSqlParameterSource.addValue("order_type", orderType.toString());
        } else {
            if (!CollectionUtils.isEmpty(orderTypes)) {
                sql.append(" and order_type in (:order_types)");
                mapSqlParameterSource.addValue(
                        "order_types",
                        orderTypes.stream().map(Enum::name).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(exceptOrderTypes)) {
                sql.append(" and order_type not in (:except_order_types)");
                mapSqlParameterSource.addValue(
                        "except_order_types",
                        exceptOrderTypes.stream().map(Enum::name).collect(Collectors.toList()));
            }
        }
    }

    private void appendOrderChannelCondition(
            StringBuilder sql,
            MapSqlParameterSource mapSqlParameterSource,
            OrderChannel orderChannel) {
        if (orderChannel != null) {
            sql.append(" and order_channel = :order_channel");
            mapSqlParameterSource.addValue("order_channel", orderChannel.toString());
        }
    }

    public PageData<PosOrder> createPageData(
            List<PosOrder> content, Long count, Integer number, Integer size) {

        List<PosOrder> pageContents = new ArrayList<>();

        int maxSize = Math.min((number * size + size), content.size());

        for (int i = number * size; i < maxSize; i++) {
            pageContents.add(content.get(i));
        }

        return new PageData<>(number, size, count, pageContents);
    }

    public void executeOrder(Symbol symbol) {
        List<PosOrder> posOrders = this.getPosOrders(symbol);
        if (CollectionUtils.isEmpty(posOrders)) {
            log.info("No orders, no execution required.  symbolId:{}", symbol.getId());
            return;
        }

        // Process SELL orders
        this.processOrdersBySide(posOrders, symbol, OrderSide.SELL);

        // Process BUY orders
        this.processOrdersBySide(posOrders, symbol, OrderSide.BUY);
    }

    private void processOrdersBySide(List<PosOrder> posOrders, Symbol symbol, OrderSide side) {
        BigDecimal totalAmount =
                posOrders.stream()
                        .filter(order -> side.equals(order.getOrderSide()))
                        .map(PosOrder::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

        String lockKey =
                getLockKeyAmount(side, symbol.getId(), symbol.getCurrencyPair().getQuoteCurrency());
        String sideName = side.name();
        try {
            if (!redisManager.executeWithLock(
                    lockKey,
                    LockParams.EXECUTE_ORDER,
                    () -> {
                        customTransactionManager.execute(
                                entityManager -> {
                                    OperateAggregatedOrder aggregateOrder =
                                            operateAggregatedOrderService.findAllByCondition(
                                                    symbol.getId(), side, OrderType.MARKET);

                                    if (Objects.isNull(aggregateOrder)) {
                                        OperateAggregatedOrder newOrder =
                                                operateAggregatedOrderService.newEntity();
                                        newOrder.setAnotherProperties(
                                                symbol.getId(),
                                                side,
                                                OrderType.MARKET,
                                                totalAmount);
                                        log.info(
                                                "[Order] Created new {} aggregated order. symbolId={}, amount={}",
                                                sideName,
                                                symbol.getId(),
                                                totalAmount);
                                        operateAggregatedOrderService.save(newOrder, entityManager);
                                    } else {
                                        BigDecimal newAmount =
                                                aggregateOrder.getAmount().add(totalAmount);
                                        log.info(
                                                "[Order] Updating existing {} order. symbolId={}, currentAmount={}, addedAmount={}, newAmount={}",
                                                sideName,
                                                symbol.getId(),
                                                aggregateOrder.getAmount(),
                                                totalAmount,
                                                newAmount);
                                        aggregateOrder.setAmount(newAmount);
                                        operateAggregatedOrderService.save(
                                                aggregateOrder, entityManager);
                                    }

                                    List<Long> orderIds =
                                            posOrders.stream()
                                                    .filter(
                                                            order ->
                                                                    side.equals(
                                                                            order.getOrderSide()))
                                                    .map(PosOrder::getId)
                                                    .toList();
                                    this.updateCovered(orderIds, entityManager);
                                });
                    })) {
                log.warn(
                        "[Order] Lock acquisition failed for {} orders. symbolId={}, lockKey={}",
                        sideName,
                        symbol.getId(),
                        lockKey);
            }
        } catch (Exception e) {
            log.error(
                    "[Order] Failed to process {} orders. symbolId={}, error={}",
                    sideName,
                    symbol.getId(),
                    e.getMessage(),
                    e);
        }
    }

    private void updateCovered(List<Long> orderIds, EntityManager entityManager) {
        orderIds.forEach(
                orderId -> {
                    PosOrder posOrder = entityManager.find(getEntityClass(), orderId);
                    posOrder.setCovered(Boolean.TRUE);
                    entityManager.merge(posOrder);
                });
    }

    private List<PosOrder> getPosOrders(Symbol symbol) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<PosOrder> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalSymbolId(criteriaBuilder, root, symbol.getId()));
                        predicates.add(predicate.equalOrderCovered(criteriaBuilder, root, false));
                        predicates.add(
                                predicate.equalIdType(criteriaBuilder, root, UserIdType.Operate));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public void handleOperateTrade(
            BcCurrencyConfig currencyConfig,
            BigDecimal amount,
            OrderSide orderSide,
            PosOrder operateOrder,
            PosTrade posTrade,
            EntityManager entityManager)
            throws Exception {
        List<CurrencyConfigData> currencyConfigDatas =
                JsonUtil.fromJsonToList(currencyConfig.getCurrencyData(), CurrencyConfigData.class);
        if (CollectionUtils.isEmpty(currencyConfigDatas)) {
            throw new GameException(
                    ErrorCode.ORDER_ERROR_BC_CURRENCY_CONFIG_NOT_FOUND,
                    ErrorCode.ORDER_ERROR_BC_CURRENCY_CONFIG_NOT_FOUND.getMessage());
        }
        // TODO handle operate aggregated order amount by worker
        for (CurrencyConfigData currencyConfigData : currencyConfigDatas) {
            BigDecimal amountSplit =
                    currencyConfigData
                            .getPercentage()
                            .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            log.info("amountSplit:{}", amountSplit);
            Long symbolId = currencyConfigData.getSymbolId();
            OperateAggregatedOrder operateAggregatedOrder =
                    operateAggregatedOrderService.findAllByCondition(
                            symbolId, orderSide, OrderType.MARKET);
            if (operateAggregatedOrder == null) {
                operateAggregatedOrder = new OperateAggregatedOrder();
                operateAggregatedOrder.setSymbolId(symbolId);
                operateAggregatedOrder.setOrderSide(orderSide);
                operateAggregatedOrder.setOrderType(OrderType.MARKET);
                operateAggregatedOrder.setAmount(BigDecimal.ZERO);
            }
            log.info(
                    "userId: {}, orderId: {},tradeId: {}, amount: {}",
                    operateOrder.getUserId(),
                    operateOrder.getId(),
                    posTrade.getId(),
                    amountSplit.multiply(amount));
            operateAggregatedOrder.setAmount(
                    operateAggregatedOrder
                            .getAmount()
                            .add(
                                    currencyConfigData
                                            .getCurrency()
                                            .getOperateScaledAmount(amountSplit.multiply(amount))));
            operateAggregatedOrderService.save(operateAggregatedOrder, entityManager);

            BcOrderCurrencySplit bcOrderCurrencySplit = new BcOrderCurrencySplit();
            bcOrderCurrencySplit.setSymbolId(symbolId);
            bcOrderCurrencySplit.setAmount(
                    currencyConfigData
                            .getCurrency()
                            .getOperateScaledAmount(amountSplit.multiply(amount)));
            bcOrderCurrencySplit.setOrderId(operateOrder.getId());
            bcOrderCurrencySplit.setTradeId(posTrade.getId());
            bcOrderCurrencySplitService.save(bcOrderCurrencySplit, entityManager);
        }
        this.updateCovered(List.of(operateOrder.getId()), entityManager);
    }
}
