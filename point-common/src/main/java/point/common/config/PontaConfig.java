package point.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "ponta")
public class PontaConfig {
    private String if1507Url;
    private String if1511Url;
    private String webIfUrl;
    private String credentials;

    private String loginUrl;
    private String callbackUrl;
    private String loginSuccessUrl;
    private String loginFailureUrl;
    private String partnerNumber;
    private String keystorePassword;
    private String keystoreBizPassword;

    private String gameLoginSuccessUrl;
    private String gameLoginFailureUrl;

    private String gameHomeUrl;
}
