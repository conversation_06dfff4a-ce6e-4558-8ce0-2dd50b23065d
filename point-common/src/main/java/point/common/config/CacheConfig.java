package point.common.config;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.session.data.redis.config.ConfigureRedisAction;
import point.common.component.CustomRedisTemplate;
import point.common.constant.ErrorCode;
import point.common.entity.AbstractEntity;
import point.common.entity.LoginAttempt;
import point.common.entity.Symbol;
import point.common.entity.SystemConfig;
import point.common.entity.WorkerMaster;
import point.common.exception.CustomException;

@Configuration
@ConfigurationProperties(prefix = "spring.data.redis")
@EnableCaching
public class CacheConfig<E extends AbstractEntity> extends CachingConfigurerSupport {

    public static void setRedisTemplateParameters(
            RedisTemplate<String, ?> redisTemplate, RedisConnectionFactory redisConnectionFactory) {
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.setHashKeySerializer(redisTemplate.getKeySerializer());
        redisTemplate.setHashValueSerializer(redisTemplate.getValueSerializer());
        redisTemplate.afterPropertiesSet();
    }

    @Getter @Setter private String host;

    @Getter @Setter private int port;

    @Getter @Setter private int maxTotal;

    @Getter @Setter private int maxIdle;

    @Getter @Setter private int minIdle;

    @Bean
    public ConfigureRedisAction configureRedisAction() {
        return ConfigureRedisAction.NO_OP;
    }

    @Bean
    public CacheManager cacheManager(RedisTemplate<String, Object> redisTemplate) throws Exception {
        RedisConnectionFactory redisConnectionFactory = redisTemplate.getConnectionFactory();

        if (redisConnectionFactory == null) {
            throw new CustomException(ErrorCode.SYSTEM_ERROR_REDIS_CONNECTION_FACTORY_IS_NULL);
        }

        return RedisCacheManager.create(redisConnectionFactory);
    }

    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        LettuceConnectionFactory lettuceConnectionFactory =
                new LettuceConnectionFactory(new RedisStandaloneConfiguration(host, port));
        //    jedisConnectionFactory.getPoolConfig().setMaxTotal(maxTotal);
        //    jedisConnectionFactory.getPoolConfig().setMaxIdle(maxIdle);
        //    jedisConnectionFactory.getPoolConfig().setMinIdle(minIdle);
        return lettuceConnectionFactory;
    }

    @Bean
    public RedisTemplate<String, E> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, E> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, BigDecimal> bigDecimalRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, BigDecimal> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Date> dateRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Date> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public CustomRedisTemplate<E> entityRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, E> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return new CustomRedisTemplate<>(redisTemplate);
    }

    @Bean
    public CustomRedisTemplate<Symbol> symbolRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Symbol> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return new CustomRedisTemplate<>(redisTemplate);
    }

    @Bean
    public CustomRedisTemplate<SystemConfig> systemConfigRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, SystemConfig> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return new CustomRedisTemplate<>(redisTemplate);
    }

    @Bean
    public CustomRedisTemplate<WorkerMaster> workerMasterRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, WorkerMaster> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return new CustomRedisTemplate<>(redisTemplate);
    }

    @Bean
    public CustomRedisTemplate<LoginAttempt> loginAttemptRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, LoginAttempt> redisTemplate = new RedisTemplate<>();
        setRedisTemplateParameters(redisTemplate, redisConnectionFactory);
        return new CustomRedisTemplate<>(redisTemplate);
    }
}
