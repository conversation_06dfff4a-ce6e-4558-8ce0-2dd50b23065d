package point.common.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.util.Properties;
import javax.sql.DataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.persistenceunit.PersistenceUnitManager;
import org.springframework.orm.jpa.vendor.AbstractJpaVendorAdapter;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

@Slf4j
public abstract class AbstractDataSourceConfig {

    @Getter @Setter private String driverClassName;

    @Getter @Setter private String url;

    @Getter @Setter private String username;

    @Getter @Setter private String password;

    @Getter @Setter private int minimumIdle;

    @Getter @Setter private int maximumPoolSize;

    @Getter @Setter private String connectionTestQuery;

    @Getter @Setter private boolean autoCommit;

    @Getter @Setter private String hibernateDialect;

    @Getter @Setter private String[] packagesToScan;

    @Getter @Setter private Long leakDetectionThreshold;

    @Getter @Setter private Long maxLifetime;
    @Getter @Setter private Long idleTimeout;

    @Autowired private Environment environment;

    protected DataSource dataSource() {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setDriverClassName(driverClassName);
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setMinimumIdle(minimumIdle);
        hikariConfig.setMaximumPoolSize(maximumPoolSize);
        hikariConfig.setConnectionTestQuery(connectionTestQuery);
        hikariConfig.setAutoCommit(autoCommit);
        if (this.maxLifetime != null) {
            hikariConfig.setMaxLifetime(maxLifetime);
        }
        if (this.idleTimeout != null) {
            hikariConfig.setIdleTimeout(idleTimeout);
        }
        if (leakDetectionThreshold != null) {
            hikariConfig.setLeakDetectionThreshold(leakDetectionThreshold);
        }

        log.info(
                "create datasource: driverClassName={}, url={}, username={}, maximumPoolSize={}, leakDetectionThreshold={}",
                driverClassName,
                url,
                username,
                maximumPoolSize,
                leakDetectionThreshold);

        return new HikariDataSource(hikariConfig);
    }

    protected Properties jpaProperties() {
        Properties properties = new Properties();

        String ddlAuto = environment.getProperty("spring.jpa.hibernate.ddl-auto");

        if (!StringUtils.isEmpty(ddlAuto)) {
            properties.put("hibernate.ddl_auto", ddlAuto);
        }

        if (!StringUtils.isEmpty(hibernateDialect)) {
            properties.put("hibernate.dialect", hibernateDialect);
        }

        String showSql = environment.getProperty("spring.jpa.show-sql");

        if (!StringUtils.isEmpty(showSql)) {
            properties.put("hibernate.show_sql", showSql);
        }

        String nonContextualCreation =
                environment.getProperty(
                        "spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation");

        if (!StringUtils.isEmpty(nonContextualCreation)) {
            properties.put("hibernate.jdbc.lob.non_contextual_creation", nonContextualCreation);
        }

        return properties;
    }

    protected JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    protected EntityManagerFactoryBuilder createEntityManagerFactoryBuilder(
            PersistenceUnitManager persistenceUnitManager, JpaProperties jpaProperties) {
        JpaVendorAdapter jpaVendorAdapter = createJpaVendorAdapter(jpaProperties);
        return new EntityManagerFactoryBuilder(
                jpaVendorAdapter, jpaProperties.getProperties(), persistenceUnitManager);
    }

    protected JpaVendorAdapter createJpaVendorAdapter(JpaProperties jpaProperties) {
        AbstractJpaVendorAdapter adapter = new HibernateJpaVendorAdapter();
        adapter.setShowSql(jpaProperties.isShowSql());
        if (jpaProperties.getDatabase() != null) {
            adapter.setDatabase(jpaProperties.getDatabase());
        }
        if (jpaProperties.getDatabasePlatform() != null) {
            adapter.setDatabasePlatform(jpaProperties.getDatabasePlatform());
        }
        adapter.setGenerateDdl(jpaProperties.isGenerateDdl());
        return adapter;
    }
}
