package point.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "aws.s3")
public class S3Config {

    public static class Bucket {

        @Getter @Setter private String name;
    }

    @Getter @Setter private Bucket kycBucket;

    @Getter @Setter private Bucket yearReportBucket;
}
