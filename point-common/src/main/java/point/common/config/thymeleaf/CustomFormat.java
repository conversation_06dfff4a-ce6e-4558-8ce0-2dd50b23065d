package point.common.config.thymeleaf;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class CustomFormat {

    public String commaSeparatedDecimal(BigDecimal decimal) {
        if (decimal == null) {
            return "";
        }

        int scale = decimal.stripTrailingZeros().scale();
        String format = "#,###";

        if (scale > 0) {
            format += "." + StringUtils.repeat("#", scale);
        }

        DecimalFormat df = new DecimalFormat(format);
        return df.format(decimal);
    }
}
