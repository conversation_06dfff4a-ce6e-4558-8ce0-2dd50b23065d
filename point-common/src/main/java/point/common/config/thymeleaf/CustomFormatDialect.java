package point.common.config.thymeleaf;

import java.util.Collections;
import java.util.Set;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.IExpressionContext;
import org.thymeleaf.dialect.IExpressionObjectDialect;
import org.thymeleaf.expression.IExpressionObjectFactory;

@Component
public class CustomFormatDialect implements IExpressionObjectDialect {

    private static final String CUSTOM_DIALECT_NAME = "customf";

    private static final Set<String> EXPRESSION_OBJECT_NAMES =
            Collections.singleton(CUSTOM_DIALECT_NAME);

    @Override
    public IExpressionObjectFactory getExpressionObjectFactory() {
        return new IExpressionObjectFactory() {

            @Override
            public Set<String> getAllExpressionObjectNames() {
                return EXPRESSION_OBJECT_NAMES;
            }

            @Override
            public Object buildObject(IExpressionContext context, String expressionObjectName) {
                if (CUSTOM_DIALECT_NAME.equals(expressionObjectName)) {
                    return new CustomFormat();
                }
                return null;
            }

            @Override
            public boolean isCacheable(String expressionObjectName) {
                return true;
            }
        };
    }

    @Override
    public String getName() {
        return "CustomFormatDialect";
    }
}
