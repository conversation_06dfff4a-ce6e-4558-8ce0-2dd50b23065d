package point.common.config;

import java.util.StringJoiner;
import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "git")
@PropertySource(value = "classpath:git.properties", ignoreResourceNotFound = true)
public class GitConfig {

    @Getter
    @Setter
    @Value("${git.remote.origin.url:#{null}}")
    private String remoteOriginUrl;

    @Getter
    @Setter
    @Value("${git.branch:#{null}}")
    private String branch;

    @Getter
    @Setter
    @Value("${git.closest.tag.name:#{null}}")
    private String closestTagName;

    @Getter
    @Setter
    @Value("${git.commit.time:#{null}}")
    private String commitTime;

    @Getter
    @Setter
    @Value("${git.commit.id:#{null}}")
    private String commitId;

    @Getter
    @Setter
    @Value("${git.commit.message.short:#{null}}")
    private String commitMessageShort;

    @PostConstruct
    public void logInfo() {
        if (remoteOriginUrl != null) {
            StringJoiner joiner =
                    new StringJoiner("\n\t")
                            .add("Build information: ")
                            .add("origin: " + remoteOriginUrl)
                            .add("branch: " + branch)
                            .add("closestTagName: " + closestTagName)
                            .add("commit.time: " + commitTime)
                            .add("commit.id: " + commitId)
                            .add("commit.message: " + commitMessageShort);

            log.info(joiner.toString());
        }
    }
}
