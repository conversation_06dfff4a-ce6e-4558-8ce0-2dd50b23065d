package point.common.config;

import java.io.UnsupportedEncodingException;
import java.util.regex.Pattern;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.ForwardedHeaderFilter;

@Configuration
public class ApplicationConfig {

    private static final Pattern PASSWORD_PATTERN =
            Pattern.compile(
                    "^(?=.*(?=[\\x21-\\x7e]+)[^0-9])(?=.*\\d)[\\x21-\\x7e]{8,64}$|^(?=.*(?=[\\x21-\\x7e]+)[^A-Za-z0-9])(?=.*[a-zA-Z])[\\x21-\\x7e]{8,64}$");

    public static boolean validatePassword(String source) {
        return PASSWORD_PATTERN.matcher(source).matches();
    }

    public static boolean validateAntiPhishing(String source) {
        try {
            if (source.length() != source.getBytes("UTF-8").length) {
                return false;
            }
        } catch (UnsupportedEncodingException e) {
            return false;
        }

        return 4 <= source.length() && source.length() <= 20;
    }

    @Bean
    public ForwardedHeaderFilter forwardedHeaderFilter() {
        return new ForwardedHeaderFilter();
    }
}
