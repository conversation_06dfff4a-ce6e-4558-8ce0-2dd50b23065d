package point.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "point-pos.base-trade.amber")
public class WhaleFinConfig {
    private String accessKey;
    private String apiHost;
    private String accessSecret;
}
