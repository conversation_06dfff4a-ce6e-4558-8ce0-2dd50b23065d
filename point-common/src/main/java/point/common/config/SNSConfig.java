package point.common.config;

import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import point.common.model.request.SNSRequestForm;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SNSConfig {

    private String host;

    private String token;

    public Map<String, String> createHeaderMap() {
        return Map.of(
                "Authorization",
                "Bearer " + token,
                "accept",
                MediaType.APPLICATION_JSON_VALUE,
                "Content-Type",
                MediaType.APPLICATION_JSON_VALUE);
    }

    public Map<String, Object> createEntityMap(SNSRequestForm snsRequestForm) {
        return Map.of(
                "to",
                snsRequestForm.getTo(),
                "text",
                snsRequestForm.getText(),
                "user_reference",
                snsRequestForm.getUser_reference());
    }
}
