package point.common.config;

import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import point.common.constant.Environment;

@Configuration
@ConfigurationProperties(prefix = "spring.config")
@RequiredArgsConstructor
@Slf4j
public class SpringConfig {

    @Getter @Setter private String domain;

    @Getter @Setter private String user;

    @Getter private Environment environment;

    @PostConstruct
    public void init() {
        if (user == null) {
            user = System.getenv("USER");
        }
    }

    public void setEnvironment(String env) {
        environment = Environment.valueOf(env);

        log.info("application environment: " + environment);
    }

    public boolean isLocal() {
        return getEnvironment() == Environment.local;
    }

    public boolean isDev() {
        return getEnvironment() == Environment.dev
                || getEnvironment() == Environment.dev2
                || getEnvironment() == Environment.dev3;
    }

    public boolean isStg() {
        return getEnvironment() == Environment.stg;
    }

    public boolean isPrd() {
        return getEnvironment() == Environment.prd;
    }

    public boolean isNotPrd() {
        return isDev() || isLocal() || isStg();
    }
}
