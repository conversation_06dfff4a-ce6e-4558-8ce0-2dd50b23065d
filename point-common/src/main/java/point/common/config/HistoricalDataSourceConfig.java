package point.common.config;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.persistenceunit.PersistenceUnitManager;
import org.springframework.transaction.PlatformTransactionManager;
import point.common.constant.ErrorCode;
import point.common.exception.CustomException;

/**
 * add configs below.
 *
 * <p>application.yaml ``` spring: datasource: historical: driver-class-name:
 * com.amazon.redshift.jdbc42.Driver connection-test-query: select 1 auto-commit: false
 * hibernate-dialect: org.hibernate.dialect.PostgreSQL9Dialect packages-to-scan:
 * point.common.entity,... ```
 *
 * <p>application-*.yaml ``` spring: datasource: historical: url: // endpoint of redshift username:
 * // username password: // password minimum-idle: 10 // suitably maximum-pool-size: 66 // suitably
 * ```
 */
@Configuration
@ConfigurationProperties(prefix = "spring.datasource.historical")
@EnableJpaRepositories(
        entityManagerFactoryRef = "historicalEntityManagerFactory",
        transactionManagerRef = "historicalJpaTransactionManager")
public class HistoricalDataSourceConfig extends AbstractDataSourceConfig {

    private final PersistenceUnitManager persistenceUnitManager;

    public HistoricalDataSourceConfig(
            ObjectProvider<PersistenceUnitManager> persistenceUnitManager) {
        this.persistenceUnitManager = persistenceUnitManager.getIfAvailable();
    }

    @Bean(name = "historicalDataSource")
    public DataSource historicalDataSource() {
        return dataSource();
    }

    @Bean(name = "historicalEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean historicalEntityManagerFactory(
            JpaProperties jpaProperties) {
        EntityManagerFactoryBuilder builder =
                super.createEntityManagerFactoryBuilder(this.persistenceUnitManager, jpaProperties);
        return builder.dataSource(historicalDataSource())
                .packages(getPackagesToScan())
                .persistenceUnit("historicalDs")
                .build();
    }

    @Bean(name = "historicalJpaTransactionManager")
    public PlatformTransactionManager historicalJpaTransactionManager(
            @Qualifier("historicalEntityManagerFactory")
                    LocalContainerEntityManagerFactoryBean localContainerEntityManagerFactoryBean)
            throws Exception {
        EntityManagerFactory entityManagerFactory =
                localContainerEntityManagerFactoryBean.getObject();

        if (entityManagerFactory == null) {
            throw new CustomException(ErrorCode.SYSTEM_ERROR_ENTITY_MANAGER_FACTORY_IS_NULL);
        }

        return new JpaTransactionManager(entityManagerFactory);
    }

    @Bean(name = "historicalJdbcTemplate")
    public JdbcTemplate historicalJdbcTemplate(
            @Qualifier("historicalDataSource") DataSource dataSource) {
        return jdbcTemplate(dataSource);
    }
}
