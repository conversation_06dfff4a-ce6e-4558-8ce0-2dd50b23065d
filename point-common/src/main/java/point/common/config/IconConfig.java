package point.common.config;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class IconConfig {

    @Value("${icon.path:/images}")
    @Getter
    @Setter
    private String path;

    public String getIconUrl(String name) {
        // String hashedFileName = SignatureUtil.getHash(Algorithm.SHA256, name);
        return path + "/" + name + ".svg";
    }
}
