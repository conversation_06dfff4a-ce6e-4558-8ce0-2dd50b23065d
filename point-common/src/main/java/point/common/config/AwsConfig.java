package point.common.config;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.model.SSECustomerKey;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import point.common.util.SignatureUtil;
import point.common.util.SignatureUtil.Algorithm;

@Configuration
public class AwsConfig {

    @Getter
    @Setter
    @Value("${aws.credentials.access-key:#{null}}")
    private String accessKey;

    @Getter
    @Setter
    @Value("${aws.credentials.secret-key:#{null}}")
    private String secretKey;

    @Getter @Setter private String salt;

    @Setter private String region;

    public AWSCredentialsProvider getCredentialsProvider() {
        if (StringUtils.isEmpty(accessKey) || StringUtils.isEmpty(secretKey)) {
            return new InstanceProfileCredentialsProvider(false);
        }
        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        return new AWSStaticCredentialsProvider(awsCredentials);
    }

    public Region getRegion() {
        if (StringUtils.isEmpty(region)) {
            return Region.getRegion(Regions.AP_NORTHEAST_1);
        }
        return Region.getRegion(Regions.fromName(region));
    }

    public SSECustomerKey getSSECustomerKey(String seed) {
        return new SSECustomerKey(
                SignatureUtil.getHash(Algorithm.SHA256, seed + salt).substring(0, 32).getBytes());
    }
}
