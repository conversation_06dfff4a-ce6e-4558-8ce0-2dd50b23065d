/**
 * Copyright (c) 2019, toiware Inc.<br>
 * All rights reserved
 */
package point.common.config;

import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import point.common.aml.WorldCheckOneClient;

/** World Check OneのAPIアクセスのための設定 */
@Configuration
@Setter
@ConfigurationProperties(prefix = "refinitiv")
public class WorldCheckOneConfig {

    private String host;

    private String apiKey;

    private String secretKey;

    private String groupId;

    public WorldCheckOneClient createClient() {
        return WorldCheckOneClient.create(host, apiKey, secretKey, groupId);
    }
}
