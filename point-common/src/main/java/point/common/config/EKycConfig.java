package point.common.config;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EKycConfig implements Serializable {

    @Getter
    @Setter
    @Value("${ekyc.token:#{null}}")
    private String token;

    @Getter
    @Setter
    @Value("${ekyc.secret:#{null}}")
    private String secret;

    @Getter
    @Setter
    @Value("${ekyc.url.applicant-id:#{null}}")
    private String applicantIdUrl;

    @Getter
    @Setter
    @Value("${ekyc.url.bpo-result:#{null}}")
    private String bpoResultUrl;

    @Getter
    @Setter
    @Value("${ekyc.url.ekyc-url:#{null}}")
    private String ekycUrl;

    @Getter
    @Setter
    @Value("${ekyc.transition-url:#{null}}")
    private String transitionUrl;

    @Getter
    @Setter
    @Value("${ekyc.enterprise-id:#{null}}")
    private String enterpriseId;

    @Getter
    @Setter
    @Value("${ekyc.api-auth-key:#{null}}")
    private String apiAuthKey;

    @Getter
    @Setter
    @Value("${ekyc.ekyc-url-valid-time:10800000}")
    private Long ekycUrlValidTime;

    @Getter
    @Setter
    @Value("${ekyc.url.sim-url:#{null}}")
    private String simUrl;
}
