package point.common.config;

import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("spring.jpa")
public class JpaPropertiesConfig {

    @Bean
    public JpaProperties jpaProperties() {
        return new JpaProperties();
    }
}
