package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.UserSummary;
import point.common.entity.UserSummary_;

@Component
public class UserSummaryPredicate extends EntityPredicate<UserSummary> {

    public Predicate greaterThanOrEqualToTargetAt(
            CriteriaBuilder criteriaBuilder, Root<UserSummary> root, Date targetAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(UserSummary_.targetAt), targetAt);
    }

    public Predicate lessThanTargetdAt(
            CriteriaBuilder criteriaBuilder, Root<UserSummary> root, Date targetAt) {
        return criteriaBuilder.lessThan(root.get(UserSummary_.targetAt), targetAt);
    }

    public Predicate equalUsers(
            CriteriaBuilder criteriaBuilder, Root<UserSummary> root, Long users) {
        return criteriaBuilder.equal(root.get(UserSummary_.users), users);
    }

    public Predicate equalUserKycDone(
            CriteriaBuilder criteriaBuilder, Root<UserSummary> root, Long userKycDone) {
        return criteriaBuilder.equal(root.get(UserSummary_.userKycDone), userKycDone);
    }

    public Predicate equalUserKycAccountOpeningDone(
            CriteriaBuilder criteriaBuilder,
            Root<UserSummary> root,
            Long userKycAccountOpeningDone) {
        return criteriaBuilder.equal(
                root.get(UserSummary_.userKycAccountOpeningDone), userKycAccountOpeningDone);
    }
}
