package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.ChoicePWithdrawal;
import point.common.entity.ChoiceRewardWithdrawal_;

@Component
public class ChoiceRewardWithdrawalPredicate extends EntityPredicate<ChoicePWithdrawal> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePWithdrawal> root, Long userId) {
        return criteriaBuilder.equal(root.get(ChoiceRewardWithdrawal_.userId), userId);
    }

    public Predicate equalActivityId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePWithdrawal> root, Long activityId) {
        return criteriaBuilder.equal(root.get(ChoiceRewardWithdrawal_.activityId), activityId);
    }
}
