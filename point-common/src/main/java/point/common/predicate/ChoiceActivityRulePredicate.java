package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.ChoiceActivityStatus;
import point.common.constant.ChoiceObtainFrequency;
import point.common.entity.ChoiceActivityRule;
import point.common.entity.ChoiceActivityRule_;

@Component
public class ChoiceActivityRulePredicate extends EntityPredicate<ChoiceActivityRule> {
    public Predicate likeActivityName(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, String activityName) {
        return criteriaBuilder.like(
                root.get(ChoiceActivityRule_.activityName), "%" + activityName + "%");
    }

    public Predicate equalStatus(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivityRule> root,
            ChoiceActivityStatus status) {
        return criteriaBuilder.equal(root.get(ChoiceActivityRule_.status), status);
    }

    public Predicate equalObtainFrequency(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivityRule> root,
            ChoiceObtainFrequency obtainFrequency) {
        return criteriaBuilder.equal(
                root.get(ChoiceActivityRule_.obtainFrequency), obtainFrequency);
    }

    public Predicate greaterThanOrEqualToPowerAmount(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, Long powerAmountFrom) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(ChoiceActivityRule_.powerAmount), powerAmountFrom);
    }

    public Predicate lessThanOrEqualToPowerAmount(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, Long powerAmountTo) {
        return criteriaBuilder.lessThanOrEqualTo(
                root.get(ChoiceActivityRule_.powerAmount), powerAmountTo);
    }

    public Predicate greaterThanOrEqualToStartTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, Date startTimeFrom) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(ChoiceActivityRule_.effectiveDate), startTimeFrom);
    }

    public Predicate lessThanOrEqualToStartTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, Date startTimeTo) {
        return criteriaBuilder.lessThanOrEqualTo(
                root.get(ChoiceActivityRule_.effectiveDate), startTimeTo);
    }

    public Predicate greaterThanOrEqualToEndTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, Date endTimeFrom) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(ChoiceActivityRule_.expiryDate), endTimeFrom);
    }

    public Predicate lessThanOrEqualToEndTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, Date endTimeTo) {
        return criteriaBuilder.lessThanOrEqualTo(
                root.get(ChoiceActivityRule_.expiryDate), endTimeTo);
    }

    public Predicate equalActivityName(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityRule> root, String activityName) {
        return criteriaBuilder.equal(root.get(ChoiceActivityRule_.activityName), activityName);
    }

    public static Predicate equalActivityFunction(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivityRule> root,
            ChoiceActivityFunction activityFunction) {
        return criteriaBuilder.equal(
                root.get(ChoiceActivityRule_.activityFunction), activityFunction);
    }
}
