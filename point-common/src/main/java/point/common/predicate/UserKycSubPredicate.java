package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.UserKycSub;
import point.common.entity.UserKycSub_;

@Component
public class UserKycSubPredicate extends EntityPredicate<UserKycSub> {

    public Predicate equalKycId(
            CriteriaBuilder criteriaBuilder, Root<UserKycSub> root, Long kycId) {
        return criteriaBuilder.equal(root.get(UserKycSub_.kycId), kycId);
    }
}
