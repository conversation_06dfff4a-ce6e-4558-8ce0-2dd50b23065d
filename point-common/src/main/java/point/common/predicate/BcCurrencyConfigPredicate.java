package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.BcCurrencyConfig;
import point.common.entity.BcCurrencyConfig_;

@Component
public class BcCurrencyConfigPredicate extends EntityPredicate<BcCurrencyConfig> {

    public Predicate equalActive(
            CriteriaBuilder criteriaBuilder, Root<BcCurrencyConfig> root, String active) {
        return criteriaBuilder.equal(root.get(BcCurrencyConfig_.active), active);
    }

    public Predicate equalCurrencyType(
            CriteriaBuilder criteriaBuilder, Root<BcCurrencyConfig> root, String currencyType) {
        return criteriaBuilder.equal(root.get(BcCurrencyConfig_.currencyType), currencyType);
    }
}
