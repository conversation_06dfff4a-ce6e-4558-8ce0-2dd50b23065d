package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.ChoiceActivityResultStatus;
import point.common.constant.ChoiceActivityType;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.entity.ChoiceActivity;
import point.common.entity.ChoiceActivity_;

@Component
public class ChoiceActivityPredicate extends EntityPredicate<ChoiceActivity> {

    public Predicate equalEndTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivity> root, Date date) {
        if (date == null) {
            return null;
        }
        return criteriaBuilder.equal(
                criteriaBuilder.function("DATE", Date.class, root.get(ChoiceActivity_.voteEndTime)),
                date);
    }

    public Predicate equalType(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivity> root,
            ChoiceActivityType activityType) {
        if (activityType == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceActivity_.ACTIVITY_TYPE), activityType);
    }

    public Predicate equalId(CriteriaBuilder criteriaBuilder, Root<ChoiceActivity> root, Long id) {
        if (id == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceActivity_.id), id);
    }

    public Predicate equalStatus(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivity> root,
            ChoiceActivityResultStatus status) {
        if (status == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceActivity_.status), status);
    }

    public Predicate betweenTotalVoteUsers(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivity> root,
            Long totalVoteUsersFrom,
            Long totalVoteUsersTo) {
        if (totalVoteUsersFrom == null && totalVoteUsersTo == null) {
            return null;
        }
        Expression<Long> totalVoteUsers =
                criteriaBuilder.sum(
                        root.get(ChoiceActivity_.totalVoteUsersUp),
                        root.get(ChoiceActivity_.totalVoteUsersDown));
        if (totalVoteUsersFrom != null && totalVoteUsersTo != null) {
            return criteriaBuilder.between(totalVoteUsers, totalVoteUsersFrom, totalVoteUsersTo);
        } else if (totalVoteUsersFrom != null) {
            return criteriaBuilder.greaterThanOrEqualTo(totalVoteUsers, totalVoteUsersFrom);
        } else {
            return criteriaBuilder.lessThanOrEqualTo(totalVoteUsers, totalVoteUsersTo);
        }
    }

    public Predicate betweenTotalVotePower(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivity> root,
            Long totalVotePowerFrom,
            Long totalVotePowerTo) {
        if (totalVotePowerFrom == null && totalVotePowerTo == null) {
            return null;
        }
        Expression<Long> totalVotePower =
                criteriaBuilder.sum(
                        root.get(ChoiceActivity_.totalVotePowerUp),
                        root.get(ChoiceActivity_.totalVotePowerDown));
        if (totalVotePowerFrom != null && totalVotePowerTo != null) {
            return criteriaBuilder.between(totalVotePower, totalVotePowerFrom, totalVotePowerTo);
        } else if (totalVotePowerFrom != null) {
            return criteriaBuilder.greaterThanOrEqualTo(totalVotePower, totalVotePowerFrom);
        } else {
            return criteriaBuilder.lessThanOrEqualTo(totalVotePower, totalVotePowerTo);
        }
    }

    public Predicate equalVoteResult(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivity> root,
            ChoiceActivityVoteResult voteResult) {
        if (voteResult == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceActivity_.voteResult), voteResult);
    }

    public Predicate betweenElectedUsers(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivity> root,
            Long electedUsersFrom,
            Long electedUsersTo,
            ChoiceActivityVoteResult voteResult) {
        if (electedUsersFrom == null && electedUsersTo == null) {
            return null;
        }
        Expression<Long> electedUsers;
        switch (voteResult) {
            case UP:
                electedUsers = root.get(ChoiceActivity_.totalVoteUsersUp);
                break;
            case DOWN:
                electedUsers = root.get(ChoiceActivity_.totalVoteUsersDown);
                break;
            case DRAW:
                electedUsers =
                        criteriaBuilder.sum(
                                root.get(ChoiceActivity_.totalVoteUsersUp),
                                root.get(ChoiceActivity_.totalVoteUsersDown));
                break;
            default:
                return null;
        }
        if (electedUsersFrom != null && electedUsersTo != null) {
            return criteriaBuilder.between(electedUsers, electedUsersFrom, electedUsersTo);
        } else if (electedUsersFrom != null) {
            return criteriaBuilder.greaterThanOrEqualTo(electedUsers, electedUsersFrom);
        } else {
            return criteriaBuilder.lessThanOrEqualTo(electedUsers, electedUsersTo);
        }
    }

    public Predicate equalVoteStartTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivity> root, Date date) {
        return criteriaBuilder.equal(root.get(ChoiceActivity_.voteStartTime), date);
    }
}
