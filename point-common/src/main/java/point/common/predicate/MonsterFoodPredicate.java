package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.UserIdType;
import point.common.entity.MonsterFood;
import point.common.entity.MonsterFood_;

@Component
public class MonsterFoodPredicate extends EntityPredicate<MonsterFood> {

    public Predicate equalType(
            CriteriaBuilder criteriaBuilder, Root<MonsterFood> root, UserIdType userIdType) {
        return criteriaBuilder.equal(root.get(MonsterFood_.TYPE), userIdType);
    }

    public Predicate equalYear(CriteriaBuilder criteriaBuilder, Root<MonsterFood> root, int year) {
        return criteriaBuilder.equal(root.get(MonsterFood_.YEAR), year);
    }

    public Predicate equalWeekOfYear(
            CriteriaBuilder criteriaBuilder, Root<MonsterFood> root, int weekOfYear) {
        return criteriaBuilder.equal(root.get(<PERSON>F<PERSON>_.WEEK), weekOfYear);
    }
}
