package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.UserIdType;
import point.common.entity.PointPartner_;
import point.common.entity.PointUser;
import point.common.entity.PointUser_;
import point.common.entity.UserIdentity_;

@Component
public class PointUserPredicate extends EntityPredicate<PointUser> {

    public Predicate equalName(CriteriaBuilder criteriaBuilder, Root<PointUser> root, String name) {
        return criteriaBuilder.equal(
                root.get(PointUser_.pointPartner).get(PointPartner_.name), name);
    }

    public Predicate equalPartnerNumber(
            CriteriaBuilder criteriaBuilder, Root<PointUser> root, String partnerNumber) {
        return criteriaBuilder.equal(
                root.get(PointUser_.pointPartner).get(PointPartner_.partnerNumber), partnerNumber);
    }

    public Predicate likePartnerMemberId(
            CriteriaBuilder criteriaBuilder, Root<PointUser> root, String partnerMemberId) {
        return criteriaBuilder.like(
                root.get(PointUser_.partnerMemberId), "%" + partnerMemberId + "%");
    }

    public Predicate equalPartnerMemberId(
            CriteriaBuilder criteriaBuilder, Root<PointUser> root, String partnerMemberId) {
        return criteriaBuilder.equal(root.get(PointUser_.partnerMemberId), partnerMemberId);
    }

    public Predicate equalUserIdType(
            CriteriaBuilder criteriaBuilder, Root<PointUser> root, UserIdType userIdType) {
        return criteriaBuilder.equal(
                root.get(PointUser_.userIdentity).get(UserIdentity_.idType), userIdType);
    }

    public Predicate equalPartnerId(
            CriteriaBuilder criteriaBuilder, Root<PointUser> root, Long partnerId) {
        return criteriaBuilder.equal(root.get(PointUser_.partnerId), partnerId);
    }

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<PointUser> root, Long userId) {
        return criteriaBuilder.equal(root.get(PointUser_.userId), userId);
    }
}
