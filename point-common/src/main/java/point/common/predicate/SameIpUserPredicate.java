package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.SameIpUser;
import point.common.entity.SameIpUser_;

@Component
public class SameIpUserPredicate extends EntityPredicate<SameIpUser> {

    public Predicate likeUserId(
            CriteriaBuilder criteriaBuilder, Root<SameIpUser> root, Long userId) {
        return criteriaBuilder.like(root.get(SameIpUser_.userIds), "%" + userId + "%");
    }
}
