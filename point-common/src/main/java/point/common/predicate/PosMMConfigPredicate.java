package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.PosMMConfig;
import point.common.entity.PosMMConfig_;

@Component
public class PosMMConfigPredicate extends EntityPredicate<PosMMConfig> {
    public Predicate equalSymbol(
            CriteriaBuilder criteriaBuilder, Root<PosMMConfig> root, Long symbolId) {
        return criteriaBuilder.equal(root.get(PosMMConfig_.SYMBOL), symbolId);
    }
}
