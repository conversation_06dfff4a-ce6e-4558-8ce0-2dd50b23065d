package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.Currency;
import point.common.constant.TradeType;
import point.common.entity.ExchangeSummary;
import point.common.entity.ExchangeSummary_;

@Component
public class ExchangeSummaryPredicate extends EntityPredicate<ExchangeSummary> {

    public Predicate equalCurrency(
            CriteriaBuilder criteriaBuilder, Root<ExchangeSummary> root, Currency currency) {
        return criteriaBuilder.equal(root.get(ExchangeSummary_.currency), currency);
    }

    public Predicate greaterThanOrEqualToTargetAt(
            CriteriaBuilder criteriaBuilder, Root<ExchangeSummary> root, Date targetAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(ExchangeSummary_.targetAt), targetAt);
    }

    public Predicate lessThanTargetAt(
            CriteriaBuilder criteriaBuilder, Root<ExchangeSummary> root, Date targetAt) {
        return criteriaBuilder.lessThan(root.get(ExchangeSummary_.targetAt), targetAt);
    }

    public Predicate equalTradeType(
            CriteriaBuilder criteriaBuilder, Root<ExchangeSummary> root, TradeType tradeType) {
        return criteriaBuilder.equal(root.get(ExchangeSummary_.tradeType), tradeType);
    }
}
