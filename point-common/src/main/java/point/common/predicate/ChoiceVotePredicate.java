package point.common.predicate;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.ChoiceVoteResult;
import point.common.entity.ChoiceVote;
import point.common.entity.ChoiceVote_;

@Component
public class ChoiceVotePredicate extends EntityPredicate<ChoiceVote> {

    public Predicate userIdsIn(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceVote> root,
            Long activityId,
            List<Long> userIds) {
        Predicate activityIdPredicate =
                criteriaBuilder.equal(root.get(ChoiceVote_.ACTIVITY_ID), activityId);
        Predicate userIdsInPredicate =
                criteriaBuilder.in(root.get(ChoiceVote_.USER_ID)).value(userIds);
        return criteriaBuilder.and(activityIdPredicate, userIdsInPredicate);
    }

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoiceVote> root, Long userId) {
        if (userId == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceVote_.USER_ID), userId);
    }

    public Predicate equalChoiceVoteResult(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceVote> root,
            ChoiceVoteResult choiceVoteResult) {
        if (choiceVoteResult == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceVote_.VOTE_RESULT), choiceVoteResult);
    }

    public Predicate equalWithdrawalAllowed(
            CriteriaBuilder criteriaBuilder, Root<ChoiceVote> root, Integer withdrawalAllowed) {
        if (withdrawalAllowed == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceVote_.WITHDRAW_ALALLOWED), withdrawalAllowed);
    }

    public Predicate equalActivityId(
            CriteriaBuilder criteriaBuilder, Root<ChoiceVote> root, Long activityId) {
        if (activityId == null) {
            return null;
        }
        return criteriaBuilder.equal(root.get(ChoiceVote_.ACTIVITY_ID), activityId);
    }

    public Predicate equalCreatedAtYYYYMMDD(
            CriteriaBuilder criteriaBuilder, Root<ChoiceVote> root, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        Expression<Integer> yearExpression =
                criteriaBuilder.function("YEAR", Integer.class, root.get(ChoiceVote_.createdAt));
        Expression<Integer> monthExpression =
                criteriaBuilder.function("MONTH", Integer.class, root.get(ChoiceVote_.createdAt));
        Expression<Integer> dayExpression =
                criteriaBuilder.function("DAY", Integer.class, root.get(ChoiceVote_.createdAt));

        Predicate yearPredicate = criteriaBuilder.equal(yearExpression, year);
        Predicate monthPredicate = criteriaBuilder.equal(monthExpression, month);
        Predicate dayPredicate = criteriaBuilder.equal(dayExpression, day);

        return criteriaBuilder.and(yearPredicate, monthPredicate, dayPredicate);
    }
}
