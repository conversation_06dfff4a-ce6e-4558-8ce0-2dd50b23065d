package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.Bank;
import point.common.entity.Bank_;

@Component
public class BankPredicate extends EntityPredicate<Bank> {

    public Predicate likeBankName(
            CriteriaBuilder criteriaBuilder, Root<Bank> root, String bankName) {
        return criteriaBuilder.like(root.get(Bank_.bankName), "%" + bankName + "%");
    }

    public Predicate likeBranchName(
            CriteriaBuilder criteriaBuilder, Root<Bank> root, String branchName) {
        return criteriaBuilder.like(root.get(Bank_.branchName), "%" + branchName + "%");
    }

    public Predicate equalBankCode(
            CriteriaBuilder criteriaBuilder, Root<Bank> root, Integer bankCode) {
        return criteriaBuilder.equal(root.get(Bank_.bankCode), bankCode);
    }

    public Predicate equalBranchCode(
            CriteriaBuilder criteriaBuilder, Root<Bank> root, Integer branchCode) {
        return criteriaBuilder.equal(root.get(Bank_.branchCode), branchCode);
    }
}
