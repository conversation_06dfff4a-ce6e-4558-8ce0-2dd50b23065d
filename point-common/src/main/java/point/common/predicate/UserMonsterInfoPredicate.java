package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.*;

@Component
public class UserMonsterInfoPredicate extends EntityPredicate<UserMonsterInfo> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserMonsterInfo_.userId), userId);
    }

    public Predicate equalIdType(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, String idType) {
        return criteriaBuilder.equal(root.get(UserMonsterInfo_.idType), idType);
    }

    public Predicate equalMonsterName(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, String monsterName) {
        return criteriaBuilder.equal(root.get(UserMonsterInfo_.monsterBase.getName()), monsterName);
    }

    public Predicate equalLevel(
            CriteriaBuilder criteriaBuilder, Root<UserMonsterInfo> root, Integer level) {
        return criteriaBuilder.equal(root.get(UserMonsterInfo_.level), level);
    }
}
