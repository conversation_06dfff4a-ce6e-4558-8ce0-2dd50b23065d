package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.NewsType;
import point.common.entity.AbstractEntity_;
import point.common.entity.News;
import point.common.entity.News_;

@Component
public class NewsPredicate extends EntityPredicate<News> {
    public Predicate isEnabled(CriteriaBuilder criteriaBuilder, Root<News> root, Boolean show) {
        return criteriaBuilder.equal(root.get(News_.enabled), show);
    }

    public Predicate equalNewsType(
            CriteriaBuilder criteriaBuilder, Root<News> root, NewsType newsType) {
        return criteriaBuilder.equal(root.get(News_.newsType), newsType);
    }

    public Predicate greaterThanOrEqualToCreatedAt(
            CriteriaBuilder criteriaBuilder, Root<News> root, Date createdAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(AbstractEntity_.createdAt), createdAt);
    }

    public Predicate lessThanCreatedAt(
            CriteriaBuilder criteriaBuilder, Root<News> root, Date createdAt) {
        return criteriaBuilder.lessThan(root.get(AbstractEntity_.createdAt), createdAt);
    }

    public Predicate greaterThanOrEqualToUpdatedAt(
            CriteriaBuilder criteriaBuilder, Root<News> root, Date updatedAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(AbstractEntity_.updatedAt), updatedAt);
    }

    public Predicate lessThanUpdatedAt(
            CriteriaBuilder criteriaBuilder, Root<News> root, Date updatedAt) {
        return criteriaBuilder.lessThan(root.get(AbstractEntity_.updatedAt), updatedAt);
    }
}
