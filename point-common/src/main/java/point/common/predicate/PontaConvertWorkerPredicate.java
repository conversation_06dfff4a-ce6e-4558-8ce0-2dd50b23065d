package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.PontaConvertWorker;
import point.common.entity.PontaConvertWorker_;

@Component
public class PontaConvertWorkerPredicate extends EntityPredicate<PontaConvertWorker> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<PontaConvertWorker> root, Long userId) {
        return criteriaBuilder.equal(root.get(PontaConvertWorker_.userId), userId);
    }

    public Predicate equalSyncFlag(
            CriteriaBuilder criteriaBuilder, Root<PontaConvertWorker> root, Integer flag) {
        return criteriaBuilder.equal(root.get(PontaConvertWorker_.flag), flag);
    }
}
