package point.common.predicate;

import java.util.Calendar;
import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.ChoiceObtainFrequency;
import point.common.entity.ChoicePowerSync;
import point.common.entity.ChoicePowerSync_;

@Component
public class ChoicePowerSyncPredicate extends EntityPredicate<ChoicePowerSync> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerSync> root, Long userId) {
        return criteriaBuilder.equal(root.get(ChoicePowerSync_.userId), userId);
    }

    public Predicate equalCreatedAtYYYYMMDD(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerSync> root, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        Expression<Integer> yearExpression =
                criteriaBuilder.function(
                        "YEAR", Integer.class, root.get(ChoicePowerSync_.createdAt));
        Expression<Integer> monthExpression =
                criteriaBuilder.function(
                        "MONTH", Integer.class, root.get(ChoicePowerSync_.createdAt));
        Expression<Integer> dayExpression =
                criteriaBuilder.function(
                        "DAY", Integer.class, root.get(ChoicePowerSync_.createdAt));

        Predicate yearPredicate = criteriaBuilder.equal(yearExpression, year);
        Predicate monthPredicate = criteriaBuilder.equal(monthExpression, month);
        Predicate dayPredicate = criteriaBuilder.equal(dayExpression, day);

        return criteriaBuilder.and(yearPredicate, monthPredicate, dayPredicate);
    }

    public Predicate equalSynchronizeFlag(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerSync> root, int i) {
        return criteriaBuilder.equal(root.get(ChoicePowerSync_.synchronizeFlag), i);
    }

    public Predicate equalObtainFrequency(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerSync> root,
            ChoiceObtainFrequency choiceObtainFrequency) {
        return criteriaBuilder.equal(
                root.get(ChoicePowerSync_.obtainFrequency), choiceObtainFrequency);
    }

    public Predicate notEqualObtainFrequency(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerSync> root,
            ChoiceObtainFrequency choiceObtainFrequency) {
        return criteriaBuilder.notEqual(
                root.get(ChoicePowerSync_.obtainFrequency), choiceObtainFrequency);
    }
}
