package point.common.predicate;

import java.util.Objects;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.HulftUploadStatusEnum;
import point.common.constant.PointTransferStatusEnum;
import point.common.constant.PointTransferTypeEnum;
import point.common.constant.UserIdType;
import point.common.entity.*;

@Component
public class PointTransferPredicate extends EntityPredicate<PointTransfer> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, Long userId) {
        return criteriaBuilder.equal(root.get(PointTransfer_.userId), userId);
    }

    public Predicate equalPartnerName(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransfer> root,
            String partnerName,
            UserIdType userIdType) {
        if (Objects.equals(userIdType, UserIdType.Invest)) {
            return equalPartnerNameForInvest(criteriaBuilder, root, partnerName);
        } else if (Objects.equals(userIdType, UserIdType.Operate)) {
            return equalPartnerNameForOperate(criteriaBuilder, root, partnerName);
        } else {
            return criteriaBuilder.or(
                    equalPartnerNameForInvest(criteriaBuilder, root, partnerName),
                    equalPartnerNameForOperate(criteriaBuilder, root, partnerName));
        }
    }

    public Predicate equalPartnerNameForInvest(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, String partnerName) {
        Join<PointTransfer, User> userJoin =
                root.join(PointTransfer_.investUser, JoinType.INNER); // PointTransfer → User
        Join<User, PointUser> pointUserJoin =
                userJoin.join(User_.pointUser, JoinType.INNER); // User → PointUser
        Join<PointUser, PointPartner> partnerJoin =
                pointUserJoin.join(
                        PointUser_.pointPartner, JoinType.INNER); // PointUser → PointPartner
        return criteriaBuilder.equal(partnerJoin.get(PointPartner_.NAME), partnerName);
    }

    public Predicate equalPartnerNameForOperate(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, String partnerName) {
        return criteriaBuilder.equal(
                root.get(PointTransfer_.pointPartner).get(PointPartner_.NAME), partnerName);
    }

    public Predicate equalPartnerMemberId(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransfer> root,
            String partnerMemberId,
            UserIdType userIdType) {
        if (Objects.equals(userIdType, UserIdType.Invest)) {
            return equalPartnerMemberIdForInvest(criteriaBuilder, root, partnerMemberId);
        } else if (Objects.equals(userIdType, UserIdType.Operate)) {
            return equalPartnerMemberIdForOperate(criteriaBuilder, root, partnerMemberId);
        } else {
            return criteriaBuilder.or(
                    equalPartnerMemberIdForInvest(criteriaBuilder, root, partnerMemberId),
                    equalPartnerMemberIdForOperate(criteriaBuilder, root, partnerMemberId));
        }
    }

    public Predicate equalPartnerMemberIdForInvest(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, String partnerMemberId) {
        Join<PointTransfer, User> userJoin =
                root.join(PointTransfer_.investUser, JoinType.INNER); // PointTransfer → User
        Join<User, PointUser> pointUserJoin =
                userJoin.join(User_.pointUser, JoinType.INNER); // User → PointUser
        return criteriaBuilder.equal(
                pointUserJoin.get(PointUser_.PARTNER_MEMBER_ID), partnerMemberId);
    }

    public Predicate equalPartnerMemberIdForOperate(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, String partnerMemberId) {
        return criteriaBuilder.equal(
                root.get(PointTransfer_.pointUser).get(PointUser_.PARTNER_MEMBER_ID),
                partnerMemberId);
    }

    public Predicate equalPartnerId(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransfer> root,
            Long partnerId,
            UserIdType userIdType) {
        if (Objects.equals(userIdType, UserIdType.Invest)) {
            return equalPartnerIdForInvest(criteriaBuilder, root, partnerId);
        } else if (Objects.equals(userIdType, UserIdType.Operate)) {
            return equalPartnerIdForOperate(criteriaBuilder, root, partnerId);
        } else {
            return criteriaBuilder.or(
                    equalPartnerIdForInvest(criteriaBuilder, root, partnerId),
                    equalPartnerIdForOperate(criteriaBuilder, root, partnerId));
        }
    }

    public Predicate equalPartnerIdForInvest(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, Long partnerId) {
        Join<PointTransfer, User> userJoin =
                root.join(PointTransfer_.investUser, JoinType.INNER); // PointTransfer → User
        Join<User, PointUser> pointUserJoin =
                userJoin.join(User_.pointUser, JoinType.INNER); // User → PointUser
        Join<PointUser, PointPartner> partnerJoin =
                pointUserJoin.join(
                        PointUser_.pointPartner, JoinType.INNER); // PointUser → PointPartner
        return criteriaBuilder.equal(partnerJoin.get(PointPartner_.id), partnerId);
    }

    public Predicate equalPartnerIdForOperate(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, Long partnerId) {
        return criteriaBuilder.equal(root.get(PointTransfer_.partnerId), partnerId);
    }

    public Predicate equalStatus(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransfer> root,
            PointTransferStatusEnum status) {
        return criteriaBuilder.equal(root.get(PointTransfer_.status), status);
    }

    public Predicate betweenDate(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, Long dateFrom, Long dateTo) {
        return criteriaBuilder.between(root.get(PointTransfer_.CREATED_AT), dateFrom, dateTo);
    }

    public Predicate equalUserIdType(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, UserIdType userIdType) {
        return criteriaBuilder.equal(root.get(PointTransfer_.userIdType), userIdType);
    }

    public Predicate equalTransferType(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransfer> root,
            PointTransferTypeEnum transferType) {
        return criteriaBuilder.equal(root.get(PointTransfer_.transferType), transferType);
    }

    public Predicate equalTradeNumber(
            CriteriaBuilder criteriaBuilder, Root<PointTransfer> root, String tradeNumber) {
        return criteriaBuilder.equal(root.get(PointTransfer_.tradeNumber), tradeNumber);
    }

    public Predicate equalUploadStatus(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransfer> root,
            HulftUploadStatusEnum uploadStatus) {
        return criteriaBuilder.equal(root.get(PointTransfer_.uploadStatus), uploadStatus);
    }
}
