package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.constant.TradeAction;
import point.common.entity.Trade;
import point.common.entity.Trade_;

public abstract class TradePredicate<E extends Trade> extends EntityPredicate<E> {

    public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<E> root, Long userId) {
        return criteriaBuilder.equal(root.get(Trade_.userId), userId);
    }

    public Predicate equalSymbolId(CriteriaBuilder criteriaBuilder, Root<E> root, Long symbolId) {
        return criteriaBuilder.equal(root.get(Trade_.symbolId), symbolId);
    }

    public Predicate equalTradeAction(
            CriteriaBuilder criteriaBuilder, Root<E> root, TradeAction tradeAction) {
        return criteriaBuilder.equal(root.get(Trade_.tradeAction), tradeAction);
    }

    public Predicate equalOrderSide(
            CriteriaBuilder criteriaBuilder, Root<E> root, OrderSide orderSide) {
        return criteriaBuilder.equal(root.get(Trade_.orderSide), orderSide);
    }

    public Predicate equalOrderType(
            CriteriaBuilder criteriaBuilder, Root<E> root, OrderType orderType) {
        return criteriaBuilder.equal(root.get(Trade_.orderType), orderType);
    }
}
