package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.HighValueTrader;
import point.common.entity.HighValueTrader_;

@Component
public class HighValueTraderPredicate extends EntityPredicate<HighValueTrader> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<HighValueTrader> root, Long userId) {
        return criteriaBuilder.equal(root.get(HighValueTrader_.userId), userId);
    }
}
