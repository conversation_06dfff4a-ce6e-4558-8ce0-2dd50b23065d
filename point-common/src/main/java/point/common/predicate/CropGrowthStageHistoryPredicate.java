package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.TradeType;
import point.common.entity.CropGrowthStageHistory;
import point.common.entity.CropGrowthStageHistory_;

@Component
public class CropGrowthStageHistoryPredicate extends EntityPredicate<CropGrowthStageHistory> {

    public Predicate equalTradeType(
            CriteriaBuilder criteriaBuilder,
            Root<CropGrowthStageHistory> root,
            TradeType tradeType) {
        return criteriaBuilder.equal(root.get(CropGrowthStageHistory_.TRADE_TYPE), tradeType);
    }
}
