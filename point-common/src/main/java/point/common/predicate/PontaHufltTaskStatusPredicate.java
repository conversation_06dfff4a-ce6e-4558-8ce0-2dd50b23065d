package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.PontaHulftTaskStatus;

@Component
public class PontaHufltTaskStatusPredicate extends EntityPredicate<PontaHulftTaskStatus> {

    public Predicate equalsStatus(
            CriteriaBuilder criteriaBuilder,
            Root<PontaHulftTaskStatus> root,
            PontaHulftTaskStatus.ParsingStatus parsingStatus) {
        return criteriaBuilder.equal(root.get("parsingStatus"), parsingStatus);
    }
}
