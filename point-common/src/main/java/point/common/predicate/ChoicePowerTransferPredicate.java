package point.common.predicate;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.*;
import org.springframework.stereotype.Component;
import point.common.constant.ChoicePowerTransferType;
import point.common.constant.UserIdType;
import point.common.entity.*;

@Component
public class ChoicePowerTransferPredicate extends EntityPredicate<ChoicePowerTransfer> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerTransfer> root, Long userId) {
        return criteriaBuilder.equal(root.get(ChoicePowerTransfer_.userId), userId);
    }

    public Predicate equalIdType(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerTransfer> root, UserIdType idType) {
        Join<ChoicePowerTransfer, UserIdentity> userIdentityJoin = root.join("userIdentity");
        return criteriaBuilder.equal(userIdentityJoin.get(UserIdentity_.idType), idType);
    }

    public Predicate equalTransferType(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerTransfer> root,
            ChoicePowerTransferType transferType) {
        return criteriaBuilder.equal(root.get(ChoicePowerTransfer_.transferType), transferType);
    }

    public Predicate equalChoiceActivityRuleId(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerTransfer> root,
            Long choiceActivityRuleId) {
        return criteriaBuilder.equal(
                root.get(ChoicePowerTransfer_.choiceActivityRuleId), choiceActivityRuleId);
    }

    public Predicate greaterThanOrEqualToCreatedAt(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerTransfer> root, Date date) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(ChoicePowerTransfer_.createdAt), date);
    }

    public Predicate lessThanCreatedAt(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerTransfer> root, Date date) {
        return criteriaBuilder.lessThan(root.get(ChoicePowerTransfer_.createdAt), date);
    }

    public Predicate equalPartnerMemberId(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerTransfer> root,
            String partnerMemberId) {
        Join<ChoicePowerTransfer, PointUser> operatorPointUserJoin =
                root.join(ChoicePowerUserRel_.OPERATOR_POINT_USER, JoinType.LEFT);
        Join<ChoicePowerTransfer, PointUser> investPointUserJoin =
                root.join(ChoicePowerUserRel_.INVEST_POINT_USER, JoinType.LEFT);
        Predicate operatorPredicate =
                criteriaBuilder.equal(
                        operatorPointUserJoin.get(PointUser_.PARTNER_MEMBER_ID), partnerMemberId);
        Predicate investPredicate =
                criteriaBuilder.equal(
                        investPointUserJoin.get(PointUser_.PARTNER_MEMBER_ID), partnerMemberId);
        return criteriaBuilder.or(operatorPredicate, investPredicate);
    }

    public Predicate inUserIds(CriteriaBuilder cb, Root<ChoicePowerTransfer> root, List<Long> ids) {
        // Example: this will create a join query
        // left join user t1 on t1.id = choice_power_transfer.user_id left join point_user t2 on
        // t2.id =
        // t1.user_id and t2.user_id = choice_power_transfer.user_id
        //    if(UserIdType.Invest.equals(userIdType)) {
        //      Join<ChoicePowerTransfer, User> investUser =
        // root.join(ChoicePowerTransfer_.investPointUser, JoinType.LEFT);
        //      Join<ChoicePowerTransfer, PointUser> pointUser =
        // root.join(ChoicePowerTransfer_.operatorPointUser, JoinType.LEFT);
        //      pointUser.on(cb.equal(pointUser.get(PointUser_.userId),
        // root.get(ChoicePowerTransfer_.userId)));
        //    }
        return cb.in(root.get(ChoicePowerTransfer_.USER_ID)).value(ids);
    }

    public Predicate equalCreatedAtYYYYMMDD(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerTransfer> root, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        Expression<Integer> yearExpression =
                criteriaBuilder.function(
                        "YEAR", Integer.class, root.get(ChoicePowerTransfer_.createdAt));
        Expression<Integer> monthExpression =
                criteriaBuilder.function(
                        "MONTH", Integer.class, root.get(ChoicePowerTransfer_.createdAt));
        Expression<Integer> dayExpression =
                criteriaBuilder.function(
                        "DAY", Integer.class, root.get(ChoicePowerTransfer_.createdAt));

        Predicate yearPredicate = criteriaBuilder.equal(yearExpression, year);
        Predicate monthPredicate = criteriaBuilder.equal(monthExpression, month);
        Predicate dayPredicate = criteriaBuilder.equal(dayExpression, day);

        return criteriaBuilder.and(yearPredicate, monthPredicate, dayPredicate);
    }

    public Predicate equalDescription(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerTransfer> root, String description) {
        return criteriaBuilder.equal(root.get(ChoicePowerTransfer_.description), description);
    }

    public Predicate likePartnerMemberId(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerTransfer> root,
            String partnerMemberId) {
        return criteriaBuilder.like(
                root.get(ChoicePowerTransfer_.OPERATOR_POINT_USER)
                        .get(PointUser_.PARTNER_MEMBER_ID),
                "%" + partnerMemberId + "%");
    }
}
