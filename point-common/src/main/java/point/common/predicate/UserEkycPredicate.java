package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.UserEkyc;
import point.common.entity.UserEkyc_;

@Component
public class UserEkycPredicate extends EntityPredicate<UserEkyc> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserEkyc> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserEkyc_.userId), userId);
    }
}
