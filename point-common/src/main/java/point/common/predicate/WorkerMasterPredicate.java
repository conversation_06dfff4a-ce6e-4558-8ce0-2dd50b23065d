package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.CurrencyPair;
import point.common.constant.Environment;
import point.common.constant.TradeType;
import point.common.entity.WorkerMaster;
import point.common.entity.WorkerMaster_;

@Component
public class WorkerMasterPredicate extends EntityPredicate<WorkerMaster> {

    public Predicate equalEnvironment(
            CriteriaBuilder criteriaBuilder, Root<WorkerMaster> root, Environment environment) {
        return criteriaBuilder.equal(root.get(WorkerMaster_.environment), environment);
    }

    public Predicate equalBeanName(
            CriteriaBuilder criteriaBuilder, Root<WorkerMaster> root, String beanName) {
        return criteriaBuilder.equal(root.get(WorkerMaster_.beanName), beanName);
    }

    public Predicate equalTradeType(
            CriteriaBuilder criteriaBuilder, Root<WorkerMaster> root, TradeType tradeType) {
        return criteriaBuilder.equal(root.get(WorkerMaster_.tradeType), tradeType);
    }

    public Predicate isNullOfTradeType(Root<WorkerMaster> root) {
        return root.get(WorkerMaster_.tradeType).isNull();
    }

    public Predicate equalCurrencyPair(
            CriteriaBuilder criteriaBuilder, Root<WorkerMaster> root, CurrencyPair currencyPair) {
        return criteriaBuilder.equal(root.get(WorkerMaster_.currencyPair), currencyPair);
    }

    public Predicate isNullOfCurrencyPair(Root<WorkerMaster> root) {
        return root.get(WorkerMaster_.currencyPair).isNull();
    }

    public Predicate isEnabled(
            CriteriaBuilder criteriaBuilder, Root<WorkerMaster> root, boolean enabled) {
        return enabled
                ? criteriaBuilder.isTrue(root.get(WorkerMaster_.enabled))
                : criteriaBuilder.isFalse(root.get(WorkerMaster_.enabled));
    }
}
