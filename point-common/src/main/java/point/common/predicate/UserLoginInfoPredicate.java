package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.UserLoginInfo;
import point.common.entity.UserLoginInfo_;

@Component
public class UserLoginInfoPredicate extends EntityPredicate<UserLoginInfo> {
    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserLoginInfo> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserLoginInfo_.userId), userId);
    }

    public Predicate equalIpAddress(
            CriteriaBuilder criteriaBuilder, Root<UserLoginInfo> root, String ipAddress) {
        return criteriaBuilder.equal(root.get(UserLoginInfo_.ipAddress), ipAddress);
    }
}
