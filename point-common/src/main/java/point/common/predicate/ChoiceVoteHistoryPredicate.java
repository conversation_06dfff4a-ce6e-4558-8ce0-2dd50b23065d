package point.common.predicate;

import java.util.List;
import java.util.Optional;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.ChoiceVoteHistory;
import point.common.entity.ChoiceVoteHistory_;

@Component
public class ChoiceVoteHistoryPredicate extends EntityPredicate<ChoiceVoteHistory> {

    public Predicate userIdsIn(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceVoteHistory> root,
            Long activityId,
            List<Long> userIds) {
        Predicate activityIdPredicate =
                criteriaBuilder.equal(root.get(ChoiceVoteHistory_.ACTIVITY_ID), activityId);
        Predicate userIdsInPredicate =
                criteriaBuilder.in(root.get(ChoiceVoteHistory_.USER_ID)).value(userIds);
        return criteriaBuilder.and(activityIdPredicate, userIdsInPredicate);
    }

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoiceVoteHistory> root, Long userId) {

        return Optional.ofNullable(userId)
                .map(id -> criteriaBuilder.equal(root.get(ChoiceVoteHistory_.USER_ID), userId))
                .orElse(null);
    }

    public Predicate equalVoteId(
            CriteriaBuilder criteriaBuilder, Root<ChoiceVoteHistory> root, Long voteId) {
        return Optional.ofNullable(voteId)
                .map(id -> criteriaBuilder.equal(root.get(ChoiceVoteHistory_.VOTE_ID), id))
                .orElse(null);
    }

    public Predicate equalActivityId(
            CriteriaBuilder criteriaBuilder, Root<ChoiceVoteHistory> root, Long activityId) {
        return Optional.ofNullable(activityId)
                .map(
                        id ->
                                criteriaBuilder.equal(
                                        root.get(ChoiceVoteHistory_.ACTIVITY_ID), activityId))
                .orElse(null);
    }
}
