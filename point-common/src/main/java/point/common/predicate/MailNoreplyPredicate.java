package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.MailNoreplyType;
import point.common.entity.MailNoreply;
import point.common.entity.MailNoreply_;

@Component
public class MailNoreplyPredicate extends EntityPredicate<MailNoreply> {

    public Predicate equalMailNoreplyType(
            CriteriaBuilder criteriaBuilder,
            Root<MailNoreply> root,
            MailNoreplyType mailNoreplyType) {
        return criteriaBuilder.equal(root.get(MailNoreply_.mailNoreplyType), mailNoreplyType);
    }
}
