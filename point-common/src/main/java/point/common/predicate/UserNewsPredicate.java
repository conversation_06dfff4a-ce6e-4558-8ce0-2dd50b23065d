package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.UserNews;
import point.common.entity.UserNews_;

@Component
public class UserNewsPredicate extends EntityPredicate<UserNews> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserNews> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserNews_.userId), userId);
    }
}
