package point.common.predicate;

import javax.persistence.criteria.*;
import org.springframework.stereotype.Component;
import point.common.constant.UserIdType;
import point.common.entity.*;

@Component
public class ChoicePowerPredicate extends EntityPredicate<ChoicePower> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePower> root, Long userId) {
        Join<ChoicePower, ChoicePowerUserRel> userRelJoin =
                root.join(ChoicePower_.CHOICE_POWER_USER_RELS, JoinType.INNER);
        Predicate userIdPredicate =
                criteriaBuilder.equal(userRelJoin.get(ChoicePowerUserRel_.USER_ID), userId);
        Predicate choicePowerIdPredicate =
                criteriaBuilder.equal(
                        userRelJoin.get(ChoicePowerUserRel_.CHOICE_POWER_ID),
                        root.get(ChoicePower_.ID));
        return criteriaBuilder.and(userIdPredicate, choicePowerIdPredicate);
    }

    public Predicate equalPartnerMemberId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePower> root, String partnerMemberId) {
        Join<ChoicePower, ChoicePowerUserRel> userRelJoin =
                root.join(ChoicePower_.CHOICE_POWER_USER_RELS, JoinType.LEFT);
        Join<ChoicePowerUserRel, PointUser> operatorPointUserJoin =
                userRelJoin.join(ChoicePowerUserRel_.OPERATOR_POINT_USER, JoinType.LEFT);
        Join<ChoicePowerUserRel, PointUser> investPointUserJoin =
                userRelJoin.join(ChoicePowerUserRel_.INVEST_POINT_USER, JoinType.LEFT);
        Predicate operatorPredicate =
                criteriaBuilder.equal(
                        operatorPointUserJoin.get(PointUser_.PARTNER_MEMBER_ID), partnerMemberId);
        Predicate investPredicate =
                criteriaBuilder.equal(
                        investPointUserJoin.get(PointUser_.PARTNER_MEMBER_ID), partnerMemberId);
        return criteriaBuilder.or(operatorPredicate, investPredicate);
    }

    public Predicate equalIdType(
            CriteriaBuilder criteriaBuilder, Root<ChoicePower> root, UserIdType idType) {
        Join<ChoicePower, ChoicePowerUserRel> userRelJoin =
                root.join(ChoicePower_.choicePowerUserRels, JoinType.LEFT);
        Join<ChoicePowerUserRel, UserIdentity> userIdentityJoin =
                userRelJoin.join(ChoicePowerUserRel_.userIdentity, JoinType.LEFT);
        return criteriaBuilder.equal(userIdentityJoin.get(UserIdentity_.idType), idType);
    }

    public Predicate betweenPowerAmount(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePower> root,
            Integer powerAmountFrom,
            Integer powerAmountTo) {
        if (powerAmountFrom != null && powerAmountTo != null) {
            return criteriaBuilder.between(
                    root.get(ChoicePower_.AMOUNT), powerAmountFrom, powerAmountTo);
        } else if (powerAmountFrom != null) {
            return criteriaBuilder.greaterThanOrEqualTo(
                    root.get(ChoicePower_.AMOUNT), powerAmountFrom);
        } else if (powerAmountTo != null) {
            return criteriaBuilder.lessThanOrEqualTo(root.get(ChoicePower_.AMOUNT), powerAmountTo);
        }
        return null;
    }
}
