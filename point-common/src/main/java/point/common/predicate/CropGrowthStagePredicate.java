package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.TradeType;
import point.common.entity.CropGrowthStage;
import point.common.entity.CropGrowthStageHistory_;

@Component
public class CropGrowthStagePredicate extends EntityPredicate<CropGrowthStage> {

    public Predicate equalTradeType(
            CriteriaBuilder criteriaBuilder, Root<CropGrowthStage> root, TradeType tradeType) {
        return criteriaBuilder.equal(root.get(CropGrowthStageHistory_.TRADE_TYPE), tradeType);
    }
}
