package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.YearlyReportCreateInfo;
import point.common.entity.YearlyReportCreateInfo_;

@Component
public class YearlyReportCreateInfoPredicate extends EntityPredicate<YearlyReportCreateInfo> {

    public Predicate equalYear(
            CriteriaBuilder criteriaBuilder, Root<YearlyReportCreateInfo> root, Integer year) {
        return criteriaBuilder.equal(root.get(YearlyReportCreateInfo_.year), year);
    }

    public Predicate equalStatus(
            CriteriaBuilder criteriaBuilder, Root<YearlyReportCreateInfo> root, String status) {
        return criteriaBuilder.equal(root.get(YearlyReportCreateInfo_.status), status);
    }

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<YearlyReportCreateInfo> root, Long userId) {
        return criteriaBuilder.equal(root.get(YearlyReportCreateInfo_.userId), userId);
    }
}
