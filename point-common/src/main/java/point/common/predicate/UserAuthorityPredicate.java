package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.Authority;
import point.common.entity.UserAuthority;
import point.common.entity.UserAuthority_;

@Component
public class UserAuthorityPredicate extends EntityPredicate<UserAuthority> {
    public Predicate equalAuthority(
            CriteriaBuilder criteriaBuilder, Root<UserAuthority> root, Authority authority) {
        return criteriaBuilder.equal(root.get(UserAuthority_.authority), authority);
    }

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserAuthority> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserAuthority_.userId), userId);
    }
}
