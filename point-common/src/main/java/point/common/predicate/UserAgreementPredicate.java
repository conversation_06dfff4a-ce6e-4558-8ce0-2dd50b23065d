package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.UserAgreementType;
import point.common.entity.UserAgreement;
import point.common.entity.UserAgreement_;

@Component
public class UserAgreementPredicate extends EntityPredicate<UserAgreement> {
    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserAgreement> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserAgreement_.userId), userId);
    }

    public Predicate equalUserAgreementType(
            CriteriaBuilder criteriaBuilder,
            Root<UserAgreement> root,
            UserAgreementType userAgreementType) {
        return criteriaBuilder.equal(root.get(UserAgreement_.userAgreementType), userAgreementType);
    }

    public Predicate equalVersion(
            CriteriaBuilder criteriaBuilder, Root<UserAgreement> root, String version) {
        return criteriaBuilder.equal(root.get(UserAgreement_.version), version);
    }
}
