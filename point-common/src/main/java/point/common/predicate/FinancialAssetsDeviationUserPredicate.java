package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.FinancialAssetsDeviationUser;
import point.common.entity.FinancialAssetsDeviationUser_;

@Component
public class FinancialAssetsDeviationUserPredicate
        extends EntityPredicate<FinancialAssetsDeviationUser> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<FinancialAssetsDeviationUser> root, Long userId) {
        return criteriaBuilder.equal(root.get(FinancialAssetsDeviationUser_.userId), userId);
    }
}
