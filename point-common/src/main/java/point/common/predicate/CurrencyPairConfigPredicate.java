package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.CurrencyPairConfig_;

@Component
public class CurrencyPairConfigPredicate extends EntityPredicate<CurrencyPairConfig> {

    public Predicate equalTradeType(
            CriteriaBuilder criteriaBuilder, Root<CurrencyPairConfig> root, TradeType tradeType) {
        return criteriaBuilder.equal(root.get(CurrencyPairConfig_.tradeType), tradeType);
    }

    public Predicate equalCurrencyPair(
            CriteriaBuilder criteriaBuilder,
            Root<CurrencyPairConfig> root,
            CurrencyPair currencyPair) {
        return criteriaBuilder.equal(root.get(CurrencyPairConfig_.currencyPair), currencyPair);
    }

    public Predicate isEnabled(
            CriteriaBuilder criteriaBuilder, Root<CurrencyPairConfig> root, boolean enabled) {
        return enabled
                ? criteriaBuilder.isTrue(root.get(CurrencyPairConfig_.enabled))
                : criteriaBuilder.isFalse(root.get(CurrencyPairConfig_.enabled));
    }
}
