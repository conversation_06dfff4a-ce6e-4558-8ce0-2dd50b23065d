package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.PointUserPartnerCredential;

@Component
public class PointUserPartnerCredentialPredicate
        extends EntityPredicate<PointUserPartnerCredential> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<PointUserPartnerCredential> root, Long userId) {
        return criteriaBuilder.equal(root.get("userId"), userId);
    }
}
