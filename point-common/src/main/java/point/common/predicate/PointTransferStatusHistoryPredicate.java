package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.PointTransferStatusHistory;
import point.common.entity.PointTransferStatusHistory_;

@Component
public class PointTransferStatusHistoryPredicate
        extends EntityPredicate<PointTransferStatusHistory> {

    public Predicate equalTransferId(
            CriteriaBuilder criteriaBuilder,
            Root<PointTransferStatusHistory> root,
            Long transferId) {
        return criteriaBuilder.equal(root.get(PointTransferStatusHistory_.transfer), transferId);
    }
}
