package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;
import point.common.entity.SystemConfig;
import point.common.entity.SystemConfig_;

@Component
public class SystemConfigPredicate extends EntityPredicate<SystemConfig> {

    public Predicate equalCurrency(
            CriteriaBuilder criteriaBuilder, Root<SystemConfig> root, Currency currency) {
        return criteriaBuilder.equal(root.get(SystemConfig_.currency), currency);
    }

    public Predicate equalCurrencyPair(
            CriteriaBuilder criteriaBuilder, Root<SystemConfig> root, CurrencyPair currencyPair) {
        return criteriaBuilder.equal(root.get(SystemConfig_.currencyPair), currencyPair);
    }

    public Predicate equalName(
            CriteriaBuilder criteriaBuilder, Root<SystemConfig> root, String name) {
        return criteriaBuilder.equal(root.get(SystemConfig_.name), name);
    }
}
