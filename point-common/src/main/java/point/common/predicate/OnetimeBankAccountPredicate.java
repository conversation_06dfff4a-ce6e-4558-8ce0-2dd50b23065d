package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.OnetimeBankAccount;
import point.common.entity.OnetimeBankAccount_;

@Component
public class OnetimeBankAccountPredicate extends EntityPredicate<OnetimeBankAccount> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<OnetimeBankAccount> root, Long userId) {
        return criteriaBuilder.equal(root.get(OnetimeBankAccount_.userId), userId);
    }

    public Predicate nullOfUserId(CriteriaBuilder criteriaBuilder, Root<OnetimeBankAccount> root) {
        return criteriaBuilder.isNull(root.get(OnetimeBankAccount_.userId));
    }

    public Predicate likeBranchName(
            CriteriaBuilder criteriaBuilder, Root<OnetimeBankAccount> root, String branchName) {
        return criteriaBuilder.like(
                root.get(OnetimeBankAccount_.branchName), "%" + branchName + "%");
    }

    public Predicate equalAccountNumber(
            CriteriaBuilder criteriaBuilder, Root<OnetimeBankAccount> root, Long accountNumber) {
        return criteriaBuilder.equal(root.get(OnetimeBankAccount_.accountNumber), accountNumber);
    }

    public Predicate equalVaId(
            CriteriaBuilder criteriaBuilder, Root<OnetimeBankAccount> root, String vaId) {
        return criteriaBuilder.equal(root.get(OnetimeBankAccount_.vaId), vaId);
    }
}
