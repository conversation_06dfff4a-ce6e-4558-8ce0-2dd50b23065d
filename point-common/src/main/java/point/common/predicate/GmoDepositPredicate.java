package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.GmoDeposit;
import point.common.entity.GmoDeposit_;

@Component
public class GmoDepositPredicate extends EntityPredicate<GmoDeposit> {

    public Predicate equalFiatDepositId(
            CriteriaBuilder criteriaBuilder, Root<GmoDeposit> root, Long fiatDepositId) {
        return criteriaBuilder.equal(root.get(GmoDeposit_.fiatDepositId), fiatDepositId);
    }

    public Predicate isNullOfFiatDepositId(CriteriaBuilder criteriaBuilder, Root<GmoDeposit> root) {
        return criteriaBuilder.isNull(root.get(GmoDeposit_.fiatDepositId));
    }

    public Predicate equalItemKey(
            CriteriaBuilder criteriaBuilder, Root<GmoDeposit> root, String itemKey) {
        return criteriaBuilder.equal(root.get(GmoDeposit_.itemKey), itemKey);
    }
}
