package point.common.predicate;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.FiatWithdrawalStatus;
import point.common.entity.FiatWithdrawal;
import point.common.entity.FiatWithdrawal_;

@Component
public class FiatWithdrawalPredicate extends EntityPredicate<FiatWithdrawal> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<FiatWithdrawal> root, Long userId) {
        return criteriaBuilder.equal(root.get(FiatWithdrawal_.userId), userId);
    }

    public Predicate equalFiatWithdrawalStatus(
            CriteriaBuilder criteriaBuilder,
            Root<FiatWithdrawal> root,
            FiatWithdrawalStatus fiatWithdrawalStatus) {
        return criteriaBuilder.equal(
                root.get(FiatWithdrawal_.fiatWithdrawalStatus), fiatWithdrawalStatus);
    }

    public Predicate inFiatWithdrawalStatus(
            CriteriaBuilder criteriaBuilder,
            Root<FiatWithdrawal> root,
            List<FiatWithdrawalStatus> fiatWithdrawalStatusList) {
        return root.get(FiatWithdrawal_.fiatWithdrawalStatus).in(fiatWithdrawalStatusList);
    }

    public Predicate isNotNullApplyNo(CriteriaBuilder criteriaBuilder, Root<FiatWithdrawal> root) {
        return root.get(FiatWithdrawal_.applyNo).isNotNull();
    }

    public Predicate isNullApplyNo(CriteriaBuilder criteriaBuilder, Root<FiatWithdrawal> root) {
        return root.get(FiatWithdrawal_.applyNo).isNull();
    }

    public Predicate equalApplyNo(
            CriteriaBuilder criteriaBuilder, Root<FiatWithdrawal> root, String applyNo) {
        return criteriaBuilder.equal(root.get(FiatWithdrawal_.applyNo), applyNo);
    }

    public Predicate equalItemId(
            CriteriaBuilder criteriaBuilder, Root<FiatWithdrawal> root, String itemId) {
        return criteriaBuilder.equal(root.get(FiatWithdrawal_.itemId), itemId);
    }
}
