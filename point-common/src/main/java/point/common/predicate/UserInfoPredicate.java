package point.common.predicate;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.UserInfo;
import point.common.entity.UserInfo_;

@Component
public class UserInfoPredicate extends EntityPredicate<UserInfo> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserInfo> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserInfo_.userId), userId);
    }

    public Predicate equalPhoneNumber(
            CriteriaBuilder criteriaBuilder, Root<UserInfo> root, String phoneNumber) {
        return criteriaBuilder.equal(root.get(UserInfo_.phoneNumber), phoneNumber);
    }

    public Predicate inUserIds(
            CriteriaBuilder criteriaBuilder, Root<UserInfo> root, List<Long> userIds) {
        return root.get(UserInfo_.userId).in(userIds);
    }

    public Predicate greaterThanOrEqualToFinancialAssets(
            CriteriaBuilder criteriaBuilder, Root<UserInfo> root, int financialAssetsCode) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(UserInfo_.financialAssets), financialAssetsCode);
    }

    public Predicate lessThanFinancialAssets(
            CriteriaBuilder criteriaBuilder, Root<UserInfo> root, int financialAssetsCode) {
        return criteriaBuilder.lessThan(root.get(UserInfo_.financialAssets), financialAssetsCode);
    }

    public Predicate inInvestmentPurposes(
            CriteriaBuilder criteriaBuilder,
            Root<UserInfo> root,
            List<Integer> investmentPurposes) {
        return root.get(UserInfo_.investmentPurposes).in(investmentPurposes);
    }
}
