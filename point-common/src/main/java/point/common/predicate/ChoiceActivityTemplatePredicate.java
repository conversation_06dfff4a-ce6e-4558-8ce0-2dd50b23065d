package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.ChoiceActivityAutoCycle;
import point.common.constant.ChoiceActivityStatus;
import point.common.entity.ChoiceActivityTemplate;
import point.common.entity.ChoiceActivityTemplate_;

@Component
public class ChoiceActivityTemplatePredicate extends EntityPredicate<ChoiceActivityTemplate> {

    public Predicate equalStatus(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivityTemplate> root,
            ChoiceActivityStatus status) {
        return criteriaBuilder.equal(root.get(ChoiceActivityTemplate_.status), status);
    }

    public Predicate equalAutoCycle(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivityTemplate> root,
            ChoiceActivityAutoCycle cycle) {
        return criteriaBuilder.equal(root.get(ChoiceActivityTemplate_.autoCycle), cycle);
    }

    public Predicate greaterThanOrEqualToStartTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityTemplate> root, Date startTimFrom) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(ChoiceActivityTemplate_.startTime), startTimFrom);
    }

    public Predicate lessThanStartTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityTemplate> root, Date startTimeTo) {
        return criteriaBuilder.lessThan(root.get(ChoiceActivityTemplate_.startTime), startTimeTo);
    }

    public Predicate lessThanOrEqualToStartTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityTemplate> root, Date startTimeTo) {
        return criteriaBuilder.lessThanOrEqualTo(
                root.get(ChoiceActivityTemplate_.startTime), startTimeTo);
    }

    public Predicate greaterThanOrEqualToEndTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityTemplate> root, Date endTimeFrom) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(ChoiceActivityTemplate_.endTime), endTimeFrom);
    }

    public Predicate lessThanEndTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceActivityTemplate> root, Date endTimeTo) {
        return criteriaBuilder.lessThan(root.get(ChoiceActivityTemplate_.endTime), endTimeTo);
    }
}
