package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.AccountType;
import point.common.entity.BankAccount;
import point.common.entity.BankAccount_;

@Component
public class BankAccountPredicate extends EntityPredicate<BankAccount> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<BankAccount> root, Long userId) {
        return criteriaBuilder.equal(root.get(BankAccount_.userId), userId);
    }

    public Predicate equalBankId(
            CriteriaBuilder criteriaBuilder, Root<BankAccount> root, Long bankId) {
        return criteriaBuilder.equal(root.get(BankAccount_.bankId), bankId);
    }

    public Predicate equalAccountType(
            CriteriaBuilder criteriaBuilder, Root<BankAccount> root, AccountType accountType) {
        return criteriaBuilder.equal(root.get(BankAccount_.accountType), accountType);
    }

    public Predicate equalAccountNumber(
            CriteriaBuilder criteriaBuilder, Root<BankAccount> root, String accountNumber) {
        return criteriaBuilder.equal(root.get(BankAccount_.accountNumber), accountNumber);
    }

    public Predicate isDeleted(
            CriteriaBuilder criteriaBuilder, Root<BankAccount> root, boolean deleted) {
        return deleted
                ? criteriaBuilder.isTrue(root.get(BankAccount_.deleted))
                : criteriaBuilder.isFalse(root.get(BankAccount_.deleted));
    }
}
