package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.Currency;
import point.common.entity.AssetSummary;
import point.common.entity.AssetSummary_;

@Component
public class AssetSummaryPredicate extends EntityPredicate<AssetSummary> {
    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<AssetSummary> root, Long userId) {
        return criteriaBuilder.equal(root.get(AssetSummary_.userId), userId);
    }

    public Predicate equalCurrency(
            CriteriaBuilder criteriaBuilder, Root<AssetSummary> root, Currency currency) {
        return criteriaBuilder.equal(root.get(AssetSummary_.currency), currency);
    }

    public Predicate greaterThanOrEqualToTargetAt(
            CriteriaBuilder criteriaBuilder, Root<AssetSummary> root, Date targetAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(AssetSummary_.targetAt), targetAt);
    }

    public Predicate lessThanTargetAt(
            CriteriaBuilder criteriaBuilder, Root<AssetSummary> root, Date targetAt) {
        return criteriaBuilder.lessThan(root.get(AssetSummary_.targetAt), targetAt);
    }
}
