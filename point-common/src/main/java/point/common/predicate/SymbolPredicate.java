package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;
import point.common.entity.Symbol;
import point.common.entity.Symbol_;

@Component
public class SymbolPredicate extends EntityPredicate<Symbol> {

    public Predicate equalTradeType(
            CriteriaBuilder criteriaBuilder, Root<Symbol> root, TradeType tradeType) {
        return criteriaBuilder.equal(root.get(Symbol_.tradeType), tradeType);
    }

    public Predicate equalCurrencyPair(
            CriteriaBuilder criteriaBuilder, Root<Symbol> root, CurrencyPair currencyPair) {
        return criteriaBuilder.equal(root.get(Symbol_.currencyPair), currencyPair);
    }

    public Predicate isVisible(CriteriaBuilder criteriaBuilder, Root<Symbol> root, boolean b) {
        return criteriaBuilder.equal(root.get(Symbol_.isVisible), b);
    }
}
