package point.common.predicate;

import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import point.common.entity.AbstractEntity;
import point.common.entity.AbstractEntity_;

public abstract class EntityPredicate<E extends AbstractEntity> {

    public Predicate equalId(CriteriaBuilder criteriaBuilder, Root<E> root, Long id) {
        return criteriaBuilder.equal(root.get(AbstractEntity_.id), id);
    }

    public Predicate greaterThanId(CriteriaBuilder criteriaBuilder, Root<E> root, Long id) {
        return criteriaBuilder.greaterThan(root.get(AbstractEntity_.id), id);
    }

    public Predicate greaterThanOrEqualToId(
            CriteriaBuilder criteriaBuilder, Root<E> root, Long id) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(AbstractEntity_.id), id);
    }

    public Predicate lessThanId(CriteriaBuilder criteriaBuilder, Root<E> root, Long id) {
        return criteriaBuilder.lessThan(root.get(AbstractEntity_.id), id);
    }

    public Predicate lessThanOrEqualToId(CriteriaBuilder criteriaBuilder, Root<E> root, Long id) {
        return criteriaBuilder.lessThanOrEqualTo(root.get(AbstractEntity_.id), id);
    }

    public Predicate inId(Root<E> root, List<Long> ids) {
        return root.get(AbstractEntity_.id).in(ids);
    }

    public Predicate notInId(Root<E> root, Long... ids) {
        return root.get(AbstractEntity_.id).in((Object[]) ids).not();
    }

    public Predicate greaterThanOrEqualToCreatedAt(
            CriteriaBuilder criteriaBuilder, Root<E> root, Date createdAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(AbstractEntity_.createdAt), createdAt);
    }

    public Predicate lessThanCreatedAt(
            CriteriaBuilder criteriaBuilder, Root<E> root, Date createdAt) {
        return criteriaBuilder.lessThan(root.get(AbstractEntity_.createdAt), createdAt);
    }

    public Predicate greaterThanOrEqualToUpdatedAt(
            CriteriaBuilder criteriaBuilder, Root<E> root, Date updatedAt) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(AbstractEntity_.updatedAt), updatedAt);
    }

    public Predicate lessThanUpdatedAt(
            CriteriaBuilder criteriaBuilder, Root<E> root, Date updatedAt) {
        return criteriaBuilder.lessThan(root.get(AbstractEntity_.updatedAt), updatedAt);
    }
}
