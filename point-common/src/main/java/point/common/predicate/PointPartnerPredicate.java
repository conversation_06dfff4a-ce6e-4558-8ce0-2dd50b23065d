package point.common.predicate;

import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.PointPartnerStatus;
import point.common.entity.PointPartner;
import point.common.entity.PointPartner_;

@Component
public class PointPartnerPredicate extends EntityPredicate<PointPartner> {

    public Predicate equalScope(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, Integer scope) {
        return criteriaBuilder.equal(root.get(PointPartner_.scope), scope);
    }

    public Predicate equalPartnerNumber(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, String partnerNumber) {
        return criteriaBuilder.equal(root.get(PointPartner_.partnerNumber), partnerNumber);
    }

    public Predicate equalName(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, String name) {
        return criteriaBuilder.equal(root.get(PointPartner_.name), name);
    }

    public Predicate likeShowName(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, String showName) {
        return criteriaBuilder.like(root.get(PointPartner_.showName), "%" + showName + "%");
    }

    public Predicate equalStatus(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, PointPartnerStatus status) {
        return criteriaBuilder.equal(root.get(PointPartner_.status), status);
    }

    public Predicate greaterThanOrEqualToEffectiveDate(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, Date effectiveDate) {
        return criteriaBuilder.greaterThanOrEqualTo(
                root.get(PointPartner_.effectiveDate), effectiveDate);
    }

    public Predicate lessThanEffectiveDate(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, Date EffectiveDate) {
        return criteriaBuilder.lessThan(root.get(PointPartner_.effectiveDate), EffectiveDate);
    }

    public Predicate greaterThanOrEqualToExpiryDate(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, Date expiryDate) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(PointPartner_.expiryDate), expiryDate);
    }

    public Predicate lessThanExpiryDate(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, Date expiryDate) {
        return criteriaBuilder.lessThan(root.get(PointPartner_.expiryDate), expiryDate);
    }

    public Predicate likePartnerNumber(
            CriteriaBuilder criteriaBuilder, Root<PointPartner> root, String partnerNumber) {
        return criteriaBuilder.like(root.get(PointPartner_.partnerNumber), partnerNumber + "%");
    }
}
