package point.common.predicate;

import java.math.BigDecimal;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.Currency;
import point.common.constant.UserIdType;
import point.common.entity.Asset;
import point.common.entity.Asset_;
import point.common.entity.UserIdentity_;

@Component
public class AssetPredicate extends EntityPredicate<Asset> {

    public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<Asset> root, Long userId) {
        return criteriaBuilder.equal(root.get(Asset_.userId), userId);
    }

    public Predicate equalCurrency(
            CriteriaBuilder criteriaBuilder, Root<Asset> root, Currency currency) {
        return criteriaBuilder.equal(root.get(Asset_.currency), currency);
    }

    public Predicate inCurrency(
            CriteriaBuilder criteriaBuilder, Root<Asset> root, List<Currency> currencies) {
        return criteriaBuilder.in(root.get(Asset_.currency).in(currencies));
    }

    public Predicate greaterThanOrEqualToOnhandAmount(
            CriteriaBuilder criteriaBuilder, Root<Asset> root, BigDecimal onhandAmount) {
        return criteriaBuilder.greaterThanOrEqualTo(root.get(Asset_.onhandAmount), onhandAmount);
    }

    public Predicate lessThanOrEqualToOnhandAmount(
            CriteriaBuilder criteriaBuilder, Root<Asset> root, BigDecimal onhandAmount) {
        return criteriaBuilder.lessThanOrEqualTo(root.get(Asset_.onhandAmount), onhandAmount);
    }

    public Predicate equalUserIdType(
            CriteriaBuilder criteriaBuilder, Root<Asset> root, UserIdType userIdType) {
        return criteriaBuilder.equal(
                root.get(Asset_.userIdentity).get(UserIdentity_.idType), userIdType);
    }
}
