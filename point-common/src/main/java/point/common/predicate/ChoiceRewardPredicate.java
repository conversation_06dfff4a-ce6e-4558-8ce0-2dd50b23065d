package point.common.predicate;

import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.WithdrawStatusType;
import point.common.entity.ChoiceReward;
import point.common.entity.ChoiceReward_;
import point.common.entity.ChoiceVote_;

@Component
public class ChoiceRewardPredicate extends EntityPredicate<ChoiceReward> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoiceReward> root, Long userId) {
        return criteriaBuilder.equal(root.get(ChoiceReward_.userId), userId);
    }

    public Predicate userIdIn(
            CriteriaBuilder criteriaBuilder, Root<ChoiceReward> root, List<Long> userId) {
        return criteriaBuilder.in(root.get(ChoiceVote_.USER_ID)).value(userId);
    }

    public Predicate equalRewardTime(
            CriteriaBuilder criteriaBuilder, Root<ChoiceReward> root, Date date) {
        if (date == null) {
            return null;
        }
        return criteriaBuilder.equal(
                criteriaBuilder.function("DATE", Date.class, root.get(ChoiceReward_.rewardTime)),
                date);
    }

    public Predicate rewardTimeGreaterThan(
            CriteriaBuilder criteriaBuilder, Root<ChoiceReward> root, Date date) {
        return criteriaBuilder.greaterThan(
                criteriaBuilder.function("DATE", Date.class, root.get(ChoiceReward_.rewardTime)),
                date);
    }

    public Predicate rewardTimeLessThan(
            CriteriaBuilder criteriaBuilder, Root<ChoiceReward> root, Date date) {
        return criteriaBuilder.lessThan(
                criteriaBuilder.function("DATE", Date.class, root.get(ChoiceReward_.rewardTime)),
                date);
    }

    public Predicate equalActivityId(
            CriteriaBuilder criteriaBuilder, Root<ChoiceReward> root, Long activityId) {
        return criteriaBuilder.equal(root.get(ChoiceReward_.activityId), activityId);
    }

    public Predicate withdrawStatusTypeIn(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceReward> root,
            List<WithdrawStatusType> withdrawStatusType) {
        return root.get(ChoiceReward_.withdrawStatusType).in(withdrawStatusType);
    }

    public Predicate lessThanExpireTime(CriteriaBuilder criteriaBuilder, Root<ChoiceReward> root) {
        return criteriaBuilder.greaterThan(root.get(ChoiceReward_.expiryTime), new Date());
    }
}
