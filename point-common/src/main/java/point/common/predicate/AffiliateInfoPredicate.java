package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.AffiliateInfo;
import point.common.entity.AffiliateInfo_;

@Component
public class AffiliateInfoPredicate extends EntityPredicate<AffiliateInfo> {

    public Predicate equalIdentify(
            CriteriaBuilder criteriaBuilder, Root<AffiliateInfo> root, String identify) {
        return criteriaBuilder.equal(root.get(AffiliateInfo_.identify), identify);
    }
}
