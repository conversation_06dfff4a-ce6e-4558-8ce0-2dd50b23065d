package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.constant.TradeType;
import point.common.entity.UserCropStatus;
import point.common.entity.UserCropStatus_;

@Component
public class UserCropStatusPredicate extends EntityPredicate<UserCropStatus> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<UserCropStatus> root, Long userId) {
        return criteriaBuilder.equal(root.get(UserCropStatus_.userId), userId);
    }

    public Predicate equalAssetId(
            CriteriaBuilder criteriaBuilder, Root<UserCropStatus> root, Long assetId) {
        return criteriaBuilder.equal(root.get(UserCropStatus_.assetId), assetId);
    }

    public Predicate equalTradeType(
            CriteriaBuilder criteriaBuilder, Root<UserCropStatus> root, TradeType tradeType) {
        return criteriaBuilder.equal(root.get(UserCropStatus_.tradeType), tradeType);
    }
}
