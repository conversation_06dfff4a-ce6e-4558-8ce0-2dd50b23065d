package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.ChoicePowerUserRel;
import point.common.entity.ChoicePowerUserRel_;

@Component
public class ChoicePowerUserRelPredicate extends EntityPredicate<ChoicePowerUserRel> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerUserRel> root, Long userId) {
        return criteriaBuilder.equal(root.get(ChoicePowerUserRel_.userId), userId);
    }

    public Predicate equalChoicePowerId(
            CriteriaBuilder criteriaBuilder, Root<ChoicePowerUserRel> root, Long choicePowerId) {
        return criteriaBuilder.equal(root.get(ChoicePowerUserRel_.choicePowerId), choicePowerId);
    }
}
