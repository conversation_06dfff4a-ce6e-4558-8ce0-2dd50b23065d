package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import point.common.entity.*;

@Component
public class MonsterGrowthHistoryPredicate extends EntityPredicate<MonsterGrowthHistory> {

    public Predicate equalUserId(
            CriteriaBuilder criteriaBuilder, Root<MonsterGrowthHistory> root, Long userId) {
        return criteriaBuilder.equal(root.get(MonsterGrowthHistory_.userId), userId);
    }

    public Predicate equalSymbolId(
            CriteriaBuilder criteriaBuilder, Root<MonsterGrowthHistory> root, Long symbolId) {
        return criteriaBuilder.equal(root.get(MonsterGrowthHistory_.symbolId), symbolId);
    }
}
