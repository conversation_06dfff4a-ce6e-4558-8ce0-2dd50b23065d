package point.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import point.common.entity.BestPrice;
import point.common.entity.BestPrice_;

public abstract class BestPricePredicate<E extends BestPrice> extends EntityPredicate<E> {

    public Predicate equalSymbolId(CriteriaBuilder criteriaBuilder, Root<E> root, Long symbolId) {
        return criteriaBuilder.equal(root.get(BestPrice_.symbolId), symbolId);
    }
}
