package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.MailNoreplyType;
import point.common.constant.ViewVariables;
import point.common.entity.MailNoreply;
import point.common.entity.MailNoreply_;
import point.common.model.response.PageData;
import point.common.predicate.MailNoreplyPredicate;

@Service
public class MailNoreplyService extends EntityService<MailNoreply, MailNoreplyPredicate> {

    @Override
    public Class<MailNoreply> getEntityClass() {
        return MailNoreply.class;
    }

    public MailNoreply findOne(MailNoreplyType mailNoreplyType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<MailNoreply, MailNoreply>() {
                    @Override
                    public MailNoreply query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalMailNoreplyType(
                                        criteriaBuilder, root, mailNoreplyType));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<MailNoreply> findByCondition() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<MailNoreply, List<MailNoreply>>() {
                    @Override
                    public List<MailNoreply> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                ViewVariables.DEFAULT_NUMBER,
                                ViewVariables.DEFAULT_SIZE,
                                criteriaBuilder.desc(root.get(MailNoreply_.createdAt)));
                    }
                });
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<MailNoreply> root,
            Long dateFrom,
            Long dateTo,
            Boolean enabled,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();

        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(dateFrom)));
        }

        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
        }

        return predicates;
    }

    public PageData<MailNoreply> findByConditionPageData(
            Long dateFrom, Long dateTo, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                dateFrom,
                                                dateTo,
                                                true,
                                                number,
                                                size));
                            }
                        });

        return new PageData<MailNoreply>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<MailNoreply, List<MailNoreply>>() {
                            @Override
                            public List<MailNoreply> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                dateFrom,
                                                dateTo,
                                                true,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(MailNoreply_.id)));
                            }
                        }));
    }
}
