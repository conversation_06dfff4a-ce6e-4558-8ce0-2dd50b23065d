package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.util.CollectionUtils;
import point.common.component.CustomRedisTemplate;
import point.common.component.CustomTransactionManager;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.entity.AbstractEntity;
import point.common.entity.AbstractEntity_;
import point.common.model.response.PageData;
import point.common.predicate.EntityPredicate;
import point.common.websocket.RedisPublisher;

@Slf4j
public abstract class EntityService<E extends AbstractEntity, P extends EntityPredicate<E>> {

    @Autowired protected P predicate;

    @Autowired protected CustomRedisTemplate<E> redisTemplate;

    @Autowired protected CustomTransactionManager customTransactionManager;

    @Autowired protected RedisPublisher redisPublisher;

    public abstract Class<E> getEntityClass();

    public E newEntity() {
        E entity = null;

        try {
            entity = getEntityClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("Entity creation failed", e);
        }

        return entity;
    }

    protected void fetch(Root<E> root) {
        // define if domain need to fetch.
    }

    protected String getCacheKey(Long id) {
        return getClass().getSimpleName() + ":" + id;
    }

    protected void saveCache(E entity) {
        redisTemplate.setValue(getCacheKey(entity.getId()), entity);
    }

    protected void deleteCache(E entity) {
        redisTemplate.delete(getCacheKey(entity.getId()));
    }

    protected void redisPublish(E entity) {
        // pub to redis and websocket broker will send to send user
        log.debug("Not implemented for redisPublish");
    }

    public E save(E entity) {
        customTransactionManager.save(entity);
        redisPublish(entity);
        deleteCache(entity);
        return entity;
    }

    public E save(E entity, EntityManager entityManager) throws Exception {
        customTransactionManager.save(entity, entityManager);
        redisPublish(entity);
        deleteCache(entity);
        return entity;
    }

    public void delete(E entity) {
        customTransactionManager.delete(entity);
        redisPublish(entity);
        deleteCache(entity);
    }

    public void delete(E entity, EntityManager entityManager) {
        customTransactionManager.delete(entity, entityManager);
        redisPublish(entity);
        deleteCache(entity);
    }

    private TypedQuery<E> createQuery(
            EntityManager entityManager,
            CriteriaQuery<E> criteriaQuery,
            Root<E> root,
            List<Predicate> predicates,
            Order... orders) {
        fetch(root);
        criteriaQuery.select(root);

        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        if (orders != null) {
            criteriaQuery.orderBy(orders);
        }

        return entityManager.createQuery(criteriaQuery);
    }

    protected E getSingleResult(
            EntityManager entityManager,
            CriteriaQuery<E> criteriaQuery,
            Root<E> root,
            List<Predicate> predicates,
            Order... orders) {
        try {
            return createQuery(entityManager, criteriaQuery, root, predicates, orders)
                    .setFirstResult(0)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    protected List<E> getResultList(
            EntityManager entityManager,
            CriteriaQuery<E> criteriaQuery,
            Root<E> root,
            List<Predicate> predicates,
            int number,
            int size,
            Order... orders) {
        TypedQuery<E> createQuery =
                createQuery(entityManager, criteriaQuery, root, predicates, orders);
        if (number * size != 0) {
            createQuery = createQuery.setFirstResult(number * size);
        }
        if (size != Integer.MAX_VALUE) {
            createQuery = createQuery.setMaxResults(size);
        }
        return createQuery.getResultList();
    }

    protected List<E> getResultList(
            EntityManager entityManager,
            CriteriaQuery<E> criteriaQuery,
            Root<E> root,
            List<Predicate> predicates,
            Order... orders) {
        return getResultList(
                entityManager, criteriaQuery, root, predicates, 0, Integer.MAX_VALUE, orders);
    }

    private E reload(Long id) {
        E entity =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<E, E>() {
                            @Override
                            public E query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        criteriaBuilder.equal(root.get(AbstractEntity_.id), id));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });

        if (entity != null) {
            saveCache(entity);
        }

        return entity;
    }

    public E findOne(Long id) {
        E entity = null;

        try {
            entity = redisTemplate.getValue(getCacheKey(id));
        } catch (Exception e) {
        }

        if (entity == null) {
            entity = reload(id);
        }

        return entity;
    }

    public E findOne(Long id, EntityManager entityManager) {
        return new QueryExecutorReturner<E, E>() {
            @Override
            public E query() {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(criteriaBuilder.equal(root.get(AbstractEntity_.id), id));
                return getSingleResult(entityManager, criteriaQuery, root, predicates);
            }
        }.execute(getEntityClass(), entityManager);
    }

    public List<E> findAll() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<E, List<E>>() {
                    @Override
                    public List<E> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                null,
                                criteriaBuilder.asc(root.get(AbstractEntity_.id)));
                    }
                });
    }

    public List<E> findAll(Iterable<Long> ids) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<E, List<E>>() {
                    @Override
                    public List<E> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(root.get(AbstractEntity_.id).in(ids));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(AbstractEntity_.id)));
                    }
                });
    }

    public PageData<E> findAll(int number, int size, Direction direction) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<E>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager, criteriaBuilder, criteriaQuery, root, null);
                            }
                        });

        return new PageData<E>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<E, List<E>>() {
                            @Override
                            public List<E> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        null,
                                        number,
                                        size,
                                        direction == Direction.ASC
                                                ? criteriaBuilder.asc(root.get(AbstractEntity_.id))
                                                : criteriaBuilder.desc(
                                                        root.get(AbstractEntity_.id)));
                            }
                        }));
    }

    public PageData<E> findAll(int number, int size) {
        return findAll(number, size, Direction.DESC);
    }

    public Long count(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Long> criteriaQuery,
            Root<E> root,
            List<Predicate> predicates) {
        criteriaQuery.select(criteriaBuilder.count(root));

        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        return entityManager.createQuery(criteriaQuery).getSingleResult();
    }

    public long countAll() {
        return customTransactionManager.count(
                getEntityClass(),
                new QueryExecutorCounter<E>() {
                    @Override
                    public Long query() {
                        return count(entityManager, criteriaBuilder, criteriaQuery, root, null);
                    }
                });
    }
}
