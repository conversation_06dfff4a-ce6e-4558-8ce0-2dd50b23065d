package point.common.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.CustomRedisTemplate;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;
import point.common.constant.Exchange;
import point.common.constant.TradeType;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.Symbol;
import point.pos.model.PosBestPriceData;
import point.pos.service.PosCandlestickService;

@RequiredArgsConstructor
@Service
public class OrderbookService {

    private static String getPosCacheKey(Symbol symbol) {
        return ("best_price:" + Exchange.AMBER + ":" + symbol.getName()).toLowerCase();
    }

    private final CustomRedisTemplate<PosBestPriceData> bestPriceRedisTemplate;

    private final SymbolService symbolService;

    private final CurrencyPairConfigService currencyPairConfigService;

    public PosBestPriceData getPos(Symbol symbol) {
        return bestPriceRedisTemplate.getValue(getPosCacheKey(symbol));
    }

    private final PosCandlestickService posCandlestickService;

    public BigDecimal getBestBidOfQuoteJpy(Currency baseCurrency) throws Exception {

        if (baseCurrency == Currency.JPY) {
            return BigDecimal.ONE;
        }
        if (baseCurrency == Currency.POINT) {
            return BigDecimal.ONE;
        }
        List<CurrencyPairConfig> currencyPairConfig =
                currencyPairConfigService.findAllByCondition(
                        TradeType.INVEST, CurrencyPair.valueOf(baseCurrency, Currency.JPY), true);
        if (!CollectionUtils.isEmpty(currencyPairConfig)) {
            PosBestPriceData posBestPriceData =
                    getPos(
                            symbolService.findByCondition(
                                    TradeType.INVEST,
                                    CurrencyPair.valueOf(baseCurrency, Currency.JPY)));
            if (posBestPriceData == null) {
                return BigDecimal.ZERO;
            }
            return posBestPriceData.getBestBid();
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getOperateBestBidOfQuoteJpy(Currency baseCurrency) throws Exception {

        if (baseCurrency == Currency.JPY) {
            return BigDecimal.ONE;
        }
        if (baseCurrency == Currency.POINT) {
            return BigDecimal.ONE;
        }
        List<CurrencyPairConfig> currencyPairConfig =
                currencyPairConfigService.findAllByCondition(
                        TradeType.OPERATE, CurrencyPair.valueOf(baseCurrency, Currency.JPY), true);
        if (!CollectionUtils.isEmpty(currencyPairConfig)) {
            PosBestPriceData posBestPriceData =
                    getPos(
                            symbolService.findByCondition(
                                    TradeType.OPERATE,
                                    CurrencyPair.valueOf(baseCurrency, Currency.JPY)));
            if (posBestPriceData == null) {
                return BigDecimal.ZERO;
            }
            return posBestPriceData.getBestBid();
        }
        return BigDecimal.ZERO;
    }

    public Map<Currency, BigDecimal> getOperateBestBidOfQuoteJpy(TradeType tradeType) {
        Map<Currency, BigDecimal> map = new ConcurrentHashMap<>();
        map.put(Currency.JPY, BigDecimal.ONE);
        map.put(Currency.POINT, BigDecimal.ONE);
        currencyPairConfigService.findAllByCondition(tradeType, null).parallelStream()
                .map(
                        currencyPairConfig ->
                                symbolService.findByCondition(
                                        tradeType, currencyPairConfig.getCurrencyPair()))
                .filter(Objects::nonNull)
                .forEach(
                        symbol -> {
                            BigDecimal bestBid =
                                    Optional.ofNullable(getPos(symbol))
                                            .map(PosBestPriceData::getBestBid)
                                            .orElse(BigDecimal.ZERO);
                            map.put(symbol.getCurrencyPair().getBaseCurrency(), bestBid);
                        });
        return map;
    }
}
