package point.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.Currency;
import point.common.constant.PointTransferStatusEnum;
import point.common.entity.PointTransfer;
import point.common.entity.PontaConvertWorker;
import point.common.ponta.PontaBizInVokerApi;
import point.common.predicate.PontaConvertWorkerPredicate;

@Slf4j
@Service
public class PontaConvertWorkerService
        extends EntityService<PontaConvertWorker, PontaConvertWorkerPredicate> {

    private final PontaBizInVokerApi pontaBizInVokerApi;
    private final AssetService assetService;
    private final PointTransferStatusHistoryService statusHistoryService;
    private final PointTransferService pointTransferService;

    private static final String DUPLICATE_TRADE_ERROR_CODE = "E024000000";
    private static final String SUCCESS_CODE_VALUE = "N000000000";
    private static final String FAILED_CODE_VALUE = "FAILED";

    @Autowired
    public PontaConvertWorkerService(
            PontaBizInVokerApi pontaBizInVokerApi,
            AssetService assetService,
            PointTransferStatusHistoryService statusHistoryService,
            @Lazy PointTransferService pointTransferService) {
        this.pontaBizInVokerApi = pontaBizInVokerApi;
        this.assetService = assetService;
        this.statusHistoryService = statusHistoryService;
        this.pointTransferService = pointTransferService;
    }

    @Override
    public Class<PontaConvertWorker> getEntityClass() {
        return PontaConvertWorker.class;
    }

    public void pontaConverSync() {
        // select all flag=0
        List<PontaConvertWorker> pendingRecords = findPendingRecords();
        for (PontaConvertWorker record : pendingRecords) {
            try {
                // send IF-003 request
                Map<String, String> response = pontaBizInVokerApi.reSendif003(record);
                // process successful, but we were unable to reproduce it
                String responseCode = response.get("ST0274_tl");
                if (SUCCESS_CODE_VALUE.equals(responseCode)
                        || DUPLICATE_TRADE_ERROR_CODE.equals(responseCode)) {
                    handleDuplicateTrade(record, null, null);
                } else {
                    log.info(
                            "IF-003 resend and responseCode: {},response: {}",
                            responseCode,
                            response);
                    handleDuplicateTrade(record, FAILED_CODE_VALUE, responseCode);
                }
            } catch (Exception e) {
                log.error(
                        "IF-003 resend failed for pontaConvertWorker ID: {}, error: {}",
                        record.getId(),
                        e.getMessage());
            }
        }
    }

    private List<PontaConvertWorker> findPendingRecords() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PontaConvertWorker, List<PontaConvertWorker>>() {
                    @Override
                    public List<PontaConvertWorker> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalSyncFlag(criteriaBuilder, root, 0));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    private void handleDuplicateTrade(PontaConvertWorker record, String code, String responseCode)
            throws Exception {

        customTransactionManager.execute(
                entityManager -> {
                    // 1. update asset & update PointTransfer & save PointTransferStatusHistory
                    updateUserBalance(record, code, responseCode, entityManager);
                    // 2. update pontaConvertWorker set flag=1
                    record.setFlag(1);
                    save(record, entityManager);
                    log.info("Handled duplicate trade for record ID: {}", record.getId());
                });
    }

    /**
     * updateUserBalance
     *
     * @param record
     * @param entityManager
     * @throws Exception
     */
    private void updateUserBalance(
            PontaConvertWorker record,
            String code,
            String responseCode,
            EntityManager entityManager)
            throws Exception {
        // 1. update asset
        if (!FAILED_CODE_VALUE.equals(code)) {
            assetService.update(
                    record.getUserId(),
                    Currency.JPY,
                    record.getPointAmount(),
                    BigDecimal.ZERO,
                    entityManager);
            log.info("PONTA CONVERT WORKER UPDATE ASSET SUCCESS ,USER_ID: {}", record.getUserId());
        }

        // 2. update PointTransfer
        PointTransfer pointTransfer =
                pointTransferService.updateStatusByTradeNumber(
                        record.getTradeNumber(), code, entityManager);
        log.info(
                "PONTA CONVERT WORKER UPDATE POINTTRANSFER SUCCESS ,USER_ID: {},TRADE_NUMBER:{}",
                record.getUserId(),
                record.getTradeNumber());

        // 3. save PointTransferStatusHistory
        statusHistoryService.recordStatusHistory(
                pointTransfer,
                PointTransferStatusEnum.PROCESSING,
                FAILED_CODE_VALUE.equals(code)
                        ? PointTransferStatusEnum.FAILED
                        : PointTransferStatusEnum.COMPLETED,
                FAILED_CODE_VALUE.equals(code) ? "振替処理失敗: Ponta API エラー" + responseCode : "振替処理完了",
                entityManager);
        log.info(
                "PONTA CONVERT WORKER SAVE POINTTRANSFERSTATUSHISTORY SUCCESS ,TRANSFER_ID: {}",
                pointTransfer.getId());
    }
}
