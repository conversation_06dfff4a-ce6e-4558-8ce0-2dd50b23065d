package point.common.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.UserIdType;
import point.common.entity.*;
import point.common.model.response.PageData;
import point.common.predicate.UserMonsterInfoPredicate;
import point.common.util.JsonUtil;
import point.pos.entity.PosTrade;

@Slf4j
@RequiredArgsConstructor
@Service
public class UserMonsterInfoService
        extends EntityService<UserMonsterInfo, UserMonsterInfoPredicate> {

    private final MonsterBaseService monsterBaseService;
    private final MonsterExperienceService monsterExperienceService;
    private final MonsterGrowthLevelService monsterGrowthLevelService;
    private final MonsterGrowthHistoryService monsterGrowthHistoryService;

    @Override
    public Class<UserMonsterInfo> getEntityClass() {
        return UserMonsterInfo.class;
    }

    public UserMonsterInfo findByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public UserMonsterInfo query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public void generateMasterInfo(
            Long userId,
            Long monsterId,
            String idType,
            Date creationDateTime,
            EntityManager entityManager)
            throws Exception {
        // find top 2 monster growth level
        List<MonsterGrowthLevel> monsterGrowthLevels =
                monsterGrowthLevelService.findTopByRequiredExperienceAsc(2);
        MonsterGrowthLevel level1 = monsterGrowthLevels.get(0); // first level
        MonsterGrowthLevel level2 = monsterGrowthLevels.get(1); // next level
        UserMonsterInfo userMonsterInfo = new UserMonsterInfo();
        userMonsterInfo.setUserId(userId);
        userMonsterInfo.setMonsterId(monsterId);
        userMonsterInfo.setIdType(idType);
        userMonsterInfo.setCurrentExperience(0L);
        userMonsterInfo.setLevel(level1.getId());
        userMonsterInfo.setMonthlyPower(level1.getRule().getPowerAmount());
        userMonsterInfo.setNextLevelExperience(level2.getRequiredExperience());
        userMonsterInfo.setCreationDateTime(creationDateTime);
        this.save(userMonsterInfo, entityManager);
    }

    /**
     * calculate user monster info base on income and posTrade
     *
     * @param income income
     * @param posTrade trading info
     */
    public void calculateMonster(BigDecimal income, PosTrade posTrade) {
        if (this.shouldSkipCalculation(posTrade)) {
            log.info(
                    "skip calculate monster info for userId: {} condition check failed",
                    posTrade.getUserId());
            return;
        }

        log.info(
                "calculate monster info for userId: {} posTrade: {}",
                posTrade.getUserId(),
                JsonUtil.encode(posTrade));

        Optional<MonsterBase> monsterBase =
                this.monsterBaseService.findByIdType(posTrade.getIdType());
        if (monsterBase.isEmpty()) {
            log.warn("No monster base found for idType: {}", posTrade.getIdType());
            return;
        }
        final long monsterId = monsterBase.get().getId();
        try {
            customTransactionManager.execute(
                    entityManager -> {
                        Date creationDateTime = posTrade.getCreatedAt();
                        Pair<Long, Optional<MonsterFood>> experiencePointsWithFood =
                                monsterExperienceService.calculateExperiencePointsWithFood(
                                        posTrade.getSymbolId(), posTrade.getIdType(), income);
                        long experiencePoints = experiencePointsWithFood.getLeft();
                        MonsterFood monsterFood = experiencePointsWithFood.getRight().orElse(null);
                        BigDecimal bonusRate = BigDecimal.ZERO;
                        if (Objects.nonNull(monsterFood)) {
                            if (monsterFood.getSymbolId().equals(posTrade.getSymbolId())) {
                                bonusRate = monsterExperienceService.getBonusRate();
                            }
                        }
                        BigDecimal conversionRate =
                                monsterExperienceService.getConversionRate(income);
                        UserMonsterInfo userMonsterInfoBefore =
                                this.findByUserId(posTrade.getUserId());
                        // if income is <= 0, we need to create a new monster info
                        if (BigDecimal.ZERO.compareTo(income) >= 0) {
                            if (Objects.isNull(userMonsterInfoBefore)) {
                                this.generateMasterInfo(
                                        posTrade.getUserId(),
                                        monsterId,
                                        posTrade.getIdType().name(),
                                        creationDateTime,
                                        entityManager);
                            }

                            // create monster growth history
                            MonsterGrowthHistory monsterGrowthHistory = new MonsterGrowthHistory();
                            monsterGrowthHistory.setUserId(posTrade.getUserId());
                            monsterGrowthHistory.setMonsterId(userMonsterInfoBefore.getMonsterId());
                            monsterGrowthHistory.setSymbolId(posTrade.getSymbolId());
                            monsterGrowthHistory.setIncome(income);
                            monsterGrowthHistory.setConversionRate(BigDecimal.ZERO);
                            monsterGrowthHistory.setBonusRate(bonusRate);
                            monsterGrowthHistory.setWeekFood(
                                    Objects.nonNull(monsterFood)
                                            ? monsterFood.getSymbolId()
                                            : null);
                            monsterGrowthHistory.setExperienceEarned(NumberUtils.LONG_ZERO);
                            monsterGrowthHistory.setCurrentExperience(
                                    userMonsterInfoBefore.getCurrentExperience());
                            monsterGrowthHistory.setNextLevelExperience(
                                    userMonsterInfoBefore.getNextLevelExperience());
                            monsterGrowthHistory.setGrowthRate(
                                    calculateGrowthRate(userMonsterInfoBefore, experiencePoints));
                            monsterGrowthHistory.setLevelBefore(userMonsterInfoBefore.getLevel());
                            monsterGrowthHistory.setLevelAfter(userMonsterInfoBefore.getLevel());
                            monsterGrowthHistoryService.save(monsterGrowthHistory, entityManager);
                            return;
                        }

                        UserMonsterInfo userMonsterInfoAfter;
                        List<MonsterGrowthLevel> monsterGrowthLevelList =
                                monsterGrowthLevelService.findAll();
                        long newSumExperience;
                        if (Objects.isNull(userMonsterInfoBefore)) {
                            userMonsterInfoAfter = new UserMonsterInfo();
                            userMonsterInfoAfter.setUserId(posTrade.getUserId());
                            userMonsterInfoAfter.setMonsterId(monsterId);
                            userMonsterInfoAfter.setIdType(posTrade.getIdType().name());
                            newSumExperience = experiencePoints;
                        } else {
                            // 更新sumExperience
                            userMonsterInfoAfter = userMonsterInfoBefore;
                            userMonsterInfoAfter.setId(userMonsterInfoBefore.getId());
                            newSumExperience =
                                    userMonsterInfoBefore.getCurrentExperience() + experiencePoints;
                        }
                        userMonsterInfoAfter.setCurrentExperience(newSumExperience);

                        long nextLevelRequiredExperience =
                                userMonsterInfoAfter.getNextLevelExperience() == null
                                        ? 0L
                                        : userMonsterInfoAfter.getNextLevelExperience();
                        // calc level
                        if (!CollectionUtils.isEmpty(monsterGrowthLevelList)) {

                            List<MonsterGrowthLevel> levelSortedList =
                                    monsterGrowthLevelList.stream()
                                            .sorted(
                                                    Comparator.comparing(
                                                            MonsterGrowthLevel
                                                                    ::getRequiredExperience))
                                            .toList();
                            int levelUpIndex = 0;
                            for (int i = 0; i < levelSortedList.size(); i++) {
                                MonsterGrowthLevel levelTbl = monsterGrowthLevelList.get(i);
                                if (newSumExperience >= levelTbl.getRequiredExperience()) {
                                    userMonsterInfoAfter.setLevel(levelTbl.getId());
                                    userMonsterInfoAfter.setMonthlyPower(
                                            levelTbl.getRule().getPowerAmount());
                                    levelUpIndex = i;
                                }
                            }

                            MonsterGrowthLevel nextLeve;
                            if (levelSortedList.size() - 1 == (levelUpIndex + 1)) {
                                nextLeve = levelSortedList.get(levelSortedList.size() - 1);
                            } else {
                                nextLeve = levelSortedList.get(levelUpIndex + 1);
                            }
                            nextLevelRequiredExperience = nextLeve.getRequiredExperience();
                            userMonsterInfoAfter.setNextLevelExperience(
                                    nextLevelRequiredExperience);
                        }
                        this.save(userMonsterInfoAfter, entityManager);

                        // MonsterGrowthHistory
                        MonsterGrowthHistory monsterGrowthHistory = new MonsterGrowthHistory();
                        monsterGrowthHistory.setUserId(posTrade.getUserId());
                        monsterGrowthHistory.setMonsterId(userMonsterInfoAfter.getMonsterId());
                        monsterGrowthHistory.setSymbolId(posTrade.getSymbolId());
                        monsterGrowthHistory.setIncome(income);
                        monsterGrowthHistory.setConversionRate(conversionRate);
                        monsterGrowthHistory.setBonusRate(bonusRate);
                        monsterGrowthHistory.setWeekFood(
                                Objects.nonNull(monsterFood) ? monsterFood.getSymbolId() : null);
                        monsterGrowthHistory.setExperienceEarned(experiencePoints);
                        monsterGrowthHistory.setCurrentExperience(newSumExperience);
                        monsterGrowthHistory.setNextLevelExperience(nextLevelRequiredExperience);
                        if (userMonsterInfoBefore == null) {
                            BigDecimal experienceBefore = new BigDecimal(experiencePoints);
                            BigDecimal experienceAfter =
                                    new BigDecimal(nextLevelRequiredExperience);
                            BigDecimal growthRate =
                                    experienceBefore.divide(
                                            experienceAfter, 4, RoundingMode.HALF_UP);
                            monsterGrowthHistory.setGrowthRate(growthRate);
                            monsterGrowthHistory.setLevelBefore(0L);
                        } else {
                            monsterGrowthHistory.setGrowthRate(
                                    calculateGrowthRate(userMonsterInfoBefore, experiencePoints));
                            monsterGrowthHistory.setLevelBefore(userMonsterInfoBefore.getLevel());
                        }
                        monsterGrowthHistory.setLevelAfter(userMonsterInfoAfter.getLevel());
                        monsterGrowthHistoryService.save(monsterGrowthHistory, entityManager);
                    });
            log.info("Finished calculate monster info for userId: {}", posTrade.getUserId());
        } catch (Exception e) {
            log.error(
                    "Calc monster info failed income : {} trade_id : {} error_message : {}",
                    income,
                    posTrade.getId(),
                    e.getMessage(),
                    e);
        }
    }

    private boolean shouldSkipCalculation(PosTrade posTrade) {
        return Objects.isNull(posTrade)
                || Objects.isNull(posTrade.getOrderSide())
                || posTrade.getOrderSide().isBuy();
    }

    // 成長比率を獲得する
    private BigDecimal calculateGrowthRate(
            UserMonsterInfo userMonsterInfoBefore, long experiencePoints) {
        if (userMonsterInfoBefore.getCurrentExperience() == 0) {
            return null;
        } else {
            BigDecimal growth = new BigDecimal(experiencePoints);
            BigDecimal previous = new BigDecimal(userMonsterInfoBefore.getCurrentExperience());
            return growth.divide(previous, 4, RoundingMode.HALF_UP);
        }
    }

    public PageData<UserMonsterInfo> findByCondition(
            Long userId,
            UserIdType idType,
            String monsterName,
            Integer level,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                idType,
                                                monsterName,
                                                level,
                                                number,
                                                size));
                            }
                        });

        return new PageData<UserMonsterInfo>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<UserMonsterInfo> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                idType,
                                                monsterName,
                                                level,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(UserMonsterInfo_.id)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<UserMonsterInfo> root,
            Long userId,
            UserIdType idType,
            String monsterName,
            Integer level,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();
        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }
        if (idType != null) {
            predicates.add(predicate.equalIdType(criteriaBuilder, root, idType.name()));
        }
        if (monsterName != null) {
            predicates.add(predicate.equalMonsterName(criteriaBuilder, root, monsterName));
        }
        if (level != null) {
            predicates.add(predicate.equalLevel(criteriaBuilder, root, level));
        }
        return predicates;
    }
}
