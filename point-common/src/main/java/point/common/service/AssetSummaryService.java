package point.common.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.component.QueryExecutorSum;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.Asset;
import point.common.entity.AssetSummary;
import point.common.entity.AssetSummary_;
import point.common.entity.Candlestick;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.Symbol;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.AssetSummaryPredicate;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.common.util.JsonUtil;
import point.pos.entity.PosCandlestick;
import point.pos.entity.PosTrade;
import point.pos.service.PosCandlestickService;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@Component
public class AssetSummaryService extends EntityService<AssetSummary, AssetSummaryPredicate> {

    private final SymbolService symbolService;
    private final AssetService assetService;
    private final CurrencyConfigService currencyConfigService;
    private final CurrencyPairConfigService currencyPairConfigService;
    private final UserService userService;
    private final FiatWithdrawalService fiatWithdrawalService;
    private final FiatDepositService fiatDepositService;
    private final PosCandlestickService posCandlestickService;
    private final PosTradeService posTradeService;
    private final PointUserService pointUserService;

    public void calculateBAK(String targetDay, TradeType tradeType) throws CustomException {
        // 日付チェックとtargetAtの設定
        // targetAtは対象日の00:00を示す
        Date targetAt = FormatUtil.parseJst(targetDay, FormatPattern.YYYYMMDD);
        if (targetAt == null) {
            log.error("invalid target date string: targetDay=" + targetDay);
            throw new CustomException(ErrorCode.COMMON_ERROR_INVALID_DATE_STRING);
        }
        // 集計の基準となる日付
        Date targetDateFrom = new Date(targetAt.getTime());
        Date targetDateTo = new Date(targetAt.getTime() + 60 * 60 * 24 * 1000);

        // 集計対象通貨の一覧を得るため、symbolテーブルを確認
        // 通貨ペアリスト取得
        List<Symbol> symbolList = new ArrayList<>();

        currencyPairConfigService
                .findAllByCondition(tradeType, null)
                .forEach(
                        currencyPairConfig -> {
                            symbolList.add(
                                    symbolService.findByCondition(
                                            tradeType, currencyPairConfig.getCurrencyPair()));
                        });

        // active(enabled)通貨リスト取得
        List<Currency> currencyList = new ArrayList<>();
        currencyConfigService
                .findAllByCondition(tradeType, null, true)
                .forEach(
                        currencyConfig -> {
                            currencyList.add(currencyConfig.getCurrency());
                        });

        Map<Currency, List<Symbol>> currencySymbolMap = createCurrencySymbolMap(symbolList);

        log.info(
                "AssetSummaryLog,targetFrom,{},targetTo,{},currencySymbolMap,{}",
                targetDateFrom,
                targetDateTo,
                currencySymbolMap);

        // userIdをキー、 Assetが存在するCurrencyのListをバリューとするMapを作成
        Map<Long, List<Currency>> userAssetCurrencyMap = new HashMap<>();
        assetService
                .findAll()
                .forEach(
                        asset -> {
                            Long userId = asset.getUserId();
                            List<Currency> currencies =
                                    userAssetCurrencyMap.computeIfAbsent(
                                            userId, k -> new ArrayList<>());
                            currencies.add(asset.getCurrency());
                        });

        // userを全件取得し、userごとに全てのCurrencyについてAssetが揃っているかを確認し、なければ作成する
        if (TradeType.INVEST.equals(tradeType)) {
            userService
                    .findAll()
                    .forEach(
                            user -> {
                                Long userId = user.getId();
                                List<Currency> currencies = userAssetCurrencyMap.get(userId);
                                currencyList.forEach(
                                        currency -> {
                                            if (currencies != null
                                                    && currencies.contains(currency)) {
                                                return;
                                            }
                                            Asset createdAsset = new Asset();
                                            createdAsset.setUserId(userId);
                                            createdAsset.setCurrency(currency);
                                            createdAsset.setOnhandAmount(BigDecimal.ZERO);
                                            createdAsset.setLockedAmount(BigDecimal.ZERO);
                                            assetService.save(createdAsset);
                                        });
                            });
        } else if (TradeType.OPERATE.equals(tradeType)) {
            pointUserService
                    .findAll()
                    .forEach(
                            user -> {
                                Long userId = user.getId();
                                List<Currency> currencies = userAssetCurrencyMap.get(userId);
                                currencyList.forEach(
                                        currency -> {
                                            if (currencies != null
                                                    && currencies.contains(currency)) {
                                                return;
                                            }
                                            Asset createdAsset = new Asset();
                                            createdAsset.setUserId(userId);
                                            createdAsset.setCurrency(currency);
                                            createdAsset.setOnhandAmount(BigDecimal.ZERO);
                                            createdAsset.setLockedAmount(BigDecimal.ZERO);
                                            assetService.save(createdAsset);
                                        });
                            });
        }
        // assetSummary
        Map<Currency, Map<Long, List<AssetSummary>>> assetSummaryMap = new HashMap<>();
        Date oneDayAgo = new Date(targetDateFrom.getTime() - 60 * 60 * 24 * 1000);
        for (Currency currency : currencyList) {
            List<AssetSummary> yesterdaySummary =
                    this.findByCondition(null, currency, oneDayAgo, targetDateFrom);
            Map<Long, List<AssetSummary>> assetSummaryByUserId =
                    yesterdaySummary.stream()
                            .collect(Collectors.groupingBy(AssetSummary::getUserId));
            assetSummaryMap.put(currency, assetSummaryByUserId);
        }

        // 販売所約定履歴マップ作成
        // master
        List<Symbol> posSymbols = symbolService.findByCondition(tradeType);
        Map<Long, Map<Long, List<PosTrade>>> posTradeMap = new HashMap<>();
        Map<Long, Map<Long, List<PosTrade>>> posTradeMapHistory = new HashMap<>();
        for (Symbol posSymbol : posSymbols) {
            List<PosTrade> posTradeList =
                    posTradeService.findAllByCondition(
                            posSymbol.getId(),
                            null,
                            null,
                            null,
                            null,
                            targetDateFrom.getTime(),
                            targetDateTo.getTime(),
                            null,
                            null,
                            null);
            posTradeList.stream()
                    .collect(Collectors.groupingBy(PosTrade::getSymbolId))
                    .forEach(
                            (k, v) -> {
                                posTradeMap.put(
                                        k,
                                        v.stream()
                                                .collect(
                                                        Collectors.groupingBy(
                                                                PosTrade::getUserId)));
                            });
            // history
            List<PosTrade> posTradeListHistory =
                    posTradeService.findFromPosTradeHistory(
                            posSymbol,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            targetDateFrom,
                            targetDateTo,
                            0,
                            Integer.MAX_VALUE);
            posTradeListHistory.stream()
                    .collect(Collectors.groupingBy(PosTrade::getSymbolId))
                    .forEach(
                            (k, v) -> {
                                posTradeMapHistory.put(
                                        k,
                                        v.stream()
                                                .collect(
                                                        Collectors.groupingBy(
                                                                PosTrade::getUserId)));
                            });
        }

        // candlestick
        Map<Currency, BigDecimal> candlestickMap = new HashMap<>();
        for (Currency currency : currencyList) {
            try {
                BigDecimal candlestick =
                        findJpyConversionFromCandlestick(currency, targetDay, 120, tradeType);
                candlestickMap.put(currency, candlestick);
            } catch (CustomException e) {
                // forEach内では検査例外を投げられないため、RuntimeExceptionに詰め替える
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }

        // 集計処理開始
        List<AssetSummary> profitList = new ArrayList<AssetSummary>();
        currencyList.forEach(
                currency -> {
                    assetService
                            .findByCurrency(currency)
                            .forEach(
                                    asset -> {
                                        // 日次集計レコードの生成・集計項目以外の設定
                                        AssetSummary assetSummary = newEntity();
                                        assetSummary.setUserId(asset.getUserId());
                                        assetSummary.setCurrency(currency);
                                        assetSummary.setTargetAt(targetAt);
                                        assetSummary.setJpyConversion(candlestickMap.get(currency));

                                        // assetは積み上げ式のため、前日までの集計結果を取得
                                        // 前日の開始時刻を取得
                                        BigDecimal yesterdayAmount =
                                                (assetSummaryMap.get(currency) == null
                                                                || assetSummaryMap
                                                                                .get(currency)
                                                                                .get(
                                                                                        asset
                                                                                                .getUserId())
                                                                        == null
                                                                || assetSummaryMap
                                                                        .get(currency)
                                                                        .get(asset.getUserId())
                                                                        .isEmpty())
                                                        ? BigDecimal.ZERO
                                                        : (assetSummaryMap
                                                                        .get(currency)
                                                                        .get(asset.getUserId()))
                                                                .get(0)
                                                                .getCurrentAmount();

                                        // 各posTradeテーブルの情報を取得
                                        List<Symbol> posSymbolList =
                                                currencySymbolMap.get(currency);
                                        if (posSymbolList != null) {
                                            posSymbolList.forEach(
                                                    symbol -> {
                                                        if (symbol.getTradeType()
                                                                .equals(tradeType)) {
                                                            if (posTradeMap.get(symbol.getId())
                                                                            != null
                                                                    && posTradeMap
                                                                                    .get(
                                                                                            symbol
                                                                                                    .getId())
                                                                                    .get(
                                                                                            asset
                                                                                                    .getUserId())
                                                                            != null) {
                                                                posTradeMap
                                                                        .get(symbol.getId())
                                                                        .get(asset.getUserId())
                                                                        .forEach(
                                                                                st -> {
                                                                                    addPosTrade(
                                                                                            assetSummary,
                                                                                            st,
                                                                                            currency,
                                                                                            symbol.getCurrencyPair()
                                                                                                    .getBaseCurrency());
                                                                                });
                                                            }
                                                        }
                                                    });

                                            // target_atが処理実行日の1日前以前の場合archiveも検索
                                            Date yesterday =
                                                    new DateTime()
                                                            .minusDays(1)
                                                            .withTime(0, 0, 0, 0)
                                                            .toDate();
                                            if (targetDateFrom.before(yesterday)) {
                                                currencySymbolMap
                                                        .get(currency)
                                                        .forEach(
                                                                symbol -> {
                                                                    if (symbol.getTradeType()
                                                                            .equals(tradeType)) {
                                                                        if (posTradeMapHistory.get(
                                                                                                symbol
                                                                                                        .getId())
                                                                                        != null
                                                                                && posTradeMapHistory
                                                                                                .get(
                                                                                                        symbol
                                                                                                                .getId())
                                                                                                .get(
                                                                                                        asset
                                                                                                                .getUserId())
                                                                                        != null) {

                                                                            posTradeMapHistory
                                                                                    .get(
                                                                                            symbol
                                                                                                    .getId())
                                                                                    .get(
                                                                                            asset
                                                                                                    .getUserId())
                                                                                    .forEach(
                                                                                            st -> {
                                                                                                addPosTrade(
                                                                                                        assetSummary,
                                                                                                        st,
                                                                                                        currency,
                                                                                                        symbol.getCurrencyPair()
                                                                                                                .getBaseCurrency());
                                                                                            });
                                                                        }
                                                                    }
                                                                });
                                            }
                                        }

                                        if (currency.equals(Currency.JPY)) {
                                            // fiat_Withdrawalテーブルの情報を取得
                                            fiatWithdrawalService
                                                    .findByCondition(
                                                            asset.getUserId(),
                                                            null,
                                                            null,
                                                            targetDateFrom.getTime(),
                                                            targetDateTo.getTime(),
                                                            List.of(FiatWithdrawalStatus.DONE),
                                                            0,
                                                            Integer.MAX_VALUE)
                                                    .forEach(
                                                            w -> {
                                                                assetSummary.setWithdrawalAmount(
                                                                        assetSummary
                                                                                .getWithdrawalAmount()
                                                                                .add(
                                                                                        w
                                                                                                .getAmount()));
                                                                assetSummary.setWithdrawalAmountJpy(
                                                                        assetSummary
                                                                                .getWithdrawalAmountJpy()
                                                                                .add(
                                                                                        w
                                                                                                .getAmount()));
                                                                assetSummary.setWithdrawalFee(
                                                                        assetSummary
                                                                                .getWithdrawalFee()
                                                                                .add(w.getFee()));
                                                                assetSummary.setWithdrawalFeeJpy(
                                                                        assetSummary
                                                                                .getWithdrawalFeeJpy()
                                                                                .add(w.getFee()));
                                                            });

                                            // fiat_depositテーブルの情報を取得
                                            List<FiatDepositStatus> fiatDepositStatusList =
                                                    new ArrayList<>();
                                            fiatDepositStatusList.add(FiatDepositStatus.DONE);
                                            fiatDepositService
                                                    .findByCondition(
                                                            asset.getUserId(),
                                                            null,
                                                            null,
                                                            targetDateFrom.getTime(),
                                                            targetDateTo.getTime(),
                                                            fiatDepositStatusList,
                                                            0,
                                                            Integer.MAX_VALUE)
                                                    .forEach(
                                                            d -> {
                                                                assetSummary.setDepositAmount(
                                                                        assetSummary
                                                                                .getDepositAmount()
                                                                                .add(
                                                                                        d
                                                                                                .getAmount()));
                                                                assetSummary.setDepositAmountJpy(
                                                                        assetSummary
                                                                                .getDepositAmountJpy()
                                                                                .add(
                                                                                        d
                                                                                                .getAmount()));
                                                                assetSummary.setDepositFee(
                                                                        assetSummary
                                                                                .getDepositFee()
                                                                                .add(d.getFee()));
                                                                assetSummary.setDepositFeeJpy(
                                                                        assetSummary
                                                                                .getDepositFeeJpy()
                                                                                .add(d.getFee()));
                                                            });
                                        }
                                        // 昨日のamountに集計結果を足し引きすることで当日終了時のamountを計算
                                        // withdrawal.amountは手数料も含まれた額のため、手数料はここでは計算の対象外
                                        assetSummary.setCurrentAmount(
                                                yesterdayAmount
                                                        .add(assetSummary.getDepositAmount())
                                                        .subtract(assetSummary.getDepositFee())
                                                        .subtract(
                                                                assetSummary.getWithdrawalAmount())
                                                        .add(assetSummary.getPosTradeBuyAmount())
                                                        .subtract(
                                                                assetSummary
                                                                        .getPosTradeSellAmount())
                                                        .subtract(assetSummary.getPosTradeFee())
                                                        .add(
                                                                assetSummary
                                                                        .getExpirationContinueReward())
                                                        .add(
                                                                assetSummary
                                                                        .getExpirationNotContinueReward()));
                                        profitList.add(assetSummary);
                                    });
                });

        log.info("AssetSummaryLog,profitList,BeforeSave");
        // 集計データ保存
        profitList.forEach(this::save);
    }

    public void calculate(String targetDay, TradeType tradeType) throws CustomException {
        // 日付チェックとtargetAtの設定
        // targetAtは対象日の00:00を示す
        Date targetAt = parseTargetDate(targetDay);
        // 集計の基準となる日付
        Date targetDateFrom = new Date(targetAt.getTime());
        Date targetDateTo = new Date(targetAt.getTime() + TimeUnit.DAYS.toMillis(1));
        log.info(
                "AssetSummaryLog,targetFrom,{},targetTo,{}",
                DateFormatUtils.format(targetDateFrom, "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(targetDateTo, "yyyy-MM-dd HH:mm:ss"));
        // 集計対象の取引データを取得"), targetDateTo);
        // 集計対象通貨の一覧を得るため、symbolテーブルを確認
        // 通貨ペアリスト取得
        List<Symbol> symbolList = getSymbolList(tradeType);
        List<Currency> currencyList = getActiveCurrencyList(tradeType);
        Map<Currency, List<Symbol>> currencySymbolMap = createCurrencySymbolMap(symbolList);

        // userIdをキー、 Assetが存在するCurrencyのListをバリューとするMapを作成
        Map<Long, List<Currency>> userAssetCurrencyMap =
                getUserAssetCurrencyMap(
                        TradeType.INVEST.equals(tradeType)
                                ? UserIdType.Invest
                                : UserIdType.Operate);
        initializeUserAssets(tradeType, userAssetCurrencyMap, currencyList);
        log.info("userAssetCurrencyMap:{}", JsonUtil.encode(userAssetCurrencyMap));
        // 前日の資産サマリーデータを取得
        Map<Currency, Map<Long, List<AssetSummary>>> assetSummaryMap =
                getYesterdayAssetSummaryMap(currencyList, targetDateFrom);
        log.info("assetSummaryMap:{}", JsonUtil.encode(assetSummaryMap));
        // 販売所約定履歴マップ作成
        // master
        Map<Long, Map<Long, List<PosTrade>>> posTradeMap =
                getPosTradeMap(symbolList, targetDateFrom, targetDateTo);
        log.info("posTradeMap:{}", JsonUtil.encode(posTradeMap));
        // history
        Map<Long, Map<Long, List<PosTrade>>> posTradeMapHistory =
                getPosTradeHistoryMap(symbolList, targetDateFrom, targetDateTo);
        log.info("posTradeMapHistory:{}", JsonUtil.encode(posTradeMapHistory));
        // candlestick
        Map<Currency, BigDecimal> candlestickMap =
                getCandlestickMap(currencyList, targetDay, tradeType);
        log.info("candlestickMap:{}", JsonUtil.encode(candlestickMap));
        // 集計処理開始
        List<AssetSummary> profitList =
                calculateAssetSummaries(
                        currencyList,
                        currencySymbolMap,
                        assetSummaryMap,
                        posTradeMap,
                        posTradeMapHistory,
                        candlestickMap,
                        targetAt,
                        tradeType);
        // log.info("profitList:{}", JsonUtil.encode(profitList));
        // 集計データ保存
        saveAssetSummaries(profitList);
    }

    // --- ヘルパーメソッド ---

    /** 対象日付を解析する */
    private Date parseTargetDate(String targetDay) throws CustomException {
        Date targetAt = FormatUtil.parseJst(targetDay, FormatPattern.YYYYMMDD);
        if (targetAt == null) {
            log.error("invalid target date string: targetDay:{}", targetDay); // 無効な対象日付文字列
            throw new CustomException(ErrorCode.COMMON_ERROR_INVALID_DATE_STRING);
        }
        return targetAt;
    }

    /** 通貨ペアリストを取得する */
    private List<Symbol> getSymbolList(TradeType tradeType) {
        List<Symbol> symbolList = new ArrayList<>();
        currencyPairConfigService
                .findAllByCondition(tradeType, null)
                .forEach(
                        currencyPairConfig -> {
                            symbolList.add(
                                    symbolService.findByCondition(
                                            tradeType, currencyPairConfig.getCurrencyPair()));
                        });
        return symbolList;
    }

    /** 有効な通貨リストを取得する */
    private List<Currency> getActiveCurrencyList(TradeType tradeType) {
        List<Currency> currencyList = new ArrayList<>();
        currencyConfigService
                .findAllByCondition(tradeType, null, true)
                .forEach(
                        currencyConfig -> {
                            currencyList.add(currencyConfig.getCurrency());
                        });
        return currencyList;
    }

    /** ユーザーの資産通貨マップを取得する */
    private Map<Long, List<Currency>> getUserAssetCurrencyMap(UserIdType userIdType) {
        Map<Long, List<Currency>> userAssetCurrencyMap = new TreeMap<>();
        assetService.findAll().stream()
                .filter(o -> o.getUserIdentity().getIdType().equals(userIdType))
                .forEach(
                        asset -> {
                            userAssetCurrencyMap
                                    .computeIfAbsent(asset.getUserId(), k -> new ArrayList<>())
                                    .add(asset.getCurrency());
                        });
        return userAssetCurrencyMap;
    }

    /** ユーザーの資産を初期化する */
    private void initializeUserAssets(
            TradeType tradeType,
            Map<Long, List<Currency>> userAssetCurrencyMap,
            List<Currency> currencyList) {
        Consumer<Long> initializeAssets =
                userId -> {
                    List<Currency> currencies = userAssetCurrencyMap.get(userId);
                    currencyList.forEach(
                            currency -> {
                                if (currencies == null || !currencies.contains(currency)) {
                                    Asset createdAsset = new Asset();
                                    createdAsset.setUserId(userId);
                                    createdAsset.setCurrency(currency);
                                    createdAsset.setOnhandAmount(BigDecimal.ZERO);
                                    createdAsset.setLockedAmount(BigDecimal.ZERO);
                                    assetService.save(createdAsset);
                                }
                            });
                };

        if (TradeType.INVEST.equals(tradeType)) {
            userService.findAll().forEach(user -> initializeAssets.accept(user.getId()));
        } else if (TradeType.OPERATE.equals(tradeType)) {
            pointUserService.findAll().forEach(user -> initializeAssets.accept(user.getId()));
        }
    }

    /** 前日の資産サマリーデータを取得する */
    private Map<Currency, Map<Long, List<AssetSummary>>> getYesterdayAssetSummaryMap(
            List<Currency> currencyList, Date targetDateFrom) {
        Map<Currency, Map<Long, List<AssetSummary>>> assetSummaryMap = new HashMap<>();
        Date oneDayAgo = new Date(targetDateFrom.getTime() - TimeUnit.DAYS.toMillis(1));
        currencyList.forEach(
                currency -> {
                    List<AssetSummary> yesterdaySummary =
                            findByCondition(null, currency, oneDayAgo, targetDateFrom);
                    Map<Long, List<AssetSummary>> assetSummaryByUserId =
                            yesterdaySummary.stream()
                                    .collect(Collectors.groupingBy(AssetSummary::getUserId));
                    assetSummaryMap.put(currency, assetSummaryByUserId);
                });
        return assetSummaryMap;
    }

    /** 取引データを取得する */
    private Map<Long, Map<Long, List<PosTrade>>> getPosTradeMap(
            List<Symbol> symbolList, Date targetDateFrom, Date targetDateTo) {
        Map<Long, Map<Long, List<PosTrade>>> posTradeMap = new HashMap<>();
        symbolList.forEach(
                symbol -> {
                    List<PosTrade> posTradeList =
                            posTradeService.findAllByCondition(
                                    symbol.getId(),
                                    null,
                                    null,
                                    null,
                                    null,
                                    targetDateFrom.getTime(),
                                    targetDateTo.getTime(),
                                    null,
                                    null,
                                    null);
                    posTradeList.stream()
                            .collect(Collectors.groupingBy(PosTrade::getSymbolId))
                            .forEach(
                                    (symbolId, trades) -> {
                                        posTradeMap.put(
                                                symbolId,
                                                trades.stream()
                                                        .collect(
                                                                Collectors.groupingBy(
                                                                        PosTrade::getUserId)));
                                    });
                });
        return posTradeMap;
    }

    /** 履歴取引データを取得する */
    private Map<Long, Map<Long, List<PosTrade>>> getPosTradeHistoryMap(
            List<Symbol> symbolList, Date targetDateFrom, Date targetDateTo) {
        Map<Long, Map<Long, List<PosTrade>>> posTradeMapHistory = new HashMap<>();
        symbolList.forEach(
                symbol -> {
                    List<PosTrade> posTradeListHistory =
                            posTradeService.findFromPosTradeHistory(
                                    symbol,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    targetDateFrom,
                                    targetDateTo,
                                    0,
                                    Integer.MAX_VALUE);
                    posTradeListHistory.stream()
                            .collect(Collectors.groupingBy(PosTrade::getSymbolId))
                            .forEach(
                                    (symbolId, trades) -> {
                                        posTradeMapHistory.put(
                                                symbolId,
                                                trades.stream()
                                                        .collect(
                                                                Collectors.groupingBy(
                                                                        PosTrade::getUserId)));
                                    });
                });
        return posTradeMapHistory;
    }

    /** 通貨のJPY換算レートを取得する */
    private Map<Currency, BigDecimal> getCandlestickMap(
            List<Currency> currencyList, String targetDay, TradeType tradeType) {
        Map<Currency, BigDecimal> candlestickMap = new HashMap<>();
        currencyList.forEach(
                currency -> {
                    try {
                        BigDecimal candlestick =
                                findJpyConversionFromCandlestick(
                                        currency, targetDay, 120, tradeType);
                        candlestickMap.put(currency, candlestick);
                    } catch (CustomException e) {
                        log.error(
                                "Failed to get JPY conversion for currency: {}",
                                currency,
                                e); // JPY換算レートの取得に失敗
                        throw new RuntimeException(e);
                    }
                });
        return candlestickMap;
    }

    /** 資産サマリーを計算する */
    private List<AssetSummary> calculateAssetSummaries(
            List<Currency> currencyList,
            Map<Currency, List<Symbol>> currencySymbolMap,
            Map<Currency, Map<Long, List<AssetSummary>>> assetSummaryMap,
            Map<Long, Map<Long, List<PosTrade>>> posTradeMap,
            Map<Long, Map<Long, List<PosTrade>>> posTradeMapHistory,
            Map<Currency, BigDecimal> candlestickMap,
            Date targetAt,
            TradeType tradeType // TradeType を追加
            ) {
        List<AssetSummary> profitList = new ArrayList<>();
        currencyList.forEach(
                currency -> {
                    assetService
                            .findByCurrencyAndUserIdType(
                                    currency,
                                    TradeType.INVEST.equals(tradeType)
                                            ? UserIdType.Invest
                                            : UserIdType.Operate)
                            .forEach(
                                    asset -> {
                                        AssetSummary assetSummary =
                                                createAssetSummary(
                                                        asset,
                                                        currency,
                                                        targetAt,
                                                        candlestickMap.get(currency));
                                        log.info("assetSummary:{}", JsonUtil.encode(assetSummary));
                                        BigDecimal yesterdayAmount =
                                                getYesterdayAmount(
                                                        assetSummaryMap,
                                                        currency,
                                                        asset.getUserId());
                                        log.info(
                                                "userId:{}, yesterdayAmount:{}",
                                                yesterdayAmount,
                                                asset.getUserId());
                                        updateAssetSummaryWithTrades(
                                                assetSummary,
                                                currencySymbolMap,
                                                posTradeMap,
                                                posTradeMapHistory,
                                                currency,
                                                asset.getUserId(),
                                                tradeType); // TradeType を渡す
                                        if (currency.equals(Currency.JPY)) {
                                            updateAssetSummaryWithFiatTransactions(
                                                    assetSummary, asset.getUserId(), targetAt);
                                        }
                                        assetSummary.setCurrentAmount(
                                                calculateCurrentAmount(
                                                        yesterdayAmount, assetSummary));
                                        profitList.add(assetSummary);
                                    });
                });
        return profitList;
    }

    /** 資産サマリーデータを保存する */
    private void saveAssetSummaries(List<AssetSummary> profitList) {
        profitList.forEach(this::save);
    }

    /** AssetSummary オブジェクトを作成し、初期化する */
    private AssetSummary createAssetSummary(
            Asset asset, Currency currency, Date targetAt, BigDecimal jpyConversion) {
        AssetSummary assetSummary = newEntity(); // 新しい AssetSummary オブジェクトを作成
        assetSummary.setUserId(asset.getUserId()); // ユーザーIDを設定
        assetSummary.setCurrency(currency); // 通貨を設定
        assetSummary.setTargetAt(targetAt); // 対象日時を設定
        assetSummary.setJpyConversion(jpyConversion); // JPY換算レートを設定
        return assetSummary;
    }

    /** 前日の資産金額を取得する */
    private BigDecimal getYesterdayAmount(
            Map<Currency, Map<Long, List<AssetSummary>>> assetSummaryMap,
            Currency currency,
            Long userId) {
        if (assetSummaryMap.get(currency) == null
                || assetSummaryMap.get(currency).get(userId) == null
                || assetSummaryMap.get(currency).get(userId).isEmpty()) {
            return BigDecimal.ZERO; // 前日のデータがない場合は0を返す
        }
        return assetSummaryMap.get(currency).get(userId).get(0).getCurrentAmount(); // 前日の金額を返す
    }

    /** 取引データに基づいて資産サマリーを更新する */
    private void updateAssetSummaryWithTrades(
            AssetSummary assetSummary,
            Map<Currency, List<Symbol>> currencySymbolMap,
            Map<Long, Map<Long, List<PosTrade>>> posTradeMap,
            Map<Long, Map<Long, List<PosTrade>>> posTradeMapHistory,
            Currency currency,
            Long userId,
            TradeType tradeType // TradeType を追加
            ) {
        List<Symbol> posSymbolList = currencySymbolMap.get(currency);
        if (posSymbolList != null) {
            posSymbolList.forEach(
                    symbol -> {
                        if (symbol.getTradeType().equals(tradeType)) { // TradeType をチェック
                            // 現在の取引データを処理
                            if (posTradeMap.get(symbol.getId()) != null
                                    && posTradeMap.get(symbol.getId()).get(userId) != null) {
                                posTradeMap
                                        .get(symbol.getId())
                                        .get(userId)
                                        .forEach(
                                                trade -> {
                                                    addPosTrade(
                                                            assetSummary,
                                                            trade,
                                                            currency,
                                                            symbol.getCurrencyPair()
                                                                    .getBaseCurrency());
                                                });
                            }
                            // 履歴取引データを処理（対象日が1日前以前の場合）
                            Date yesterday =
                                    new DateTime().minusDays(1).withTime(0, 0, 0, 0).toDate();
                            if (assetSummary.getTargetAt().before(yesterday)) {
                                if (posTradeMapHistory.get(symbol.getId()) != null
                                        && posTradeMapHistory.get(symbol.getId()).get(userId)
                                                != null) {
                                    posTradeMapHistory
                                            .get(symbol.getId())
                                            .get(userId)
                                            .forEach(
                                                    trade -> {
                                                        addPosTrade(
                                                                assetSummary,
                                                                trade,
                                                                currency,
                                                                symbol.getCurrencyPair()
                                                                        .getBaseCurrency());
                                                    });
                                }
                            }
                        }
                    });
        }
    }

    /** 法幣取引データに基づいて資産サマリーを更新する（JPYの場合） */
    private void updateAssetSummaryWithFiatTransactions(
            AssetSummary assetSummary, Long userId, Date targetAt) {
        // 出金データを処理
        fiatWithdrawalService
                .findByCondition(
                        userId,
                        null,
                        null,
                        targetAt.getTime(),
                        targetAt.getTime() + TimeUnit.DAYS.toMillis(1),
                        List.of(FiatWithdrawalStatus.DONE),
                        0,
                        Integer.MAX_VALUE)
                .forEach(
                        withdrawal -> {
                            assetSummary.setWithdrawalAmount(
                                    assetSummary.getWithdrawalAmount().add(withdrawal.getAmount()));
                            assetSummary.setWithdrawalAmountJpy(
                                    assetSummary
                                            .getWithdrawalAmountJpy()
                                            .add(withdrawal.getAmount()));
                            assetSummary.setWithdrawalFee(
                                    assetSummary.getWithdrawalFee().add(withdrawal.getFee()));
                            assetSummary.setWithdrawalFeeJpy(
                                    assetSummary.getWithdrawalFeeJpy().add(withdrawal.getFee()));
                        });

        // 入金データを処理
        List<FiatDepositStatus> fiatDepositStatusList = List.of(FiatDepositStatus.DONE);
        fiatDepositService
                .findByCondition(
                        userId,
                        null,
                        null,
                        targetAt.getTime(),
                        targetAt.getTime() + TimeUnit.DAYS.toMillis(1),
                        fiatDepositStatusList,
                        0,
                        Integer.MAX_VALUE)
                .forEach(
                        deposit -> {
                            assetSummary.setDepositAmount(
                                    assetSummary.getDepositAmount().add(deposit.getAmount()));
                            assetSummary.setDepositAmountJpy(
                                    assetSummary.getDepositAmountJpy().add(deposit.getAmount()));
                            assetSummary.setDepositFee(
                                    assetSummary.getDepositFee().add(deposit.getFee()));
                            assetSummary.setDepositFeeJpy(
                                    assetSummary.getDepositFeeJpy().add(deposit.getFee()));
                        });
    }

    /** 現在の資産金額を計算する */
    private BigDecimal calculateCurrentAmount(
            BigDecimal yesterdayAmount, AssetSummary assetSummary) {
        // 昨日の金額に、入金金額、出金金額、買取引金額、売取引金額、取引手数料、継続報酬、非継続報酬を加算して返す
        BigDecimal currentAmount =
                yesterdayAmount
                        .add(assetSummary.getDepositAmount()) // 入金額を加算
                        .subtract(assetSummary.getDepositFee()) // 入金手数料を減算
                        .subtract(assetSummary.getWithdrawalAmount()) // 出金額を減算
                        .add(assetSummary.getPosTradeBuyAmount()) // 買取引金額を加算
                        .subtract(assetSummary.getPosTradeSellAmount()) // 売取引金額を減算
                        .subtract(assetSummary.getPosTradeFee()) // 取引手数料を減算
                        .add(assetSummary.getExpirationContinueReward()) // 継続報酬を加算
                        .add(assetSummary.getExpirationNotContinueReward()); // 非継続報酬を加算
        log.info(
                "AssetSummaryLog,currentAmount,{},AssetSummaryLog,assetSummary,{}",
                currentAmount,
                JsonUtil.encode(assetSummary));
        return currentAmount;
    }

    private Map<Currency, List<Symbol>> createCurrencySymbolMap(List<Symbol> Symbols) {
        Map<Currency, List<Symbol>> currencySymbolMap = new HashMap<Currency, List<Symbol>>();
        Symbols.forEach(
                s -> {
                    CurrencyPair currencyPair = s.getCurrencyPair();
                    Currency baseCurrency = currencyPair.getBaseCurrency();
                    if (currencySymbolMap.containsKey(baseCurrency)) {
                        currencySymbolMap.get(baseCurrency).add(s);
                    } else {
                        currencySymbolMap.put(baseCurrency, new ArrayList<>(List.of(s)));
                    }
                    Currency quoteCurrency = currencyPair.getQuoteCurrency();
                    if (currencySymbolMap.containsKey(quoteCurrency)) {
                        currencySymbolMap.get(quoteCurrency).add(s);
                    } else {
                        currencySymbolMap.put(quoteCurrency, new ArrayList<>(List.of(s)));
                    }
                });
        return currencySymbolMap;
    }

    private void addPosTrade(
            AssetSummary assetSummary, PosTrade pt, Currency currency, Currency baseCurrency) {
        if (currency == baseCurrency) {
            if (pt.getOrderSide() == OrderSide.BUY) {
                // 買い注文の場合はBuyのCurrencyが増える
                assetSummary.setPosTradeBuyAmount(
                        assetSummary.getPosTradeBuyAmount().add(pt.getAmount()));
                assetSummary.setPosTradeBuyAmountJpy(
                        assetSummary
                                .getPosTradeBuyAmountJpy()
                                .add(pt.getAssetAmount().multiply(pt.getJpyConversion())));
            } else {
                // 売り注文の場合はSellのCurrencyが増える
                assetSummary.setPosTradeSellAmount(
                        assetSummary.getPosTradeSellAmount().add(pt.getAmount()));
                assetSummary.setPosTradeSellAmountJpy(
                        assetSummary
                                .getPosTradeSellAmountJpy()
                                .add(pt.getAssetAmount().multiply(pt.getJpyConversion())));
            }
        } else {
            // quote currencyの場合は、baseと売り買いが逆になり、assetAmountになる
            if (pt.getOrderSide() == OrderSide.BUY) {
                assetSummary.setPosTradeSellAmount(
                        assetSummary.getPosTradeSellAmount().add(pt.getAssetAmount()));
                assetSummary.setPosTradeSellAmountJpy(
                        assetSummary
                                .getPosTradeSellAmountJpy()
                                .add(pt.getAssetAmount().multiply(pt.getJpyConversion())));

            } else {
                assetSummary.setPosTradeBuyAmount(
                        assetSummary.getPosTradeBuyAmount().add(pt.getAssetAmount()));
                assetSummary.setPosTradeBuyAmountJpy(
                        assetSummary
                                .getPosTradeBuyAmountJpy()
                                .add(pt.getAssetAmount().multiply(pt.getJpyConversion())));
            }
            assetSummary.setPosTradeFee(assetSummary.getPosTradeFee().add(pt.getFee()));
            assetSummary.setPosTradeFeeJpy(
                    assetSummary
                            .getPosTradeFeeJpy()
                            .add(pt.getFee().multiply(pt.getJpyConversion())));
        }
    }

    public List<AssetSummary> findByCondition(
            Long userId, Currency currency, Date targetFrom, Date targetTo) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<AssetSummary, List<AssetSummary>>() {
                    @Override
                    public List<AssetSummary> query() {
                        List<Predicate> predicates =
                                getPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        userId,
                                        currency,
                                        targetFrom,
                                        targetTo);
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(AssetSummary_.id)));
                    }
                });
    }

    public PageData<AssetSummary> findByConditionPageData(
            Long userId,
            Currency currency,
            Date targetAtFrom,
            Date targetAtTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                currency,
                                                targetAtFrom,
                                                targetAtTo));
                            }
                        });

        return new PageData<AssetSummary>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<AssetSummary, List<AssetSummary>>() {
                            @Override
                            public List<AssetSummary> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                currency,
                                                targetAtFrom,
                                                targetAtTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(AssetSummary_.targetAt)),
                                        criteriaBuilder.desc(root.get(AssetSummary_.id)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<AssetSummary> root,
            Long userId,
            Currency currency,
            Date targetFrom,
            Date targetTo) {
        List<Predicate> predicates = new ArrayList<>();
        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }
        if (currency != null) {
            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
        }
        if (targetFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, targetFrom));
        }
        if (targetTo != null) {
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetTo));
        }

        return predicates;
    }

    @Override
    public Class<AssetSummary> getEntityClass() {
        return AssetSummary.class;
    }

    /*
     * candleStickを元に日本円のレートを取得する。
     */
    public BigDecimal findJpyConversionFromCandlestick(
            Currency currency, String targetDay, int widthSecond, TradeType tradeType)
            throws CustomException {
        BigDecimal result = null;
        List<Candlestick> candlestickList = null;
        List<PosCandlestick> posCandlesticks = null;
        // 通貨が円の場合、レートは常に1
        if (currency == Currency.JPY || currency == Currency.POINT) {
            result = BigDecimal.ONE;
        } else {
            Date dateFrom = FormatUtil.parseJst(targetDay + "235800", FormatPattern.YYYYMMDDHHMMSS);
            Date dateTo = FormatUtil.parseJst(targetDay + "235959", FormatPattern.YYYYMMDDHHMMSS);

            List<CurrencyPairConfig> currencyPairConfig =
                    currencyPairConfigService.findAllByCondition(
                            tradeType, CurrencyPair.valueOf(currency, Currency.JPY), true);
            if (CollectionUtils.isEmpty(currencyPairConfig)) {
                if (currency == Currency.ETH) {
                    // ETH SYMBOL ID:3
                    long dateToForETH =
                            FormatUtil.parseJst(targetDay, FormatPattern.YYYYMMDD).getTime()
                                    + 3600 * 1000 * 24;
                    Candlestick latest =
                            posCandlestickService.findOneByCondition(
                                    3l, CandlestickType.P1D, null, dateToForETH, false);
                    if (latest == null) {
                        result = BigDecimal.ZERO;
                    } else {
                        result = latest.getClose();
                    }
                }
            } else {
                Symbol symbol =
                        symbolService.findByCondition(
                                tradeType, CurrencyPair.valueOf(currency, Currency.JPY));
                posCandlesticks =
                        posCandlestickService.findByCondition(
                                symbol.getId(), CandlestickType.PT1M, dateFrom, dateTo);
                if (posCandlesticks != null && !posCandlesticks.isEmpty()) {
                    result = posCandlesticks.get(posCandlesticks.size() - 1).getClose();
                } else {
                    result = BigDecimal.ZERO;
                }
            }
        }
        return result;
    }

    public Object[] sumAssetSummaryByCondition(
            Long userId, Currency currency, Date targetAtFrom, Date targetAtTo) {
        return customTransactionManager.sum(
                getEntityClass(),
                new QueryExecutorSum<AssetSummary>() {
                    @Override
                    public Object[] query() {

                        List<Predicate> predicates =
                                getPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        userId,
                                        currency,
                                        targetAtFrom,
                                        targetAtTo);

                        return sumAssetSummary(
                                entityManager, criteriaBuilder, criteriaQuery, root, predicates);
                    }
                });
    }

    private Object[] sumAssetSummary(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Object[]> criteriaQuery,
            Root<AssetSummary> root,
            List<Predicate> predicates) {

        /** group by */
        criteriaQuery.groupBy(root.get(AssetSummary_.currency));

        criteriaQuery.multiselect(
                criteriaBuilder.sum(root.get(AssetSummary_.depositAmount)), // INDEX:0
                criteriaBuilder.sum(root.get(AssetSummary_.depositAmountJpy)), // INDEX:1
                criteriaBuilder.sum(root.get(AssetSummary_.depositFee)), // INDEX:2
                criteriaBuilder.sum(root.get(AssetSummary_.depositFeeJpy)), // INDEX:3
                criteriaBuilder.sum(root.get(AssetSummary_.withdrawalAmount)), // INDEX:4
                criteriaBuilder.sum(root.get(AssetSummary_.withdrawalAmountJpy)), // INDEX:5
                criteriaBuilder.sum(root.get(AssetSummary_.withdrawalFee)), // INDEX:6
                criteriaBuilder.sum(root.get(AssetSummary_.withdrawalFeeJpy)), // INDEX:7
                criteriaBuilder.sum(root.get(AssetSummary_.posTradeBuyAmount)), // INDEX:14
                criteriaBuilder.sum(root.get(AssetSummary_.posTradeBuyAmountJpy)), // INDEX:15
                criteriaBuilder.sum(root.get(AssetSummary_.posTradeSellAmount)), // INDEX:16
                criteriaBuilder.sum(root.get(AssetSummary_.posTradeSellAmountJpy)), // INDEX:17
                criteriaBuilder.sum(root.get(AssetSummary_.posTradeFee)), // INDEX:18
                criteriaBuilder.sum(root.get(AssetSummary_.posTradeFeeJpy)), // INDEX:19
                criteriaBuilder.sum(
                        criteriaBuilder.sum(
                                root.get(AssetSummary_.expirationNotContinueReward),
                                root.get(AssetSummary_.expirationContinueReward))), // INDEX:20
                criteriaBuilder.sum(
                        criteriaBuilder.prod(
                                criteriaBuilder.sum(
                                        root.get(AssetSummary_.expirationNotContinueReward),
                                        root.get(AssetSummary_.expirationContinueReward)),
                                root.get(AssetSummary_.jpyConversion))) // INDEX:21
                );

        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        try {
            return entityManager
                    .createQuery(criteriaQuery)
                    .setFirstResult(0)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    public Long countByCondition(
            Long userId, Currency currency, Date dateFromDate, Date dateToDate) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                currency,
                                                dateFromDate,
                                                dateToDate));
                            }
                        });
        return count;
    }
}
