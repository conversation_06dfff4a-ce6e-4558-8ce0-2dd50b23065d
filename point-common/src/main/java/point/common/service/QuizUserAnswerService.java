package point.common.service;

import java.time.*;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.constant.*;
import point.common.entity.*;
import point.common.predicate.QuizUserAnswerPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class QuizUserAnswerService extends EntityService<QuizUserAnswer, QuizUserAnswerPredicate> {

    @Override
    public Class<QuizUserAnswer> getEntityClass() {
        return QuizUserAnswer.class;
    }
}
