package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.MfaType;
import point.common.entity.UserMfa;
import point.common.predicate.UserMfaPredicate;

@Service
public class UserMfaService extends EntityService<UserMfa, UserMfaPredicate> {

    @Override
    public Class<UserMfa> getEntityClass() {
        return UserMfa.class;
    }

    public MfaType getMfaType(Long userId) {
        MfaType mfaType = MfaType.EMAIL;

        for (UserMfa userMfa : findByCondition(userId)) {
            if (userMfa.getMfaType() == MfaType.GOOGLE && userMfa.isAuthenticated()) {
                mfaType = MfaType.GOOGLE;
                break;
            } else if (userMfa.getMfaType() == MfaType.SMS) {
                mfaType = MfaType.SMS;
            }
        }

        return mfaType;
    }

    public UserMfa findByCondition(Long userId, MfaType mfaType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserMfa, UserMfa>() {
                    @Override
                    public UserMfa query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(predicate.equalMfaType(criteriaBuilder, root, mfaType));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<UserMfa> findByCondition(Long userId, Integer number, Integer size) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserMfa, List<UserMfa>>() {
                    @Override
                    public List<UserMfa> query() {
                        List<Predicate> predicates = new ArrayList<>();

                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }

                        return getResultList(
                                entityManager, criteriaQuery, root, predicates, number, size);
                    }
                });
    }

    public List<UserMfa> findByCondition(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserMfa, List<UserMfa>>() {
                    @Override
                    public List<UserMfa> query() {
                        List<Predicate> predicates = new ArrayList<>();

                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }

                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
