package point.common.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import point.common.component.RedisManager;
import point.common.constant.*;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.ponta.PontaRequestUtil;
import point.common.predicate.PointUserPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PontaUserCallbackService extends EntityService<PointUser, PointUserPredicate> {

    private final UserAgreementService userAgreementService;
    private final PontaRequestUtil pontaRequestUtil;
    private final PointUserService pointUserService;
    private final PointPartnerService pointPartnerService;
    private final PointUserPartnerCredentialService pointUserPartnerCredentialService;
    private final RedisManager redisManager;

    private static final String REDIS_KEY_PREFIX_FOR_USER_CALLBACK = "operate:user:callback:";
    private static final int RETRY_DELAY_MILLIS = 500; // ms
    private static final int REDIS_KEY_TIME_TO_LIVE_SECONDS = 30; // seconds

    @Override
    public Class<PointUser> getEntityClass() {
        return PointUser.class;
    }

    public Map<String, String> getPartnerMemberId(String secretCode) throws CustomException {
        // Log the start of the process
        log.info("Starting getPartnerMemberId for secretCode: {}", secretCode);

        String redisKey = REDIS_KEY_PREFIX_FOR_USER_CALLBACK + secretCode;
        Map<String, String> result = this.getCallbackInfoFromRedis(redisKey);
        if (!result.isEmpty()) {
            return result;
        }

        log.info(
                "AccessToken not found in Redis for secretCode: {}, ready to call ponta API",
                secretCode);
        try {
            String accessToken = pontaRequestUtil.if1507(secretCode);
            String memberId = pontaRequestUtil.if1511(accessToken);
            result.put(CommonConstants.ACCESS_TOKEN, accessToken);
            result.put(CommonConstants.PONTA_MEMBER_ID, memberId);
            redisManager.hset(redisKey, result, REDIS_KEY_TIME_TO_LIVE_SECONDS);
            return result;
        } catch (CustomException e) {
            if (ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_ACCESS_TOKEN_FAILED.equals(
                            e.getErrorCode())
                    || ErrorCode.PONTA_AUTHENTICATION_ERROR_GET_MEMBER_ID_FAILED.equals(
                            e.getErrorCode())) {
                return this.retryFromRedisWithCompletableFuture(redisKey, e);
            }
            throw e;
        }
    }

    public PointPartner getWithValidatePointPartner(String partnerNumber, String source)
            throws CustomException {
        PointPartner pointPartner = pointPartnerService.findByPartnerNumber(partnerNumber);
        if (Objects.isNull(pointPartner)) {
            log.info("Cannot find PointPartner by partnerNumber: {}", partnerNumber);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_INVALID_PARTNER_NUMBER);
        }

        if (!pointPartner.isActive()) {
            log.info(
                    "The PointPartner status is not ACTIVE, status: {}, partnerNumber: {}",
                    PointPartnerStatus.ACTIVE,
                    partnerNumber);
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_INVALID_PARTNER_NUMBER);
        }

        if (!pointPartner.isValidate()) {
            log.info(
                    "The date of PointPartner is not valid, partnerNumber: {}, effectiveDate: {}, expiryDate: {}",
                    partnerNumber,
                    pointPartner.getEffectiveDate(),
                    pointPartner.getExpiryDate());
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_INVALID_PARTNER_NUMBER);
        }

        if ((!StringUtils.hasLength(source) || CommonConstants.SOURCE_OPERATE.equals(source))
                && !pointPartner.isAllowOperate()) {
            log.info(
                    "The partner is not allow to login(operate), partnerNumber: {} scope: {}",
                    pointPartner.getPartnerNumber(),
                    pointPartner.getScope());
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_SCOPE_NOT_ENOUGH);
        }

        if (StringUtils.hasLength(source)
                && CommonConstants.SOURCE_INVEST.equals(source)
                && !pointPartner.isAllowInvest()) {
            log.info(
                    "The partner is not allow to login(invest), partnerNumber: {} scope: {}",
                    pointPartner.getPartnerNumber(),
                    pointPartner.getScope());
            throw new CustomException(ErrorCode.PONTA_AUTHENTICATION_ERROR_SCOPE_NOT_ENOUGH);
        }
        return pointPartner;
    }

    public PointUser createIfNotExists(
            String partnerMemberId, Long pointPartnerId, String accessToken) throws Exception {
        return customTransactionManager.execute(
                entityManager -> {
                    // Create a PointUser(Operate)
                    PointUser pointUser =
                            pointUserService.save(partnerMemberId, pointPartnerId, entityManager);
                    this.updateOrCreatePointUserPartnerCredential(
                            pointUser.getId(), accessToken, entityManager);

                    // User agreement handle
                    userAgreementService.acceptAgreementsFirstTimeOnly(
                            pointUser.getId(),
                            List.of(UserAgreementType.TERMS_OF_SERVICE_FOR_OPERATE),
                            entityManager);
                    return pointUser;
                });
    }

    public void updateOrCreatePointUserPartnerCredential(
            Long userId, String accessToken, EntityManager entityManager) throws Exception {
        PointUserPartnerCredential pointUserPartnerCredential =
                pointUserPartnerCredentialService.findByCondition(userId);
        if (Objects.isNull(pointUserPartnerCredential)) {
            pointUserPartnerCredential =
                    PointUserPartnerCredential.builder()
                            .userId(userId)
                            .accessToken(accessToken)
                            .refreshToken(accessToken)
                            .build();
        } else {
            pointUserPartnerCredential.setAccessToken(accessToken);
            pointUserPartnerCredential.setRefreshToken(accessToken);
        }
        pointUserPartnerCredentialService.save(pointUserPartnerCredential, entityManager);
    }

    /**
     * Get accessToken and memberId from Redis.
     *
     * @param redisKey The Redis key.
     * @return A map containing accessToken and memberId, or an empty map if not found.
     */
    private Map<String, String> getCallbackInfoFromRedis(String redisKey) {
        String accessToken = redisManager.hget(redisKey, CommonConstants.ACCESS_TOKEN);
        String memberId = redisManager.hget(redisKey, CommonConstants.PONTA_MEMBER_ID);
        Map<String, String> map = new HashMap<>(2);
        if (StringUtils.hasLength(accessToken) && StringUtils.hasLength(memberId)) {
            map.put(CommonConstants.ACCESS_TOKEN, accessToken);
            map.put(CommonConstants.PONTA_MEMBER_ID, memberId);
        }
        return map;
    }

    /**
     * Retry getting accessToken and memberId from Redis using CompletableFuture.
     *
     * @param redisKey The Redis key.
     * @param e The original exception.
     * @return A map containing accessToken and memberId.
     * @throws CustomException If failed to get data from Redis.
     */
    private Map<String, String> retryFromRedisWithCompletableFuture(
            String redisKey, CustomException e) throws CustomException {
        log.info("Retrying to get accessToken and memberId from Redis: {}", redisKey);
        // Create a CompletableFuture for retry
        CompletableFuture<Map<String, String>> future =
                CompletableFuture.supplyAsync(() -> this.getCallbackInfoFromRedis(redisKey))
                        .thenApplyAsync(
                                result -> {
                                    if (result.isEmpty()) {
                                        throw new CompletionException(
                                                e); // Wrap the original exception
                                    }
                                    return result;
                                },
                                CompletableFuture.delayedExecutor(
                                        RETRY_DELAY_MILLIS,
                                        TimeUnit.MILLISECONDS)); // Delay retry by 100ms

        try {
            return future.get(); // Wait for the result
        } catch (InterruptedException | ExecutionException ex) {
            log.error("Failed to retry from Redis for get accessToken and memberId", ex);
            throw e; // Re-throw the original exception
        }
    }
}
