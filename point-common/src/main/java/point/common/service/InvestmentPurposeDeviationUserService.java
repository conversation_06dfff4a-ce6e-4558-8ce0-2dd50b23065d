package point.common.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ErrorCode;
import point.common.constant.TradeType;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.InvestmentPurposeDeviationUser;
import point.common.entity.InvestmentPurposeDeviationUser_;
import point.common.entity.Symbol;
import point.common.entity.SystemConfig;
import point.common.entity.User;
import point.common.entity.UserInfo;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.InvestmentPurposeDeviationUserPredicate;
import point.common.util.DateUnit;
import point.common.util.JsonUtil;
import point.pos.service.PosTradeService;

@RequiredArgsConstructor
@Service
@Slf4j
public class InvestmentPurposeDeviationUserService
        extends EntityService<
                InvestmentPurposeDeviationUser, InvestmentPurposeDeviationUserPredicate> {

    private final CurrencyPairConfigService currencyPairConfigService;
    private final SymbolService symbolService;
    private final SystemConfigService systemConfigService;
    private final UserInfoService userInfoService;
    private final UserService userService;

    private final int MAX_CHECK_SPAN_HOURS = 40;

    private int CHECK_SPAN_HOURS = 0;
    private int TRADES_THRESHOLD = 0;
    private List<Integer> INVESTMENT_PURPOSE = null;

    private String dbtradeType;
    private final PosTradeService posTradeService;

    @Override
    public Class<InvestmentPurposeDeviationUser> getEntityClass() {
        return InvestmentPurposeDeviationUser.class;
    }

    /*
     * 【運用】predicates作成メソッドは共通化して使用する
     * インデックス順でaddする
     */
    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<InvestmentPurposeDeviationUser> root,
            Long userId,
            Date dateFrom,
            Date dateTo) {
        List<Predicate> predicates = new ArrayList<>();

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, dateFrom));
        }

        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, dateTo));
        }

        return predicates;
    }

    public PageData<InvestmentPurposeDeviationUser> findByCondition(
            Long userId, Date dateFrom, Date dateTo, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicates(
                                                criteriaBuilder, root, userId, dateFrom, dateTo));
                            }
                        });

        return new PageData<InvestmentPurposeDeviationUser>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                InvestmentPurposeDeviationUser,
                                List<InvestmentPurposeDeviationUser>>() {
                            @Override
                            public List<InvestmentPurposeDeviationUser> query() {
                                List<Predicate> predicates =
                                        createPredicates(
                                                criteriaBuilder, root, userId, dateFrom, dateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(
                                                root.get(InvestmentPurposeDeviationUser_.id)));
                            }
                        }));
    }

    public List<InvestmentPurposeDeviationUser> findByCondition(
            Long userId, Date dateFrom, Date dateTo, boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<
                        InvestmentPurposeDeviationUser, List<InvestmentPurposeDeviationUser>>() {
                    @Override
                    public List<InvestmentPurposeDeviationUser> query() {
                        List<Predicate> predicates =
                                createPredicates(criteriaBuilder, root, userId, dateFrom, dateTo);
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                isAscending
                                        ? criteriaBuilder.asc(
                                                root.get(InvestmentPurposeDeviationUser_.id))
                                        : criteriaBuilder.desc(
                                                root.get(InvestmentPurposeDeviationUser_.id)));
                    }
                });
    }

    private void save(long userId, int trades, String dbtradeType, EntityManager entityManager)
            throws Exception {
        InvestmentPurposeDeviationUser investmentPurposeDeviationUser =
                new InvestmentPurposeDeviationUser();
        investmentPurposeDeviationUser.setUserId(userId);
        investmentPurposeDeviationUser.setTrades(trades);
        investmentPurposeDeviationUser.setTradeType(dbtradeType);
        save(investmentPurposeDeviationUser, entityManager);
    }

    private void getThreshholds() throws Exception {
        SystemConfig checkSpanHoursConfig =
                systemConfigService.findByCondition(
                        null, null, "INVESTMENT_PURPOSE_DEVIATION_CHECK_SPAN_HOURS");
        SystemConfig tradesThreshholdConfig =
                systemConfigService.findByCondition(
                        null, null, "INVESTMENT_PURPOSE_DEVIATION_TRADES_THRESHOLD");
        SystemConfig investmentPurposeConfig =
                systemConfigService.findByCondition(
                        null, null, "INVESTMENT_PURPOSE_DEVIATION_USER_INFO");

        if (checkSpanHoursConfig == null
                || tradesThreshholdConfig == null
                || investmentPurposeConfig == null) {
            log.error("InvestmentPurposeDeviationCheckerLog,system config not found");
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        // 検知対象の約定履歴の対象期間
        CHECK_SPAN_HOURS = Integer.parseInt(checkSpanHoursConfig.getValue());
        // 約定回数の閾値
        TRADES_THRESHOLD = Integer.parseInt(tradesThreshholdConfig.getValue());
        // 投資目的
        INVESTMENT_PURPOSE =
                Arrays.asList(investmentPurposeConfig.getValue().split(",")).stream()
                        .map(investmentPurpose -> Integer.parseInt(investmentPurpose))
                        .collect(Collectors.toList());
        ;

        log.info(
                "InvestmentPurposeDeviationCheckerLog,CHECK_SPAN_HOURS,"
                        + CHECK_SPAN_HOURS
                        + ",TRADES_THRESHOLD,"
                        + TRADES_THRESHOLD
                        + ",INVESTMENT_PURPOSE,"
                        + JsonUtil.encode(INVESTMENT_PURPOSE));

        // 取引履歴アーカイブを対象外とするため、MAX2日弱
        if (CHECK_SPAN_HOURS > MAX_CHECK_SPAN_HOURS) {
            log.error("InvestmentPurposeDeviationCheckerLog, CHECK_SPAN_HOURS should be shorter");
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }
    }

    // ユーザーID,約定件数のMapに追加(同一keyなら合算)
    private void addUserTradesMapFromObject(
            Map<Long, Integer> userTradesMap, List<Object[]> newUserTradeCountList) {
        for (Object[] newUserTradeCount : newUserTradeCountList) {
            if (userTradesMap.containsKey((Long) newUserTradeCount[0])) {
                // 同一ユーザーIDの場合は合算して置き換え
                userTradesMap.replace(
                        (Long) newUserTradeCount[0],
                        userTradesMap.get((Long) newUserTradeCount[0])
                                + Integer.parseInt(newUserTradeCount[1].toString()));
            } else {
                userTradesMap.put(
                        (Long) newUserTradeCount[0],
                        Integer.parseInt(newUserTradeCount[1].toString()));
            }
        }
        return;
    }

    private void countOverTradesPos(
            Symbol symbol,
            Map<Long, Integer> userTradesMap,
            List<Long> userListTradeUncapped,
            Long dateFrom) {
        // 直近{CHECK_SPAN_HOURS}の期間で、
        // 約定件数{TRADES_THRESHOLD}回以上のユーザーリスト作成
        System.out.println(DateFormatUtils.format(new Date(dateFrom), "yyyyMMddHHmmss"));
        List<Object[]> posTradeCountResult =
                posTradeService.countPosTradesByUserByCondition(
                        symbol.getId(), userListTradeUncapped, dateFrom, null);

        log.info(
                "DBMITO,InvestmentPurposeDeviationCheckerLog,symbolId,{},posTradeCountResult,{},trade_type,POS",
                symbol.getId(),
                JsonUtil.encode(posTradeCountResult));

        if (!CollectionUtils.isEmpty(posTradeCountResult)
                && posTradeCountResult.get(0)[0] != null) {
            // 全通貨ペアでユーザーID,閾値回数以上の約定件数Mapにマージ
            addUserTradesMapFromObject(userTradesMap, posTradeCountResult);
            log.info(
                    "DBMITO,InvestmentPurposeDeviationCheckerLog,symbolId,{},userTradesMap,{},trade_type,POS",
                    symbol.getId(),
                    JsonUtil.encode(userTradesMap));
        }
    }

    private List<Long> mergeUserList(List<UserInfo> userInfoList) {
        List<Long> resultList = new ArrayList<>();
        userInfoList.stream().forEach(list -> resultList.add(list.getUserId()));
        return resultList;
    }

    public List<Long> execute(TradeType tradeType, Long now, List<Long> posSavedUserId)
            throws Exception {

        String logParam;
        if (tradeType != null) {
            logParam = tradeType.toString();
            dbtradeType = tradeType.getName();
        } else {
            logParam = "INVEST";
            dbtradeType = "INVEST";
        }

        List<CurrencyPairConfig> currencyPairConfigs = null;
        if (tradeType != null) {
            /** 有効な通貨ペアリスト取得 */
            currencyPairConfigs =
                    currencyPairConfigService.findAllByCondition(tradeType, null, true);
        } else {
            // 販売所をチェックする
            currencyPairConfigs =
                    currencyPairConfigService.findAllByCondition(TradeType.INVEST, null, true);
        }

        if (CollectionUtils.isEmpty(currencyPairConfigs)) {
            log.warn(
                    "InvestmentPurposeDeviationCheckerLog,start,enabled currencyPairConfigs not found,trade_type,"
                            + logParam);
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        /** 閾値取得 */
        getThreshholds();

        Long dateFrom = now - DateUnit.HOUR.getMillis() * CHECK_SPAN_HOURS;
        log.info(
                "InvestmentPurposeDeviationCheckerLog,start,now,{},dateFrom,{},CHECK_SPAN_HOURS,{},tradeType,{}",
                new Date(now),
                new Date(dateFrom),
                CHECK_SPAN_HOURS,
                logParam);

        /** 運用口座取得(tradeUncappedで判定) */
        List<User> userListTradeUncapped = userService.findByTradeUncapped(true);
        List<Long> userIdListTradeUncapped = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userListTradeUncapped)) {
            userIdListTradeUncapped =
                    userListTradeUncapped.stream().map(User::getId).collect(Collectors.toList());
        }

        /** 判定①取引履歴 */
        // 直近{CHECK_SPAN_HOURS}の期間で、
        // 約定件数{TRADES_THRESHOLD}回以上のユーザーリスト作成
        Map<Long, Integer> userTradesMap = new HashMap<>(); // ユーザーID,約定件数Map

        for (CurrencyPairConfig currencyPairConfig : currencyPairConfigs) {
            TradeType tradeTypeUse = null;
            // typeの設定
            if (tradeType != null) {
                tradeTypeUse = tradeType;
            } else {
                tradeTypeUse = currencyPairConfig.getTradeType();
            }

            Symbol symbol =
                    symbolService.findByCondition(
                            tradeTypeUse, currencyPairConfig.getCurrencyPair());
            if (symbol == null) {
                throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
            }
            if (TradeType.INVEST.equals(tradeTypeUse)) {
                // 取引履歴集計(当日中。運用口座・アーカイブは対象外)
                countOverTradesPos(symbol, userTradesMap, userIdListTradeUncapped, dateFrom);
            }
        }

        List<Long> savedUserList = new ArrayList<>();
        // 全通貨ペア合算の一定期間の約定件数が{TRADES_THRESHOLD}回以上のユーザーList作成
        List<Long> userList = new ArrayList<>();
        userTradesMap.entrySet().stream()
                .filter(map -> map.getValue() >= TRADES_THRESHOLD)
                .forEach(map -> userList.add(map.getKey()));

        log.info(
                "DBMITO,InvestmentPurposeDeviationCheckerLog,userList,{},userTradeMap,{},tradeType,{}",
                JsonUtil.encode(userList),
                JsonUtil.encode(userTradesMap),
                logParam);

        if (CollectionUtils.isEmpty(userList)) {
            log.info(
                    "InvestmentPurposeDeviationCheckerLog,no user over threshhold,trade_type,{}",
                    logParam);
            return savedUserList;
        }

        /** 判定②投資目的 */
        // かつ、投資目的が{INVESTMENT_PURPOSE}に合致

        // 件数少ないとみなし、user_id in(ユーザーリスト)を使用する
        // ※テーブル登録無しの場合は対象外と判定
        // 個人とも判定する
        List<UserInfo> userInfoList =
                userInfoService.findByCondition(null, userList, null, INVESTMENT_PURPOSE, true);

        List<Long> deviatedUserList = mergeUserList(userInfoList);

        // 検知対象ユーザー・閾値以上約定件数のMap抽出
        Map<Long, Integer> resultMap =
                userTradesMap.entrySet().stream()
                        .filter(map -> deviatedUserList.contains(map.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 販売所に乖離しているユーザーを除く
        if (tradeType == null) {
            Map<Long, Integer> resultMapPos = new HashMap<Long, Integer>();
            for (Map.Entry<Long, Integer> result : resultMap.entrySet()) {
                if (!posSavedUserId.contains(result.getKey())) {
                    resultMapPos.put(result.getKey(), result.getValue());
                }
            }
            /** 検知データ保存 */
            customTransactionManager.execute(
                    entityManager -> {
                        for (Map.Entry<Long, Integer> result : resultMapPos.entrySet()) {
                            save(result.getKey(), result.getValue(), dbtradeType, entityManager);
                        }
                    });
        } else {
            /** 検知データ保存 */
            customTransactionManager.execute(
                    entityManager -> {
                        for (Map.Entry<Long, Integer> result : resultMap.entrySet()) {
                            savedUserList.add(result.getKey());
                            save(result.getKey(), result.getValue(), dbtradeType, entityManager);
                        }
                    });
        }

        /** 検知時通知 */
        log.info(
                "InvestmentPurposeDeviationCheckerLog,deviated users found,trade_type,{}",
                logParam);

        return savedUserList;
    }
}
