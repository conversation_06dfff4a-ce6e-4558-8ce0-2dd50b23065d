package point.common.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.AntisocialStatus;
import point.common.constant.CommonConstants;
import point.common.constant.Country;
import point.common.constant.ErrorCode;
import point.common.constant.KycStatus;
import point.common.constant.MfaType;
import point.common.constant.UserStatus;
import point.common.entity.User;
import point.common.entity.UserEkyc;
import point.common.entity.UserInfo;
import point.common.entity.UserInfo_;
import point.common.entity.UserKyc;
import point.common.entity.UserKycSub;
import point.common.entity.UserMfa;
import point.common.exception.CustomException;
import point.common.model.request.UpdateUserInfoForm;
import point.common.model.request.UserInfoForm;
import point.common.predicate.UserInfoPredicate;
import point.common.repos.UserInfoRepository;
import point.common.util.CharUtil;
import point.common.util.DateUnit;
import point.common.util.TransliteratorUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoService extends EntityService<UserInfo, UserInfoPredicate> {

    private final UserService userService;
    private final UserKycService userKycService;
    private final UserMfaService userMfaService;
    private final UserEkycService userEkycService;
    private final UserInfoRepository userInfoRepository;
    private final UserKycSubService userKycSubService;

    @Override
    public Class<UserInfo> getEntityClass() {
        return UserInfo.class;
    }

    /*
     * 【運用】predicates作成メソッドは共通化して使用する
     * インデックス順でaddする
     */
    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<UserInfo> root,
            Long userId,
            List<Long> userIds,
            Integer financialAssetsCode,
            List<Integer> investmentPurposes) {
        List<Predicate> predicates = new ArrayList<>();

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        } else {
            if (!CollectionUtils.isEmpty(userIds)) {
                predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
            }
        }

        if (financialAssetsCode != null) {
            predicates.add(
                    predicate.lessThanFinancialAssets(criteriaBuilder, root, financialAssetsCode));
        }

        if (!CollectionUtils.isEmpty(investmentPurposes)) {
            predicates.add(
                    predicate.inInvestmentPurposes(criteriaBuilder, root, investmentPurposes));
        }

        return predicates;
    }

    public List<UserInfo> findByCondition(
            Long userId,
            List<Long> userIds,
            Integer financialAssetsCode,
            List<Integer> investmentPurposes,
            boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserInfo, List<UserInfo>>() {
                    @Override
                    public List<UserInfo> query() {
                        List<Predicate> predicates =
                                createPredicates(
                                        criteriaBuilder,
                                        root,
                                        userId,
                                        userIds,
                                        financialAssetsCode,
                                        investmentPurposes);

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                isAscending
                                        ? criteriaBuilder.asc(root.get(UserInfo_.id))
                                        : criteriaBuilder.desc(root.get(UserInfo_.id)));
                    }
                });
    }

    public UserInfo findOneByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public UserInfo query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<UserInfo> findOneByPhoneNumber(String phoneNumber) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<UserInfo> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalPhoneNumber(criteriaBuilder, root, phoneNumber));
                        criteriaQuery.groupBy(root.get(UserInfo_.USER_ID));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public Long countByPhoneNumber(String phoneNumber) {
        return customTransactionManager.count(
                getEntityClass(),
                new QueryExecutorCounter<>() {
                    @Override
                    public Long query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                criteriaBuilder.equal(
                                        root.get(UserInfo_.PHONE_NUMBER), phoneNumber));
                        return count(
                                entityManager, criteriaBuilder, criteriaQuery, root, predicates);
                    }
                });
    }

    public void updateUserInfo(User user, UpdateUserInfoForm form) throws Exception {
        customTransactionManager.execute(
                entityManager -> {
                    UserInfo userInfoNew = new UserInfo(user.getId());
                    userInfoNew.setInsider(form.getInsider());
                    UserInfo userInfoOld = this.findOne(user.getUserInfoId());
                    String beforeUserInfo = getBeforeBasicUserInfo(form);
                    String afterUserInfo = getAfterBasicUserInfo(userInfoOld);
                    String beforeTransactionUserInfo = getBeforeTradeUserInfo(form);
                    String afterTransactionUserInfo = getAfterTradeUserInfo(userInfoOld);
                    if (!userInfoOld.getPhoneNumber().equals(form.getPhoneNumber())) {
                        log.info(
                                "The mobile phone number before the change is phoneNumber:{}",
                                userInfoOld.getPhoneNumber());
                        int countByPhoneNumber =
                                userInfoRepository.countPhoneNumber(form.getPhoneNumber());
                        if (countByPhoneNumber != 0) {
                            throw new CustomException(
                                    ErrorCode.REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS);
                        }
                        List<UserMfa> userMfaList = userMfaService.findByCondition(user.getId());
                        if (!CollectionUtils.isEmpty(userMfaList)) {
                            for (UserMfa userMfa : userMfaList) {
                                if (MfaType.SMS.equals(userMfa.getMfaType())) {
                                    log.info("update userMfa userId: {}", user.getId());
                                    userMfaService.delete(userMfa, entityManager);
                                }
                            }
                        }
                        userInfoNew.setPhoneNumber(form.getPhoneNumber());
                        log.info(
                                "The changed mobile phone number is phoneNumber: {}",
                                form.getPhoneNumber());
                    } else {
                        userInfoNew.setPhoneNumber(userInfoOld.getPhoneNumber());
                    }
                    if (!afterUserInfo.equals(beforeUserInfo)) {
                        log.info("ekyc applying userId: {}", user.getId());
                        // ekyc applying
                        this.save(setProperties(userInfoNew, form), entityManager);
                        UserKyc userKyc = new UserKyc(user.getId());
                        userKyc.setKycStatus(KycStatus.DOCUMENT_WAITING_APPLY);
                        userKyc.setOperator(CommonConstants.APP);
                        userKyc.setUserInfoId(userInfoNew.getId());
                        userKycService.save(userKyc, entityManager);
                        user.setKycStatus(KycStatus.DOCUMENT_WAITING_APPLY);
                        user.setInsider(form.getInsider());
                        user.setUserKycId(userKyc.getId());
                        user.setUserStatus(UserStatus.REVIEWING);
                        user.setUserInfoId(userInfoNew.getId());
                        userService.save(user, entityManager);
                    } else if (!afterTransactionUserInfo.equals(beforeTransactionUserInfo)
                            || user.isInsider() != form.getInsider()) {
                        log.info("update user kyc status is : {}", KycStatus.FIRST_SCREENING);
                        // one review
                        this.save(setProperties(userInfoNew, form), entityManager);
                        UserKyc userKyc = new UserKyc(user.getId());
                        userKyc.setAntisocialStatus(AntisocialStatus.OK);
                        userKyc.setKycStatus(KycStatus.FIRST_SCREENING);
                        userKyc.setOperator(CommonConstants.APP);
                        userKyc.setUserInfoId(userInfoNew.getId());
                        userKycService.save(userKyc, entityManager);
                        user.setKycStatus(KycStatus.FIRST_SCREENING);
                        user.setInsider(form.getInsider());
                        user.setUserKycId(userKyc.getId());
                        user.setUserStatus(UserStatus.REVIEWING);
                        user.setUserInfoId(userInfoNew.getId());
                        userService.save(user, entityManager);
                    } else if (!userInfoOld.getPhoneNumber().equals(form.getPhoneNumber())) {
                        log.info("ekyc applying userId: {}", user.getId());
                        // ekyc applying
                        this.save(setProperties(userInfoNew, form), entityManager);
                        UserKyc userKyc = new UserKyc(user.getId());
                        List<UserKyc> userKycList =
                                userKycService
                                        .findByUserId(user.getId())
                                        .orElseThrow(
                                                () ->
                                                        new CustomException(
                                                                ErrorCode
                                                                        .REQUEST_ERROR_INVALID_USER));
                        UserKyc latestUserKyc =
                                userKycList.stream()
                                        .sorted((u1, u2) -> u2.getId().compareTo(u1.getId()))
                                        .findFirst()
                                        .orElse(null);
                        // KYCステータス
                        userKyc.setKycStatus(latestUserKyc.getKycStatus());
                        userKyc.setOperator(CommonConstants.APP);
                        userKyc.setUserInfoId(userInfoNew.getId());
                        userKyc.setChangeType("02");
                        // 制裁チェック
                        userKyc.setAntisocialStatus(latestUserKyc.getAntisocialStatus());
                        // 承認/否認メール送信日時
                        userKyc.setMailSendAt(latestUserKyc.getMailSendAt());
                        // eKYC/ファイルアップロード
                        userKyc.setKycType(latestUserKyc.getKycType());
                        // メール送信状況
                        userKyc.setKycMailStatus(latestUserKyc.getKycMailStatus());
                        // 審査コメント
                        userKyc.setJudgingComment(latestUserKyc.getJudgingComment());
                        // AML/CFT用コメント
                        userKyc.setAmlCftComment(latestUserKyc.getAmlCftComment());
                        userKycService.save(userKyc, entityManager);
                        // 不備/謝絶理由
                        List<UserKycSub> userKycSubList =
                                userKycSubService.findByKycId(latestUserKyc.getId());
                        for (UserKycSub userKycSub : userKycSubList) {
                            UserKycSub userKycSubNew = new UserKycSub();
                            userKycSubNew.setKycSubStatus(userKycSub.getKycSubStatus());
                            userKycSubNew.setKycId(userKyc.getId());
                            userKycSubService.save(userKycSubNew);
                        }
                        user.setUserKycId(userKyc.getId());
                        user.setUserInfoId(userInfoNew.getId());
                        userService.save(user, entityManager);
                    }
                });
        if (KycStatus.DOCUMENT_WAITING_APPLY.equals(user.getKycStatus())) {
            UserEkyc userEkyc = userEkycService.getValidUserEkyc(user.getId());
            if (userEkyc != null) {
                Date createdAt =
                        Date.from(
                                LocalDateTime.now()
                                        .minusHours(4)
                                        .atZone(ZoneId.systemDefault())
                                        .toInstant());
                userEkyc.setCreatedAt(createdAt);
                userEkyc.setUpdatedAt(createdAt);
                userEkycService.save(userEkyc);
            }
            userEkycService.createEkyc(user.getId());
        }
    }

    private String getBeforeBasicUserInfo(UpdateUserInfoForm form) throws JsonProcessingException {
        Map<String, String> customerInfo = new HashMap<>();
        customerInfo.put("firstName", CharUtil.toDbc(form.getFirstName()));
        customerInfo.put("lastName", CharUtil.toDbc(form.getLastName()));
        customerInfo.put("firstKana", CharUtil.toDbc(form.getFirstKana()));
        customerInfo.put("lastKana", CharUtil.toDbc(form.getLastKana()));
        customerInfo.put("prefecture", form.getPrefecture());
        customerInfo.put("city", CharUtil.toDbc(form.getCity()));
        customerInfo.put("address", CharUtil.toDbc(form.getAddress()));
        customerInfo.put("zipCode", form.getZipCode());
        customerInfo.put("building", CharUtil.toDbc(form.getBuilding()));
        customerInfo.put("birthday", form.getBirthday());
        customerInfo.put("gender", form.getGender());
        customerInfo.put("nationality", form.getNationality());
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(customerInfo);
    }

    private String getAfterBasicUserInfo(UserInfo userInfo) throws JsonProcessingException {
        Map<String, String> customerInfo = new HashMap<>();
        customerInfo.put("firstName", CharUtil.toDbc(userInfo.getFirstName()));
        customerInfo.put("lastName", CharUtil.toDbc(userInfo.getLastName()));
        customerInfo.put("firstKana", CharUtil.toDbc(userInfo.getFirstKana()));
        customerInfo.put("lastKana", CharUtil.toDbc(userInfo.getLastKana()));
        customerInfo.put("prefecture", userInfo.getPrefecture());
        customerInfo.put("city", CharUtil.toDbc(userInfo.getCity()));
        customerInfo.put("address", CharUtil.toDbc(userInfo.getAddress()));
        customerInfo.put("zipCode", userInfo.getZipCode());
        customerInfo.put("building", CharUtil.toDbc(userInfo.getBuilding()));
        customerInfo.put("birthday", userInfo.getBirthday());
        customerInfo.put("gender", userInfo.getGender() + StringUtils.EMPTY);
        customerInfo.put("nationality", userInfo.getNationality());
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(customerInfo);
    }

    private String getBeforeTradeUserInfo(UserInfoForm form) throws JsonProcessingException {
        Map<String, Object> customerInfo = new HashMap<>();
        customerInfo.put("occupation", form.getOccupation());
        customerInfo.put("industry", form.getIndustry());
        customerInfo.put("workPlace", CharUtil.toDbc(form.getWorkPlace()));
        customerInfo.put("position", CharUtil.toDbc(form.getPosition()));
        customerInfo.put("priceFrom", CharUtil.toDbc(form.getPriceFrom()));
        customerInfo.put("income", form.getIncome());
        customerInfo.put("financialAssets", form.getFinancialAssets());
        customerInfo.put("purpose", form.getPurpose());
        customerInfo.put("investmentPurposes", form.getInvestmentPurposes());
        customerInfo.put("cryptoExperience", form.getCryptoExperience());
        customerInfo.put("fxExperience", form.getFxExperience());
        customerInfo.put("stocksExperience", form.getStocksExperience());
        customerInfo.put("fundExperience", form.getFundExperience());
        customerInfo.put("applicationHistory", form.getApplicationHistory());
        customerInfo.put(
                "applicationHistoryOther",
                StringUtils.isNotBlank(form.getApplicationHistoryOther())
                        ? CharUtil.toDbc(form.getApplicationHistoryOther())
                        : null);
        customerInfo.put("foreignPeps", form.getForeignPeps());
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(customerInfo);
    }

    private String getAfterTradeUserInfo(UserInfo userInfo) throws JsonProcessingException {
        Map<String, String> customerInfo = new HashMap<>();
        customerInfo.put("occupation", userInfo.getOccupation() + StringUtils.EMPTY);
        customerInfo.put(
                "industry",
                userInfo.getIndustry() == null ? null : String.valueOf(userInfo.getIndustry()));
        customerInfo.put("workPlace", CharUtil.toDbc(userInfo.getWorkPlace()));
        customerInfo.put("position", CharUtil.toDbc(userInfo.getPosition()));
        customerInfo.put(
                "priceFrom",
                userInfo.getPriceFrom() == null
                        ? StringUtils.EMPTY
                        : String.valueOf(userInfo.getPriceFrom()));
        customerInfo.put("income", userInfo.getIncome() + StringUtils.EMPTY);
        customerInfo.put("financialAssets", userInfo.getFinancialAssets() + StringUtils.EMPTY);
        customerInfo.put("purpose", userInfo.getPurpose() + StringUtils.EMPTY);
        customerInfo.put(
                "investmentPurposes", userInfo.getInvestmentPurposes() + StringUtils.EMPTY);
        customerInfo.put("cryptoExperience", userInfo.getCryptoExperience() + StringUtils.EMPTY);
        customerInfo.put("fxExperience", userInfo.getFxExperience() + StringUtils.EMPTY);
        customerInfo.put("stocksExperience", userInfo.getStocksExperience() + StringUtils.EMPTY);
        customerInfo.put("fundExperience", userInfo.getFundExperience() + StringUtils.EMPTY);
        customerInfo.put(
                "applicationHistory", userInfo.getApplicationHistory() + StringUtils.EMPTY);
        customerInfo.put(
                "applicationHistoryOther",
                StringUtils.isNotBlank(userInfo.getApplicationHistoryOther())
                        ? CharUtil.toDbc(userInfo.getApplicationHistoryOther())
                        : null);
        customerInfo.put("foreignPeps", userInfo.isForeignPeps() + StringUtils.EMPTY);
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(customerInfo);
    }

    private UserInfo setProperties(UserInfo userInfo, UpdateUserInfoForm form) throws Exception {
        userInfo.setFirstName(TransliteratorUtil.halfWidthToFullWidth(form.getFirstName()));
        userInfo.setLastName(TransliteratorUtil.halfWidthToFullWidth(form.getLastName()));
        userInfo.setFirstKana(TransliteratorUtil.halfWidthToFullWidth(form.getFirstKana()));
        userInfo.setLastKana(TransliteratorUtil.halfWidthToFullWidth(form.getLastKana()));
        userInfo.setZipCode(form.getZipCode());
        userInfo.setNationality(form.getNationality());
        userInfo.setPrefecture(form.getPrefecture());
        userInfo.setCity(TransliteratorUtil.halfWidthToFullWidth(form.getCity()));
        userInfo.setAddress(TransliteratorUtil.halfWidthToFullWidth(form.getAddress()));

        if (!StringUtils.isEmpty(form.getBuilding())) {
            userInfo.setBuilding(TransliteratorUtil.halfWidthToFullWidth(form.getBuilding()));
        }

        userInfo.setBirthday(form.getBirthday());
        userInfo.setGender(Integer.parseInt(form.getGender()));
        //    userInfo.setPhoneNumber(form.getPhoneNumber());
        userInfo.setOccupation(Integer.parseInt(form.getOccupation()));

        userInfo.setIndustry(
                StringUtils.isBlank(form.getIndustry())
                        ? null
                        : Integer.parseInt(form.getIndustry()));

        if (!StringUtils.isEmpty(form.getWorkPlace())) {
            userInfo.setWorkPlace(TransliteratorUtil.halfWidthToFullWidth(form.getWorkPlace()));
        }

        if (!StringUtils.isEmpty(form.getPosition())) {
            userInfo.setPosition(TransliteratorUtil.halfWidthToFullWidth(form.getPosition()));
        }

        if (!StringUtils.isEmpty(form.getPriceFrom())) {
            userInfo.setPriceFrom(
                    StringUtils.isBlank(form.getPriceFrom())
                            ? null
                            : Integer.parseInt(form.getPriceFrom()));
        }

        userInfo.setIncome(Integer.parseInt(form.getIncome()));
        userInfo.setFinancialAssets(Integer.parseInt(form.getFinancialAssets()));
        userInfo.setPurpose(Integer.parseInt(form.getPurpose()));
        userInfo.setInvestmentPurposes(Integer.parseInt(form.getInvestmentPurposes()));
        userInfo.setCryptoExperience(Integer.parseInt(form.getCryptoExperience()));
        userInfo.setFxExperience(Integer.parseInt(form.getFxExperience()));
        userInfo.setStocksExperience(Integer.parseInt(form.getStocksExperience()));
        userInfo.setFundExperience(Integer.parseInt(form.getFundExperience()));
        userInfo.setApplicationHistory(Integer.parseInt(form.getApplicationHistory()));
        userInfo.setApplicationHistoryOther(
                !StringUtils.isEmpty(form.getApplicationHistoryOther())
                        ? TransliteratorUtil.halfWidthToFullWidth(form.getApplicationHistoryOther())
                        : null);
        userInfo.setForeignPeps(Boolean.parseBoolean(form.getForeignPeps()));
        if (!StringUtils.isEmpty(form.getCountry())) {
            userInfo.setCountry(Country.valueOfName(form.getCountry()));
        }
        userInfo.setResidenceStatus(form.getResidenceStatus());
        userInfo.setResidenceCardExpiredAt(
                StringUtils.isBlank(form.getResidenceCardExpiredAt())
                        ? null
                        : DateUnit.toFormatStrToDate(form.getResidenceCardExpiredAt()));
        return userInfo;
    }
}
