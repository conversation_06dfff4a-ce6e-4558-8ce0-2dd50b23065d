package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.UserIdType;
import point.common.entity.MonsterFood;
import point.common.predicate.MonsterFoodPredicate;

@RequiredArgsConstructor
@Service
public class MonsterFoodService extends EntityService<MonsterFood, MonsterFoodPredicate> {

    @Override
    public Class<MonsterFood> getEntityClass() {
        return MonsterFood.class;
    }

    public MonsterFood findByYearAndWeekOfYear(UserIdType userIdType, int year, int weekOfYear) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<MonsterFood, MonsterFood>() {
                    @Override
                    public MonsterFood query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalType(criteriaBuilder, root, userIdType));
                        predicates.add(predicate.equalYear(criteriaBuilder, root, year));
                        predicates.add(
                                predicate.equalWeekOfYear(criteriaBuilder, root, weekOfYear));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
