package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.TradeType;
import point.common.entity.CropGrowthStageHistory;
import point.common.entity.CropGrowthStageHistory_;
import point.common.model.response.PageData;
import point.common.predicate.CropGrowthStageHistoryPredicate;

@RequiredArgsConstructor
@Service
public class CropGrowthStageHistoryService
        extends EntityService<CropGrowthStageHistory, CropGrowthStageHistoryPredicate> {

    @Override
    public Class<CropGrowthStageHistory> getEntityClass() {
        return CropGrowthStageHistory.class;
    }

    public CropGrowthStageHistory getlastestData(TradeType tradeType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<CropGrowthStageHistory, CropGrowthStageHistory>() {
                    @Override
                    public CropGrowthStageHistory query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get("updatedAt")));
                    }
                });
    }

    public PageData<CropGrowthStageHistory> findByConditionPageData(
            String tradeType, Integer number, Integer size) {

        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        buildPredicate(criteriaBuilder, root, tradeType));
                            }
                        });
        return new PageData<CropGrowthStageHistory>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                CropGrowthStageHistory, List<CropGrowthStageHistory>>() {
                            @Override
                            public List<CropGrowthStageHistory> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        buildPredicate(criteriaBuilder, root, tradeType),
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(CropGrowthStageHistory_.id)));
                            }
                        }));
    }

    private List<Predicate> buildPredicate(
            CriteriaBuilder criteriaBuilder, Root<CropGrowthStageHistory> root, String tradeType) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(tradeType)) {
            predicates.add(
                    predicate.equalTradeType(criteriaBuilder, root, TradeType.valueOf(tradeType)));
        }
        return predicates;
    }
}
