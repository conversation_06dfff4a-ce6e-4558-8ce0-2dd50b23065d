package point.common.service;

import io.netty.util.internal.StringUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import point.common.component.DataSourceManager;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.component.QueryExecutorSum;
import point.common.component.RedisManager;
import point.common.constant.Currency;
import point.common.constant.ErrorCode;
import point.common.constant.FiatWithdrawalStatus;
import point.common.entity.FiatWithdrawal;
import point.common.entity.FiatWithdrawalAudit;
import point.common.entity.FiatWithdrawal_;
import point.common.entity.User;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.FiatWithdrawalPredicate;

@RequiredArgsConstructor
@Service
@Slf4j
public class FiatWithdrawalService extends EntityService<FiatWithdrawal, FiatWithdrawalPredicate> {

    private final AssetService assetService;
    private final UserService userService;
    private final DataSourceManager dataSourceManager;

    @Override
    public Class<FiatWithdrawal> getEntityClass() {
        return FiatWithdrawal.class;
    }

    @Override
    protected void fetch(Root<FiatWithdrawal> root) {
        super.fetch(root);
        root.fetch(FiatWithdrawal_.user, JoinType.LEFT);
    }

    @Override
    public FiatWithdrawal save(FiatWithdrawal entity) {
        var auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            entity.setUpdatedBy(auth.getName());
            if (!StringUtils.hasText(entity.getCreatedBy())) {
                entity.setCreatedBy(auth.getName());
            }
        }

        try {
            customTransactionManager.execute(
                    entityManager -> {
                        super.save(entity, entityManager);
                        saveAudit(entity, entityManager);
                    });

        } catch (Exception e) {
            e.printStackTrace();
        }
        return entity;
    }

    @Override
    public FiatWithdrawal save(FiatWithdrawal entity, EntityManager entityManager)
            throws Exception {
        var auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            entity.setUpdatedBy(auth.getName());
            if (!StringUtils.hasText(entity.getCreatedBy())) {
                entity.setCreatedBy(auth.getName());
            }
        }
        super.save(entity, entityManager);
        saveAudit(entity, entityManager);
        return entity;
    }

    @Autowired private RedisManager redisManager;

    private static String getLockKey(Long userId, Currency currency) {
        return "lock:asset:" + userId + ":" + currency;
    }

    public void saveAudit(FiatWithdrawal entity, EntityManager entityManager) {
        FiatWithdrawalAudit record = new FiatWithdrawalAudit();
        record.setFiatWithdrawalId(entity.getId());
        record.setStatus(entity.getFiatWithdrawalStatus());
        record.setCreatedBy(entity.getUpdatedBy());
        record.setCreatedAt(entity.getUpdatedAt());
        entityManager.persist(record);
    }

    public FiatWithdrawal request(FiatWithdrawal fiatWithdrawal) throws Exception {
        if (!redisManager.executeWithLock(
                getLockKey(fiatWithdrawal.getUserId(), Currency.JPY),
                RedisManager.LockParams.LOCKKEY,
                () -> {
                    //    		return (FiatWithdrawal)
                    customTransactionManager.execute(
                            entityManager -> {
                                save(fiatWithdrawal, entityManager);
                                assetService.updateWithExternalLock(
                                        fiatWithdrawal.getUserId(),
                                        Currency.JPY,
                                        BigDecimal.ZERO,
                                        fiatWithdrawal.getAmount(),
                                        entityManager);
                                return fiatWithdrawal;
                            });
                })) {
            log.error(
                    getClass().getSimpleName()
                            + ",could not get lock. key: "
                            + getLockKey(fiatWithdrawal.getUserId(), Currency.JPY));
            throw new CustomException(
                    ErrorCode.LOCK_KEY, "AssetUpdate userId: " + fiatWithdrawal.getUserId());
        }
        ;
        return fiatWithdrawal;
    }

    public FiatWithdrawal apply(Long id) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.DONE);
                    save(fiatWithdrawal, entityManager);
                    assetService.update(
                            fiatWithdrawal.getUserId(),
                            Currency.JPY,
                            fiatWithdrawal.getAmount().negate(),
                            fiatWithdrawal.getFee().negate(),
                            entityManager);
                    return fiatWithdrawal;
                });
    }

    public FiatWithdrawal applyWithDrawal(Long id) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.DONE);
                    save(fiatWithdrawal, entityManager);
                    assetService.update(
                            fiatWithdrawal.getUserId(),
                            Currency.JPY,
                            fiatWithdrawal.getAmount().negate(),
                            fiatWithdrawal.getAmount().negate(),
                            entityManager);
                    return fiatWithdrawal;
                });
    }

    /**
     * 出金完了（WORKER）
     *
     * @param id
     * @return
     * @throws Exception
     */
    public FiatWithdrawal applyGMOWithDrawal(Long id) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        if (!(FiatWithdrawalStatus.APPROVING.equals(fiatWithdrawal.getFiatWithdrawalStatus())
                || FiatWithdrawalStatus.WAITTING.equals(
                        fiatWithdrawal.getFiatWithdrawalStatus()))) {
            return null;
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.DONE);
                    fiatWithdrawal.setUpdatedBy("WORKER");
                    save(fiatWithdrawal, entityManager);
                    assetService.update(
                            fiatWithdrawal.getUserId(),
                            Currency.JPY,
                            fiatWithdrawal.getAmount().negate(),
                            fiatWithdrawal.getAmount().negate(),
                            entityManager);
                    return fiatWithdrawal;
                });
    }

    /**
     * 明細番号より、出金完了（WORKER）
     *
     * @param applyNo
     * @param itemId
     * @return
     * @throws Exception
     */
    public FiatWithdrawal applyGMOWithDrawalByItemId(String applyNo, String itemId)
            throws Exception {
        FiatWithdrawal fiatWithdrawal = findByItemId(applyNo, itemId);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        if (!(FiatWithdrawalStatus.APPROVING.equals(fiatWithdrawal.getFiatWithdrawalStatus())
                || FiatWithdrawalStatus.WAITTING.equals(
                        fiatWithdrawal.getFiatWithdrawalStatus()))) {
            return null;
        }

        if (!redisManager.executeWithLock(
                getLockKey(fiatWithdrawal.getUserId(), Currency.JPY),
                RedisManager.LockParams.LOCKKEY,
                () -> {
                    //    	  return (FiatWithdrawal)
                    customTransactionManager.execute(
                            entityManager -> {
                                fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.DONE);
                                fiatWithdrawal.setUpdatedBy("WORKER");
                                save(fiatWithdrawal, entityManager);
                                assetService.updateWithExternalLock(
                                        fiatWithdrawal.getUserId(),
                                        Currency.JPY,
                                        fiatWithdrawal.getAmount().negate(),
                                        fiatWithdrawal.getAmount().negate(),
                                        entityManager);
                                return fiatWithdrawal;
                            });
                })) {
            log.error(
                    getClass().getSimpleName()
                            + ",could not get lock. key: "
                            + getLockKey(fiatWithdrawal.getUserId(), Currency.JPY));
            throw new CustomException(
                    ErrorCode.LOCK_KEY, "AssetUpdate userId: " + fiatWithdrawal.getUserId());
        }
        ;
        return fiatWithdrawal;
    }

    /**
     * 受付番号と明細番号より検索
     *
     * @param applyNo
     * @param itemId
     * @return
     */
    private FiatWithdrawal findByItemId(@NotNull String applyNo, @NotNull String itemId) {
        List<FiatWithdrawal> withdrawal =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {

                            @Override
                            public List<FiatWithdrawal> query() {
                                List<Predicate> predicates =
                                        getPredicatesByItemId(
                                                criteriaBuilder, root, applyNo, itemId);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(FiatWithdrawal_.id)));
                            }
                        });
        return withdrawal == null ? null : withdrawal.get(0);
    }

    private List<Predicate> getPredicatesByItemId(
            CriteriaBuilder criteriaBuilder,
            Root<FiatWithdrawal> root,
            String applyNo,
            String itemId) {
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(predicate.equalApplyNo(criteriaBuilder, root, applyNo));
        predicates.add(predicate.equalItemId(criteriaBuilder, root, itemId));

        return predicates;
    }

    /**
     * 受付番号を更新(WORKER)
     *
     * @param id
     * @param applyNo 受付番号
     * @param itemId
     * @return
     * @throws Exception
     */
    public FiatWithdrawal updateApplyNo(Long id, String applyNo, String itemId) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setApplyNo(applyNo);
                    if (!StringUtil.isNullOrEmpty(itemId)) {
                        fiatWithdrawal.setItemId(itemId);
                    }
                    fiatWithdrawal.setUpdatedBy("WORKER");
                    save(fiatWithdrawal, entityManager);
                    return fiatWithdrawal;
                });
    }

    public FiatWithdrawal rejectWithDrawal(Long id, String comment) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        if (!redisManager.executeWithLock(
                getLockKey(fiatWithdrawal.getUserId(), Currency.JPY),
                RedisManager.LockParams.LOCKKEY,
                () -> {
                    //        	return (FiatWithdrawal)
                    customTransactionManager.execute(
                            entityManager -> {
                                fiatWithdrawal.setFiatWithdrawalStatus(
                                        FiatWithdrawalStatus.REJECTED);
                                fiatWithdrawal.setComment(comment);
                                save(fiatWithdrawal, entityManager);
                                assetService.updateWithExternalLock(
                                        fiatWithdrawal.getUserId(),
                                        Currency.JPY,
                                        BigDecimal.ZERO,
                                        fiatWithdrawal.getAmount().negate(),
                                        entityManager);
                                return fiatWithdrawal;
                            });
                })) {
            log.error(
                    getClass().getSimpleName()
                            + ",could not get lock. key: "
                            + getLockKey(fiatWithdrawal.getUserId(), Currency.JPY));
            throw new CustomException(
                    ErrorCode.LOCK_KEY, "AssetUpdate userId: " + fiatWithdrawal.getUserId());
        }
        ;
        return fiatWithdrawal;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<FiatWithdrawal> root,
            Long userId,
            Long createdAtFrom,
            Long createdAtTo,
            Long updatedAtFrom,
            Long updatedAtTo,
            FiatWithdrawalStatus fiatWithdrawalStatus) {
        List<Predicate> predicates = new ArrayList<>();

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (createdAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(createdAtFrom)));
        }

        if (createdAtTo != null) {
            predicates.add(
                    predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
        }

        if (updatedAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToUpdatedAt(
                            criteriaBuilder, root, new Date(updatedAtFrom)));
        }

        if (updatedAtTo != null) {
            predicates.add(
                    predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updatedAtTo)));
        }

        if (fiatWithdrawalStatus != null) {
            predicates.add(
                    predicate.equalFiatWithdrawalStatus(
                            criteriaBuilder, root, fiatWithdrawalStatus));
        }

        return predicates;
    }

    public PageData<FiatWithdrawal> findByConditionPageData(
            Long userId,
            Long dateFrom,
            Long dateTo,
            FiatWithdrawalStatus fiatWithdrawalStatus,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                dateFrom,
                                                dateTo,
                                                null,
                                                null,
                                                fiatWithdrawalStatus));
                            }
                        });

        return new PageData<FiatWithdrawal>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {
                            @Override
                            public List<FiatWithdrawal> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                dateFrom,
                                                dateTo,
                                                null,
                                                null,
                                                fiatWithdrawalStatus);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(FiatWithdrawal_.id)));
                            }
                        }));
    }

    public List<FiatWithdrawal> findAllByCondition(
            Long userId,
            Long createdAtFrom,
            Long createdAtTo,
            Long updatedAtFrom,
            Long updatedAtTo,
            FiatWithdrawalStatus fiatWithdrawalStatus) {
        List<FiatWithdrawal> withdrawal =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {

                            @Override
                            public List<FiatWithdrawal> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                createdAtFrom,
                                                createdAtTo,
                                                updatedAtFrom,
                                                updatedAtTo,
                                                fiatWithdrawalStatus);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(FiatWithdrawal_.id)));
                            }
                        });
        return withdrawal;
    }

    public List<FiatWithdrawal> findByCondition(
            Long userId,
            Long dateFrom,
            Long dateTo,
            Long updateFrom,
            Long updateTo,
            List<FiatWithdrawalStatus> fiatWithdrawalStatuses,
            Integer number,
            Integer size) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {
                    @Override
                    public List<FiatWithdrawal> query() {
                        List<Predicate> predicates = new ArrayList<>();

                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }

                        if (dateFrom != null) {
                            predicates.add(
                                    predicate.greaterThanOrEqualToCreatedAt(
                                            criteriaBuilder, root, new Date(dateFrom)));
                        }

                        if (dateTo != null) {
                            predicates.add(
                                    predicate.lessThanCreatedAt(
                                            criteriaBuilder, root, new Date(dateTo)));
                        }

                        if (updateFrom != null) {
                            predicates.add(
                                    predicate.greaterThanOrEqualToUpdatedAt(
                                            criteriaBuilder, root, new Date(updateFrom)));
                        }

                        if (updateTo != null) {
                            predicates.add(
                                    predicate.lessThanUpdatedAt(
                                            criteriaBuilder, root, new Date(updateTo)));
                        }

                        if (fiatWithdrawalStatuses != null && !fiatWithdrawalStatuses.isEmpty()) {
                            predicates.add(
                                    predicate.inFiatWithdrawalStatus(
                                            criteriaBuilder, root, fiatWithdrawalStatuses));
                        }

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(FiatWithdrawal_.id)));
                    }
                });
    }

    private Object[] sumExchange(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Object[]> criteriaQuery,
            Root<FiatWithdrawal> root,
            List<Predicate> predicates) {

        criteriaQuery.multiselect(
                criteriaBuilder.sum(root.get(FiatWithdrawal_.amount)),
                criteriaBuilder.sum(root.get(FiatWithdrawal_.fee)));

        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        try {
            return entityManager
                    .createQuery(criteriaQuery)
                    .setFirstResult(0)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    // 取引所サマリ
    public Object[] sumExchangeByCondition(
            FiatWithdrawalStatus fiatWithdrawalStatus, Long updatedAtFrom, Long updatedAtTo) {
        return customTransactionManager.sum(
                getEntityClass(),
                new QueryExecutorSum<FiatWithdrawal>() {
                    @Override
                    public Object[] query() {
                        List<Predicate> predicates =
                                getPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        null,
                                        null,
                                        null,
                                        updatedAtFrom,
                                        updatedAtTo,
                                        fiatWithdrawalStatus);
                        return sumExchange(
                                entityManager, criteriaBuilder, criteriaQuery, root, predicates);
                    }
                });
    }

    public FiatWithdrawal cancel(Long id) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ID);
        }

        User user = userService.findOne(fiatWithdrawal.getUserId());

        if (!user.isActive()) {
            throw new CustomException(ErrorCode.ORDER_ERROR_HALT);
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.REJECTED);
                    save(fiatWithdrawal, entityManager);
                    assetService.update(
                            fiatWithdrawal.getUserId(),
                            Currency.JPY,
                            BigDecimal.ZERO,
                            fiatWithdrawal.getAmount().negate(),
                            entityManager);
                    return fiatWithdrawal;
                });
    }

    /**
     * 振込限度額を超える部分は「待機中」に更新（WORKER）
     *
     * @param id
     * @return
     * @throws Exception
     */
    public FiatWithdrawal applyWaitting(Long id) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        if (!(FiatWithdrawalStatus.APPROVING.equals(fiatWithdrawal.getFiatWithdrawalStatus())
                || FiatWithdrawalStatus.WAITTING.equals(
                        fiatWithdrawal.getFiatWithdrawalStatus()))) {
            return null;
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.WAITTING);
                    fiatWithdrawal.setUpdatedBy("WORKER");
                    save(fiatWithdrawal, entityManager);
                    return fiatWithdrawal;
                });
    }

    /**
     * 明細番号より、振込限度額を超える部分は「待機中」に更新（WORKER）
     *
     * @param applyNo
     * @param itemId
     * @return
     * @throws Exception
     */
    public FiatWithdrawal applyWaittingByItemId(String applyNo, String itemId) throws Exception {
        FiatWithdrawal fiatWithdrawal = findByItemId(applyNo, itemId);

        if (fiatWithdrawal == null) {
            return fiatWithdrawal;
        }

        if (!(FiatWithdrawalStatus.APPROVING.equals(fiatWithdrawal.getFiatWithdrawalStatus())
                || FiatWithdrawalStatus.WAITTING.equals(
                        fiatWithdrawal.getFiatWithdrawalStatus()))) {
            return null;
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.WAITTING);
                    fiatWithdrawal.setUpdatedBy("WORKER");
                    save(fiatWithdrawal, entityManager);
                    return fiatWithdrawal;
                });
    }

    /**
     * 振込依頼の対象を取得
     *
     * @return
     */
    public List<FiatWithdrawal> findBulkTransferTarget() {
        List<FiatWithdrawal> withdrawal =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {

                            @Override
                            public List<FiatWithdrawal> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindBulkTransfer(criteriaBuilder, root);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(FiatWithdrawal_.id)));
                            }
                        });
        return withdrawal;
    }

    /**
     * 振込依頼結果照合の対象を取得
     *
     * @return
     */
    public List<FiatWithdrawal> findBulkTransferResultTarget() {
        List<FiatWithdrawal> withdrawal =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {

                            @Override
                            public List<FiatWithdrawal> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindBulkTransferResult(
                                                criteriaBuilder, root);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery.groupBy(root.get(FiatWithdrawal_.applyNo)),
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(FiatWithdrawal_.applyNo)));
                            }
                        });
        return withdrawal;
    }

    /**
     * 振込依頼の対象を取得(条件)
     *
     * @param criteriaBuilder
     * @param root
     * @return
     */
    private List<Predicate> getPredicatesOfFindBulkTransfer(
            CriteriaBuilder criteriaBuilder, Root<FiatWithdrawal> root) {
        List<Predicate> predicates = new ArrayList<>();

        // 受付完了と待機中のデータ
        List<FiatWithdrawalStatus> fiatWithdrawalStatusList = new ArrayList<>();
        fiatWithdrawalStatusList.add(FiatWithdrawalStatus.APPROVING);
        fiatWithdrawalStatusList.add(FiatWithdrawalStatus.WAITTING);
        predicates.add(
                predicate.inFiatWithdrawalStatus(criteriaBuilder, root, fiatWithdrawalStatusList));

        // 今日前
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // UCTに変換
        calendar.add(Calendar.HOUR, -9);

        Date today = calendar.getTime();
        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, today));

        // 受付番号が空
        predicates.add(predicate.isNullApplyNo(criteriaBuilder, root));

        return predicates;
    }

    /**
     * 総合振込依頼結果照会対象を取得(条件)
     *
     * @param criteriaBuilder
     * @param root
     * @return
     */
    private List<Predicate> getPredicatesOfFindBulkTransferResult(
            CriteriaBuilder criteriaBuilder, Root<FiatWithdrawal> root) {
        List<Predicate> predicates = new ArrayList<>();

        // 受付完了と待機中のデータ
        List<FiatWithdrawalStatus> fiatWithdrawalStatusList = new ArrayList<>();
        fiatWithdrawalStatusList.add(FiatWithdrawalStatus.APPROVING);
        fiatWithdrawalStatusList.add(FiatWithdrawalStatus.WAITTING);
        predicates.add(
                predicate.inFiatWithdrawalStatus(criteriaBuilder, root, fiatWithdrawalStatusList));

        // 受付番号が空ではない
        predicates.add(predicate.isNotNullApplyNo(criteriaBuilder, root));

        return predicates;
    }

    /**
     * 出金エラーの処理(WORKER)
     *
     * @param id
     * @return
     * @throws Exception
     */
    public FiatWithdrawal transferError(Long id) throws Exception {
        FiatWithdrawal fiatWithdrawal = findOne(id);

        if (fiatWithdrawal == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ID);
        }

        if (!(FiatWithdrawalStatus.APPROVING.equals(fiatWithdrawal.getFiatWithdrawalStatus())
                || FiatWithdrawalStatus.WAITTING.equals(
                        fiatWithdrawal.getFiatWithdrawalStatus()))) {
            return null;
        }

        return customTransactionManager.execute(
                entityManager -> {
                    fiatWithdrawal.setFiatWithdrawalStatus(FiatWithdrawalStatus.TRANSACTION_ERROR);
                    fiatWithdrawal.setUpdatedBy("WORKER");
                    save(fiatWithdrawal, entityManager);
                    assetService.update(
                            fiatWithdrawal.getUserId(),
                            Currency.JPY,
                            BigDecimal.ZERO,
                            fiatWithdrawal.getAmount().negate(),
                            entityManager);
                    return fiatWithdrawal;
                });
    }

    /**
     * 明細番号より、出金エラーの処理(WORKER)
     *
     * @param applyNo
     * @param itemId
     * @return
     * @throws Exception
     */
    public FiatWithdrawal transferErrorByItemId(String applyNo, String itemId) throws Exception {
        FiatWithdrawal fiatWithdrawal = findByItemId(applyNo, itemId);

        if (fiatWithdrawal == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ID);
        }

        if (!(FiatWithdrawalStatus.APPROVING.equals(fiatWithdrawal.getFiatWithdrawalStatus())
                || FiatWithdrawalStatus.WAITTING.equals(
                        fiatWithdrawal.getFiatWithdrawalStatus()))) {
            return null;
        }

        if (!redisManager.executeWithLock(
                getLockKey(fiatWithdrawal.getUserId(), Currency.JPY),
                RedisManager.LockParams.LOCKKEY,
                () -> {
                    //    	  return (FiatWithdrawal)
                    customTransactionManager.execute(
                            entityManager -> {
                                fiatWithdrawal.setFiatWithdrawalStatus(
                                        FiatWithdrawalStatus.TRANSACTION_ERROR);
                                fiatWithdrawal.setUpdatedBy("WORKER");
                                save(fiatWithdrawal, entityManager);
                                assetService.updateWithExternalLock(
                                        fiatWithdrawal.getUserId(),
                                        Currency.JPY,
                                        BigDecimal.ZERO,
                                        fiatWithdrawal.getAmount().negate(),
                                        entityManager);
                                return fiatWithdrawal;
                            });
                })) {
            log.error(
                    getClass().getSimpleName()
                            + ",could not get lock. key: "
                            + getLockKey(fiatWithdrawal.getUserId(), Currency.JPY));
            throw new CustomException(
                    ErrorCode.LOCK_KEY, "AssetUpdate userId: " + fiatWithdrawal.getUserId());
        }
        ;
        return fiatWithdrawal;
    }

    /**
     * 利用者ごとの合計一日出金額を取得
     *
     * @param userId
     * @return
     */
    public BigDecimal getSumToday(Long userId) {

        BigDecimal sumTodayAmount = new BigDecimal(0);
        String sql =
                "SELECT sum(amount) FROM `fiat_withdrawal` where TO_DAYS(adddate(created_at, interval 9 hour)) = TO_DAYS(adddate(Now(), interval 9 hour)) and user_id = :uid";

        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        try {
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("uid", userId);
            @SuppressWarnings("unchecked")
            List<BigDecimal> list = query.getResultList();
            if (list != null && list.size() > 0) {
                if (list.get(0) != null) {
                    sumTodayAmount = list.get(0);
                }
            }
        } finally {
            entityManager.clear();
            entityManager.close();
        }
        return sumTodayAmount;
    }

    /**
     * 受付番号よりデータ取得
     *
     * @param applyNo
     * @return
     */
    public List<FiatWithdrawal> findAllByApplyNo(String applyNo) {
        List<FiatWithdrawal> withdrawal =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {

                            @Override
                            public List<FiatWithdrawal> query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalApplyNo(criteriaBuilder, root, applyNo));
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(FiatWithdrawal_.id)));
                            }
                        });
        return withdrawal;
    }

    /**
     * 今日依頼済みのデータを検索
     *
     * @param today
     * @return
     */
    public List<FiatWithdrawal> findLikeTodaysApplyNo(String today) {
        List<FiatWithdrawal> withdrawal =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatWithdrawal, List<FiatWithdrawal>>() {

                            @Override
                            public List<FiatWithdrawal> query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        criteriaBuilder.like(
                                                root.get(FiatWithdrawal_.applyNo), today + "%"));
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(FiatWithdrawal_.id)));
                            }
                        });
        return withdrawal;
    }
}
