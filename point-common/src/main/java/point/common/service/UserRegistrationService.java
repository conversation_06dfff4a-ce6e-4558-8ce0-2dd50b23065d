package point.common.service;

import java.util.Date;
import java.util.Objects;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.constant.CommonConstants;
import point.common.constant.UserIdType;
import point.common.entity.InvestUser;
import point.common.entity.UserIdentity;
import point.common.exception.CustomException;
import point.common.repos.InvestUserRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserRegistrationService {

    private final UserIdentityService userIdentityService;
    private final InvestUserRepository investUserRepository;

    public InvestUser save(InvestUser investUser, EntityManager entityManager) throws Exception {
        try {
            Date now = new Date();
            investUser.setUpdatedAt(now);
            if (Objects.isNull(investUser.getId())) {
                UserIdentity userIdentity =
                        userIdentityService.create(UserIdType.Invest, entityManager);
                investUser.setId(userIdentity.getId());
                investUser.setCreatedAt(now);
                entityManager.persist(investUser);
                return investUser;
            } else {
                return entityManager.merge(investUser);
            }
        } catch (Exception e) {
            log.error(investUser.getClass().getName(), e);
            throw new CustomException(e);
        }
    }

    @Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public InvestUser findByEmail(String email) {
        return investUserRepository.findByEmail(email);
    }
}
