package point.common.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.PointPartnerStatus;
import point.common.entity.PointPartner;
import point.common.entity.PointPartner_;
import point.common.model.request.PointPartnerForm;
import point.common.model.response.PageData;
import point.common.predicate.PointPartnerPredicate;
import point.common.util.FormatUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointPartnerService extends EntityService<PointPartner, PointPartnerPredicate> {

    @Override
    public Class<PointPartner> getEntityClass() {
        return PointPartner.class;
    }

    public PointPartner findByPartnerNumber(String partnerNumber) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PointPartner query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalPartnerNumber(criteriaBuilder, root, partnerNumber));
                        predicates.add(
                                predicate.equalStatus(
                                        criteriaBuilder, root, PointPartnerStatus.ACTIVE));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public PointPartner findByPartnerNumberWithoutStatusLimit(String partnerNumber) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PointPartner query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalPartnerNumber(criteriaBuilder, root, partnerNumber));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public PageData<PointPartner> findByCondition(
            Long id,
            Integer scope,
            String partnerNumber,
            String showName,
            String name,
            PointPartnerStatus status,
            Long expiryDateFrom,
            Long expiryDateTo,
            Long effectiveDateFrom,
            Long effectiveDateTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                scope,
                                                partnerNumber,
                                                showName,
                                                name,
                                                status,
                                                expiryDateFrom,
                                                expiryDateTo,
                                                effectiveDateFrom,
                                                effectiveDateTo));
                            }
                        });

        return new PageData<PointPartner>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PointPartner, List<PointPartner>>() {
                            @Override
                            public List<PointPartner> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                scope,
                                                partnerNumber,
                                                showName,
                                                name,
                                                status,
                                                expiryDateFrom,
                                                expiryDateTo,
                                                effectiveDateFrom,
                                                effectiveDateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(PointPartner_.updatedAt)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PointPartner> root,
            Long id,
            Integer scope,
            String partnerNumber,
            String showName,
            String name,
            PointPartnerStatus status,
            Long expiryDateFrom,
            Long expiryDateTo,
            Long effectiveDateFrom,
            Long effectiveDateTo) {
        List<Predicate> predicates = new ArrayList<>();

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {

            if (scope != null && scope > 0) {
                predicates.add(predicate.equalScope(criteriaBuilder, root, scope));
            }
            if (StringUtils.isNotBlank(partnerNumber)) {
                predicates.add(predicate.equalPartnerNumber(criteriaBuilder, root, partnerNumber));
            }
            if (StringUtils.isNotBlank(showName)) {
                predicates.add(predicate.likeShowName(criteriaBuilder, root, showName));
            }
            if (StringUtils.isNotBlank(name)) {
                predicates.add(predicate.equalName(criteriaBuilder, root, name));
            }
            if (status != null) {
                predicates.add(predicate.equalStatus(criteriaBuilder, root, status));
            }

            if (effectiveDateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToEffectiveDate(
                                criteriaBuilder, root, new Date(effectiveDateFrom)));
            }
            if (effectiveDateTo != null) {
                predicates.add(
                        predicate.lessThanEffectiveDate(
                                criteriaBuilder, root, new Date(effectiveDateTo)));
            }
            if (expiryDateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToExpiryDate(
                                criteriaBuilder, root, new Date(expiryDateFrom)));
            }
            if (expiryDateTo != null) {
                predicates.add(
                        predicate.lessThanExpiryDate(
                                criteriaBuilder, root, new Date(expiryDateTo)));
            }
        }
        return predicates;
    }

    public List<PointPartner> findByMaxPartnerNumber(String partnerNumber) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<PointPartner> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.likePartnerNumber(criteriaBuilder, root, partnerNumber));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    /**
     * create
     *
     * @param form
     * @return
     */
    public PointPartner create(PointPartnerForm form) {
        String partnerNumberTemp =
                "BC" + FormatUtil.format(new Date(), FormatUtil.FormatPattern.YYYYMM) + "_";
        List<PointPartner> partnerList = findByMaxPartnerNumber(partnerNumberTemp);

        if (partnerList.isEmpty()) {
            partnerNumberTemp =
                    "BC"
                            + FormatUtil.format(new Date(), FormatUtil.FormatPattern.YYYYMM)
                            + "_00001";
        } else {
            List<String> partnerNumberList = new ArrayList<>();
            for (PointPartner partner : partnerList) {
                partnerNumberList.add(partner.getPartnerNumber());
            }
            Collections.sort(partnerNumberList);
            String partnerNumber = partnerNumberList.get(partnerNumberList.size() - 1);
            Integer maxSerialNumber =
                    Integer.parseInt(partnerNumber.substring(partnerNumber.length() - 5));
            String maxSerialNumberStr = String.format("%05d", maxSerialNumber + 1);
            partnerNumberTemp =
                    "BC"
                            + FormatUtil.format(new Date(), FormatUtil.FormatPattern.YYYYMM)
                            + "_"
                            + maxSerialNumberStr;
        }
        form.setPartnerNumber(partnerNumberTemp);
        PointPartnerStatus status = PointPartnerStatus.ABOLISH;
        if (form.getEffectiveDate().after(new Date())) {
            status = PointPartnerStatus.INACTIVE;
        } else if (form.getEffectiveDate().before(new Date())
                && form.getExpiryDate().after(new Date())) {
            status = PointPartnerStatus.ACTIVE;
        } else if (form.getExpiryDate().before(new Date())) {
            status = PointPartnerStatus.ABOLISH;
        }

        PointPartner pointPartner =
                PointPartner.builder()
                        .partnerNumber(form.getPartnerNumber())
                        .name(form.getName())
                        .description(form.getDescription())
                        .websiteUrl(form.getWebsiteUrl())
                        .expiryDate(form.getExpiryDate())
                        .status(status)
                        .effectiveDate(form.getEffectiveDate())
                        .showName(form.getShowName())
                        .responsiblePerson(form.getResponsiblePerson())
                        .phoneNum(form.getPhoneNum())
                        .mail(form.getMail())
                        .address(form.getAddress())
                        .scope(form.getScope())
                        .createdBy(form.getCreateBy())
                        .build();
        return customTransactionManager.save(pointPartner);
    }

    public void updatePointPartnerStatus() {
        String sql =
                "update point_partner "
                        + "set status = case "
                        + "    when :nowTime > effective_date and :nowTime < expiry_date AND status = 'INACTIVE' then 'ACTIVE' "
                        + "    when :nowTime > expiry_date AND status IN ('ACTIVE', 'INACTIVE') then 'ABOLISH' "
                        + "    else status "
                        + "end,"
                        + "updated_at = :nowTime ,"
                        + "updated_by = 'system worker'"
                        + "where (:nowTime > effective_date AND :nowTime < expiry_date AND status = 'INACTIVE') "
                        + "   OR (:nowTime > expiry_date AND status IN ('ACTIVE', 'INACTIVE'));";
        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("nowTime", LocalDateTime.now());
        customTransactionManager.multiUpdate(sql, mapSqlParameterSource);
        log.info("get_value,{}", sql);
    }
}
