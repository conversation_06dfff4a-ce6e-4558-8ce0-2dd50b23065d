package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.entity.Bank;
import point.common.entity.Bank_;
import point.common.model.response.PageData;
import point.common.predicate.BankPredicate;

@RequiredArgsConstructor
@Service
public class BankService extends EntityService<Bank, BankPredicate> {

    @Override
    public Class<Bank> getEntityClass() {
        return Bank.class;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<Bank> root,
            String bankName,
            Integer bankCode,
            String branchName,
            Integer branchCode,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();

        if (bankName != null) {
            predicates.add(predicate.likeBankName(criteriaBuilder, root, bankName));
        }
        if (bankCode != null) {
            predicates.add(predicate.equalBankCode(criteriaBuilder, root, bankCode));
        }
        if (branchName != null) {
            predicates.add(predicate.likeBranchName(criteriaBuilder, root, branchName));
        }
        if (branchCode != null) {
            predicates.add(predicate.equalBranchCode(criteriaBuilder, root, branchCode));
        }

        return predicates;
    }

    public PageData<Bank> findByConditionPageData(
            String bankName,
            Integer bankCode,
            String branchName,
            Integer branchCode,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                bankName,
                                                bankCode,
                                                branchName,
                                                branchCode,
                                                number,
                                                size));
                            }
                        });

        return new PageData<Bank>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<Bank, List<Bank>>() {
                            @Override
                            public List<Bank> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                bankName,
                                                bankCode,
                                                branchName,
                                                branchCode,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(Bank_.id)));
                            }
                        }));
    }

    public List<Bank> findAllByCondition(
            String bankName, Integer bankCode, String branchName, Integer branchCode) {
        List<Bank> dep =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<Bank, List<Bank>>() {
                            @Override
                            public List<Bank> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                bankName,
                                                bankCode,
                                                branchName,
                                                branchCode,
                                                0,
                                                Integer.MAX_VALUE);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.desc(root.get(Bank_.id)));
                            }
                        });
        return dep;
    }

    public List<Bank> findByCondition(
            String bankName,
            Integer bankCode,
            String branchName,
            Integer branchCode,
            Integer number,
            Integer size) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<Bank, List<Bank>>() {
                    @Override
                    public List<Bank> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (bankName != null) {
                            predicates.add(predicate.likeBankName(criteriaBuilder, root, bankName));
                        }
                        if (bankCode != null) {
                            predicates.add(
                                    predicate.equalBankCode(criteriaBuilder, root, bankCode));
                        }
                        if (branchName != null) {
                            predicates.add(
                                    predicate.likeBranchName(criteriaBuilder, root, branchName));
                        }
                        if (branchCode != null) {
                            predicates.add(
                                    predicate.equalBranchCode(criteriaBuilder, root, branchCode));
                        }

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                number,
                                size,
                                criteriaBuilder.desc(root.get(Bank_.id)));
                    }
                });
    }

    public Bank findOneByBankAndBranchCode(Integer bankCode, Integer branchCode) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<Bank, Bank>() {
                    @Override
                    public Bank query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalBankCode(criteriaBuilder, root, bankCode));
                        predicates.add(
                                predicate.equalBranchCode(criteriaBuilder, root, branchCode));

                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<Bank> findAllBankName() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<Bank, List<Bank>>() {
                    @Override
                    public List<Bank> query() {
                        List<Predicate> predicates = new ArrayList<>();

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                0,
                                Integer.MAX_VALUE,
                                criteriaBuilder.desc(root.get(Bank_.id)));
                    }
                });
    }

    public Bank findById(Long bankId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<Bank, Bank>() {
                    @Override
                    public Bank query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalId(criteriaBuilder, root, bankId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
