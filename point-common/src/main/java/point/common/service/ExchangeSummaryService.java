package point.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;
import point.common.constant.ErrorCode;
import point.common.constant.FiatDepositStatus;
import point.common.constant.FiatWithdrawalStatus;
import point.common.constant.OrderType;
import point.common.constant.TradeType;
import point.common.entity.CurrencyConfig;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.ExchangeSummary;
import point.common.entity.ExchangeSummary_;
import point.common.entity.Symbol;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.ExchangeSummaryPredicate;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.common.util.JsonUtil;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@Service
public class ExchangeSummaryService
        extends EntityService<ExchangeSummary, ExchangeSummaryPredicate> {

    private final AssetSummaryService assetSummaryService;
    private final CurrencyConfigService currencyConfigService;
    private final CurrencyPairConfigService currencyPairConfigService;
    private final FiatDepositService fiatDepositService;
    private final FiatWithdrawalService fiatWithdrawalService;
    private final SymbolService symbolService;
    private final PosTradeService posTradeService;

    @Override
    public Class<ExchangeSummary> getEntityClass() {
        return ExchangeSummary.class;
    }

    /*
     * 【運用】predicates作成メソッドは共通化して使用する
     * インデックス順でaddする
     */
    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<ExchangeSummary> root,
            Currency currency,
            Date targetFrom,
            Date targetTo,
            TradeType tradeType) {
        List<Predicate> predicates = new ArrayList<>();
        if (currency != null) {
            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
        }
        if (targetFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, targetFrom));
        }
        if (targetTo != null) {
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetTo));
        }
        if (tradeType != null) {
            predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
        }

        return predicates;
    }

    public List<ExchangeSummary> findByCondition(
            Currency currency,
            Date targetFrom,
            Date targetTo,
            boolean isAscending,
            TradeType tradeType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ExchangeSummary, List<ExchangeSummary>>() {
                    @Override
                    public List<ExchangeSummary> query() {
                        List<Predicate> predicates =
                                createPredicates(
                                        criteriaBuilder,
                                        root,
                                        currency,
                                        targetFrom,
                                        targetTo,
                                        tradeType);
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                isAscending
                                        ? criteriaBuilder.asc(root.get(ExchangeSummary_.id))
                                        : criteriaBuilder.desc(root.get(ExchangeSummary_.id)));
                    }
                });
    }

    public ExchangeSummary findOne(
            Currency currency, Date targetFrom, Date targetTo, TradeType tradeType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ExchangeSummary, ExchangeSummary>() {
                    @Override
                    public ExchangeSummary query() {
                        List<Predicate> predicates =
                                createPredicates(
                                        criteriaBuilder,
                                        root,
                                        currency,
                                        targetFrom,
                                        targetTo,
                                        tradeType);
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public ExchangeSummary findOne(
            Currency currency,
            Date targetFrom,
            Date targetTo,
            EntityManager entityManager,
            TradeType tradeType) {
        return new QueryExecutorReturner<ExchangeSummary, ExchangeSummary>() {
            @Override
            public ExchangeSummary query() {
                List<Predicate> predicates =
                        createPredicates(
                                criteriaBuilder, root, currency, targetFrom, targetTo, tradeType);
                return getSingleResult(entityManager, criteriaQuery, root, predicates);
            }
        }.execute(getEntityClass(), entityManager);
    }

    private void fiatDepositWithdrawalSumExchange(
            ExchangeSummary exchangeSummaryData, Long targetFrom, Long targetTo) {
        // データ編集：入金（日本円）
        Object[] fiatDepositSumResult =
                fiatDepositService.sumExchangeByCondition(
                        FiatDepositStatus.DONE, targetFrom, targetTo);
        log.info(
                "DBMITO,ExchangeSummaryLog,fiatDepositSumResult,"
                        + JsonUtil.encode(fiatDepositSumResult));

        if (fiatDepositSumResult != null) {
            if (fiatDepositSumResult[0] != null) {
                exchangeSummaryData.setDepositAmount(((BigDecimal) fiatDepositSumResult[0]));
                exchangeSummaryData.setDepositAmountJpy(((BigDecimal) fiatDepositSumResult[0]));
                exchangeSummaryData.setDepositFee(((BigDecimal) fiatDepositSumResult[1]));
                exchangeSummaryData.setDepositFeeJpy(((BigDecimal) fiatDepositSumResult[1]));
            }
        }

        // データ編集：出金（日本円）
        Object[] fiatWithdrawalSumResult =
                fiatWithdrawalService.sumExchangeByCondition(
                        FiatWithdrawalStatus.DONE, targetFrom, targetTo);
        log.info(
                "DBMITO,ExchangeSummaryLog,fiatWithdrawalSumResult,"
                        + JsonUtil.encode(fiatWithdrawalSumResult));

        if (fiatWithdrawalSumResult != null) {
            if (fiatWithdrawalSumResult[0] != null) {
                exchangeSummaryData.setWithdrawalAmount(((BigDecimal) fiatWithdrawalSumResult[0]));
                exchangeSummaryData.setWithdrawalAmountJpy(
                        ((BigDecimal) fiatWithdrawalSumResult[0]));
                exchangeSummaryData.setWithdrawalFee(((BigDecimal) fiatWithdrawalSumResult[1]));
                exchangeSummaryData.setWithdrawalFeeJpy(((BigDecimal) fiatWithdrawalSumResult[1]));
            }
        }
    }

    private void posTradeSumSimple(
            Symbol symbol,
            Currency quoteCurrency,
            ExchangeSummary exchangeSummaryData,
            BigDecimal jpyConversion,
            List<OrderType> orderTypes,
            Date targetDateFrom,
            Date targetDateTo) {

        /** 1.販売所集計 */
        Object[] posTradeSumResult =
                posTradeService.sumPosExchangeByCondition(
                        symbol.getId(),
                        quoteCurrency,
                        null,
                        orderTypes,
                        targetDateFrom,
                        targetDateTo);

        log.info(
                "DBMITO,ExchangeSummaryLog,posTradeSumResult,"
                        + JsonUtil.encode(posTradeSumResult));

        if (posTradeSumResult != null) {
            if (posTradeSumResult[0] != null) {
                exchangeSummaryData.setSimpleMarketPosTradeBuyAmount(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeBuyAmount()
                                .add(((BigDecimal) posTradeSumResult[0])));
                exchangeSummaryData.setSimpleMarketPosTradeBuyAmountJpy(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeBuyAmountJpy()
                                .add((BigDecimal) posTradeSumResult[1]));
                exchangeSummaryData.setSimpleMarketPosTradeSellAmount(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeSellAmount()
                                .add(((BigDecimal) posTradeSumResult[2])));
                exchangeSummaryData.setSimpleMarketPosTradeSellAmountJpy(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeSellAmountJpy()
                                .add((BigDecimal) posTradeSumResult[3]));

                if (quoteCurrency != null) {
                    exchangeSummaryData.setSimpleMarketPosTradeFee(
                            exchangeSummaryData
                                    .getSimpleMarketPosTradeFee()
                                    .add(((BigDecimal) posTradeSumResult[4])));
                    exchangeSummaryData.setSimpleMarketPosTradeFeeJpy(
                            exchangeSummaryData
                                    .getSimpleMarketPosTradeFeeJpy()
                                    .add(((BigDecimal) posTradeSumResult[5])));
                }
            }
        }

        /** 1-2.販売所集計(アーカイブ) */
        // 起動日時の取得
        Date today = new Date(); // JST 10/15 00:03起動の場合、Thu Oct 14 15:03:11 GMT 2021

        // 前日の取得
        Date before24H =
                new Date(
                        today.getTime() - DateUnit.DAY.getMillis()); // Wed Oct 13 15:03:11 GMT 2021

        // 前日集計なら、targetDateToは起動日時-24Hより後
        // targetDateToがそれより前の場合、前々日以前の再集計処理 => redshiftアーカイブも集計対象とする
        if (targetDateTo.before(before24H)) {
            log.info(
                    "ExchangeSummaryLog,targetDateTo < before24H,"
                            + ",targetDateTo,"
                            + targetDateTo
                            + ",before24H,"
                            + before24H);

            Map<String, Object> posTradeHistorySumResult =
                    posTradeService.sumPosExchangeByConditionFromHistory(
                            symbol, quoteCurrency, null, orderTypes, targetDateFrom, targetDateTo);

            log.info(
                    "DBMITO,ExchangeSummaryLog,posTradeHistorySumResult,"
                            + JsonUtil.encode(posTradeHistorySumResult));

            if (posTradeHistorySumResult.get("buyamount") != null) {
                exchangeSummaryData.setSimpleMarketPosTradeBuyAmount(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeBuyAmount()
                                .add(((BigDecimal) posTradeHistorySumResult.get("buyamount"))));
                exchangeSummaryData.setSimpleMarketPosTradeBuyAmountJpy(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeBuyAmountJpy()
                                .add((BigDecimal) posTradeHistorySumResult.get("buyamountjpy")));
                exchangeSummaryData.setSimpleMarketPosTradeSellAmount(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeSellAmount()
                                .add(((BigDecimal) posTradeHistorySumResult.get("sellamount"))));
                exchangeSummaryData.setSimpleMarketPosTradeSellAmountJpy(
                        exchangeSummaryData
                                .getSimpleMarketPosTradeSellAmountJpy()
                                .add((BigDecimal) posTradeHistorySumResult.get("sellamountjpy")));

                if (quoteCurrency != null) {
                    exchangeSummaryData.setSimpleMarketPosTradeFee(
                            exchangeSummaryData
                                    .getSimpleMarketPosTradeFee()
                                    .add(((BigDecimal) posTradeHistorySumResult.get("feesum"))));
                    exchangeSummaryData.setSimpleMarketPosTradeFeeJpy(
                            exchangeSummaryData
                                    .getSimpleMarketPosTradeFeeJpy()
                                    .add(((BigDecimal) posTradeHistorySumResult.get("feesumjpy"))));
                }
            }
        }
    }

    /** 前日の通貨別の取引所・販売所データサマリ & exchange_summary Delete&Insert */
    // to do 当日再実行を考慮し、delete & insertとする
    // to do 過去分再作成を考慮し、redshiftからも取得する

    public void create(Date targetDateFrom, TradeType tradeType) throws Exception {
        // targetFrom : JST 00:00基準の前日のUTC変換後Date
        // 例）JST 10/15 00:03起動の場合、Wed Oct 13 15:00:00 GMT 2021
        Long targetFrom = targetDateFrom.getTime();
        Long targetTo = targetFrom + DateUnit.DAY.getMillis();
        Date targetDateTo = new Date(targetTo); // Wed Oct 14 15:00:00 GMT 2021
        // JST前日YYYYMMDD
        String targetDay = FormatUtil.formatJst(targetDateFrom, FormatPattern.YYYYMMDD); // 20211014

        log.info(
                "ExchangeSummaryLog,start,targetDay,"
                        + targetDay
                        + ",targetDateFrom,"
                        + targetDateFrom
                        + ",targetDateTo,"
                        + targetDateTo);

        /** 1.有効な通貨・通貨ペアリスト取得 */
        // 有効な(enabled=true)通貨のみ
        List<CurrencyConfig> currencyConfigs =
                currencyConfigService.findAllByCondition(tradeType, null, true);

        if (CollectionUtils.isEmpty(currencyConfigs)) {
            log.warn("ExchangeSummaryLog,start,enabled currencyConfigs not found");
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        List<CurrencyPairConfig> currencyPairConfigs =
                currencyPairConfigService.findAllByCondition(tradeType, null, true);

        if (CollectionUtils.isEmpty(currencyPairConfigs)) {
            log.warn("ExchangeSummaryLog,start,enabled currencyPairConfigs not found");
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        /** 2.前日取引データサマリ・更新データ作成【通貨ごと】 */
        List<ExchangeSummary> exchangeSummaryList = new ArrayList<>();

        for (CurrencyConfig currencyConfig : currencyConfigs) {
            log.info(
                    "DBMITO,ExchangeSummaryLog,currency," + currencyConfig.getCurrency().getName());

            Currency currency = currencyConfig.getCurrency();
            ExchangeSummary exchangeSummaryData = new ExchangeSummary();

            // jpyConversionは入出金の円貨換算には使用しない
            // 例）JST 10/15 00:03起動の場合、Wed Oct 14 14:58:00 GMT 2021 (JST 10/14 23:58)
            exchangeSummaryData.setJpyConversion(
                    assetSummaryService.findJpyConversionFromCandlestick(
                            currency, targetDay, 120, tradeType));

            log.info(
                    "DBMITO,ExchangeSummaryLog,exchangeSummaryData,"
                            + JsonUtil.encode(exchangeSummaryData));

            /** ①入金・出金履歴サマリ & 編集 */
            if (currency.equals(Currency.JPY)) {
                fiatDepositWithdrawalSumExchange(exchangeSummaryData, targetFrom, targetTo);
            }

            for (CurrencyPairConfig currencyPairConfig : currencyPairConfigs) {
                CurrencyPair currencyPair = currencyPairConfig.getCurrencyPair();
                Symbol symbol = null;
                if (currencyPairConfig.getTradeType().equals(tradeType)) {
                    symbol =
                            symbolService.findByCondition(
                                    tradeType, currencyPairConfig.getCurrencyPair());
                }
                if (symbol == null) {
                    throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
                }

                /** 販売所 取引履歴サマリ & 編集 */
                // pos_tradeは48時間経過でアーカイブのため、前日集計の場合以外はredshiftからも集計する
                List<OrderType> orderTypes = new ArrayList<>();
                orderTypes.add(OrderType.SIMPLE_MARKET);
                if (currencyPair.getBaseCurrency().equals(currency)) {
                    if (symbol.getTradeType().equals(tradeType)) {
                        // 販売所・取引履歴サマリ
                        posTradeSumSimple(
                                symbol,
                                null,
                                exchangeSummaryData,
                                exchangeSummaryData.getJpyConversion(),
                                orderTypes,
                                targetDateFrom,
                                targetDateTo);
                    }
                }
                if (currencyPair.getQuoteCurrency().equals(currency)) {
                    if (symbol.getTradeType().equals(tradeType)) {
                        // 販売所・取引履歴サマリ
                        posTradeSumSimple(
                                symbol,
                                currency,
                                exchangeSummaryData,
                                exchangeSummaryData.getJpyConversion(),
                                orderTypes,
                                targetDateFrom,
                                targetDateTo);
                    }
                }
            }

            /** ③前日分ExchangeSummary取得 */
            Long targetFromBefore = targetFrom - DateUnit.DAY.getMillis();
            Date targetDateFromBefore = new Date(targetFromBefore);
            ExchangeSummary exchangeSummaryBefore =
                    findOne(currency, targetDateFromBefore, targetDateFrom, tradeType);
            log.info(
                    "DBMITO, ExchangeSummaryLog,invalid exchangeSummaryBefore,{}",
                    JsonUtil.encode(exchangeSummaryBefore));
            BigDecimal currentAmountPrevious =
                    (exchangeSummaryBefore != null)
                            ? exchangeSummaryBefore.getCurrentAmount()
                            : BigDecimal.valueOf(0);

            log.info(
                    "DEBUG TMP DBMITO, ExchangeSummaryLog,invalid exchangeSummaryData,"
                            + JsonUtil.encode(exchangeSummaryData));
            /** ④データ編集：共通 */
            exchangeSummaryData.setTargetAt(targetDateFrom);
            exchangeSummaryData.setCurrency(currency);
            exchangeSummaryData.setCurrentAmount(
                    currentAmountPrevious
                            .add(exchangeSummaryData.getSimpleMarketPosTradeBuyAmount())
                            .subtract(exchangeSummaryData.getSimpleMarketPosTradeSellAmount())
                            .subtract(exchangeSummaryData.getSimpleMarketPosTradeFee())
                            .add(exchangeSummaryData.getDepositAmount())
                            .subtract(exchangeSummaryData.getDepositFee())
                            .subtract(exchangeSummaryData.getWithdrawalAmount())
                            .add(exchangeSummaryData.getExpirationContinueReward())
                            .add(exchangeSummaryData.getExpirationNotContinueReward())); // 前日分＋当日各値

            // 1万円出金の場合、withdrawal.amount=10000, withdrawal.withdrawal_fee=100,
            //  => assetから1万円減額(手数料込み)され、出金先口座へは9900円着金する
            // => ここではamountに手数料が含まれているため、計算には含めない
            //      .subtract(exchangeSummaryData.getWithdrawalFee())
            //      .subtract(exchangeSummaryData.getTransactionFee())

            log.info(
                    "DBMITO,ExchangeSummaryLog,invalid currentAmount after calculated,{},currency,{}",
                    exchangeSummaryData.getCurrentAmount(),
                    currency);

            if (exchangeSummaryData.getCurrentAmount().signum() < 0) {
                log.warn(
                        "ExchangeSummaryLog,minus currentAmount after calculated,{},currency,{}",
                        exchangeSummaryData.getCurrentAmount(),
                        currency);
                //        throw new CustomException(ErrorCode.REQUEST_ERROR_AMOUNT_NOT_POSITIVE);
            }

            // 通貨単位リストにadd
            exchangeSummaryList.add(exchangeSummaryData);
        }

        /** 3.前日集計データDelete & Insert */
        // 1トランザクション1コミットとする (削除〜挿入まで、全通貨分)
        log.info(
                "DBMITO,ExchangeSummaryLog,exchangeSummaryList,"
                        + JsonUtil.encode(exchangeSummaryList));

        customTransactionManager.execute(
                entityManager -> {
                    for (ExchangeSummary exchangeSummaryData : exchangeSummaryList) {
                        /** ①挿入対象の当日分exchange_summary存在チェック */
                        ExchangeSummary exchangeSummaryTarget =
                                findOne(
                                        exchangeSummaryData.getCurrency(),
                                        targetDateFrom,
                                        targetDateTo,
                                        entityManager,
                                        tradeType);

                        log.info(
                                "DBMITO,ExchangeSummaryLog,exist check,{}",
                                JsonUtil.encode(exchangeSummaryTarget));

                        /** ②挿入対象のexchange_summaryが存在する場合、delete */
                        if (exchangeSummaryTarget != null) {
                            delete(exchangeSummaryTarget, entityManager);
                            log.info(
                                    "DBMITO,ExchangeSummaryLog,exchangeSummaryToday(exist) deleted");
                        }
                        exchangeSummaryData.setTradeType(tradeType);
                        /** ③exchange_summary挿入処理 */
                        save(exchangeSummaryData, entityManager);
                    }
                });

        // 重複チェック
        List<ExchangeSummary> exchangeSummaryTargetList =
                findByCondition(null, targetDateFrom, targetDateTo, false, tradeType);

        log.info(
                "DBMITO,ExchangeSummaryLog,duplicate key(currency, target_at),"
                        + "exchangeSummaryTargetList.size,"
                        + exchangeSummaryTargetList.size()
                        + ",currencyConfigs.size,"
                        + currencyConfigs.size());

        if (exchangeSummaryTargetList.size() != currencyConfigs.size()) {
            log.info(
                    "ExchangeSummaryLog,duplicate key(currency, target_at),"
                            + "exchangeSummaryTargetList.size,"
                            + exchangeSummaryTargetList.size()
                            + ",currencyConfigs.size,"
                            + currencyConfigs.size());
            throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
        }

        log.info(
                "ExchangeSummaryLog,end,targetDay,"
                        + targetDay
                        + ",targetDateFrom,"
                        + targetDateFrom
                        + ",targetDateTo,"
                        + targetDateTo);
    }

    public PageData<ExchangeSummary> findByConditionPageData(
            Currency currency,
            Date dateFrom,
            Date dateTo,
            boolean isAscending,
            TradeType tradeType,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<ExchangeSummary>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicates(
                                                criteriaBuilder,
                                                root,
                                                currency,
                                                dateFrom,
                                                dateTo,
                                                tradeType));
                            }
                        });
        return new PageData<ExchangeSummary>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<ExchangeSummary, List<ExchangeSummary>>() {
                            @Override
                            public List<ExchangeSummary> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        createPredicates(
                                                criteriaBuilder,
                                                root,
                                                currency,
                                                dateFrom,
                                                dateTo,
                                                tradeType),
                                        number,
                                        size,
                                        isAscending
                                                ? criteriaBuilder.desc(
                                                        root.get(ExchangeSummary_.id))
                                                : criteriaBuilder.asc(
                                                        root.get(ExchangeSummary_.id)));
                            }
                        }));
    }
}
