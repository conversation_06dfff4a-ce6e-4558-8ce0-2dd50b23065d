package point.common.service;

import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.constant.CommonConstants;
import point.common.entity.UserEkycRequestHistory;
import point.common.repos.UserEkycRequestHistoryRepository;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
@RequiredArgsConstructor
public class UserEkycRequestHistoryService {

    private final UserEkycRequestHistoryRepository repository;

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void applicationIdHistory(
            Long userId,
            String requestHeader,
            String requestEntity,
            String responseHeader,
            String responseEntity,
            String applicantIdUrl) {
        UserEkycRequestHistory entity =
                UserEkycRequestHistory.builder()
                        .userId(userId)
                        .type("CreateApplicantId")
                        .requestTime(new Date())
                        .requestHeaderJson(requestHeader)
                        .requestEntityJson(requestEntity)
                        .responseHeaderJson(responseHeader)
                        .responseEntityJson(responseEntity)
                        .url(applicantIdUrl)
                        .build();
        repository.save(entity);
    }

    @Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void ekycUrlHistory(
            Long userId,
            String requestHeader,
            String requestEntity,
            String responseHeader,
            String responseEntity,
            String ekycUrl) {
        UserEkycRequestHistory entity =
                UserEkycRequestHistory.builder()
                        .userId(userId)
                        .type("CreateEkycUrl")
                        .requestTime(new Date())
                        .requestHeaderJson(requestHeader)
                        .requestEntityJson(requestEntity)
                        .responseHeaderJson(responseHeader)
                        .responseEntityJson(responseEntity)
                        .url(ekycUrl)
                        .build();
        repository.save(entity);
    }

    @Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void bpoResultHistory(
            Long userId,
            String requestHeader,
            String requestEntity,
            String responseHeader,
            String responseEntity,
            String ekycUrl) {
        UserEkycRequestHistory entity =
                UserEkycRequestHistory.builder()
                        .userId(userId)
                        .type("FetchBpoResult")
                        .requestTime(new Date())
                        .requestHeaderJson(requestHeader)
                        .requestEntityJson(requestEntity)
                        .responseHeaderJson(responseHeader)
                        .responseEntityJson(responseEntity)
                        .url(ekycUrl)
                        .build();
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        repository.save(entity);
    }
}
