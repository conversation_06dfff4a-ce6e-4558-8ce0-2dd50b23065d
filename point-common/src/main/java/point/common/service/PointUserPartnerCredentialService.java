package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.entity.PointUserPartnerCredential;
import point.common.predicate.PointUserPartnerCredentialPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointUserPartnerCredentialService
        extends EntityService<PointUserPartnerCredential, PointUserPartnerCredentialPredicate> {

    @Override
    public Class<PointUserPartnerCredential> getEntityClass() {
        return PointUserPartnerCredential.class;
    }

    public PointUserPartnerCredential findByCondition(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PointUserPartnerCredential query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
