package point.common.service;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.constant.CommonConstants;
import point.common.entity.ChoiceActivity;
import point.common.entity.ChoiceVote;
import point.common.repos.ChoiceActivityRepository;
import point.common.repos.ChoiceVoteRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserVoteRewardCalculatorService {

    private static final int MAX_VOTE_RECORDS = 10000;
    private static final int PAGE_SIZE = 2000;

    private final UserVoteRewardService userVoteRewardService;
    private final ChoiceVoteRepository choiceVoteRepository;
    private final ChoiceActivityRepository choiceActivityRepository;

    /** 计算并分配投票奖励 */
    @Transactional(
            rollbackFor = Exception.class,
            readOnly = true,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void calculateVotePower() {
        ChoiceActivity activity = userVoteRewardService.findYesterdayActivity();
        if (Objects.isNull(activity)) {
            log.info("No activity found for yesterday, skip processing");
            return;
        }

        log.info("Start processing vote rewards for activity ID: {}", activity.getId());
        long startTime = System.currentTimeMillis();
        int priceComparison =
                activity.getBasePriceElectionDay().compareTo(activity.getBasePriceVotingDay());
        log.info(
                "Activity[{}] result: {} (0=Draw,1=UP,-1=DOWN)", activity.getId(), priceComparison);

        try {
            Long voteTotalRecords =
                    choiceVoteRepository.countChoiceVoteByActivityId(activity.getId());
            if (voteTotalRecords == 0) {
                userVoteRewardService.handleNoVotesCase(activity, priceComparison);
                return;
            }
            if (voteTotalRecords > MAX_VOTE_RECORDS) {
                log.warn(
                        "Vote records exceed maximum limit ({} > {}), consider batch processing",
                        voteTotalRecords,
                        MAX_VOTE_RECORDS);
            }
            Long lastId = null;
            while (true) {
                Page<ChoiceVote> votePage;
                if (lastId == null) {
                    votePage =
                            choiceVoteRepository.findByActivityIdOrderByIdAsc(
                                    activity.getId(), PageRequest.of(0, PAGE_SIZE));
                    log.info("Fetching vote data for activity[ID:{}]", activity.getId());
                } else {
                    votePage =
                            choiceVoteRepository.findByActivityIdAndIdGreaterThanOrderByIdAsc(
                                    activity.getId(), lastId, PageRequest.of(0, PAGE_SIZE));
                    log.info(
                            "Fetching vote data for activity[ID:{}] starting from voteId: {}",
                            activity.getId(),
                            lastId);
                }
                List<ChoiceVote> votes = votePage.getContent();
                if (votes.isEmpty()) {
                    break;
                }
                userVoteRewardService.processRewards(activity, priceComparison, votes);
                lastId = votes.get(votes.size() - 1).getId();
                if (votes.size() < PAGE_SIZE) {
                    break;
                }
            }
            log.info(
                    "Completed vote reward processing for activity[{}], took {}ms",
                    activity.getId(),
                    System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error(
                    "Failed to process vote rewards for activity[{}]: {}",
                    activity.getId(),
                    e.getMessage(),
                    e);
            throw e;
        }
    }
}
