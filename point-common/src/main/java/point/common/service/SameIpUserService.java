package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.entity.SameIpUser;
import point.common.entity.SameIpUser_;
import point.common.model.response.PageData;
import point.common.predicate.SameIpUserPredicate;

@RequiredArgsConstructor
@Service
public class SameIpUserService extends EntityService<SameIpUser, SameIpUserPredicate> {

    @Override
    public Class<SameIpUser> getEntityClass() {
        return SameIpUser.class;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<SameIpUser> root,
            Long dateFrom,
            Long dateTo,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();

        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(dateFrom)));
        }

        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
        }

        return predicates;
    }

    public PageData<SameIpUser> findByConditionPageData(
            Long dateFrom, Long dateTo, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                dateFrom,
                                                dateTo,
                                                number,
                                                size));
                            }
                        });

        return new PageData<SameIpUser>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<SameIpUser, List<SameIpUser>>() {
                            @Override
                            public List<SameIpUser> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                dateFrom,
                                                dateTo,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(SameIpUser_.id)));
                            }
                        }));
    }

    public List<SameIpUser> findAllByCondition(Long dateFrom, Long dateTo) {
        List<SameIpUser> SameIpUser =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<SameIpUser, List<SameIpUser>>() {

                            @Override
                            public List<SameIpUser> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                dateFrom,
                                                dateTo,
                                                0,
                                                Integer.MAX_VALUE);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(SameIpUser_.id)));
                            }
                        });
        return SameIpUser;
    }

    public List<SameIpUser> findByCondition(
            Long dateFrom, Long dateTo, Integer number, Integer size) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<SameIpUser, List<SameIpUser>>() {
                    @Override
                    public List<SameIpUser> query() {
                        List<Predicate> predicates = new ArrayList<>();

                        if (dateFrom != null) {
                            predicates.add(
                                    predicate.greaterThanOrEqualToCreatedAt(
                                            criteriaBuilder, root, new Date(dateFrom)));
                        }

                        if (dateTo != null) {
                            predicates.add(
                                    predicate.lessThanCreatedAt(
                                            criteriaBuilder, root, new Date(dateTo)));
                        }

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(SameIpUser_.id)));
                    }
                });
    }
}
