package point.common.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.exception.GameException;
import point.common.model.response.GlobalApiResponse;
import point.common.predicate.ChoiceRewardWithdrawalPredicate;
import point.common.repos.ChoiceRewardRepository;
import point.common.util.ChoiceActivityUtil;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChoiceRewardWithdrawalService
        extends EntityService<ChoicePWithdrawal, ChoiceRewardWithdrawalPredicate> {

    private final ChoicePowerService choicePowerService;
    private final AssetService assetService;
    private final ChoiceRewardService choiceRewardService;
    private final ChoicePowerUserRelService choicePowerUserRelService;
    private final ChoiceRewardRepository choiceRewardRepository;
    private final UserVoteRewardService userVoteRewardService;
    private final ChoiceActivityService choiceActivityService;

    @Override
    public Class<ChoicePWithdrawal> getEntityClass() {
        return ChoicePWithdrawal.class;
    }

    public ResponseEntity<GlobalApiResponse<String>> rewardWithdrawal(
            Pair<Long, Long> userIdPair,
            BigDecimal withdrawAmount,
            WithdrawType withdrawType,
            UserIdType userIdType)
            throws Exception {

        if (!isInteger(withdrawAmount)) {
            throw new GameException(
                    ErrorCode.GAME_INVALID_WITHDRAW_AMOUNT,
                    ErrorCode.GAME_INVALID_WITHDRAW_AMOUNT.getMessage());
        }

        List<ChoiceReward> investReward =
                choiceRewardService.findByCondition(
                        userIdPair.getLeft(),
                        List.of(
                                WithdrawStatusType.PENDING,
                                WithdrawStatusType.PARTIALLY_WITHDRAWN));

        List<ChoiceReward> operateReward =
                choiceRewardService.findByCondition(
                        userIdPair.getRight(),
                        List.of(
                                WithdrawStatusType.PENDING,
                                WithdrawStatusType.PARTIALLY_WITHDRAWN));

        // -- start process reward
        List<ChoiceReward> allRewards =
                Stream.concat(investReward.stream(), operateReward.stream())
                        .sorted(Comparator.comparingLong(AbstractEntity::getId))
                        .toList();

        BigDecimal total = BigDecimal.ZERO;

        total =
                allRewards.stream()
                        .map(
                                reward ->
                                        Objects.nonNull(reward.getRemainingAmount())
                                                ? reward.getRemainingAmount() // the status is
                                                // PARTIALLY_WITHDRAWN
                                                : reward.getAmount())
                        .reduce(total, BigDecimal::add);
        //  user 7~9:00，山分けポイント，total include worker execute reward
        BigDecimal finalTotal = total;
        if (ChoiceActivityUtil.isInRewardDistributionTimeRange()
                && total.compareTo(BigDecimal.ZERO) > 0) {
            ChoiceActivity choiceActivityByToday =
                    choiceActivityService.findChoiceActivityByToday();
            if (Objects.isNull(choiceActivityByToday)) {
                choiceActivityByToday = choiceActivityService.findLatestCompletedActivity();
            }
            Optional<ChoiceActivity> previousChoiceActivity =
                    choiceActivityService.findChoiceActivityWithoutIdOrderByIdDesc(
                            choiceActivityByToday.getId());
            if (previousChoiceActivity.isPresent()) {
                ChoiceReward reward =
                        choiceRewardService.findRewardByActivityId(
                                Stream.of(userIdPair.getLeft(), userIdPair.getRight())
                                        .filter(Objects::nonNull)
                                        .toList(),
                                previousChoiceActivity.get().getId());
                if (Objects.nonNull(reward)) {
                    total = total.subtract(reward.getAmount());
                }
            }
        }
        log.info(
                "invest userId:{},operate userId:{} ,withdraw amount:{}, total reward amount:{}",
                userIdPair.getLeft(),
                userIdPair.getRight(),
                withdrawAmount,
                total);
        // must withdraw all integer choiceP
        int result = withdrawAmount.compareTo(new BigDecimal(total.intValue()));
        if (result < 0) {
            throw new GameException(
                    ErrorCode.GAME_WITHDRAW_BALANCE_NOT_EQUAL,
                    ErrorCode.GAME_WITHDRAW_BALANCE_NOT_EQUAL.getMessage());
        }
        if (result > 0) {

            List<Long> idList =
                    Stream.of(userIdPair.getLeft(), userIdPair.getRight())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

            // calculate expire reward
            List<ChoiceReward> expiredReward =
                    choiceRewardRepository
                            .findChoiceRewardByUserIdInAndExpiryTimeLessThanAndWithdrawStatusTypeIn(
                                    idList,
                                    new Date(),
                                    List.of(
                                            WithdrawStatusType.PENDING,
                                            WithdrawStatusType.PARTIALLY_WITHDRAWN));

            if (CollectionUtils.isEmpty(expiredReward)) {
                throw new GameException(
                        ErrorCode.GAME_WITHDRAW_BALANCE_NOT_ENOUGH,
                        ErrorCode.GAME_WITHDRAW_BALANCE_NOT_ENOUGH.getMessage());
            } else {

                BigDecimal totalExpire =
                        expiredReward.stream()
                                .map(
                                        reward ->
                                                reward.getRemainingAmount() != null
                                                        ? reward.getRemainingAmount()
                                                        : reward.getAmount())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                log.info(
                        "exists expired reward,size:{},total expire amount:{}",
                        expiredReward.size(),
                        totalExpire);

                customTransactionManager.execute(
                        entityManager -> {
                            userVoteRewardService.calculateChoicePowerAfterExpireByEntityManager(
                                    expiredReward.get(0).getUserId(), totalExpire, entityManager);
                            for (ChoiceReward reward : expiredReward) {
                                if (reward.getRemainingAmount() != null) {
                                    reward.setWithdrawStatusType(
                                            WithdrawStatusType.PARTIALLY_EXPIRED);
                                } else {
                                    reward.setWithdrawStatusType(WithdrawStatusType.FULLY_EXPIRED);
                                }
                                choiceRewardService.save(reward, entityManager);
                            }
                        });
                return ResponseEntity.ok()
                        .body(
                                new GlobalApiResponse<>(
                                        HttpStatus.OK.value(),
                                        BizCode.CHOICE_P_BALANCE_CHANGE.getMessage()));
            }
        }
        customTransactionManager.execute(
                entityManager -> {
                    log.info(
                            "user type:{},user invest id:{} operate id:{},withdraw amount :{}",
                            userIdType.name(),
                            userIdPair.getLeft(),
                            userIdPair.getRight(),
                            withdrawAmount);

                    // remaining choice power
                    BigDecimal fractionalChoice = finalTotal.subtract(withdrawAmount);

                    log.info("remaining choice power:{}", fractionalChoice);

                    ChoicePower choicePower = getChoicePower(userIdPair);

                    choicePower.setChoicePAmount(fractionalChoice);

                    choicePowerService.save(choicePower, entityManager);

                    // -- start process onHandAmount
                    // invest has two records currency is jpy or point ,but only point support
                    // withdraw投资 2条
                    // jpy point都用 提现只能提到point
                    // operate user only one record in table asset currency is jpy
                    Long userId =
                            WithdrawType.POINT == withdrawType
                                    ? userIdPair.getLeft()
                                    : userIdPair.getRight();

                    Currency currency =
                            WithdrawType.POINT == withdrawType ? Currency.POINT : Currency.JPY;

                    log.info(
                            "update onhand amount ,user id:{}, currency:{}, amount:{}",
                            userId,
                            currency.name(),
                            withdrawAmount);
                    this.updateOnHandAmount(userId, currency, withdrawAmount, entityManager);
                    // -- end process onHandAmount

                    BigDecimal remainingWithdrawAmount = withdrawAmount;
                    for (ChoiceReward reward : allRewards) {
                        if (remainingWithdrawAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            break; // 提现金额分配完毕
                        }

                        BigDecimal availableAmount =
                                Objects.nonNull(reward.getRemainingAmount())
                                        ? reward.getRemainingAmount()
                                        : reward.getAmount();

                        if (availableAmount.compareTo(remainingWithdrawAmount) >= 0) {
                            reward.setRemainingAmount(
                                    availableAmount.subtract(remainingWithdrawAmount));
                            reward.setWithdrawStatusType(
                                    reward.getRemainingAmount().compareTo(BigDecimal.ZERO) == 0
                                            ? WithdrawStatusType.FULLY_WITHDRAWN
                                            : WithdrawStatusType.PARTIALLY_WITHDRAWN);
                            remainingWithdrawAmount = BigDecimal.ZERO; // 提现金额分配完毕
                        } else {
                            // 当前记录的金额不足以提现, 全部提走
                            reward.setRemainingAmount(BigDecimal.ZERO);
                            reward.setWithdrawStatusType(WithdrawStatusType.FULLY_WITHDRAWN);
                            remainingWithdrawAmount =
                                    remainingWithdrawAmount.subtract(availableAmount);
                        }
                        choiceRewardService.save(reward, entityManager);
                    }
                    saveWithdraw(
                            withdrawAmount, withdrawType, userIdType, userIdPair, entityManager);
                });
        return ResponseEntity.ok()
                .body(
                        new GlobalApiResponse<>(
                                HttpStatus.OK.value(), BizCode.WITHDRAW_SUCCESS.getMessage()));
    }

    public ChoicePower getChoicePower(Pair<Long, Long> userIdPair) throws CustomException {
        Long choicePowerId;
        ChoicePowerUserRel investChoicePower =
                choicePowerUserRelService.getChoicePowerUserRel(userIdPair.getLeft());

        ChoicePowerUserRel operatorChoicePower =
                choicePowerUserRelService.getChoicePowerUserRel(userIdPair.getRight());

        if (ObjectUtils.allNull(investChoicePower, operatorChoicePower)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_NOT_EXIST_USER);
        }
        choicePowerId =
                Optional.ofNullable(investChoicePower)
                        .orElse(operatorChoicePower)
                        .getChoicePowerId();

        ChoicePower choicePower = choicePowerService.findOne(choicePowerId);
        log.info("current choice power:{}", choicePower);
        return choicePower;
    }

    public void saveWithdraw(
            BigDecimal total,
            WithdrawType withdrawType,
            UserIdType userIdType,
            Pair<Long, Long> userIdPair,
            EntityManager entityManager)
            throws Exception {
        ChoicePWithdrawal rewardWithdrawal = new ChoicePWithdrawal();

        rewardWithdrawal.setAmount(total);

        rewardWithdrawal.setWithdrawType(withdrawType);

        // current login user id
        if (UserIdType.Invest == userIdType) {
            rewardWithdrawal.setUserId(userIdPair.getLeft());
        }
        if (UserIdType.Operate == userIdType) {
            rewardWithdrawal.setUserId(userIdPair.getRight());
        }

        // target withdraw user id
        if (WithdrawType.JPY == withdrawType) {
            rewardWithdrawal.setTargetUserId(userIdPair.getRight());
        }
        if (WithdrawType.POINT == withdrawType) {
            rewardWithdrawal.setTargetUserId(userIdPair.getLeft());
        }
        rewardWithdrawal.setWithdrawTime(new Date());
        this.save(rewardWithdrawal, entityManager);
    }

    public void updateOnHandAmount(
            Long userId, Currency currency, BigDecimal total, EntityManager entityManager)
            throws Exception {
        assetService.update(userId, currency, total, BigDecimal.ZERO, entityManager);
    }

    private BigDecimal getTotalRewardAmount(Pair<Long, Long> userIdPair) {
        List<ChoiceReward> investReward =
                choiceRewardService.findByCondition(
                        userIdPair.getLeft(),
                        List.of(
                                WithdrawStatusType.PENDING,
                                WithdrawStatusType.PARTIALLY_WITHDRAWN));

        List<ChoiceReward> operateReward =
                choiceRewardService.findByCondition(
                        userIdPair.getRight(),
                        List.of(
                                WithdrawStatusType.PENDING,
                                WithdrawStatusType.PARTIALLY_WITHDRAWN));

        List<ChoiceReward> allRewards = new ArrayList<>();
        allRewards.addAll(investReward);
        allRewards.addAll(operateReward);

        return allRewards.stream()
                .map(
                        reward ->
                                Objects.nonNull(reward.getRemainingAmount())
                                        ? reward.getRemainingAmount()
                                        : reward.getAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Check whether the number is an integer
     *
     * @param number input number
     * @return true: integer false: decimal
     */
    public boolean isInteger(BigDecimal number) {
        return number.scale() <= 0 || number.stripTrailingZeros().scale() <= 0;
    }

    public void splitToIntegerWithdrawAmount(
            List<ChoiceReward> list, BigDecimal totalRemainingPart) {
        for (int i = list.size() - 1; i >= 0; i--) {
            ChoiceReward reward = list.get(i);
            BigDecimal currentValue =
                    reward.getRemainingAmount() != null
                            ? reward.getRemainingAmount()
                            : reward.getAmount();

            if (isInteger(currentValue)) {
                continue;
            }

            if (currentValue.compareTo(totalRemainingPart) < 0) {
                continue;
            }
            reward.setRemainingAmount(totalRemainingPart);
            reward.setWithdrawStatusType(WithdrawStatusType.PARTIALLY_WITHDRAWN);
            break;
        }
    }
}
