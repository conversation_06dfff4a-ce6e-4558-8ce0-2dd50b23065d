package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ErrorCode;
import point.common.entity.PosMMConfig;
import point.common.exception.CustomException;
import point.common.model.request.PosMMConfigForm;
import point.common.predicate.PosMMConfigPredicate;
import point.common.repos.PosMMConfigRepository;

@RequiredArgsConstructor
@Service
public class PosMMConfigService extends EntityService<PosMMConfig, PosMMConfigPredicate> {

    @Autowired PosMMConfigRepository posMMConfigRepository;

    @Override
    public Class<PosMMConfig> getEntityClass() {
        return PosMMConfig.class;
    }

    public PosMMConfig getConfig(@PathVariable Long id) throws CustomException {
        return posMMConfigRepository
                .findById(id)
                .orElseThrow(() -> new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR));
    }

    public List<PosMMConfig> getAllConfig(Long symbolId) {

        List<PosMMConfig> result =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<PosMMConfig> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder, root, symbolId);
                                return getResultList(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });

        return result;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder, Root<PosMMConfig> root, Long symbol) {
        List<Predicate> predicates = new ArrayList<>();
        if (symbol != null) {
            predicates.add(predicate.equalSymbol(criteriaBuilder, root, symbol));
        }
        return predicates;
    }

    public PosMMConfig editConfig(PosMMConfigForm form) throws Exception {
        return customTransactionManager.execute(
                entityManager -> {
                    PosMMConfig config =
                            posMMConfigRepository
                                    .findById(form.getId())
                                    .orElseThrow(
                                            () ->
                                                    new CustomException(
                                                            ErrorCode.COMMON_ERROR_SYSTEM_ERROR));
                    config.setEnabled(form.isEnabled());
                    config.setQuantity(form.getQuantity());
                    return this.save(config, entityManager);
                });
    }
}
