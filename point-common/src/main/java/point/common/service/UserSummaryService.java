package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ErrorCode;
import point.common.constant.KycStatus;
import point.common.entity.User;
import point.common.entity.UserSummary;
import point.common.entity.UserSummary_;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.UserSummaryPredicate;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserSummaryService extends EntityService<UserSummary, UserSummaryPredicate> {

    private final UserService userService;
    private final UserKycService userKycService;

    @Override
    public Class<UserSummary> getEntityClass() {
        return UserSummary.class;
    }

    public PageData<UserSummary> findByTargetAtPageData(
            Date targetAtFrom, Date targetAtTo, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                targetAtFrom,
                                                targetAtTo,
                                                null,
                                                null,
                                                null));
                            }
                        });
        return new PageData<UserSummary>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<UserSummary, List<UserSummary>>() {
                            @Override
                            public List<UserSummary> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                targetAtFrom,
                                                targetAtTo,
                                                null,
                                                null,
                                                null);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(UserSummary_.id)));
                            }
                        }));
    }

    public void calculate(String targetDay) throws CustomException {
        // 日付チェックとtargetAtの設定
        // targetAtは対象日の00:00を示す
        Date targetAt = FormatUtil.parseJst(targetDay, FormatPattern.YYYYMMDD);
        if (targetAt == null) {
            log.error("invalid target date string: targetDay=" + targetDay);
            throw new CustomException(ErrorCode.COMMON_ERROR_INVALID_DATE_STRING);
        }

        /*
         *  ベースデータの取得
         */

        // 全件ユーザー
        List<User> userListAll = userService.findAll();

        /*
         *  レコード作成
         */
        UserSummary userSummary = new UserSummary(targetAt);

        // 全user件数設定
        userSummary.setUsers(Long.valueOf(userListAll.size()));

        // 口座開設完了
        userSummary.setUserKycAccountOpeningDone(
                userListAll.stream()
                        .filter(user -> user.getKycStatus() == KycStatus.ACCOUNT_OPENING_DONE)
                        .count());

        // 承認（変更完了）
        userSummary.setUserKycDone(
                userListAll.stream().filter(user -> user.getKycStatus() == KycStatus.DONE).count());

        // 保存
        this.save(userSummary);
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<UserSummary> root,
            Date targetAtFrom,
            Date targetAtTo,
            Long users,
            Long userKycAccountOpeningDone,
            Long userKycDone) {
        List<Predicate> predicates = new ArrayList<>();

        if (targetAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, targetAtFrom));
        }

        if (targetAtTo != null) {
            predicates.add(predicate.lessThanTargetdAt(criteriaBuilder, root, targetAtTo));
        }

        if (users != null) {
            predicates.add(predicate.equalUsers(criteriaBuilder, root, users));
        }

        if (userKycAccountOpeningDone != null) {
            predicates.add(
                    predicate.equalUserKycAccountOpeningDone(
                            criteriaBuilder, root, userKycAccountOpeningDone));
        }

        if (userKycDone != null) {
            predicates.add(predicate.equalUserKycDone(criteriaBuilder, root, userKycDone));
        }

        return predicates;
    }
}
