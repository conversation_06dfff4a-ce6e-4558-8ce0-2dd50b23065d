package point.common.service;

import java.math.BigDecimal;
import java.util.Objects;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.RedisManager;
import point.common.constant.*;
import point.common.exception.CustomException;
import point.operate.entity.OperateAggregatedOrder;
import point.operate.entity.OperateCoverOrder;
import point.operate.service.OperateAggregatedOrderService;
import point.operate.service.OperateCoverOrderService;
import point.pos.service.PosOrderService;

/**
 * Offsetting of buying and selling orders
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperateInternalOffsetService {

    private final RedisManager redisManager;
    private final OperateAggregatedOrderService operateAggregatedOrderService;
    private final PosOrderService posOrderService;
    private static final String LOCK_KEY_PREFIX = "operate_internal_offset_lock:";

    public void offsetOrders(Long symbolId, OperateCoverOrderService coverOrderService)
            throws CustomException {
        String lockKey = LOCK_KEY_PREFIX + symbolId;
        try {
            log.info("Attempting to acquire lock for symbolId: {}", symbolId);
            if (!redisManager.executeWithLock(
                    lockKey,
                    RedisManager.LockParams.EXECUTE_ORDER,
                    () -> {
                        log.info("Successfully acquired lock for symbolId: {}", symbolId);

                        OperateAggregatedOrder sellOrder =
                                operateAggregatedOrderService.findAllByCondition(
                                        symbolId, OrderSide.SELL, OrderType.MARKET);
                        OperateAggregatedOrder buyOrder =
                                operateAggregatedOrderService.findAllByCondition(
                                        symbolId, OrderSide.BUY, OrderType.MARKET);

                        if (Objects.isNull(sellOrder) || Objects.isNull(buyOrder)) {
                            log.info(
                                    "No orders found for symbolId: {}. skip order offsetting",
                                    symbolId);
                            return;
                        }

                        log.info(
                                "Found {} amount for SELL order and {} amount for BUY order for symbolId: {}",
                                sellOrder.getAmount().toPlainString(),
                                buyOrder.getAmount().toPlainString(),
                                symbolId);

                        BigDecimal offsetAmount = sellOrder.getAmount().min(buyOrder.getAmount());
                        if (offsetAmount.compareTo(BigDecimal.ZERO) > 0) {
                            log.info(
                                    "Executing offset for SELL order ID: {} and BUY order ID: {} with amount: {}",
                                    sellOrder.getId(),
                                    buyOrder.getId(),
                                    offsetAmount.toPlainString());

                            coverOrderService.customTransactionManager.execute(
                                    entityManager -> {
                                        executeOffset(
                                                sellOrder,
                                                buyOrder,
                                                offsetAmount,
                                                coverOrderService,
                                                entityManager);
                                    });

                        } else {
                            log.info(
                                    "No offset possible for SELL order ID: {} and BUY order ID: {}",
                                    sellOrder.getId(),
                                    buyOrder.getId());
                        }
                    })) {
                log.info(
                        "Could not acquire lock for symbolId: {}. Another worker may have taken the lock",
                        symbolId);
            }
        } catch (Exception e) {
            log.warn(
                    "Failed to execute offsetting orders for symbolId: {}, errorMessage: {}",
                    symbolId,
                    e.getMessage(),
                    e);
            throw new CustomException(
                    ErrorCode.COMMON_ERROR_SYSTEM_ERROR,
                    "Failed to execute offsetting orders for symbolId: " + symbolId);
        }
    }

    private void executeOffset(
            OperateAggregatedOrder sellOrder,
            OperateAggregatedOrder buyOrder,
            BigDecimal offsetAmount,
            OperateCoverOrderService coverOrderService,
            EntityManager entityManager)
            throws Exception {
        log.info("Reducing SELL order ID: {} amount by {}", sellOrder.getId(), offsetAmount);
        sellOrder.setAmount(sellOrder.getAmount().subtract(offsetAmount));
        log.info("Reducing BUY order ID: {} amount by {}", buyOrder.getId(), offsetAmount);
        buyOrder.setAmount(buyOrder.getAmount().subtract(offsetAmount));

        if (sellOrder.getAmount().compareTo(BigDecimal.ZERO) < 0
                || buyOrder.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            log.warn(
                    "No offset possible for SELL order ID: {} and BUY order ID: {} due to insufficient amount",
                    sellOrder.getId(),
                    buyOrder.getId());
            return;
        }

        operateAggregatedOrderService.save(sellOrder, entityManager);
        operateAggregatedOrderService.save(buyOrder, entityManager);

        log.info("Creating cover order for SELL order ID: {}", sellOrder.getId());
        createCoverOrder(sellOrder, offsetAmount, OrderSide.SELL, coverOrderService, entityManager);
        log.info("Creating cover order for BUY order ID: {}", buyOrder.getId());
        createCoverOrder(buyOrder, offsetAmount, OrderSide.BUY, coverOrderService, entityManager);
    }

    private void createCoverOrder(
            OperateAggregatedOrder aggregatedOrder,
            BigDecimal offsetAmount,
            OrderSide orderSide,
            OperateCoverOrderService coverOrderService,
            EntityManager entityManager)
            throws Exception {
        log.info(
                "Creating new cover order for aggregated order ID: {} with amount: {} and side: {}",
                aggregatedOrder.getId(),
                offsetAmount,
                orderSide);

        // fetch pos_order for get price
        //        Optional<PosOrder> posOrderOptional =
        //                posOrderService.findByCondition(
        //                        aggregatedOrder.getSymbolId(), orderSide, UserIdType.Operate);

        OperateCoverOrder coverOrder = new OperateCoverOrder();
        coverOrder.setSymbolId(aggregatedOrder.getSymbolId());
        coverOrder.setAggregatedOrderId(aggregatedOrder.getId());
        coverOrder.setOrderSide(orderSide);
        coverOrder.setOrderType(OrderType.MARKET);
        //
        // coverOrder.setPrice(posOrderOptional.map(PosOrder::getPrice).orElse(BigDecimal.ZERO));
        coverOrder.setPrice(BigDecimal.ZERO); // price is 0 for offset orders
        coverOrder.setUsdtPrice(BigDecimal.ZERO);
        coverOrder.setAmount(offsetAmount);
        coverOrder.setRemainingAmount(BigDecimal.ZERO);
        coverOrder.setFee(BigDecimal.ZERO);
        coverOrder.setMm(Exchange.OFFSET);
        coverOrder.setMmOrderId(null);
        coverOrder.setOrderStatus(OrderStatus.FULLY_FILLED);
        coverOrder.setOffset(true);

        coverOrderService.save(coverOrder, entityManager);
        log.info(
                "Successfully saved cover order for aggregated order ID: {}",
                aggregatedOrder.getId());
    }
}
