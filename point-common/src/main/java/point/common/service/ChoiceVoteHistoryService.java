package point.common.service;

import java.util.*;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.entity.ChoiceVoteHistory;
import point.common.predicate.ChoiceVoteHistoryPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoiceVoteHistoryService
        extends EntityService<ChoiceVoteHistory, ChoiceVoteHistoryPredicate> {

    @Override
    public Class<ChoiceVoteHistory> getEntityClass() {
        return ChoiceVoteHistory.class;
    }

    public void createVoteHistory(
            Long voteId,
            Long activityId,
            Long userId,
            Long beforeVotePower,
            Long afterVotePower,
            EntityManager entityManager)
            throws Exception {
        ChoiceVoteHistory entity = new ChoiceVoteHistory();
        entity.setVoteId(voteId);
        entity.setActivityId(activityId);
        entity.setUserId(userId);
        entity.setBeforeVotePower(beforeVotePower);
        entity.setAfterVotePower(afterVotePower);
        customTransactionManager.save(entity, entityManager);
    }
}
