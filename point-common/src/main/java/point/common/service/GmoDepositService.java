package point.common.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;
import java.util.Map.Entry;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.service.spi.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import point.common.component.CustomLogger;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.component.RedisManager;
import point.common.component.SesManager;
import point.common.config.GmoAuthorizationTokenConfiguration;
import point.common.config.GmoConfig;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.Asset;
import point.common.entity.FiatDeposit;
import point.common.entity.GmoDeposit;
import point.common.entity.GmoDeposit_;
import point.common.entity.MailNoreply;
import point.common.entity.OnetimeBankAccount;
import point.common.entity.User;
import point.common.exception.CustomException;
import point.common.model.request.GmoDepositDetailsForm;
import point.common.model.request.GmoDepositForm;
import point.common.model.response.DepositTransactionsData;
import point.common.model.response.PageData;
import point.common.model.response.VaTransactionsData;
import point.common.predicate.GmoDepositPredicate;
import point.common.repos.GmoServiceRepository;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.common.util.JsonUtil;
import point.common.util.TransliteratorUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class GmoDepositService extends EntityService<GmoDeposit, GmoDepositPredicate> {

    private static final CustomLogger customLogger =
            new CustomLogger(GmoDepositService.class.getName());

    private final String GMO_LOG_PREFIX = "worker [GMO]";

    /** 取引日のソート順を指定するコード値 ・1=昇順 ・2=降順 */
    private final String SORT_ORDER_CODE = "1";

    private final GmoService gmoService;

    private final RestTemplate restTemplate;

    private final GmoConfig gmoConfig;

    private final UserService userService;

    private final OnetimeBankAccountService onetimeBankAccountService;

    private final FiatDepositService fiatDepositService;

    private final AssetService assetService;

    private final GmoServiceRepository gmoServiceRepository;

    private final MailNoreplyService mailNoreplyService;

    private final SesManager sesManager;

    private MailNoreply mailNoreply;

    @Autowired private RedisManager redisManager;

    private static String getLockKey(Long userId, Currency currency) {
        return "lock:asset:" + userId + ":" + currency;
    }

    //  @PostConstruct
    //  public void init() {
    //    mailNoreply = mailNoreplyService.findOne(MailNoreplyType.DEPOSIT_DONE);
    //  }

    @Override
    public Class<GmoDeposit> getEntityClass() {
        return GmoDeposit.class;
    }

    public PageData<GmoDeposit> findByConditionPageData(
            Boolean noFiatDepositId,
            Long fiatDepositId,
            Long dateFrom,
            Long dateTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                noFiatDepositId,
                                                fiatDepositId,
                                                dateFrom,
                                                dateTo));
                            }
                        });

        return new PageData<GmoDeposit>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<GmoDeposit> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                noFiatDepositId,
                                                fiatDepositId,
                                                dateFrom,
                                                dateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        new Order[] {
                                            criteriaBuilder.desc(root.get(GmoDeposit_.id))
                                        });
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<GmoDeposit> root,
            Boolean noFiatDepositId,
            Long fiatDepositId,
            Long dateFrom,
            Long dateTo) {
        List<Predicate> predicates = new ArrayList<>();
        if (noFiatDepositId) {
            predicates.add(predicate.isNullOfFiatDepositId(criteriaBuilder, root));
        }

        if (fiatDepositId != null) {
            predicates.add(predicate.equalFiatDepositId(criteriaBuilder, root, fiatDepositId));
        }

        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(dateFrom)));
        }

        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
        }
        return predicates;
    }

    public Optional<DepositTransactionsData> invokeGmoDepositTransactions(
            GmoDepositForm gmoDepositForm) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        headers.add(HttpHeaders.ACCEPT_CHARSET, String.valueOf(StandardCharsets.UTF_8));
        headers.add(CommonConstants.GMO_HEADER_TOKEN_KEY, gmoDepositForm.getAccessToken());
        log.info("{} request accessToken : {}", GMO_LOG_PREFIX, gmoDepositForm.getAccessToken());
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        MultiValueMap<String, String> requestParam = new LinkedMultiValueMap<>();
        requestParam.add("raId", gmoDepositForm.getRaId());
        requestParam.add("dateFrom", Optional.ofNullable(gmoDepositForm.getDateFrom()).orElse(""));
        requestParam.add("dateTo", Optional.ofNullable(gmoDepositForm.getDateTo()).orElse(""));
        requestParam.add(
                "sortOrderCode",
                Optional.ofNullable(gmoDepositForm.getSortOrderCode()).orElse(SORT_ORDER_CODE));
        requestParam.add(
                "nextItemKey", Optional.ofNullable(gmoDepositForm.getNextItemKey()).orElse(""));
        log.info("{} request api deposit transactions parameter {}", GMO_LOG_PREFIX, requestParam);
        String host_api = gmoConfig.getStgBaseEndpoint() + gmoConfig.getDepositTransactions();
        URI uri =
                UriComponentsBuilder.fromHttpUrl(host_api)
                        .queryParams(requestParam)
                        .build()
                        .toUri();
        log.info("{} request host: {} deposit transactions", GMO_LOG_PREFIX, uri.getHost());
        log.info("{} request api : {} deposit transactions", GMO_LOG_PREFIX, uri.getPath());
        setRestTemplate();
        ResponseEntity<DepositTransactionsData> responseEntity =
                restTemplate.exchange(
                        uri, HttpMethod.GET, requestEntity, DepositTransactionsData.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error(
                    "{} http get request return errorCode: {},errorMessage: {}",
                    GMO_LOG_PREFIX,
                    responseEntity.getBody().getErrorCode(),
                    responseEntity.getBody().getErrorMessage());
            return Optional.empty();
        }
        log.info("{} body :{}", GMO_LOG_PREFIX, JsonUtil.encode(responseEntity.getBody()));
        return Optional.ofNullable(responseEntity.getBody());
    }

    private void setRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setOutputStreaming(false);
        ResponseErrorHandler responseErrorHandler =
                new ResponseErrorHandler() {
                    @Override
                    public boolean hasError(ClientHttpResponse response) throws IOException {
                        return true;
                    }

                    @Override
                    public void handleError(ClientHttpResponse response) throws IOException {
                        log.info(
                                "{} restTemplate response information statusCode is :{}",
                                GMO_LOG_PREFIX,
                                response.getStatusCode());
                    }
                };
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter =
                new MappingJackson2HttpMessageConverter();
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(
                Arrays.asList(MediaType.TEXT_HTML, MediaType.TEXT_PLAIN));
        restTemplate.getMessageConverters().add(mappingJackson2HttpMessageConverter);
        restTemplate.setRequestFactory(factory);
        restTemplate.setErrorHandler(responseErrorHandler);
    }

    public void calculate() throws Exception {
        // get token
        Optional<GmoAuthorizationTokenConfiguration> optional =
                Optional.ofNullable(gmoService.getGmoAuthConfig());
        if (optional.isEmpty()) {
            log.info("{} find token failure token is empty ", GMO_LOG_PREFIX);
            return;
        }
        String accessToken = optional.get().getAccessToken();
        // get raId
        String raId = gmoService.accounts(accessToken);
        if (StringUtils.isBlank(raId)) {
            log.info("{} request raId failure raId: {} ", GMO_LOG_PREFIX, raId);
            return;
        }
        String gmoDepositData = gmoServiceRepository.gmoLatestItemKey();
        // 対象期間Fromは照会する日付より1日前の日付を指定
        String dateFrom =
                FormatUtil.formatJst(
                        new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000),
                        FormatPattern.YYYY_MM_DD);
        // 対象期間Toは照会する日付を指定
        String dateTo =
                FormatUtil.formatJst(
                        new Date(System.currentTimeMillis()), FormatPattern.YYYY_MM_DD);
        GmoDepositForm gmoDepositForm =
                GmoDepositForm.builder()
                        .raId(raId)
                        .sortOrderCode(SORT_ORDER_CODE)
                        .nextItemKey(gmoDepositData)
                        .accessToken(accessToken)
                        .dateFrom(dateFrom)
                        .dateTo(dateTo)
                        .build();
        Optional<DepositTransactionsData> optionalData =
                invokeGmoDepositTransactions(gmoDepositForm);
        if (optionalData.isEmpty() || optionalData.get().getVaTransactions().size() == 0) {
            log.info("{} response deposit data is null vaTransactions is empty ", GMO_LOG_PREFIX);
            return;
        }
        DepositTransactionsData depositData = optionalData.get();
        for (VaTransactionsData vaTransaction : depositData.getVaTransactions()) {
            GmoDeposit existGmoDeposit = this.findOneByItemKey(vaTransaction.getItemKey());
            if (existGmoDeposit != null) {
                continue;
            }
            GmoDeposit gmoDeposit = getGmoDeposit(vaTransaction);
            FiatDeposit fiatDeposit = new FiatDeposit();
            OnetimeBankAccount onetimeBankAccount =
                    onetimeBankAccountService.findOneByAccountNumberAndVaId(
                            vaTransaction.getVaAccountNumber(), vaTransaction.getVaId());
            if (onetimeBankAccount != null && onetimeBankAccount.getUserId() != null) {
                User user = userService.findOne(onetimeBankAccount.getUserId());
                log.info("{} select user id:{}", GMO_LOG_PREFIX, user.getId());
                if (user.getUserStatus().equals(UserStatus.ACTIVE)
                        || user.getUserStatus().equals(UserStatus.UNTRADABLE)
                        || user.getUserStatus().equals(UserStatus.UNWITHDRAWABLE)) {
                    // 口座名義チェック
                    String userName = "";
                    if (user.getUserInfoId() != null) {
                        userName =
                                convertSmallKanaToKana(
                                        user.getUserInfo().getLastKana()
                                                + "　"
                                                + user.getUserInfo().getFirstKana());
                        log.info(
                                "{} personal lastKana+firstKana is :{} ", GMO_LOG_PREFIX, userName);
                    }
                    // compare remitter_name_kana & with user info last_kana and first_kana
                    String remitterNameKana =
                            TransliteratorUtil.halfWidthToFullWidth(
                                    vaTransaction.getRemitterNameKana().strip());
                    if (userName.equals(remitterNameKana)) {
                        fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);
                        setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
                        gmoDeposit.setRemarks(vaTransaction.getRemarks());
                        log.info("{} normal deposit process", GMO_LOG_PREFIX);
                    } else {
                        fiatDeposit.setFiatDepositStatus(FiatDepositStatus.UNKNOWN_DEPOSIT);
                        setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
                        gmoDeposit.setRemarks("【入金エラー】口座名義相違");
                        log.info(
                                "{} 【入金エラー】口座名義相違 username:{}, remitterNameKana:{}",
                                GMO_LOG_PREFIX,
                                userName,
                                remitterNameKana);
                    }
                } else {
                    fiatDeposit.setFiatDepositStatus(FiatDepositStatus.UNKNOWN_DEPOSIT);
                    setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
                    gmoDeposit.setRemarks("【入金エラー】その他");
                    log.info("{} 【入金エラー】 userStatus :{}", GMO_LOG_PREFIX, user.getKycStatus());
                }
            } else {
                gmoDeposit.setRemarks("【入金エラー】対象口座ユーザー登録なし");
                log.error("{}【入金エラー】対象口座ユーザー登録なし not fund userId", GMO_LOG_PREFIX);
            }
            this.applyNew(gmoDeposit, fiatDeposit);
        }
    }

    public void reCalculate(String targetDateFrom, String targetDateTo) throws Exception {
        // get token
        Optional<GmoAuthorizationTokenConfiguration> optional =
                Optional.ofNullable(gmoService.getGmoAuthConfig());
        if (optional.isEmpty()) {
            log.info("{} find token failure token is empty ", GMO_LOG_PREFIX);
            return;
        }
        String accessToken = optional.get().getAccessToken();
        // get raId
        String raId = gmoService.accounts(accessToken);
        if (StringUtils.isBlank(raId)) {
            log.info("{} request raId failure raId: {} ", GMO_LOG_PREFIX, raId);
            return;
        }
        GmoDepositForm gmoDepositForm =
                GmoDepositForm.builder()
                        .raId(raId)
                        .dateFrom(targetDateFrom)
                        .dateTo(targetDateTo)
                        .sortOrderCode(SORT_ORDER_CODE)
                        .accessToken(accessToken)
                        .build();
        Optional<DepositTransactionsData> optionalData =
                invokeGmoDepositTransactions(gmoDepositForm);
        if (optionalData.isEmpty() || optionalData.get().getVaTransactions().size() == 0) {
            log.info("{} response deposit data is null vaTransactions is empty ", GMO_LOG_PREFIX);
            return;
        }

        String nextItemKey = "";
        nextItemKey = checkFiatDeposit(optionalData);
        while (!StringUtils.isEmpty(nextItemKey)) {
            log.info("{} nextItemKey is " + nextItemKey, GMO_LOG_PREFIX);
            GmoDepositForm gmoDepositFormNext =
                    GmoDepositForm.builder()
                            .raId(raId)
                            .dateFrom(targetDateFrom)
                            .dateTo(targetDateTo)
                            .sortOrderCode(SORT_ORDER_CODE)
                            .nextItemKey(nextItemKey)
                            .accessToken(accessToken)
                            .build();
            Optional<DepositTransactionsData> optionalDataNext =
                    invokeGmoDepositTransactions(gmoDepositFormNext);
            if (optionalDataNext.isEmpty()
                    || optionalDataNext.get().getVaTransactions().size() == 0) {
                log.info(
                        "{} response deposit data is null vaTransactions is empty ",
                        GMO_LOG_PREFIX);
                break;
            }
            nextItemKey = checkFiatDeposit(optionalDataNext);
        }
    }

    private String checkFiatDeposit(Optional<DepositTransactionsData> optionalData)
            throws Exception {
        String nextItemKey = "";
        DepositTransactionsData depositData = optionalData.get();

        for (VaTransactionsData vaTransaction : depositData.getVaTransactions()) {
            GmoDeposit existGmoDeposit = this.findOneByItemKey(vaTransaction.getItemKey());
            if (existGmoDeposit != null) {
                continue;
            }
            GmoDeposit gmoDeposit = getGmoDeposit(vaTransaction);
            FiatDeposit fiatDeposit = new FiatDeposit();
            OnetimeBankAccount onetimeBankAccount =
                    onetimeBankAccountService.findOneByAccountNumberAndVaId(
                            vaTransaction.getVaAccountNumber(), vaTransaction.getVaId());
            if (onetimeBankAccount != null && onetimeBankAccount.getUserId() != null) {
                User user = userService.findOne(onetimeBankAccount.getUserId());
                log.info("{} select user id:{}", GMO_LOG_PREFIX, user.getId());
                if (user.getUserStatus().equals(UserStatus.ACTIVE)
                        || user.getUserStatus().equals(UserStatus.UNTRADABLE)
                        || user.getUserStatus().equals(UserStatus.UNWITHDRAWABLE)) {
                    // 口座名義チェック
                    String userName = "";
                    if (user.getUserInfoId() != null) {
                        userName =
                                convertSmallKanaToKana(
                                        user.getUserInfo().getLastKana()
                                                + "　"
                                                + user.getUserInfo().getFirstKana());
                        log.info(
                                "{} personal lastKana+firstKana is :{} ", GMO_LOG_PREFIX, userName);
                    }
                    // compare remitter_name_kana & with user info last_kana and first_kana
                    String remitterNameKana =
                            TransliteratorUtil.halfWidthToFullWidth(
                                    vaTransaction.getRemitterNameKana().strip());
                    if (userName.equals(remitterNameKana)) {
                        fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);
                        setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
                        gmoDeposit.setRemarks(vaTransaction.getRemarks());
                        log.info("{} normal deposit process", GMO_LOG_PREFIX);
                    } else {
                        fiatDeposit.setFiatDepositStatus(FiatDepositStatus.UNKNOWN_DEPOSIT);
                        setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
                        gmoDeposit.setRemarks("【入金エラー】口座名義相違");
                        log.info(
                                "{} 【入金エラー】口座名義相違 username:{}, remitterNameKana:{}",
                                GMO_LOG_PREFIX,
                                userName,
                                remitterNameKana);
                    }
                } else {
                    fiatDeposit.setFiatDepositStatus(FiatDepositStatus.UNKNOWN_DEPOSIT);
                    setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
                    gmoDeposit.setRemarks("【入金エラー】その他");
                    log.info("{} 【入金エラー】 userStatus :{}", GMO_LOG_PREFIX, user.getKycStatus());
                }
            } else {
                gmoDeposit.setRemarks("【入金エラー】対象口座ユーザー登録なし");
                log.error("{}【入金エラー】対象口座ユーザー登録なし not fund userId", GMO_LOG_PREFIX);
            }
            this.applyNew(gmoDeposit, fiatDeposit);
        }

        if ("true".equals(depositData.getHasNext())) {
            nextItemKey = depositData.getNextItemKey();
        }
        return nextItemKey;
    }

    private void setFiatDeposit(
            VaTransactionsData vaTransaction,
            FiatDeposit fiatDeposit,
            OnetimeBankAccount onetimeBankAccount) {
        // 日本円入金情報
        fiatDeposit.setOnetimeBankAccountId(onetimeBankAccount.getId());
        fiatDeposit.setUserId(onetimeBankAccount.getUserId());
        fiatDeposit.setAmount(BigDecimal.valueOf(Long.parseLong(vaTransaction.getDepositAmount())));
        fiatDeposit.setFee(BigDecimal.ZERO);
    }

    private GmoDeposit getGmoDeposit(VaTransactionsData vaTransaction) {
        return GmoDeposit.builder()
                .itemKey(vaTransaction.getItemKey())
                .transactionDate(
                        FormatUtil.parse(
                                vaTransaction.getTransactionDate(), FormatPattern.YYYY_MM_DD))
                .vaAccountNameKana(vaTransaction.getVaAccountNameKana())
                .remitterNameKana(vaTransaction.getRemitterNameKana())
                .paymentBankName(vaTransaction.getPaymentBankName())
                .paymentBranchName(vaTransaction.getPaymentBranchName())
                .partnerName(vaTransaction.getPartnerName())
                .depositAmount(Long.valueOf(vaTransaction.getDepositAmount()))
                .vaId(vaTransaction.getVaId())
                .build();
    }

    public void applyNew(GmoDeposit gmoDeposit, FiatDeposit fiatDeposit) throws Exception {

        if (gmoDeposit == null) {
            return;
        }
        // 入金情報を作成できない場合、GMO銀行の入金のみ記録
        if (fiatDeposit.getUserId() == null) {
            customTransactionManager.execute(
                    entityManager -> {
                        try {
                            save(gmoDeposit, entityManager);
                            return;
                        } catch (Exception e) {
                            customLogger.severe(getClass().getName(), e);
                        }
                    });
        } else {
            if (!redisManager.executeWithLock(
                    getLockKey(fiatDeposit.getUserId(), Currency.JPY),
                    RedisManager.LockParams.LOCKKEY,
                    () -> {
                        //    	  return (FiatDeposit)
                        customTransactionManager.execute(
                                entityManager -> {
                                    try {
                                        FiatDeposit newFiatDeposit =
                                                fiatDepositService.save(fiatDeposit, entityManager);
                                        if (FiatDepositStatus.DONE.equals(
                                                fiatDeposit.getFiatDepositStatus())) {
                                            assetService.updateWithExternalLock(
                                                    fiatDeposit.getUserId(),
                                                    Currency.JPY,
                                                    fiatDeposit.getAmount(),
                                                    BigDecimal.ZERO,
                                                    entityManager);
                                        }
                                        gmoDeposit.setFiatDepositId(newFiatDeposit.getId());
                                        save(gmoDeposit, entityManager);
                                    } catch (Exception e) {
                                        customLogger.severe(getClass().getName(), e);
                                    }
                                    return fiatDeposit;
                                });
                    })) {
                log.error(
                        getClass().getSimpleName()
                                + ",could not get lock. key: "
                                + getLockKey(fiatDeposit.getUserId(), Currency.JPY));
                throw new CustomException(
                        ErrorCode.LOCK_KEY, "AssetUpdate userId: " + fiatDeposit.getUserId());
            }
            ;
            if (FiatDepositStatus.DONE.equals(fiatDeposit.getFiatDepositStatus())) {
                try {
                    Asset asset = assetService.findOne(fiatDeposit.getUserId(), Currency.JPY);
                    User user = userService.findOne(fiatDeposit.getUserId());
                    mailNoreply = mailNoreplyService.findOne(MailNoreplyType.DEPOSIT_DONE);
                    MessageFormat messageFormat = new MessageFormat(this.mailNoreply.getContents());
                    DecimalFormat decimalFormat = new DecimalFormat("0");
                    String onhandAmount = decimalFormat.format(asset.getOnhandAmount());
                    String[] args = new String[] {fiatDeposit.getAmount().toString(), onhandAmount};
                    String mailContent = messageFormat.format(args);
                    boolean isSendMail =
                            sesManager.send(
                                    this.mailNoreply.getFromAddress(),
                                    user.getEmail(),
                                    this.mailNoreply.getTitle(),
                                    mailContent);
                    if (isSendMail) {
                        log.info(
                                " deposit send mail successfully email address is: {}",
                                user.getEmail());
                    } else {
                        log.warn(" deposit send mail server exception");
                    }
                } catch (Exception e) {
                    log.error(" deposit send mail fail please check parameters");
                }
            }
        }
    }

    // カタカナの小文字を大文字に変換 カンノ　アユミ
    private String convertSmallKanaToKana(String kanaText) {
        Map<String, String> kanaMap = new HashMap<>();
        kanaMap.put("ァ", "ア");
        kanaMap.put("ィ", "イ");
        kanaMap.put("ゥ", "ウ");
        kanaMap.put("ェ", "エ");
        kanaMap.put("ォ", "オ");
        kanaMap.put("ヵ", "カ");
        kanaMap.put("ヶ", "ケ");
        kanaMap.put("ッ", "ツ");
        kanaMap.put("ャ", "ヤ");
        kanaMap.put("ュ", "ユ");
        kanaMap.put("ョ", "ヨ");
        kanaMap.put("ヮ", "ワ");
        kanaMap.put("ー", "－");
        String resultText = kanaText;
        for (Iterator<Entry<String, String>> iterator = kanaMap.entrySet().iterator();
                iterator.hasNext(); ) {
            Map.Entry<String, String> map = iterator.next();
            resultText = resultText.replace(map.getKey(), map.getValue());
        }
        return resultText;
    }

    public GmoDeposit findOneByItemKey(String itemKey) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<GmoDeposit, GmoDeposit>() {
                    @Override
                    public GmoDeposit query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalItemKey(criteriaBuilder, root, itemKey));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public void save(GmoDepositDetailsForm gmoDepositDetailsForm) throws Exception {
        VaTransactionsData vaTransaction = gmoDepositDetailsForm.getVaTransaction();
        if (vaTransaction == null) {
            log.info("Invalid transaction data");
            throw new IllegalArgumentException("Invalid transaction data");
        }

        String itemKey = vaTransaction.getItemKey();
        if (itemKey == null || itemKey.isEmpty()) {
            log.info("Invalid item key");
            throw new IllegalArgumentException("Invalid item key");
        }

        GmoDeposit existGmoDeposit = this.findOneByItemKey(itemKey);
        if (existGmoDeposit != null) {
            log.info("GMO deposit exists.");
            throw new IllegalArgumentException("GMO deposit exists.");
        }

        GmoDeposit gmoDeposit = getGmoDeposit(vaTransaction);
        FiatDeposit fiatDeposit = new FiatDeposit();

        String accountNumber = vaTransaction.getVaAccountNumber();
        String vaId = vaTransaction.getVaId();
        OnetimeBankAccount onetimeBankAccount =
                onetimeBankAccountService.findOneByAccountNumberAndVaId(accountNumber, vaId);

        if (onetimeBankAccount != null && onetimeBankAccount.getUserId() != null) {
            User user = userService.findOne(onetimeBankAccount.getUserId());
            if (user == null) {
                throw new ServiceException("User not found");
            }
            log.info("{} select user id:{}", GMO_LOG_PREFIX, user.getId());

            if (isValidUserStatus(user.getUserStatus())) {
                processValidUser(user, vaTransaction, fiatDeposit, gmoDeposit, onetimeBankAccount);
            } else {
                processInvalidUser(
                        fiatDeposit,
                        gmoDeposit,
                        vaTransaction,
                        onetimeBankAccount,
                        user.getKycStatus());
            }
        } else {
            processUnregisteredAccount(gmoDeposit);
        }
        this.applyNew(gmoDeposit, fiatDeposit);
    }

    private boolean isValidUserStatus(UserStatus status) {
        return EnumSet.of(UserStatus.ACTIVE, UserStatus.UNTRADABLE, UserStatus.UNWITHDRAWABLE)
                .contains(status);
    }

    private void processValidUser(
            User user,
            VaTransactionsData vaTransaction,
            FiatDeposit fiatDeposit,
            GmoDeposit gmoDeposit,
            OnetimeBankAccount onetimeBankAccount) {
        String userName = getUserName(user);
        log.info("API [GMO] personal lastKana+firstKana is :{} ", userName);
        String remitterNameKana =
                TransliteratorUtil.halfWidthToFullWidth(
                        vaTransaction.getRemitterNameKana().strip());

        if (userName.equals(remitterNameKana)) {
            fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);
            setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
            gmoDeposit.setRemarks(vaTransaction.getRemarks());
            log.info("API [GMO] normal deposit process");
        } else {
            fiatDeposit.setFiatDepositStatus(FiatDepositStatus.UNKNOWN_DEPOSIT);
            setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
            gmoDeposit.setRemarks("【入金エラー】口座名義相違");
            log.info(
                    "API [GMO] 【入金エラー】口座名義相違 username:{}, remitterNameKana:{}",
                    userName,
                    remitterNameKana);
        }
    }

    private void processInvalidUser(
            FiatDeposit fiatDeposit,
            GmoDeposit gmoDeposit,
            VaTransactionsData vaTransaction,
            OnetimeBankAccount onetimeBankAccount,
            KycStatus kycStatus) {
        fiatDeposit.setFiatDepositStatus(FiatDepositStatus.UNKNOWN_DEPOSIT);
        setFiatDeposit(vaTransaction, fiatDeposit, onetimeBankAccount);
        gmoDeposit.setRemarks("入金エラー】その他");
        log.info("API [GMO]【入金エラー】 userStatus :{}", kycStatus);
    }

    private void processUnregisteredAccount(GmoDeposit gmoDeposit) {
        gmoDeposit.setRemarks("入金エラー】対象口座ユーザー登録なし");
        log.error("API [GMO]【入金エラー】対象口座ユーザー登録なし not fund userId");
    }

    private String getUserName(User user) {
        if (user.getUserInfoId() != null) {
            return convertSmallKanaToKana(
                    user.getUserInfo().getLastKana() + "　" + user.getUserInfo().getFirstKana());
        }
        return "";
    }
}
