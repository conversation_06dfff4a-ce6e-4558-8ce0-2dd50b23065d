package point.common.service;

import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.RedisManager;

@Slf4j
@Service
@RequiredArgsConstructor
public class OneTimePasswordService {

    private final RedisManager redisManager;

    public String generateOneTimePassword(String key, long ttl) {
        String otp = UUID.randomUUID().toString();
        redisManager.set(key, otp, ttl);
        return otp;
    }

    public boolean validateOneTimePassword(String key, String otp) {
        String redisOtp = redisManager.get(key);
        if (redisOtp != null && redisOtp.equals(otp)) {
            redisManager.delete(key);
            return true;
        }
        return false;
    }
}
