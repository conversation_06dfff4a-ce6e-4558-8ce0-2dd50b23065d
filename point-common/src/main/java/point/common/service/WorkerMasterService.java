package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.CurrencyPair;
import point.common.constant.Environment;
import point.common.constant.TradeType;
import point.common.entity.WorkerMaster;
import point.common.entity.WorkerMaster_;
import point.common.predicate.WorkerMasterPredicate;

@Slf4j
@Service
public class WorkerMasterService extends EntityService<WorkerMaster, WorkerMasterPredicate> {

    @Override
    public Class<WorkerMaster> getEntityClass() {
        return WorkerMaster.class;
    }

    private String getCacheKey(
            Environment environment,
            String beanName,
            TradeType tradeType,
            CurrencyPair currencyPair) {
        return "workerMaster:"
                + environment
                + ":"
                + beanName
                + ":"
                + tradeType
                + ":"
                + currencyPair;
    }

    private String getCacheKey(WorkerMaster workerMaster) {
        return getCacheKey(
                workerMaster.getEnvironment(),
                workerMaster.getBeanName(),
                workerMaster.getTradeType(),
                workerMaster.getCurrencyPair());
    }

    public WorkerMaster findOne(
            Environment environment,
            String beanName,
            TradeType tradeType,
            CurrencyPair currencyPair) {
        WorkerMaster workerMaster = null;
        String key = getCacheKey(environment, beanName, tradeType, currencyPair);

        try {
            workerMaster = redisTemplate.getValue(key);
        } catch (Exception e) {
            log.warn("redisTemplate.getValue() is missing. key: " + key);
        }

        if (workerMaster == null) {
            workerMaster =
                    customTransactionManager.find(
                            getEntityClass(),
                            new QueryExecutorReturner<WorkerMaster, WorkerMaster>() {
                                @Override
                                public WorkerMaster query() {
                                    List<Predicate> predicates = new ArrayList<>();
                                    predicates.add(
                                            predicate.equalEnvironment(
                                                    criteriaBuilder, root, environment));
                                    predicates.add(
                                            predicate.equalBeanName(
                                                    criteriaBuilder, root, beanName));
                                    predicates.add(
                                            tradeType == null
                                                    ? predicate.isNullOfTradeType(root)
                                                    : predicate.equalTradeType(
                                                            criteriaBuilder, root, tradeType));
                                    predicates.add(
                                            currencyPair == null
                                                    ? predicate.isNullOfCurrencyPair(root)
                                                    : predicate.equalCurrencyPair(
                                                            criteriaBuilder, root, currencyPair));
                                    return getSingleResult(
                                            entityManager, criteriaQuery, root, predicates);
                                }
                            });

            if (workerMaster != null) {
                saveCache(workerMaster);
            }
        }

        return workerMaster;
    }

    public List<WorkerMaster> findByEnvironmentAndBeanName(
            Environment environment, String beanName) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<WorkerMaster, List<WorkerMaster>>() {
                    @Override
                    public List<WorkerMaster> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalEnvironment(criteriaBuilder, root, environment));
                        predicates.add(predicate.equalBeanName(criteriaBuilder, root, beanName));
                        predicates.add(predicate.isEnabled(criteriaBuilder, root, true));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(WorkerMaster_.id)));
                    }
                });
    }

    @Override
    protected void saveCache(WorkerMaster workerMaster) {
        super.saveCache(workerMaster);
        redisTemplate.setValue(getCacheKey(workerMaster), workerMaster);
    }

    @Override
    protected void deleteCache(WorkerMaster workerMaster) {
        super.deleteCache(workerMaster);
        redisTemplate.delete(getCacheKey(workerMaster));
    }
}
