package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.KycMailStatus;
import point.common.constant.KycStatus;
import point.common.constant.KycType;
import point.common.entity.UserKyc;
import point.common.entity.UserKyc_;
import point.common.model.response.PageData;
import point.common.predicate.UserKycPredicate;

@Service
public class UserKycService extends EntityService<UserKyc, UserKycPredicate> {

    @Override
    public Class<UserKyc> getEntityClass() {
        return UserKyc.class;
    }

    public Optional<List<UserKyc>> findByUserId(Long userId) {
        return Optional.ofNullable(
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<UserKyc, List<UserKyc>>() {
                            @Override
                            public List<UserKyc> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null),
                                        criteriaBuilder.asc(root.get(UserKyc_.userId)));
                            }
                        }));
    }

    public PageData<UserKyc> findByUserIdPageData(Long userId, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null));
                            }
                        });
        return new PageData<UserKyc>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<UserKyc, List<UserKyc>>() {
                            @Override
                            public List<UserKyc> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null,
                                                null);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(UserKyc_.id)));
                            }
                        }));
    }

    public List<UserKyc> findByDate(
            KycStatus kycStatus,
            Date mailSendAtFrom,
            Date mailSendAtTo,
            Date createdAtFrom,
            Date createdAtTo,
            Date updatedAtFrom,
            Date updatedAtTo) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserKyc, List<UserKyc>>() {
                    @Override
                    public List<UserKyc> query() {

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                getPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        null,
                                        null,
                                        kycStatus,
                                        null,
                                        mailSendAtFrom,
                                        mailSendAtTo,
                                        createdAtFrom,
                                        createdAtTo,
                                        updatedAtFrom,
                                        updatedAtTo),
                                criteriaBuilder.asc(root.get(UserKyc_.id)));
                    }
                });
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<UserKyc> root,
            Long userId,
            KycType kycType,
            KycStatus kycStatus,
            KycMailStatus kycMailStatus,
            Date mailSendAtFrom,
            Date mailSendAtTo,
            Date createdAtFrom,
            Date createdAtTo,
            Date updatedAtFrom,
            Date updatedAtTo) {
        List<Predicate> predicates = new ArrayList<>();

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (kycType != null) {
            predicates.add(predicate.equalKycType(criteriaBuilder, root, kycType));
        }

        if (kycStatus != null) {
            predicates.add(predicate.equalKycStatus(criteriaBuilder, root, kycStatus));
        }

        if (kycMailStatus != null) {
            predicates.add(predicate.equalKycMailStatus(criteriaBuilder, root, kycMailStatus));
        }

        if (mailSendAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToMailSendAt(
                            criteriaBuilder, root, mailSendAtFrom));
        }
        if (mailSendAtTo != null) {
            predicates.add(predicate.lessThanMailSendAt(criteriaBuilder, root, mailSendAtTo));
        }
        if (createdAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, createdAtFrom));
        }
        if (createdAtTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, createdAtTo));
        }
        if (updatedAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root, updatedAtFrom));
        }
        if (updatedAtTo != null) {
            predicates.add(predicate.lessThanUpdatedAt(criteriaBuilder, root, updatedAtTo));
        }

        return predicates;
    }
}
