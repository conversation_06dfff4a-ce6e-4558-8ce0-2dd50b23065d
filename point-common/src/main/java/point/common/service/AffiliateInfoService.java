package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.entity.AffiliateInfo;
import point.common.predicate.AffiliateInfoPredicate;

@RequiredArgsConstructor
@Service
@Slf4j
public class AffiliateInfoService extends EntityService<AffiliateInfo, AffiliateInfoPredicate> {

    @Override
    public Class<AffiliateInfo> getEntityClass() {
        return AffiliateInfo.class;
    }

    @Override
    protected void fetch(Root<AffiliateInfo> root) {
        super.fetch(root);
    }

    public AffiliateInfo findOneByIdentify(String identify) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<AffiliateInfo, AffiliateInfo>() {
                    @Override
                    public AffiliateInfo query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalIdentify(criteriaBuilder, root, identify));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
