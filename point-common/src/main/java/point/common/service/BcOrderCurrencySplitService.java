package point.common.service;

import org.springframework.stereotype.Service;
import point.common.entity.BcOrderCurrencySplit;
import point.common.predicate.BcOrderCurrencySplitPredicate;

@Service
public class BcOrderCurrencySplitService
        extends EntityService<BcOrderCurrencySplit, BcOrderCurrencySplitPredicate> {
    @Override
    public Class<BcOrderCurrencySplit> getEntityClass() {
        return BcOrderCurrencySplit.class;
    }
}
