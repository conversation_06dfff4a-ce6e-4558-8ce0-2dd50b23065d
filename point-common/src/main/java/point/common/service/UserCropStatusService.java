package point.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.component.RedisManager;
import point.common.constant.TradeType;
import point.common.entity.*;
import point.common.predicate.UserCropStatusPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCropStatusService extends EntityService<UserCropStatus, UserCropStatusPredicate> {

    private final RedisManager redisManager;
    private final CropGrowthStageService cropGrowthStageService;
    private final UserCropStatusHistoryService userCropStatusHistoryService;

    @Override
    public Class<UserCropStatus> getEntityClass() {
        return UserCropStatus.class;
    }

    public String getLockKey(Long userId, Long assetId, TradeType tradeType) {
        return "user:crop:status:" + userId + ":" + assetId + ":" + tradeType;
    }

    public Optional<UserCropStatus> findBy(Long userId, Long assetId, TradeType tradeType) {
        UserCropStatus entity =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public UserCropStatus query() {
                                List<Predicate> predicates = new ArrayList<>(3);
                                predicates.add(
                                        predicate.equalUserId(criteriaBuilder, root, userId));
                                predicates.add(
                                        predicate.equalAssetId(criteriaBuilder, root, assetId));
                                predicates.add(
                                        predicate.equalTradeType(criteriaBuilder, root, tradeType));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        return Optional.ofNullable(entity);
    }

    public Optional<UserCropStatus> findBy(
            Long userId, Long assetId, TradeType tradeType, EntityManager entityManager) {
        UserCropStatus entity =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public UserCropStatus query() {
                                List<Predicate> predicates = new ArrayList<>(3);
                                predicates.add(
                                        predicate.equalUserId(criteriaBuilder, root, userId));
                                predicates.add(
                                        predicate.equalAssetId(criteriaBuilder, root, assetId));
                                predicates.add(
                                        predicate.equalTradeType(criteriaBuilder, root, tradeType));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        },
                        entityManager);
        return Optional.ofNullable(entity);
    }

    public Optional<UserCropStatus> save(
            @NotNull Long userId,
            @NotNull Long assetId,
            @NotNull TradeType tradeType,
            @NotNull BigDecimal profitLossAmount)
            throws Exception {
        List<CropGrowthStage> cropGrowthStages =
                cropGrowthStageService.findAll().stream()
                        .filter(d -> Objects.equals(d.getTradeType(), TradeType.OPERATE))
                        .toList();

        Optional<Long> growthStageId =
                cropGrowthStages.stream()
                        .filter(
                                it ->
                                        Objects.nonNull(it.getEvaluationFrom())
                                                && Objects.nonNull(it.getEvaluationTo()))
                        .filter(
                                it -> {
                                    long profitLossAmtLong = profitLossAmount.longValue();
                                    return profitLossAmtLong >= it.getEvaluationFrom()
                                            && profitLossAmtLong <= it.getEvaluationTo();
                                })
                        .findFirst()
                        .map(CropGrowthStage::getGrowthStageId);

        log.info(
                "Save or update userCropStatus by userId: {}, assetId: {}, tradeType: {}. the profitLossAmount: {}, original profitLossAmount: {}",
                userId,
                assetId,
                tradeType,
                profitLossAmount.longValue(),
                profitLossAmount);

        if (!redisManager.executeWithLock(
                getLockKey(userId, assetId, tradeType),
                RedisManager.LockParams.LOCKKEY,
                () ->
                        customTransactionManager.execute(
                                entityManager -> {
                                    Optional<UserCropStatus> userCropStatusOptional =
                                            this.findBy(userId, assetId, tradeType, entityManager);

                                    UserCropStatus entity =
                                            userCropStatusOptional.orElseGet(UserCropStatus::new);
                                    Integer beforeGrowthStageId = entity.getGrowthStageId();
                                    entity.setUserId(userId);
                                    entity.setTradeType(tradeType);
                                    entity.setAssetId(assetId);
                                    entity.setProfitLossAmount(profitLossAmount.intValue());
                                    entity.setGrowthStageId(
                                            growthStageId.orElse(NumberUtils.LONG_ZERO).intValue());

                                    if (Objects.isNull(entity.getId())) {
                                        // new entity
                                        save(entity, entityManager);
                                    } else {
                                        if (!Objects.equals(
                                                beforeGrowthStageId, entity.getGrowthStageId())) {
                                            // the growthStageId has changed
                                            save(entity, entityManager);
                                        }
                                    }

                                    if (Objects.nonNull(beforeGrowthStageId)
                                            && !Objects.equals(
                                                    beforeGrowthStageId,
                                                    entity.getGrowthStageId())) {
                                        UserCropStatusHistory userCropStatusHistory =
                                                new UserCropStatusHistory();
                                        userCropStatusHistory.setBeforeGrowthStageId(
                                                beforeGrowthStageId);
                                        userCropStatusHistory.setAfterGrowthStageId(
                                                entity.getGrowthStageId());
                                        userCropStatusHistory.setUserId(userId);
                                        userCropStatusHistory.setAssetId(assetId);
                                        userCropStatusHistory.setTradeType(tradeType);
                                        userCropStatusHistory.setProfitLossAmount(
                                                entity.getProfitLossAmount());
                                        userCropStatusHistoryService.save(
                                                userCropStatusHistory, entityManager);
                                    }
                                }))) {
            // Failed to get lock key
            log.info(
                    "The user duplicated create UserCropStatus entity, userId: {}, assetId: {}, tradeType: {}",
                    userId,
                    assetId,
                    tradeType);
        }
        return this.findBy(userId, assetId, tradeType);
    }
}
