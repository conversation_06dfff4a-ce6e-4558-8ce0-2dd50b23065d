package point.common.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.UserAgreementType;
import point.common.entity.UserAgreement;
import point.common.entity.UserAgreementFile;
import point.common.entity.UserAgreement_;
import point.common.model.dto.UserAgreementDTO;
import point.common.predicate.UserAgreementPredicate;
import point.common.repos.UserAgreementRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserAgreementService extends EntityService<UserAgreement, UserAgreementPredicate> {

    private final UserAgreementRepository userAgreementRepository;
    private final UserAgreementFileService userAgreementFileService;

    @Override
    public Class<UserAgreement> getEntityClass() {
        return UserAgreement.class;
    }

    public List<UserAgreementDTO> findEnabledAgreementByUserId(Long userId) {
        return userAgreementRepository.findByUserId(userId);
    }

    public List<UserAgreement> findByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserAgreement, List<UserAgreement>>() {
                    @Override
                    public List<UserAgreement> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(UserAgreement_.id)));
                    }
                });
    }

    public UserAgreement findByCondition(Long userId, UserAgreementType userAgreementType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserAgreement, UserAgreement>() {
                    @Override
                    public UserAgreement query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }
                        if (userAgreementType != null) {
                            predicates.add(
                                    predicate.equalUserAgreementType(
                                            criteriaBuilder, root, userAgreementType));
                        }
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(UserAgreement_.id)));
                    }
                });
    }

    public Optional<UserAgreement> saveOrUpdateVersion(
            Long userId, UserAgreementType userAgreementType, Integer version) {

        List<UserAgreementFile> userAgreementFiles =
                userAgreementFileService.findByAgreementType(userAgreementType);
        if (Objects.isNull(userAgreementFiles)) {
            return Optional.empty();
        }

        boolean isVersionValid =
                userAgreementFiles.stream()
                        .anyMatch(it -> Objects.equals(it.getVersion(), version));
        if (!isVersionValid) {
            return Optional.empty();
        }

        UserAgreement entity = this.findByCondition(userId, userAgreementType);
        if (Objects.nonNull(entity)) {
            entity.setEnabled(true);
            entity.setVersion(version);
            return Optional.of(this.save(entity));
        } else {
            UserAgreement userAgreement = new UserAgreement(userId, userAgreementType, version);
            return Optional.of(this.save(userAgreement));
        }
    }

    /**
     * Records user agreement acceptance for the first time only.
     *
     * @param userId User ID
     * @param userAgreementTypes List of agreement types to accept
     * @param entityManager EntityManager for database operations
     * @throws IllegalStateException when existing agreement version doesn't match current version
     */
    public void acceptAgreementsFirstTimeOnly(
            Long userId, List<UserAgreementType> userAgreementTypes, EntityManager entityManager)
            throws Exception {
        for (UserAgreementType userAgreementType : userAgreementTypes) {
            UserAgreementFile userAgreementFile =
                    userAgreementFileService.findMaxVersion(userAgreementType);
            if (Objects.isNull(userAgreementFile)) {
                log.info("User agreement file not found for type: {}", userAgreementType);
                continue;
            }

            UserAgreement entity = this.findByCondition(userId, userAgreementType);
            if (Objects.isNull(entity)) {
                entity =
                        new UserAgreement(
                                userId, userAgreementType, userAgreementFile.getVersion());
                super.save(entity, entityManager);
            }
        }
    }

    public void agreeDocuments(
            Long userId, List<UserAgreementType> userAgreementTypes, EntityManager entityManager)
            throws Exception {
        for (UserAgreementType userAgreementType : userAgreementTypes) {
            UserAgreementFile userAgreementFile =
                    userAgreementFileService.findMaxVersion(userAgreementType);
            if (Objects.nonNull(userAgreementFile)) {
                UserAgreement entity = this.findByCondition(userId, userAgreementType);
                if (Objects.nonNull(entity)) {
                    if (entity.getVersion() < userAgreementFile.getVersion()) {
                        entity.setVersion(userAgreementFile.getVersion());
                        super.save(entity, entityManager);
                    }
                } else {
                    entity =
                            new UserAgreement(
                                    userId, userAgreementType, userAgreementFile.getVersion());
                    super.save(entity, entityManager);
                }
            }
        }
    }
}
