package point.common.service;

import static point.common.entity.AbstractEntity_.createdAt;

import java.math.BigDecimal;
import java.util.*;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.config.SpringConfig;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.PointUserPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointUserService extends EntityService<PointUser, PointUserPredicate> {

    private final UserIdentityService userIdentityService;
    private final AssetService assetService;
    private final FiatDepositService fiatDepositService;
    private final SpringConfig springConfig;
    private final ChoicePowerUserRelService choicePowerUserRelService;
    private final ChoicePowerService choicePowerService;
    private final MonsterBaseService monsterBaseService;
    private final UserMonsterInfoService userMonsterInfoService;
    private final CurrencyConfigService currencyConfigService;

    @Override
    public Class<PointUser> getEntityClass() {
        return PointUser.class;
    }

    public PageData<PointUser> findByCondition(
            Long id,
            String name,
            String partnerId,
            String partnerNumber,
            String partnerMemberId,
            Long dateFrom,
            Long dateTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                name,
                                                partnerId,
                                                partnerNumber,
                                                partnerMemberId,
                                                dateFrom,
                                                dateTo));
                            }
                        });
        return new PageData<PointUser>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<PointUser, List<PointUser>>() {
                            @Override
                            public List<PointUser> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                name,
                                                partnerId,
                                                partnerNumber,
                                                partnerMemberId,
                                                dateFrom,
                                                dateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(createdAt)));
                            }
                        }));
    }

    public List<PointUser> findByAllCondition(
            Long id,
            String name,
            String partnerId,
            String partnerNumber,
            String partnerMemberId,
            Long dateFrom,
            Long dateTo) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PointUser, List<PointUser>>() {
                    @Override
                    public List<PointUser> query() {
                        List<Predicate> predicates =
                                getPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        id,
                                        name,
                                        partnerId,
                                        partnerNumber,
                                        partnerMemberId,
                                        dateFrom,
                                        dateTo);
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(createdAt)));
                    }
                });
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<PointUser> root,
            Long id,
            String name,
            String partnerId,
            String partnerNumber,
            String partnerMemberId,
            Long dateFrom,
            Long dateTo) {
        List<Predicate> predicates = new ArrayList<>();
        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        }
        if (StringUtils.isNotBlank(name)) {
            predicates.add(predicate.equalName(criteriaBuilder, root, name));
        }
        if (StringUtils.isNotBlank(partnerId)) {
            predicates.add(
                    predicate.equalPartnerId(criteriaBuilder, root, Long.valueOf(partnerId)));
        }
        if (StringUtils.isNotBlank(partnerMemberId)) {
            predicates.add(predicate.likePartnerMemberId(criteriaBuilder, root, partnerMemberId));
        }
        if (StringUtils.isNotBlank(partnerNumber)) {
            predicates.add(predicate.equalPartnerNumber(criteriaBuilder, root, partnerNumber));
        }
        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(dateFrom)));
        }
        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
        }

        predicates.add(predicate.equalUserIdType(criteriaBuilder, root, UserIdType.Operate));

        return predicates;
    }

    public void bindRelationWithSyncPowerChoice(Long operateUserId, Long investUserId)
            throws Exception {
        customTransactionManager.execute(
                entityManager -> {
                    PointUser pointUser = super.findOne(operateUserId);
                    PointUser investUser = this.findByUserId(investUserId);
                    if (Objects.nonNull(investUser)
                            && !Objects.equals(investUser.getId(), operateUserId)) {
                        throw new CustomException(
                                ErrorCode.PONTA_AUTHENTICATION_ERROR_USER_ID_ALREADY_BOUND);
                    }
                    if (Objects.nonNull(pointUser.getUserId())
                            && !Objects.equals(pointUser.getUserId(), investUserId)) {
                        throw new CustomException(
                                ErrorCode.PONTA_AUTHENTICATION_ERROR_USER_ID_ALREADY_BOUND);
                    }
                    if (Objects.isNull(pointUser.getUserId())) {
                        pointUser.setUserId(investUserId);
                        super.save(pointUser);
                        this.syncPowerChoice(operateUserId, investUserId, entityManager);
                    }
                });
    }

    /**
     * Saves or updates a PointUser associated with partner member ID
     *
     * @param partnerMemberId The partner's member ID
     * @param partnerId The partner ID
     * @param entityManager The EntityManager for database operations
     * @return The saved/updated PointUser
     * @throws Exception if any error occurs during the operation
     */
    public PointUser save(String partnerMemberId, Long partnerId, EntityManager entityManager)
            throws Exception {
        // Check if PointUser already exists
        PointUser pointUser = this.findByPartnerMemberId(partnerMemberId, entityManager);
        if (Objects.isNull(pointUser)) {
            // Create new userIdentity
            UserIdentity userIdentity =
                    userIdentityService.create(UserIdType.Operate, entityManager);

            // Create and persist new OperateUser
            OperateUser operateUser = new OperateUser();
            operateUser.setId(userIdentity.getId());
            operateUser.setPartnerId(partnerId);
            operateUser.setPartnerMemberId(partnerMemberId);
            operateUser.setCreatedAt(Calendar.getInstance().getTime());
            operateUser.setUpdatedAt(Calendar.getInstance().getTime());
            entityManager.persist(operateUser);

            // Initialize user assets
            this.initUserAsset(operateUser.getId(), entityManager);

            // Sync power choice data
            this.syncPowerChoice(userIdentity.getId(), null, entityManager);

            // Handle monster base assignment
            Optional<MonsterBase> monsterBase =
                    this.monsterBaseService.findByIdType(UserIdType.Operate);
            if (monsterBase.isEmpty()) {
                log.error("No monster base found for idType: {}", UserIdType.Operate);
                throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
            }

            // Generate monster info for the new user
            Long monsterId = monsterBase.get().getId();
            userMonsterInfoService.generateMasterInfo(
                    operateUser.getId(),
                    monsterId,
                    UserIdType.Operate.name(),
                    new Date(),
                    entityManager);
        } else {
            if (!pointUser.getPointPartner().getId().equals(partnerId)) {
                throw new CustomException(
                        ErrorCode.PONTA_AUTHENTICATION_ERROR_INVALID_PARTNER_NUMBER);
            }

            // Update an existing user case
            pointUser.setUpdatedAt(new Date());
            this.save(pointUser, entityManager);
            return pointUser;
        }

        // Return the saved PointUser
        return this.findByPartnerMemberId(partnerMemberId, entityManager);
    }

    public PointUser findByPartnerMemberId(String partnerMemberId, EntityManager entityManager) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PointUser query() {
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                List.of(
                                        criteriaBuilder.equal(
                                                root.get(PointUser_.partnerMemberId),
                                                partnerMemberId)));
                    }
                },
                entityManager);
    }

    public PointUser findByPartnerMemberId(String partnerMemberId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PointUser, PointUser>() {
                    @Override
                    public PointUser query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalPartnerMemberId(
                                        criteriaBuilder, root, partnerMemberId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public PointUser findByPartnerId(Long partnerId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PointUser, PointUser>() {
                    @Override
                    public PointUser query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalPartnerId(criteriaBuilder, root, partnerId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    private void initUserAsset(Long userId, EntityManager entityManager) throws Exception {
        List<Asset> userAssets = assetService.findUserAssets(userId, entityManager);
        List<Currency> userCurrencies = userAssets.stream().map(Asset::getCurrency).toList();

        List<CurrencyConfig> currencyConfigs =
                currencyConfigService.findAllByCondition(
                        TradeType.OPERATE, null, true, entityManager);

        if (CollectionUtils.isEmpty(currencyConfigs)) {
            throw new CustomException(ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND);
        }

        List<Currency> missingCurrencies =
                currencyConfigs.stream()
                        .map(CurrencyConfig::getCurrency)
                        .filter(currency -> !userCurrencies.contains(currency))
                        .toList();

        if (!CollectionUtils.isEmpty(missingCurrencies)) {
            for (Currency missingCurrency : missingCurrencies) {
                assetService.findOrCreate(userId, missingCurrency, entityManager);
            }
        }
    }

    private void addAsset(Long userId, EntityManager entityManager) throws Exception {
        if (springConfig.isStg() || springConfig.isPrd()) {
            return;
        }

        {
            // 資産を1000万円追加する
            Asset asset = new Asset();
            asset.setUserId(userId);
            asset.setCurrency(Currency.JPY);
            asset.setOnhandAmount(BigDecimal.valueOf(**********));
            asset.setLockedAmount(BigDecimal.ZERO);
            assetService.save(asset, entityManager);

            // assetSummaryがズレないように入金データも1000万円追加する
            FiatDeposit fiatDeposit = new FiatDeposit();
            fiatDeposit.setUserId(userId);
            fiatDeposit.setBankAccountId(null);
            fiatDeposit.setOnetimeBankAccountId(1L); // 入ってさえいれば問題ないと思われるためデータのある1とする
            fiatDeposit.setAmount(BigDecimal.valueOf(**********));

            fiatDeposit.setFee(BigDecimal.ZERO);
            fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);
            fiatDeposit.setFiatDepositSubStatus(null);
            fiatDeposit.setComment(null);
            fiatDepositService.save(fiatDeposit, entityManager);
        }
    }

    public PointUser findByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<PointUser, PointUser>() {
                    @Override
                    public PointUser query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    /**
     * sync the power choice for the user
     *
     * @param operateUserId point user id
     * @param investUserId user id
     * @param entityManager entity manager
     * @throws Exception
     */
    void syncPowerChoice(Long operateUserId, Long investUserId, EntityManager entityManager)
            throws Exception {
        ChoicePowerUserRel operateUserRel =
                choicePowerUserRelService.getChoicePowerUserRel(operateUserId);
        if (investUserId == null) {
            if (operateUserRel == null) {
                createNewUserRel(operateUserId, entityManager);
            } else {
                updateExistingUserRel(operateUserRel, entityManager);
            }
        } else {
            if (operateUserRel != null) {
                ChoicePowerUserRel investUserRel = new ChoicePowerUserRel();
                investUserRel.setUserId(investUserId);
                investUserRel.setChoicePowerId(operateUserRel.getChoicePowerId());
                choicePowerUserRelService.save(investUserRel, entityManager);
                updateExistingUserRel(operateUserRel, entityManager);
            } else {
                createNewUserRel(operateUserId, entityManager);
                ChoicePowerUserRel newOperateUserRel =
                        choicePowerUserRelService.getChoicePowerUserRel(operateUserId);
                ChoicePowerUserRel investUserRel = new ChoicePowerUserRel();
                investUserRel.setUserId(investUserId);
                investUserRel.setChoicePowerId(newOperateUserRel.getChoicePowerId());
                choicePowerUserRelService.save(investUserRel, entityManager);
                // 更新净金额
                updateExistingUserRel(newOperateUserRel, entityManager);
            }
        }
    }

    private void updateExistingUserRel(ChoicePowerUserRel existingRel, EntityManager entityManager)
            throws Exception {
        // 获取关联的 ChoicePower
        ChoicePower choicePower = choicePowerService.findOne(existingRel.getChoicePowerId());

        // 重新计算净金额并更新
        Long currentNetAmount = processUserChoicePower(existingRel.getUserId());
        choicePower.setAmount(currentNetAmount);
        choicePowerService.save(choicePower, entityManager);
    }

    private void createNewUserRel(Long userId, EntityManager entityManager) throws Exception {
        // 计算用户净金额
        Long netAmount = processUserChoicePower(userId);

        // 创建新的 ChoicePower
        ChoicePower newChoicePower = new ChoicePower();
        newChoicePower.setAmount(netAmount);
        ChoicePower savedPower = choicePowerService.save(newChoicePower, entityManager);

        // 创建用户关联记录
        ChoicePowerUserRel newRel = new ChoicePowerUserRel();
        newRel.setUserId(userId);
        newRel.setChoicePowerId(savedPower.getId());
        choicePowerUserRelService.save(newRel, entityManager);
    }

    /** processUserChoicePower */
    public Long processUserChoicePower(Long userId) throws Exception {
        String querySql =
                "SELECT transfer_type, SUM(amount) as total_amount FROM choice_power_transfer WHERE user_id = "
                        + userId
                        + " GROUP BY transfer_type";
        List<Object[]> results =
                customTransactionManager.execute(
                        em -> {
                            return em.createNativeQuery(querySql).getResultList();
                        });

        return calculateNetAmount(results);
    }

    /** calculateNetAmount */
    private long calculateNetAmount(List<Object[]> results) {
        long totalIn = 0;
        long totalOut = 0;
        for (Object[] result : results) {
            String transferType = (String) result[0];
            long amount = ((Number) result[1]).longValue();
            if (ChoicePowerTransferType.IN.name().equals(transferType)) {
                totalIn += amount;
            } else if (ChoicePowerTransferType.OUT.name().equals(transferType)) {
                totalOut += amount;
            }
        }
        return totalIn - totalOut;
    }

    public PointUser findById(Long id) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PointUser query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalId(criteriaBuilder, root, id));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
