package point.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.component.RedisManager;
import point.common.constant.Currency;
import point.common.constant.ErrorCode;
import point.common.constant.UserIdType;
import point.common.entity.Asset;
import point.common.entity.Asset_;
import point.common.exception.CustomException;
import point.common.exception.GameException;
import point.common.model.response.PageData;
import point.common.predicate.AssetPredicate;
import point.common.util.JsonUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssetService extends EntityService<Asset, AssetPredicate> {

    private final RedisManager redisManager;
    private final CurrencyConfigService currencyConfigService;

    @Override
    public Class<Asset> getEntityClass() {
        return Asset.class;
    }

    private static String getLockKey(Long userId, Currency currency) {
        return "lock:asset:" + userId + ":" + currency;
    }

    public List<Asset> findUserAssets(Long userId) {
        return this.findUserAssets(userId, null);
    }

    public List<Asset> findUserAssets(Long userId, EntityManager entityManager) {
        return Objects.isNull(entityManager)
                ? customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<Asset> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        List.of(
                                                predicate.equalUserId(
                                                        criteriaBuilder, root, userId)),
                                        criteriaBuilder.asc(root.get(Asset_.currency)));
                            }
                        })
                : customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<Asset> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        List.of(
                                                predicate.equalUserId(
                                                        criteriaBuilder, root, userId)),
                                        criteriaBuilder.asc(root.get(Asset_.currency)));
                            }
                        },
                        entityManager);
    }

    public List<Asset> findByCurrency(Currency currency) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<Asset> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(Asset_.userId)));
                    }
                });
    }

    public List<Asset> findByCurrencyAndUserIdType(Currency currency, UserIdType userIdType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<Asset> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
                        predicates.add(
                                predicate.equalUserIdType(criteriaBuilder, root, userIdType));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(Asset_.userId)));
                    }
                });
    }

    // findOneは原則使用せず、findOrCreateを使用する
    public Asset findOne(Long userId, Currency currency) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public Asset query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public Asset findOne(Long userId, Currency currency, EntityManager entityManager) {
        return new QueryExecutorReturner<Asset, Asset>() {
            @Override
            public Asset query() {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
                return getSingleResult(entityManager, criteriaQuery, root, predicates);
            }
        }.execute(getEntityClass(), entityManager);
    }

    // Assetが存在しない場合は初期値で作成する
    public Asset findOrCreate(Long userId, Currency currency) throws Exception {
        return findOrCreate(userId, currency, null);
    }

    public Asset findOrCreate(Long userId, Currency currency, EntityManager entityManager)
            throws Exception {

        if (userId == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        }

        if (currency == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_CURRENCY);
        }

        Asset foundAsset =
                (entityManager == null)
                        ? findOne(userId, currency)
                        : findOne(userId, currency, entityManager);

        if (foundAsset != null) {
            return foundAsset;
        }

        return (Asset)
                redisManager.executeWithLock(
                        getLockKey(userId, currency),
                        RedisManager.LockParams.DEFAULT,
                        () -> {
                            Asset createdAsset = newEntity();
                            createdAsset.setUserId(userId);
                            createdAsset.setCurrency(currency);
                            createdAsset.setOnhandAmount(BigDecimal.ZERO);
                            createdAsset.setLockedAmount(BigDecimal.ZERO);

                            return (entityManager == null)
                                    ? save(createdAsset)
                                    : save(createdAsset, entityManager);
                        });
    }

    private Asset findOrCreateNoLock(Long userId, Currency currency, EntityManager entityManager)
            throws Exception {

        if (userId == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        }

        if (currency == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_CURRENCY);
        }

        Asset foundAsset =
                (entityManager == null)
                        ? findOne(userId, currency)
                        : findOne(userId, currency, entityManager);

        if (foundAsset != null) {
            return foundAsset;
        } else {
            Asset createdAsset = newEntity();
            createdAsset.setUserId(userId);
            createdAsset.setCurrency(currency);
            createdAsset.setOnhandAmount(BigDecimal.ZERO);
            createdAsset.setLockedAmount(BigDecimal.ZERO);

            return (entityManager == null) ? save(createdAsset) : save(createdAsset, entityManager);
        }
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<Asset> root,
            Long userId,
            Currency currency,
            BigDecimal onhandAmountFrom,
            BigDecimal onhandAmountTo,
            UserIdType userIdType,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();
        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (currency != null) {
            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
        }

        if (onhandAmountFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToOnhandAmount(
                            criteriaBuilder, root, onhandAmountFrom));
        }

        if (onhandAmountTo != null) {
            predicates.add(
                    predicate.lessThanOrEqualToOnhandAmount(criteriaBuilder, root, onhandAmountTo));
        }
        if (userIdType != null) {
            predicates.add(predicate.equalUserIdType(criteriaBuilder, root, userIdType));
        }

        return predicates;
    }

    public PageData<Asset> findByCondition(
            Long userId,
            Currency currency,
            BigDecimal onhandAmountFrom,
            BigDecimal onhandAmountTo,
            UserIdType userIdType,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                currency,
                                                onhandAmountFrom,
                                                onhandAmountTo,
                                                userIdType,
                                                number,
                                                size));
                            }
                        });

        return new PageData<Asset>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<Asset, List<Asset>>() {
                            @Override
                            public List<Asset> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                currency,
                                                onhandAmountFrom,
                                                onhandAmountTo,
                                                userIdType,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(Asset_.id)));
                            }
                        }));
    }

    public void update(
            Long userId,
            Currency currency,
            BigDecimal onhandAmountDiff,
            BigDecimal lockedAmountDiff,
            EntityManager entityManager)
            throws Exception {
        if (!redisManager.executeWithLock(
                getLockKey(userId, currency),
                RedisManager.LockParams.DEFAULT,
                () -> {
                    Asset asset = findOrCreate(userId, currency, entityManager);

                    asset.setOnhandAmount(asset.getOnhandAmount().add(onhandAmountDiff));
                    asset.setLockedAmount(asset.getLockedAmount().add(lockedAmountDiff));
                    save(asset, entityManager);

                    if (asset.getOnhandAmount().subtract(asset.getLockedAmount()).signum() < 0
                            || asset.getOnhandAmount().signum() < 0
                            || asset.getLockedAmount().signum() < 0) {

                        throw new GameException(
                                ErrorCode.PONTA_POINTS_NOT_ENOUGH,
                                ErrorCode.PONTA_POINTS_NOT_ENOUGH.getMessage());
                    }
                })) {
            throw new GameException(
                    ErrorCode.COMMON_ERROR_LOCK, ErrorCode.COMMON_ERROR_LOCK.getMessage());
        }
        ;
    }

    public void updateWithOneLock(
            Long userId,
            Currency currency,
            BigDecimal onhandAmountDiff,
            BigDecimal lockedAmountDiff,
            EntityManager entityManager)
            throws Exception {
        if (!redisManager.executeWithLock(
                getLockKey(userId, currency),
                RedisManager.LockParams.DEFAULT,
                () -> {
                    Asset asset = findOrCreateNoLock(userId, currency, entityManager);

                    asset.setOnhandAmount(asset.getOnhandAmount().add(onhandAmountDiff));
                    asset.setLockedAmount(asset.getLockedAmount().add(lockedAmountDiff));
                    save(asset, entityManager);

                    if (asset.getOnhandAmount().subtract(asset.getLockedAmount()).signum() < 0
                            || asset.getOnhandAmount().signum() < 0
                            || asset.getLockedAmount().signum() < 0) {

                        throw new CustomException(
                                ErrorCode.ORDER_ERROR_INVALID_ASSET,
                                "onhandAmount: "
                                        + JsonUtil.encode(asset.getOnhandAmount())
                                        + ", lockedAmount: "
                                        + JsonUtil.encode(asset.getLockedAmount())
                                        + ", userId: "
                                        + JsonUtil.encode(userId));
                    }
                })) {
            throw new CustomException(ErrorCode.COMMON_ERROR_LOCK, "AssetUpdate userId: " + userId);
        }
        ;
    }

    public void updateWithExternalLock(
            Long userId,
            Currency currency,
            BigDecimal onhandAmountDiff,
            BigDecimal lockedAmountDiff,
            EntityManager entityManager)
            throws Exception {
        Asset asset = findOrCreate(userId, currency, entityManager);

        asset.setOnhandAmount(asset.getOnhandAmount().add(onhandAmountDiff));
        asset.setLockedAmount(asset.getLockedAmount().add(lockedAmountDiff));
        save(asset, entityManager);
    }

    public void updateAssestAmountWithExternalLock(
            Long userId,
            Currency currency,
            BigDecimal onhandAmountDiff,
            BigDecimal lockedAmountDiff,
            BigDecimal evalProfitLossAmt,
            BigDecimal evalProfitLossAmtRate,
            BigDecimal avgAcqUnitPrice,
            BigDecimal assetTotal,
            EntityManager entityManager)
            throws Exception {
        Asset asset = findOrCreate(userId, currency, entityManager);

        asset.setOnhandAmount(asset.getOnhandAmount().add(onhandAmountDiff));
        asset.setLockedAmount(asset.getLockedAmount().add(lockedAmountDiff));
        asset.setEvalProfitLossAmt(evalProfitLossAmt);
        asset.setEvalProfitLossAmtRate(evalProfitLossAmtRate);
        asset.setAvgAcqUnitPrice(avgAcqUnitPrice);
        asset.setAssetTotal(assetTotal);
        save(asset, entityManager);
    }

    @Override
    public void redisPublish(Asset entity) {
        redisPublisher.publish(entity);
    }
}
