package point.common.service;

import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.entity.MonsterGrowthLevel;
import point.common.entity.MonsterGrowthLevel_;
import point.common.predicate.MonsterGrowthLevelPredicate;

@RequiredArgsConstructor
@Service
public class MonsterGrowthLevelService
        extends EntityService<MonsterGrowthLevel, MonsterGrowthLevelPredicate> {

    @Override
    public Class<MonsterGrowthLevel> getEntityClass() {
        return MonsterGrowthLevel.class;
    }

    public List<MonsterGrowthLevel> findTopByRequiredExperienceAsc(int size) {
        return super.customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<MonsterGrowthLevel> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                Collections.emptyList(),
                                NumberUtils.INTEGER_ZERO,
                                size,
                                criteriaBuilder.asc(
                                        root.get(MonsterGrowthLevel_.requiredExperience)));
                    }
                });
    }
}
