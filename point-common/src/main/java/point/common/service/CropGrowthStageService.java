package point.common.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.TradeType;
import point.common.entity.CropGrowthStage;
import point.common.entity.CropGrowthStageHistory;
import point.common.model.request.CropGrowthStageUpdateForm;
import point.common.model.request.CropGrowthStageUpdateRequest;
import point.common.model.response.CropGrowthStageListData;
import point.common.predicate.CropGrowthStagePredicate;

@RequiredArgsConstructor
@Service
public class CropGrowthStageService
        extends EntityService<CropGrowthStage, CropGrowthStagePredicate> {

    private final CropGrowthStageHistoryService cropGrowthStageHistoryService;

    @Override
    public Class<CropGrowthStage> getEntityClass() {
        return CropGrowthStage.class;
    }

    public CropGrowthStageListData selectCropGrowthStageList(final String tradeType)
            throws Exception {

        if (StringUtils.isBlank(tradeType)) {
            List<CropGrowthStage> allData = findAll();

            var histMap =
                    Arrays.stream(TradeType.values())
                            .map(cropGrowthStageHistoryService::getlastestData)
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            CropGrowthStageHistory::getTradeType,
                                            Function.identity()));

            return CropGrowthStageListData.builder()
                    .listData(allData)
                    .investLastestHist(histMap.get(TradeType.INVEST))
                    .operateLastestHist(histMap.get(TradeType.OPERATE))
                    .build();
        }

        var hist = cropGrowthStageHistoryService.getlastestData(TradeType.valueOf(tradeType));
        var dataList = findByCondition(tradeType);
        return CropGrowthStageListData.builder()
                .listData(dataList)
                .investLastestHist(
                        Objects.equals(TradeType.INVEST.getName(), tradeType) ? hist : null)
                .operateLastestHist(
                        Objects.equals(TradeType.INVEST.getName(), tradeType) ? hist : null)
                .build();
    }

    public List<CropGrowthStage> findByCondition(final String tradeType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<CropGrowthStage, List<CropGrowthStage>>() {
                    @Override
                    public List<CropGrowthStage> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                buildPredicate(criteriaBuilder, root, tradeType));
                    }
                });
    }

    private List<Predicate> buildPredicate(
            CriteriaBuilder criteriaBuilder, Root<CropGrowthStage> root, String tradeType) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(tradeType)) {
            predicates.add(
                    predicate.equalTradeType(criteriaBuilder, root, TradeType.valueOf(tradeType)));
        }
        return predicates;
    }

    public void update(CropGrowthStageUpdateRequest request) throws Exception {

        if (isOriginalAndUpdatedItemsEqual(request)) {
            return;
        }

        customTransactionManager.execute(
                entityManager -> {
                    TradeType tradeType = TradeType.valueOf(request.getTradeType());

                    // 更新が必要なフォームデータを取得
                    List<CropGrowthStageUpdateForm> cropGrowthStageUpdateFormList =
                            request.getUpdatedItems();
                    Set<Long> formIds =
                            cropGrowthStageUpdateFormList.stream()
                                    .map(CropGrowthStageUpdateForm::getId)
                                    .collect(Collectors.toSet());

                    // すべての既存レコードを一度に取得
                    List<CropGrowthStage> allExistingRecords =
                            this.findAll().stream()
                                    .filter(d -> Objects.equals(d.getTradeType(), tradeType))
                                    .toList();
                    Map<Long, CropGrowthStage> existingRecordsMap =
                            allExistingRecords.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    CropGrowthStage::getId, Function.identity()));

                    // フォームに含まれていないレコードを一括削除
                    List<CropGrowthStage> recordsToDelete =
                            allExistingRecords.stream()
                                    .filter(record -> !formIds.contains(record.getId()))
                                    .toList();
                    recordsToDelete.forEach(record -> this.delete(record, entityManager));

                    // レコードを一括更新または挿入
                    List<CropGrowthStage> recordsToSave = new ArrayList<>();
                    for (CropGrowthStageUpdateForm form : cropGrowthStageUpdateFormList) {
                        CropGrowthStage existingRecord = existingRecordsMap.get(form.getId());

                        if (existingRecord != null) {
                            boolean isUpdated = false;
                            if (!existingRecord.getGrowthStage().equals(form.getGrowthStage())) {
                                existingRecord.setGrowthStage(form.getGrowthStage());
                                isUpdated = true;
                            }
                            if (!existingRecord
                                    .getEvaluationFrom()
                                    .equals(form.getEvaluationFrom())) {
                                existingRecord.setEvaluationFrom(form.getEvaluationFrom());
                                isUpdated = true;
                            }
                            if (!existingRecord.getEvaluationTo().equals(form.getEvaluationTo())) {
                                existingRecord.setEvaluationTo(form.getEvaluationTo());
                                isUpdated = true;
                            }
                            if (isUpdated) {
                                recordsToSave.add(existingRecord);
                            }
                        } else {
                            CropGrowthStage cropGrowthStageNew = new CropGrowthStage();
                            cropGrowthStageNew.setTradeType(tradeType);
                            cropGrowthStageNew.setGrowthStageId(form.getGrowthStageId());
                            cropGrowthStageNew.setGrowthStage(form.getGrowthStage());
                            cropGrowthStageNew.setEvaluationFrom(form.getEvaluationFrom());
                            cropGrowthStageNew.setEvaluationTo(form.getEvaluationTo());
                            cropGrowthStageNew.setUpdatedBy(
                                    getName(
                                            SecurityContextHolder.getContext()
                                                    .getAuthentication()
                                                    .getName()));
                            recordsToSave.add(cropGrowthStageNew);
                        }
                    }

                    // 履歴レコードを作成
                    CropGrowthStageHistory cropGrowthStageHistory = createHistory(request);
                    cropGrowthStageHistoryService.save(cropGrowthStageHistory, entityManager);

                    // 更新または挿入するレコードを一括保存
                    recordsToSave.forEach(
                            record -> {
                                try {
                                    this.save(record, entityManager);
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            });
                });
    }

    private CropGrowthStageHistory createHistory(CropGrowthStageUpdateRequest request) {
        List<CropGrowthStageUpdateForm> originalItems = request.getOriginalItems();
        List<CropGrowthStageUpdateForm> updatedItems = request.getUpdatedItems();

        CropGrowthStageHistory history = new CropGrowthStageHistory();

        StringBuffer beforeGrowthStage = new StringBuffer();
        StringBuffer beforeEvaluation = new StringBuffer();
        int originalItemsIndex = 0;
        for (CropGrowthStageUpdateForm item : originalItems) {
            if (beforeGrowthStage.length() > 0) {
                beforeGrowthStage.append(", ");
            }

            beforeGrowthStage.append(originalItemsIndex + 1);
            if (StringUtils.isNoneEmpty(item.getGrowthStage())) {
                beforeGrowthStage.append("(");
                beforeGrowthStage.append(item.getGrowthStage());
                beforeGrowthStage.append(")");
            } else {
                beforeGrowthStage.append(item.getGrowthStage());
            }
            originalItemsIndex++;

            if (beforeEvaluation.length() > 0) {
                beforeEvaluation.append(", ");
            }

            // 处理第一个和最后一个实体类的EvaluationFrom和EvaluationTo
            String evaluationFrom =
                    (originalItemsIndex == 1) ? "マイナス無限大" : item.getEvaluationFrom().toString();
            String evaluationTo =
                    (originalItemsIndex == originalItems.size())
                            ? "プラス無限大"
                            : item.getEvaluationTo().toString();

            beforeEvaluation.append(evaluationFrom).append("~").append(evaluationTo);
        }
        history.setBeforGrowthStage(beforeGrowthStage.toString());
        history.setBeforEvaluation(beforeEvaluation.toString());

        // 处理 updatedItems
        StringBuffer afterGrowthStage = new StringBuffer();
        StringBuffer afterEvaluation = new StringBuffer();
        int updatedItemsIndex = 0;
        for (CropGrowthStageUpdateForm item : updatedItems) {
            if (afterGrowthStage.length() > 0) {
                afterGrowthStage.append(", ");
            }

            afterGrowthStage.append(updatedItemsIndex + 1);
            if (StringUtils.isNoneEmpty(item.getGrowthStage())) {
                afterGrowthStage.append("(");
                afterGrowthStage.append(item.getGrowthStage());
                afterGrowthStage.append(")");
            } else {
                afterGrowthStage.append(item.getGrowthStage());
            }
            updatedItemsIndex++;

            if (afterEvaluation.length() > 0) {
                afterEvaluation.append(", ");
            }

            // 处理第一个和最后一个实体类的EvaluationFrom和EvaluationTo
            String evaluationFrom =
                    (updatedItemsIndex == 1) ? "マイナス無限大" : item.getEvaluationFrom().toString();
            String evaluationTo =
                    (updatedItemsIndex == updatedItems.size())
                            ? "プラス無限大"
                            : item.getEvaluationTo().toString();

            afterEvaluation.append(evaluationFrom).append("~").append(evaluationTo);
        }
        history.setAfterGrowthStage(afterGrowthStage.toString());
        history.setAfterEvaluation(afterEvaluation.toString());
        history.setCreatedBy(
                getName(SecurityContextHolder.getContext().getAuthentication().getName()));
        history.setTradeType(TradeType.valueOf(request.getTradeType()));
        return history;
    }

    public boolean isOriginalAndUpdatedItemsEqual(CropGrowthStageUpdateRequest request) {
        List<CropGrowthStageUpdateForm> originalItems = request.getOriginalItems();
        List<CropGrowthStageUpdateForm> updatedItems = request.getUpdatedItems();

        if (originalItems == null && updatedItems == null) {
            return true;
        }
        if (originalItems == null || updatedItems == null) {
            return false;
        }
        if (originalItems.size() != updatedItems.size()) {
            return false;
        }
        for (int i = 0; i < originalItems.size(); i++) {
            CropGrowthStageUpdateForm original = originalItems.get(i);
            CropGrowthStageUpdateForm updated = updatedItems.get(i);
            if (!isEqual(original.getId(), updated.getId())) {
                return false;
            }
            if (!isEqual(original.getGrowthStage(), updated.getGrowthStage())) {
                return false;
            }
            if (!isEqual(original.getEvaluationFrom(), updated.getEvaluationFrom())) {
                return false;
            }
            if (!isEqual(original.getEvaluationTo(), updated.getEvaluationTo())) {
                return false;
            }
        }
        return true;
    }

    private boolean isEqual(Object obj1, Object obj2) {
        if (obj1 == null && obj2 == null) {
            return true;
        }
        if (obj1 == null || obj2 == null) {
            return false;
        }
        return obj1.equals(obj2);
    }

    private String getName(String email) {
        return email.substring(0, email.indexOf('@'));
    }
}
