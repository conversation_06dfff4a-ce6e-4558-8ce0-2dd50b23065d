package point.common.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import point.common.component.CustomTransactionManager;
import point.common.component.RedisManager;
import point.common.config.ChoiceVoteConfiguration;
import point.common.constant.*;
import point.common.entity.*;
import point.common.exception.GameException;
import point.common.model.request.ChoiceVoteForm;
import point.common.model.request.ChoiceVoteModifyForm;
import point.common.model.response.ChoiceInfoData;
import point.common.model.response.ChoiceVoteData;
import point.common.model.response.GlobalApiResponse;
import point.common.util.ChoiceActivityUtil;
import point.common.websocket.RedisPublisher;

@Slf4j
@RequiredArgsConstructor
@Service
public class ChoiceVoteInfoService {

    private final AppConfigurationService appConfigurationService;
    private final ChoicePowerService choicePowerService;
    private final ChoiceActivityService choiceActivityService;
    private final ChoiceVoteService choiceVoteService;
    private final ChoicePowerTransferService choicePowerTransferService;
    private final CustomTransactionManager customTransactionManager;
    private static final String VOTE_STR = "vote";
    private final RedisManager redisManager;
    private final ChoiceVoteHistoryService choiceVoteHistoryService;
    private final ChoiceActivityTemplateService choiceActivityTemplateService;
    private final ChoiceRewardService choiceRewardService;
    private final RedisPublisher redisPublisher;

    private static String getLockKey(Long activityId, Long userId) {
        return "lock:activityId:" + activityId + ":" + userId;
    }

    public ChoiceInfoData getChoiceInfo(List<Long> userIds) {
        Long userId = userIds.get(0);
        ChoiceInfoData choiceInfoData = new ChoiceInfoData();

        ChoicePower choicePower = choicePowerService.findOneByUserId(userId);
        choiceInfoData.setUserPower(
                Objects.nonNull(choicePower) ? choicePower.getAmount() : NumberUtils.LONG_ZERO);
        BigDecimal choiceP =
                Objects.nonNull(choicePower) && Objects.nonNull(choicePower.getChoicePAmount())
                        ? choicePower.getChoicePAmount()
                        : BigDecimal.ZERO;
        choiceInfoData.setWithdrawChoicePAmount(choiceP);

        ChoiceActivity choiceActivityByToday = choiceActivityService.findChoiceActivityByToday();
        if (Objects.isNull(choiceActivityByToday)) {
            choiceActivityByToday = choiceActivityService.findLatestCompletedActivity(); // 新增方法
        }

        if (Objects.isNull(choiceActivityByToday)) {
            return choiceInfoData;
        }

        long count = choiceVoteService.countByActivityId(choiceActivityByToday.getId());
        choiceInfoData.setVoteStartTime(choiceActivityByToday.getVoteStartTime());
        choiceInfoData.setVoteEndTime(choiceActivityByToday.getVoteEndTime());
        choiceInfoData.setLockStartTime(choiceActivityByToday.getLockStartTime());
        choiceInfoData.setLockEndTime(choiceActivityByToday.getLockEndTime());
        choiceInfoData.setBasePrice(choiceActivityByToday.getBasePriceVotingDay());
        choiceInfoData.setRewardPool(choiceActivityByToday.getRewardPool());
        choiceInfoData.setStatus(choiceActivityByToday.getStatus());
        choiceInfoData.setActivityId(choiceActivityByToday.getId());
        choiceInfoData.setAllUserVoteCount(count);
        choiceInfoData.setBasePriceDate(choiceActivityByToday.getVoteStartTime());

        if (ChoiceActivityUtil.isInRewardDistributionTimeRange()
                && choiceP.compareTo(BigDecimal.ZERO) > 0) {
            Optional<ChoiceActivity> previousChoiceActivity =
                    choiceActivityService.findChoiceActivityWithoutIdOrderByIdDesc(
                            choiceActivityByToday.getId());
            if (previousChoiceActivity.isPresent()) {
                ChoiceReward reward =
                        choiceRewardService.findRewardByActivityId(
                                userIds, previousChoiceActivity.get().getId());
                if (Objects.nonNull(reward)) {
                    choiceP = choiceP.subtract(reward.getAmount());
                }
            }
        }
        choiceInfoData.setWithdrawChoicePAmount(choiceP);

        Long totalVotePowerUp = choiceActivityByToday.getTotalVotePowerUp();
        Long totalVotePowerDown = choiceActivityByToday.getTotalVotePowerDown();

        BigDecimal upVoteRate = BigDecimal.ZERO;
        BigDecimal downVoteRate = BigDecimal.ZERO;

        totalVotePowerUp = Optional.ofNullable(totalVotePowerUp).orElse(NumberUtils.LONG_ZERO);
        totalVotePowerDown = Optional.ofNullable(totalVotePowerDown).orElse(NumberUtils.LONG_ZERO);
        choiceInfoData.setTotalVotePowerUp(totalVotePowerUp);
        choiceInfoData.setTotalVotePowerDown(totalVotePowerDown);

        BigDecimal totalVotes = new BigDecimal(totalVotePowerUp + totalVotePowerDown);
        if (totalVotes.compareTo(BigDecimal.ZERO) > 0) {
            upVoteRate =
                    new BigDecimal(totalVotePowerUp).divide(totalVotes, 4, RoundingMode.HALF_UP);
            downVoteRate =
                    new BigDecimal(totalVotePowerDown).divide(totalVotes, 4, RoundingMode.HALF_UP);
        } else if (totalVotePowerUp > 0) {
            upVoteRate = BigDecimal.ONE;
        } else if (totalVotePowerDown > 0) {
            downVoteRate = BigDecimal.ONE;
        }

        BigDecimal upVoteRateBD =
                upVoteRate.multiply(new BigDecimal(100)).setScale(2, RoundingMode.DOWN);
        BigDecimal downVoteRateBD =
                downVoteRate.multiply(new BigDecimal(100)).setScale(2, RoundingMode.DOWN);

        choiceInfoData.setUpVoteRate(upVoteRateBD);
        choiceInfoData.setDownVoteRate(downVoteRateBD);

        Optional<ChoiceVote> choiceVoteOptional =
                choiceVoteService.findByUserIdsWithActivityId(
                        userIds, choiceActivityByToday.getId());
        choiceInfoData.setButtonFlag(false);
        choiceInfoData.setEditFlag(false);
        if (ChoiceActivityResultStatus.VOTING.equals(choiceActivityByToday.getStatus())) {
            if (choiceVoteOptional.isEmpty()) {
                choiceInfoData.setButtonFlag(true);
            } else {
                choiceInfoData.setEditFlag(true);
            }
        }
        choiceVoteOptional.ifPresent(
                choiceVote -> {
                    choiceInfoData.setChoiceVoteId(choiceVote.getId());
                    choiceInfoData.setChoiceUserVotePower(choiceVote.getVotePower());
                    choiceInfoData.setVoteCategory(choiceVote.getVoteDirection().toString());
                });
        return choiceInfoData;
    }

    public ResponseEntity<GlobalApiResponse<ChoiceVoteData>> modifyVotePower(
            List<Long> userIds, ChoiceVoteModifyForm choiceVoteForm) throws Exception {
        Long userId = userIds.get(0);
        // Check choice_activity exists
        ChoiceActivity choiceActivityByToday =
                choiceActivityService.findOne(choiceVoteForm.getActivityId());
        if (Objects.isNull(choiceActivityByToday)) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_NOT_START));
        }

        // Check vote time
        if (!choiceActivityByToday.isInValidTime() || choiceActivityByToday.isInLockTime()) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_NOT_START));
        }

        // Check choice_activity_template exists
        Optional<ChoiceActivityTemplate> choiceActivityTemplateOptional =
                this.validateAndGetChoiceTemplate();
        if (choiceActivityTemplateOptional.isEmpty()) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_NOT_START));
        }

        // Validate if vote power meets minimum requirement from activity template
        ChoiceActivityTemplate defaultChoiceTemplate = choiceActivityTemplateOptional.get();
        if (choiceVoteForm.getVotePower() < defaultChoiceTemplate.getVotePowerLimit()) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_POWER_LESS_THAN_MINIMUM
                                            .getMessage(
                                                    defaultChoiceTemplate.getVotePowerLimit())));
        }

        // Validate if total vote power exceeds system maximum limit per user
        Optional<ChoiceVoteConfiguration> choiceVoteConfiguration =
                appConfigurationService.findValueByKey(
                        CommonConstants.CHOICE_VOTE_CONFIGURATION, ChoiceVoteConfiguration.class);
        int maxVotePowerPerUser =
                choiceVoteConfiguration
                        .map(ChoiceVoteConfiguration::getMaxVotePowerPerUser)
                        .orElse(ChoiceVoteConfiguration.DEFAULT_MAX_VOTE_POWER_PER_USER);
        if (choiceVoteForm.getVotePower() > maxVotePowerPerUser) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_POWER_EXCEED_MAXIMUM
                                            .getMessage(maxVotePowerPerUser)));
        }

        ChoicePower choicePower = choicePowerService.findOneByUserId(userId);
        if (Objects.isNull(choicePower)) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_POWER_NOT_ENOUGH));
        }

        log.info(
                "Modify Vote power: {}, userId: {}, activityId: {}",
                choiceVoteForm.getVotePower(),
                userId,
                choiceVoteForm.getActivityId());

        Long choicePowerAmount = choicePower.getAmount();
        AtomicReference<Long> balanceVoteRef = new AtomicReference<>();
        ChoiceVote choiceVoteEntity =
                customTransactionManager.execute(
                        entityManager -> {
                            ChoiceVote choiceVote =
                                    choiceVoteService.findOne(choiceVoteForm.getChoiceVoteId());
                            Long beforeVotePower = choiceVote.getVotePower();
                            Long afterVotePower = choiceVoteForm.getVotePower();
                            if (Objects.equals(beforeVotePower, afterVotePower)) {
                                return choiceVote;
                            }
                            choiceVote.setVotePower(choiceVoteForm.getVotePower());
                            ChoicePowerTransfer choicePowerTransfer =
                                    choicePowerTransferService.findChoicePowerTransferByUserToday(
                                            userIds);
                            Long getPowerVote = choicePowerTransfer.getAmount();
                            Long balanceVote = choiceVoteForm.getVotePower() - getPowerVote;
                            if ((choicePowerAmount + getPowerVote)
                                    < choiceVoteForm.getVotePower()) {
                                throw new GameException(
                                        ErrorCode.REQUEST_ERROR_CHOICE_POWER_NOT_ENOUGH,
                                        ErrorCode.REQUEST_ERROR_CHOICE_POWER_NOT_ENOUGH
                                                .getMessage());
                            }
                            balanceVoteRef.set(balanceVote);

                            choicePowerTransfer.setAmount(choiceVoteForm.getVotePower());

                            choicePowerTransferService.save(choicePowerTransfer, entityManager);

                            choicePower.setAmount(choicePower.getAmount() - balanceVote);
                            choicePowerService.save(choicePower, entityManager);

                            ChoiceVote modifiedVote =
                                    choiceVoteService.save(choiceVote, entityManager);
                            // save choice power history
                            choiceVoteHistoryService.createVoteHistory(
                                    choiceVote.getId(),
                                    choiceVote.getActivityId(),
                                    userId,
                                    beforeVotePower,
                                    afterVotePower,
                                    entityManager);
                            return modifiedVote;
                        });

        log.info(
                "Modify vote success, userId: {}, activityId: {}, choiceVote id:{}, votePower: {}",
                userId,
                choiceVoteForm.getActivityId(),
                Optional.ofNullable(choiceVoteEntity).map(AbstractEntity::getId).orElse(null),
                choiceVoteForm.getVotePower());

        assert choiceVoteEntity != null;
        //        if (!Objects.equals(choiceVoteEntity.getVotePower(),
        // choiceVoteForm.getVotePower())) {
        redisPublisher.publish(
                choiceVoteEntity, ChoiceActivityVoteAction.MODIFY, balanceVoteRef.get());
        //        }
        return ResponseEntity.ok()
                .body(GlobalApiResponse.success(new ChoiceVoteData(choiceVoteEntity)));
    }

    public ResponseEntity<GlobalApiResponse<ChoiceVoteData>> choiceVote(
            List<Long> userIds, ChoiceVoteForm choiceVoteForm) throws Exception {
        Long userId = userIds.get(0);

        // Check choice_activity exists
        Optional<ChoiceActivity> choiceActivityOptional =
                this.validateAndGetChoiceActivity(choiceVoteForm);
        if (choiceActivityOptional.isEmpty()) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_NOT_START));
        }

        // Check vote time
        ChoiceActivity choiceActivityByToday = choiceActivityOptional.get();
        if (!choiceActivityByToday.isInValidTime() || choiceActivityByToday.isInLockTime()) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_NOT_START));
        }

        // Check choice_activity_template exists
        Optional<ChoiceActivityTemplate> choiceActivityTemplateOptional =
                this.validateAndGetChoiceTemplate();
        if (choiceActivityTemplateOptional.isEmpty()) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_NOT_START));
        }

        // check a repeat vote
        boolean existsVote =
                choiceVoteService.existsByUserId(choiceVoteForm.getActivityId(), userIds);
        if (existsVote) {
            throw new GameException(
                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_REPEAT,
                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_REPEAT.getMessage());
        }

        // Validate if vote power meets the minimum requirement from activity template
        ChoiceActivityTemplate defaultChoiceTemplate = choiceActivityTemplateOptional.get();
        if (choiceVoteForm.getVotePower() < defaultChoiceTemplate.getVotePowerLimit()) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_POWER_LESS_THAN_MINIMUM
                                            .getMessage(
                                                    defaultChoiceTemplate.getVotePowerLimit())));
        }

        // Validate if total vote power exceeds system maximum limit per user
        Optional<ChoiceVoteConfiguration> choiceVoteConfiguration =
                appConfigurationService.findValueByKey(
                        CommonConstants.CHOICE_VOTE_CONFIGURATION, ChoiceVoteConfiguration.class);
        int maxVotePowerPerUser =
                choiceVoteConfiguration
                        .map(ChoiceVoteConfiguration::getMaxVotePowerPerUser)
                        .orElse(ChoiceVoteConfiguration.DEFAULT_MAX_VOTE_POWER_PER_USER);
        long existingVotePower =
                choiceVoteService.sumVotePowerByActivityIdWithUserId(
                        choiceVoteForm.getActivityId(), userIds);
        long requestVotePower = choiceVoteForm.getVotePower();
        if ((requestVotePower + existingVotePower) > maxVotePowerPerUser) {
            return ResponseEntity.ok()
                    .body(
                            GlobalApiResponse.badRequest(
                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_POWER_EXCEED_MAXIMUM
                                            .getMessage(maxVotePowerPerUser, existingVotePower)));
        }

        AtomicReference<ChoiceVote> choiceVoteAtomicRef = new AtomicReference<>();
        if (!redisManager.executeWithLock(
                getLockKey(choiceVoteForm.getActivityId(), userId),
                RedisManager.LockParams.LOCKKEY,
                () -> {
                    ChoiceVote choiceVoteEntity =
                            customTransactionManager.execute(
                                    entityManager -> {
                                        // double check
                                        if (choiceVoteService.existsByUserId(
                                                choiceVoteForm.getActivityId(), userIds)) {
                                            throw new GameException(
                                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_REPEAT,
                                                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_REPEAT
                                                            .getMessage());
                                        }
                                        log.info(
                                                "Choice Vote power: {}, userId: {}, activityId: {}",
                                                choiceVoteForm.getVotePower(),
                                                userId,
                                                choiceVoteForm.getActivityId());
                                        ChoicePower choicePower =
                                                choicePowerService.findOneByUserId(userId);
                                        // check the vote power enough
                                        if (choicePower
                                                        .getAmount()
                                                        .compareTo(choiceVoteForm.getVotePower())
                                                < NumberUtils.INTEGER_ZERO) {
                                            throw new GameException(
                                                    ErrorCode.REQUEST_ERROR_CHOICE_POWER_NOT_ENOUGH,
                                                    ErrorCode.REQUEST_ERROR_CHOICE_POWER_NOT_ENOUGH
                                                            .getMessage());
                                        }

                                        // update choice power for the user
                                        choicePower.setAmount(
                                                choicePower.getAmount()
                                                        - choiceVoteForm.getVotePower());
                                        choicePowerService.save(choicePower, entityManager);

                                        // create transfer record
                                        ChoicePowerTransfer choicePowerTransfer =
                                                new ChoicePowerTransfer();
                                        choicePowerTransfer.setAmount(
                                                choiceVoteForm.getVotePower());
                                        choicePowerTransfer.setUserId(userId);
                                        choicePowerTransfer.setTransferType(
                                                ChoicePowerTransferType.OUT);
                                        choicePowerTransfer.setChoiceActivityRuleId(
                                                ChoiceActivityRuleEnum.CHOICE_CONSUMPTION.getId());
                                        choicePowerTransfer.setDescription(VOTE_STR);
                                        choicePowerTransferService.save(
                                                choicePowerTransfer, entityManager);

                                        // create vote record
                                        ChoiceVote choiceVote = new ChoiceVote();
                                        choiceVote.setVotePower(choiceVoteForm.getVotePower());
                                        choiceVote.setVoteDirection(
                                                choiceVoteForm.getChoiceActivityVoteResult());
                                        choiceVote.setUserId(userId);
                                        choiceVote.setActivityId(choiceVoteForm.getActivityId());
                                        choiceVote.setVoteResult(ChoiceVoteResult.PENDING);
                                        choiceVote.setVoteTime(new Date());
                                        choiceVote.setWithdrawalAllowed(0);
                                        return choiceVoteService.save(choiceVote, entityManager);
                                    });
                    choiceVoteAtomicRef.set(choiceVoteEntity);
                })) {
            // Failed to get lock key
            log.error(
                    "Failed to get lock for vote key: {}",
                    getLockKey(choiceVoteForm.getActivityId(), userId));
            throw new GameException(
                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_REPEAT,
                    ErrorCode.REQUEST_ERROR_CHOICE_VOTE_REPEAT.getMessage());
        }

        ChoiceVote choiceVoteResp = choiceVoteAtomicRef.get();
        log.info(
                "Choice vote success, userId: {}, activityId: {}, choiceVote id:{}, votePower: {}",
                userId,
                choiceVoteForm.getActivityId(),
                Optional.ofNullable(choiceVoteResp).map(AbstractEntity::getId).orElse(null),
                choiceVoteForm.getVotePower());

        // publish event
        redisPublisher.publish(choiceVoteResp, ChoiceActivityVoteAction.CREATE, null);

        assert choiceVoteResp != null;
        return ResponseEntity.ok()
                .body(new GlobalApiResponse<>(200, new ChoiceVoteData(choiceVoteResp)));
    }

    private Optional<ChoiceActivityTemplate> validateAndGetChoiceTemplate() {
        Optional<List<ChoiceActivityTemplate>> choiceActivityTemplates =
                choiceActivityTemplateService.findByStatus(ChoiceActivityStatus.ACTIVE);
        if (choiceActivityTemplates.isEmpty() || choiceActivityTemplates.get().isEmpty()) {
            return Optional.empty();
        }

        List<ChoiceActivityTemplate> activityTemplates = choiceActivityTemplates.get();
        ChoiceActivityTemplate defaultChoiceTemplate = activityTemplates.get(0);
        if (activityTemplates.size() > NumberUtils.LONG_ONE) {
            log.warn(
                    "ChoiceActivityTemplate size is greater than 1 with status: {}, use the first choiceActivityTemplate by default, id: {}",
                    ChoiceActivityStatus.ACTIVE,
                    defaultChoiceTemplate.getId());
        }
        return Optional.ofNullable(activityTemplates.get(0));
    }

    private Optional<ChoiceActivity> validateAndGetChoiceActivity(ChoiceVoteForm choiceVoteForm) {
        return Optional.ofNullable(choiceActivityService.findOne(choiceVoteForm.getActivityId()));
    }
}
