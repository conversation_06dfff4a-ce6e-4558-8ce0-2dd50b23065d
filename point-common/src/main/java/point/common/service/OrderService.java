package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.util.CollectionUtils;
import point.common.component.HistoricalTransactionManager;
import point.common.component.QueryExecutorReturner;
import point.common.component.RedisManager;
import point.common.entity.Order;
import point.common.entity.Order_;
import point.common.entity.Symbol;
import point.common.model.OrderRowMapper;
import point.common.predicate.OrderPredicate;

@Slf4j
public abstract class OrderService<
                E extends Order, P extends OrderPredicate<E>, R extends OrderRowMapper<E>>
        extends EntityService<E, P> {

    @Autowired protected HistoricalTransactionManager historicalTransactionManager;

    @Autowired protected RedisManager redisManager;

    @Autowired protected SymbolService symbolService;

    public abstract void archive(Symbol symbol);

    public abstract Class<R> getRowMapperClass();

    public R newRowMapper() {
        R rowMapper = null;

        try {
            rowMapper = getRowMapperClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("newRowMapper failed", e);
        }

        return rowMapper;
    }

    public E findFromHistory(Symbol symbol, Long id) {
        return historicalTransactionManager.findOneFromHistory(
                "select * from " + Order.getTableName(symbol) + " where id = ?",
                id,
                newRowMapper());
    }

    public List<E> findAllFromHistory(Symbol symbol, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("ids", ids);
        return historicalTransactionManager.findFromHistory(
                "select * from " + Order.getTableName(symbol) + " where id in (:ids)",
                mapSqlParameterSource,
                newRowMapper());
    }

    public List<E> findByCondition(
            Long symbolId,
            Long userId,
            Long id,
            Long idFrom,
            Long idTo,
            Long dateFrom,
            Long dateTo,
            Integer number,
            Integer size) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<E, List<E>>() {
                    @Override
                    public List<E> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));

                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }

                        if (id != null) {
                            predicates.add(predicate.equalId(criteriaBuilder, root, id));
                        } else {
                            if (idFrom != null) {
                                predicates.add(
                                        predicate.greaterThanOrEqualToId(
                                                criteriaBuilder, root, idFrom));
                            }

                            if (idTo != null) {
                                predicates.add(predicate.lessThanId(criteriaBuilder, root, idTo));
                            }

                            if (dateFrom != null) {
                                predicates.add(
                                        predicate.greaterThanOrEqualToCreatedAt(
                                                criteriaBuilder, root, new Date(dateFrom)));
                            }

                            if (dateTo != null) {
                                predicates.add(
                                        predicate.lessThanCreatedAt(
                                                criteriaBuilder, root, new Date(dateTo)));
                            }
                        }

                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                number,
                                size,
                                criteriaBuilder.asc(root.get(Order_.id)));
                    }
                });
    }
}
