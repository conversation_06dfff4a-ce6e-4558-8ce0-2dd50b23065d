package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.PointTransferStatusEnum;
import point.common.entity.PointTransfer;
import point.common.entity.PointTransferStatusHistory;
import point.common.entity.PointTransferStatusHistory_;
import point.common.predicate.PointTransferStatusHistoryPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointTransferStatusHistoryService
        extends EntityService<PointTransferStatusHistory, PointTransferStatusHistoryPredicate> {

    @Override
    public Class<PointTransferStatusHistory> getEntityClass() {
        return PointTransferStatusHistory.class;
    }

    /**
     * 状態履歴を記録する
     *
     * @param transfer 関連する PointTransfer レコード
     * @param previousStatus 変更前の状態
     * @param currentStatus 変更後の状態
     * @param remarks 備考
     */
    public void recordStatusHistory(
            PointTransfer transfer,
            PointTransferStatusEnum previousStatus,
            PointTransferStatusEnum currentStatus,
            String remarks,
            EntityManager entityManager)
            throws Exception {
        PointTransferStatusHistory statusHistory = new PointTransferStatusHistory();
        statusHistory.setTransfer(transfer);
        statusHistory.setPreviousStatus(previousStatus == null ? null : previousStatus.getCode());
        statusHistory.setCurrentStatus(currentStatus.getCode());
        statusHistory.setChangedAt(new Date());
        statusHistory.setRemarks(remarks);
        this.save(statusHistory, entityManager);
        log.info(
                "状態履歴を記録しました: transferId={}, previousStatus={}, currentStatus={}, remarks={}",
                transfer.getId(),
                previousStatus,
                currentStatus,
                remarks);
    }

    public List<PointTransferStatusHistory> findByTransferId(Long transferId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<PointTransferStatusHistory> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalTransferId(criteriaBuilder, root, transferId));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(
                                        root.get(PointTransferStatusHistory_.createdAt)));
                    }
                });
    }
}
