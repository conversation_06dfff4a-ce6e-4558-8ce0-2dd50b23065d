package point.common.service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.UserIdType;
import point.common.entity.ChoicePowerUserRel;
import point.common.entity.Trade_;
import point.common.model.response.PageData;
import point.common.model.response.UserPowerBalancePageData;
import point.common.predicate.ChoicePowerUserRelPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoicePowerUserRelService
        extends EntityService<ChoicePowerUserRel, ChoicePowerUserRelPredicate> {

    @Override
    public Class<ChoicePowerUserRel> getEntityClass() {
        return ChoicePowerUserRel.class;
    }

    public PageData<UserPowerBalancePageData> findByCondition(
            Long userId,
            String partnerMemberId,
            UserIdType idType,
            Integer powerAmountFrom,
            Integer powerAmountTo,
            Integer number,
            Integer size) {

        final var countCondition =
                buildCountCondition(
                        userId, partnerMemberId, idType, powerAmountFrom, powerAmountTo);
        final var resultCondition =
                buildResultCountCondition(
                        userId, partnerMemberId, idType, powerAmountFrom, powerAmountTo);
        try {
            return customTransactionManager.execute(
                    entityManager -> {
                        long count = countByCondition(entityManager, countCondition);
                        List<UserPowerBalancePageData> resultByCondition =
                                getResultByCondition(entityManager, resultCondition, number, size);
                        return new PageData<>(number, size, count, resultByCondition);
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    protected Pair<String, Map<String, Object>> buildCountCondition(
            Long userId,
            String partnerMemberId,
            UserIdType idType,
            Integer powerAmountFrom,
            Integer powerAmountTo) {
        var condition = new StringBuilder();
        var params = new HashMap<String, Object>(5);

        if (Objects.nonNull(userId)) {
            condition.append(" and t1.user_id = :userId ");
            params.put("userId", userId);
        }

        //        if (Objects.nonNull(idType) && StringUtils.isNoneBlank(idType.toString())) {
        condition.append(
                " and exists(select 1 from user_identity where id = t1.user_id and id_type = :idType) ");
        params.put("idType", UserIdType.Operate.name());
        //        }

        if (Objects.nonNull(powerAmountFrom) && Objects.nonNull(powerAmountTo)) {
            condition.append(
                    " and exists(select 1 from choice_power where id = t1.choice_power_id and amount >= :powerAmountFrom and amount <= :powerAmountTo) ");
            params.put("powerAmountFrom", powerAmountFrom);
            params.put("powerAmountTo", powerAmountTo);
        } else {
            if (Objects.nonNull(powerAmountFrom)) {
                condition.append(
                        " and exists(select 1 from choice_power where id = t1.choice_power_id and amount >= :powerAmountFrom) ");
                params.put("powerAmountFrom", powerAmountFrom);
            }

            if (Objects.nonNull(powerAmountTo)) {
                condition.append(
                        " and exists(select 1 from choice_power where id = t1.choice_power_id and amount <= :powerAmountTo) ");
                params.put("powerAmountTo", powerAmountTo);
            }
        }

        if (StringUtils.isNoneBlank(partnerMemberId)) {
            condition.append(
                    " and exists(select 1 from point_user where id = t1.user_id and partner_member_id = :partnerMemberId) ");
            params.put("partnerMemberId", partnerMemberId);
        }

        return Pair.of(condition.toString(), params);
    }

    protected Pair<String, Map<String, Object>> buildResultCountCondition(
            Long userId,
            String partnerMemberId,
            UserIdType idType,
            Integer powerAmountFrom,
            Integer powerAmountTo) {
        var condition = new StringBuilder();
        var params = new HashMap<String, Object>(5);

        if (Objects.nonNull(userId)) {
            condition.append(" and t1.user_id = :userId ");
            params.put("userId", userId);
        }

        //        if (Objects.nonNull(idType) && StringUtils.isNoneBlank(idType.toString())) {
        condition.append(" and t2.id_type = :idType ");
        params.put("idType", UserIdType.Operate.name());
        //        }

        if (Objects.nonNull(powerAmountFrom)) {
            condition.append(" and t3.amount >= :powerAmountFrom ");
            params.put("powerAmountFrom", powerAmountFrom);
        }

        if (Objects.nonNull(powerAmountTo)) {
            condition.append(" and t3.amount <= :powerAmountTo ");
            params.put("powerAmountTo", powerAmountTo);
        }

        if (StringUtils.isNoneBlank(partnerMemberId)) {
            condition.append(" and pu.partner_member_id = :partnerMemberId ");
            params.put("partnerMemberId", partnerMemberId);
        }

        return Pair.of(condition.toString(), params);
    }

    protected long countByCondition(
            EntityManager entityManager, Pair<String, Map<String, Object>> condition) {
        String sql =
                """
            SELECT count(1)
            FROM choice_power_user_rel t1
            WHERE 1 = 1"""
                        + condition.getFirst();

        var countQuery = entityManager.createNativeQuery(sql);
        condition.getSecond().forEach(countQuery::setParameter);
        return ((BigInteger) countQuery.getSingleResult()).longValue();
    }

    protected List<UserPowerBalancePageData> getResultByCondition(
            EntityManager entityManager,
            Pair<String, Map<String, Object>> condition,
            int num,
            int size) {
        String sql =
                new StringBuilder(
                                """
                    SELECT t1.id as id,
                       t1.user_id as userId,
                       pu.partner_member_id as partnerMemberId,
                       pp.name as name,
                       pp.partner_number as partnerNumber,
                       t2.id_type as idType,
                       t3.amount as amount
                FROM choice_power_user_rel t1
                FORCE INDEX (choice_power_user_rel_user_id_index)
                         INNER JOIN
                     user_identity t2 ON t1.user_id = t2.id
                         INNER JOIN
                     choice_power t3 ON t1.choice_power_id = t3.id
                         LEFT JOIN
                     point_user pu ON t2.id = pu.id
                         LEFT JOIN
                     point_partner pp ON pu.partner_id = pp.id
                WHERE 1 = 1""")
                        .append(condition.getFirst())
                        .append(" limit :size offset :over")
                        .toString();
        var params = condition.getSecond();
        params.put("size", size);
        params.put("over", size * num);
        var query = entityManager.createNativeQuery(sql, "UserPowerBalancePageDataMapping");
        params.forEach(query::setParameter);
        return query.getResultList();
    }

    public ChoicePowerUserRel getChoicePowerUserRel(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoicePowerUserRel, ChoicePowerUserRel>() {
                    @Override
                    public ChoicePowerUserRel query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(Trade_.updatedAt)));
                    }
                });
    }

    public List<ChoicePowerUserRel> getChoicePowerUserRelList(Long choicePowerId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoicePowerUserRel, List<ChoicePowerUserRel>>() {
                    @Override
                    public List<ChoicePowerUserRel> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalChoicePowerId(criteriaBuilder, root, choicePowerId));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(Trade_.updatedAt)));
                    }
                });
    }
}
