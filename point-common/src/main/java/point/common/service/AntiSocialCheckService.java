package point.common.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import point.common.aml.WorldCheckOneClient;
import point.common.aml.WorldCheckOneReq;
import point.common.aml.WorldCheckOneReqType;
import point.common.aml.WorldCheckOneRes;
import point.common.config.WorldCheckOneConfig;
import point.common.constant.AntisocialStatus;
import point.common.constant.CommonConstants;
import point.common.constant.CustomerAttribute;
import point.common.constant.KycData;
import point.common.constant.KycStatus;
import point.common.entity.User;
import point.common.entity.UserAntisocialCheck;
import point.common.entity.UserInfo;
import point.common.entity.UserKyc;
import point.common.repos.UserAntisocialCheckRepository;
import point.common.repos.UserInfoRepository;
import point.common.repos.UserKycRepository;
import point.common.repos.UserRepository;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
public class AntiSocialCheckService {

    // -- instance variables
    private final WorldCheckOneConfig config;
    private final UserAntisocialCheckRepository repository;
    private final UserInfoRepository userInfoRepository;
    private final UserRepository userRepository;
    private final UserEkycStatusChangeHistoryService userEkycStatusChangeHistoryService;
    private final UserKycRepository userKycRepository;

    // -- instance methods

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void antiSocialCheck() {

        List<User> users =
                userRepository.findByKycStatusAndUserInfoIdNotNull(KycStatus.DOCUMENT_CONFIRMED);
        if (CollectionUtils.isEmpty(users)) {
            log.info("no users requiring anti-social checks");
            return;
        }

        WorldCheckOneClient client = config.createClient();
        for (User user : users) {
            UserInfo userInfo = user.getUserInfo();
            if (userInfo == null) {
                log.info("the userinfo is null by userId: {}", user.getId());
                continue;
            }

            try {
                this.executeAntiSocialCheck(client, userInfo);
            } catch (Exception e) {
                log.warn(
                        "some error occurred by anti-social-check, user: {}, userInfoId: {}",
                        user.getId(),
                        userInfo.getId());
                log.error(e.getMessage(), e);
            }
        }
    }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void saveAntiSocialCheckResult(@NotNull UserAntisocialCheck antisocialCheck) {

        Long userId = antisocialCheck.getUserId();
        Long userInfoId = antisocialCheck.getUserInfoId();

        // never be null
        UserInfo userInfo = userInfoRepository.findById(userInfoId).get();
        userInfo.setAntisocialStatus(antisocialCheck.getKycFlag());
        userInfo.setUpdatedAt(new Date());
        userInfoRepository.save(userInfo);

        repository.save(antisocialCheck);
        log.info(
                "anti social check result persisted: userId: {}, userInfoId: {}", userId, userInfo);
        UserKyc userKyc = saveUserKyc(userId, antisocialCheck, userInfoId);
        // update customer status
        // never be null
        User user = userRepository.findById(userId).get();
        KycStatus beforeStatus = user.getKycStatus();
        KycStatus currentStatus =
                KycData.NONE.getCode().equals(antisocialCheck.getKycFlag())
                        ? KycStatus.FIRST_SCREENING
                        : KycStatus.REFINITIV_CHECKED;
        user.setKycStatus(currentStatus);
        user.setUpdatedAt(new Date());
        user.setUserKycId(userKyc.getId());
        userRepository.save(user);

        // change customer status from `beforeStatus` to REFINITIV_CHECKED
        userEkycStatusChangeHistoryService.createAntiSocialHistory(
                userId,
                beforeStatus,
                currentStatus,
                "[UserAntiSocialCheckJob]: update status to " + currentStatus.name());
    }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void handleAntiSocialCheckError(Long userId, Long userInfoId) {
        // update customer status
        User user = userRepository.findById(userId).get();
        KycStatus beforeStatus = user.getKycStatus();
        KycStatus currentStatus = KycStatus.DOCUMENT_CONFIRMED;
        user.setKycStatus(currentStatus);
        userRepository.save(user);

        userEkycStatusChangeHistoryService.createAntiSocialHistory(
                userId,
                beforeStatus,
                currentStatus,
                "[UserAntiSocialCheckJob]: fail to anti social check");
    }

    // -- private methods

    /** 顧客1件あたりの反社チェック処理 */
    public void executeAntiSocialCheck(WorldCheckOneClient client, @NotNull UserInfo userInfo) {

        Long userId = userInfo.getUserId();
        Long userInfoId = userInfo.getId();
        log.info("anti-social-check start: userId {}, userInfoId: {}", userId, userInfoId);

        try {
            // 個人口座の場合、顧客本人についてのみ反社チェックを行う
            WorldCheckOneRes worldCheckOneRes = this.requestScreening(client, userInfo);
            // 反社チェック結果の永続化
            UserAntisocialCheck antisocialCheck =
                    this.createAntisocialCheck(userId, userInfoId, worldCheckOneRes);
            this.saveAntiSocialCheckResult(antisocialCheck);
            log.info("anti-social-check finished: userId {}", userId);
        } catch (Exception e) {
            log.warn(
                    "an error occurred when calling one world check api: {} userId: {}, userInfoId: {}",
                    e.getMessage(),
                    userId,
                    userInfoId);
            log.error(e.getMessage(), e);
            this.handleAntiSocialCheckError(userId, userInfoId);
        }
    }

    /** World Check Oneへの反社チェックリクエスト(個人口座) */
    public WorldCheckOneRes requestScreening(WorldCheckOneClient client, UserInfo userInfo) {
        log.info("request screening for userId {}", userInfo.getUserId());

        char[] chars = userInfo.getBirthday().toCharArray();
        String year = new String(Arrays.copyOfRange(chars, 0, 4));
        String month = new String(Arrays.copyOfRange(chars, 4, 6));
        String day = new String(Arrays.copyOfRange(chars, 6, 8));
        LocalDate birthday =
                LocalDate.of(
                        Integer.parseInt(year), Integer.parseInt(month), Integer.parseInt(day));

        String genderCode;
        switch (userInfo.getGender()) {
            case 0 -> genderCode = "MALE";
            case 1 -> genderCode = "FEMALE";
            default -> genderCode = "OTHER";
        }

        WorldCheckOneReq req =
                WorldCheckOneReq.builder()
                        .type(WorldCheckOneReqType.INDIVIDUAL)
                        .customerAttribute(CustomerAttribute.INDIVIDUAL)
                        .name(userInfo.getLastName() + StringUtils.SPACE + userInfo.getFirstName())
                        .birthday(birthday)
                        .gender(genderCode)
                        .build();
        return client.requestScreening(req);
    }

    private UserAntisocialCheck createAntisocialCheck(
            Long userId, Long userInfoId, WorldCheckOneRes res) {
        UserAntisocialCheck ac = new UserAntisocialCheck();
        ac.setUserId(userId);
        ac.setUserInfoId(userInfoId);
        ac.setKycFlag(res.getKycData().getCode());
        ac.setReferenceId(res.getCaseId());
        ac.setCheckGroupKey(createCheckGroupKey(userId, LocalDateTime.now()));
        ac.setCreatedAt(new Date());
        ac.setUpdatedAt(new Date());
        return ac;
    }

    private String createCheckGroupKey(Long userId, LocalDateTime checkedDatetime) {
        String datetime = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(checkedDatetime);
        return String.format("%s_%s_%s", userId, datetime, CustomerAttribute.INDIVIDUAL.name());
    }

    public UserKyc saveUserKyc(
            Long userId, UserAntisocialCheck userAntisocialCheck, Long userInfoId) {
        if (KycData.EXISTS.getCode().equals(userAntisocialCheck.getKycFlag())) {
            UserKyc userKyc = new UserKyc(userId);
            userKyc.setKycStatus(KycStatus.REFINITIV_CHECKED);
            userKyc.setJudgingComment(StringUtils.EMPTY);
            userKyc.setAmlCftComment(StringUtils.EMPTY);
            userKyc.setAntisocialStatus(AntisocialStatus.CONFIRMATION_REQUIRED);
            userKyc.setCreatedAt(new Date());
            userKyc.setUpdatedAt(new Date());
            userKyc.setOperator(CommonConstants.WORKER);
            userKyc.setUserInfoId(userInfoId);
            return userKycRepository.save(userKyc);
        } else if (KycData.NONE.getCode().equals(userAntisocialCheck.getKycFlag())) {
            UserKyc userKyc;
            {
                userKyc = new UserKyc(userId);
                userKyc.setKycStatus(KycStatus.REFINITIV_CHECKED);
                userKyc.setJudgingComment(StringUtils.EMPTY);
                userKyc.setAmlCftComment(StringUtils.EMPTY);
                userKyc.setAntisocialStatus(AntisocialStatus.OK);
                userKyc.setCreatedAt(new Date());
                userKyc.setUpdatedAt(new Date());
                userKyc.setOperator(CommonConstants.WORKER);
                userKyc.setUserInfoId(userInfoId);
                userKycRepository.save(userKyc);
            }
            {
                userKyc = new UserKyc(userId);
                userKyc.setKycStatus(KycStatus.FIRST_SCREENING);
                userKyc.setJudgingComment(StringUtils.EMPTY);
                userKyc.setAmlCftComment(StringUtils.EMPTY);
                userKyc.setAntisocialStatus(AntisocialStatus.OK);
                userKyc.setCreatedAt(new Date());
                userKyc.setUpdatedAt(new Date());
                userKyc.setOperator(CommonConstants.WORKER);
                userKyc.setUserInfoId(userInfoId);
                userKycRepository.save(userKyc);
            }
            return userKyc;
        } else {
            log.info("kycFlag No corresponding results were matched");
            return null;
        }
    }
}
