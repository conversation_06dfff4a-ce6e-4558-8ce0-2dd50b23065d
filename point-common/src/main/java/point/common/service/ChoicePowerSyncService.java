package point.common.service;

import static point.common.entity.AbstractEntity_.createdAt;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ChoiceActivityRuleEnum;
import point.common.constant.ChoiceObtainFrequency;
import point.common.constant.UserIdType;
import point.common.entity.ChoiceActivityRule;
import point.common.entity.ChoicePowerSync;
import point.common.model.response.PageData;
import point.common.predicate.ChoicePowerSyncPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoicePowerSyncService
        extends EntityService<ChoicePowerSync, ChoicePowerSyncPredicate> {

    private final ChoiceActivityRuleService choiceActivityRuleService;

    @Override
    public Class<ChoicePowerSync> getEntityClass() {
        return ChoicePowerSync.class;
    }

    public PageData<ChoicePowerSync> findByCondition(
            Long userId,
            String partnerMemberId,
            UserIdType idType,
            Integer powerAmountFrom,
            Integer powerAmountTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                partnerMemberId,
                                                idType,
                                                powerAmountFrom,
                                                powerAmountTo));
                            }
                        });
        return new PageData<ChoicePowerSync>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<ChoicePowerSync, List<ChoicePowerSync>>() {
                            @Override
                            public List<ChoicePowerSync> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                partnerMemberId,
                                                idType,
                                                powerAmountFrom,
                                                powerAmountTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(createdAt)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerSync> root,
            Long userId,
            String partnerMemberId,
            UserIdType idType,
            Integer powerAmountFrom,
            Integer powerAmountTo) {
        List<Predicate> predicates = new ArrayList<>();
        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }
        return predicates;
    }

    /**
     * Save power by rule.
     *
     * @param userId the user id
     * @param choiceActivityRuleEnum the choice activity rule enum
     * @param powerAmount the power amount
     */
    public void savePowerByRule(
            Long userId,
            ChoiceActivityRuleEnum choiceActivityRuleEnum,
            @Nullable BigDecimal powerAmount) {

        try {
            log.info(
                    "Starting to save power by rule for userId: {}, choiceActivityRule: {}, powerAmount: {}",
                    userId,
                    choiceActivityRuleEnum,
                    powerAmount);

            // Check if the rule is for login
            if (ChoiceActivityRuleEnum.LOGIN_OPERATION_COURSE.equals(choiceActivityRuleEnum)
                    || ChoiceActivityRuleEnum.LOGIN_INVESTMENT_COURSE.equals(
                            choiceActivityRuleEnum)) {
                ChoicePowerSync choicePower =
                        customTransactionManager.find(
                                getEntityClass(),
                                new QueryExecutorReturner<>() {
                                    @Override
                                    public ChoicePowerSync query() {
                                        List<Predicate> predicates = new ArrayList<>(2);
                                        predicates.add(
                                                predicate.equalUserId(
                                                        criteriaBuilder, root, userId));
                                        predicates.add(
                                                predicate.equalCreatedAtYYYYMMDD(
                                                        criteriaBuilder, root, new Date()));
                                        return getSingleResult(
                                                entityManager, criteriaQuery, root, predicates);
                                    }
                                });

                if (Objects.nonNull(choicePower)) {
                    log.info("Existing power record found for userId: {}, skipping save", userId);
                    return;
                }
            }

            // Fetch the choice activity rule
            ChoiceActivityRule choiceActivityRule =
                    choiceActivityRuleService.findOneByEffectiveDate(
                            choiceActivityRuleEnum.getId());
            if (Objects.isNull(choiceActivityRule)) {
                log.warn(
                        "No choice activity rule found for ruleId: {}",
                        choiceActivityRuleEnum.getId());
                return;
            }

            // Handle null powerAmount
            if (Objects.isNull(powerAmount)) {
                powerAmount = BigDecimal.ZERO;
            }

            // Calculate final power amount based on the rule
            BigDecimal finalPowerAmount;
            if (ChoiceActivityRuleEnum.RAISE_MONSTER_LEVEL_1.equals(choiceActivityRuleEnum)) {
                finalPowerAmount = powerAmount;
            } else if (ChoiceActivityRuleEnum.BUY_SELL_TRADE.equals(choiceActivityRuleEnum)) {
                finalPowerAmount = choiceActivityRule.getPowerAmountRate().multiply(powerAmount);
            } else {
                finalPowerAmount = new BigDecimal(choiceActivityRule.getPowerAmount());
            }

            // Build the ChoicePowerSync object
            ChoicePowerSync choicePowerSync =
                    ChoicePowerSync.builder()
                            .userId(userId)
                            .choiceActivityRuleId(choiceActivityRule.getId())
                            .activityFunction(choiceActivityRule.getActivityFunction())
                            .obtainFrequency(choiceActivityRule.getObtainFrequency())
                            .getType(choiceActivityRule.getGetType())
                            .synchronizeFlag(0)
                            .build();

            // Round the final power amount
            long roundedPowerAmount =
                    finalPowerAmount.setScale(0, RoundingMode.HALF_UP).longValue();
            if (roundedPowerAmount == 0) {
                log.info("Rounded power amount is zero, skipping save for userId: {}", userId);
                return;
            }
            choicePowerSync.setPowerAmount(roundedPowerAmount);

            // Save the ChoicePowerSync object
            log.info(
                    "Saving ChoicePowerSync object for userId: {} with powerAmount: {}",
                    userId,
                    roundedPowerAmount);
            customTransactionManager.save(choicePowerSync);
        } catch (Exception e) {
            log.error(
                    "Failed to sync power by rule, userId: {}, choiceActivityRule: {}, powerAmount: {}, errorMessage: {}",
                    userId,
                    choiceActivityRuleEnum,
                    powerAmount,
                    e.getMessage(),
                    e);
        }
    }

    public List<ChoicePowerSync> findByCondition(ChoiceObtainFrequency choiceObtainFrequency) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoicePowerSync, List<ChoicePowerSync>>() {
                    @Override
                    public List<ChoicePowerSync> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalSynchronizeFlag(criteriaBuilder, root, 0));
                        if (ChoiceObtainFrequency.MONTHLY.equals(choiceObtainFrequency)) {
                            predicates.add(
                                    predicate.equalObtainFrequency(
                                            criteriaBuilder, root, ChoiceObtainFrequency.MONTHLY));
                        } else {
                            predicates.add(
                                    predicate.notEqualObtainFrequency(
                                            criteriaBuilder, root, ChoiceObtainFrequency.MONTHLY));
                        }
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
