package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.ChoiceActivityStatus;
import point.common.constant.ChoiceObtainFrequency;
import point.common.entity.ChoiceActivityRule;
import point.common.entity.ChoiceActivityRule_;
import point.common.model.request.ChoiceActivityRuleForm;
import point.common.model.response.PageData;
import point.common.predicate.ChoiceActivityRulePredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoiceActivityRuleService
        extends EntityService<ChoiceActivityRule, ChoiceActivityRulePredicate> {

    @Override
    public Class<ChoiceActivityRule> getEntityClass() {
        return ChoiceActivityRule.class;
    }

    public List<ChoiceActivityRule> fingCampaignList() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoiceActivityRule, List<ChoiceActivityRule>>() {
                    @Override
                    public List<ChoiceActivityRule> query() {
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                Stream.of(
                                                criteriaBuilder.notEqual(
                                                        root.get(
                                                                ChoiceActivityRule_
                                                                        .activityFunction),
                                                        ChoiceActivityFunction.FARM))
                                        .toList());
                    }
                });
    }

    public PageData<ChoiceActivityRule> findByCondition(
            String activityName,
            ChoiceActivityStatus status,
            ChoiceObtainFrequency obtainFrequency,
            Long powerAmountFrom,
            Long powerAmountTo,
            Long startTimeFrom,
            Long startTimeTo,
            Long endTimeFrom,
            Long endTimeTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                activityName,
                                                status,
                                                obtainFrequency,
                                                powerAmountFrom,
                                                powerAmountTo,
                                                startTimeFrom,
                                                startTimeTo,
                                                endTimeFrom,
                                                endTimeTo));
                            }
                        });

        return new PageData<ChoiceActivityRule>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<ChoiceActivityRule, List<ChoiceActivityRule>>() {
                            @Override
                            public List<ChoiceActivityRule> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                activityName,
                                                status,
                                                obtainFrequency,
                                                powerAmountFrom,
                                                powerAmountTo,
                                                startTimeFrom,
                                                startTimeTo,
                                                endTimeFrom,
                                                endTimeTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(ChoiceActivityRule_.ORDERED)),
                                        criteriaBuilder.desc(
                                                root.get(ChoiceActivityRule_.effectiveDate)),
                                        criteriaBuilder.desc(
                                                root.get(ChoiceActivityRule_.expiryDate)),
                                        criteriaBuilder.desc(
                                                root.get(ChoiceActivityRule_.updatedAt)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivityRule> root,
            String activityName,
            ChoiceActivityStatus status,
            ChoiceObtainFrequency obtainFrequency,
            Long powerAmountFrom,
            Long powerAmountTo,
            Long startTimeFrom,
            Long startTimeTo,
            Long endTimeFrom,
            Long endTimeTo) {
        List<Predicate> predicates = new ArrayList<>();
        if (activityName != null && !activityName.isEmpty()) {
            predicates.add(predicate.likeActivityName(criteriaBuilder, root, activityName));
        }

        if (status != null) {
            predicates.add(predicate.equalStatus(criteriaBuilder, root, status));
        }

        if (obtainFrequency != null) {
            predicates.add(predicate.equalObtainFrequency(criteriaBuilder, root, obtainFrequency));
        }

        if (powerAmountFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToPowerAmount(
                            criteriaBuilder, root, powerAmountFrom));
        }
        if (powerAmountTo != null) {
            predicates.add(
                    predicate.lessThanOrEqualToPowerAmount(criteriaBuilder, root, powerAmountTo));
        }

        if (startTimeFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToStartTime(
                            criteriaBuilder, root, new Date(startTimeFrom)));
        }
        if (startTimeTo != null) {
            predicates.add(
                    predicate.lessThanOrEqualToStartTime(
                            criteriaBuilder, root, new Date(startTimeTo)));
        }

        if (endTimeFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToEndTime(
                            criteriaBuilder, root, new Date(endTimeFrom)));
        }
        if (endTimeTo != null) {
            predicates.add(
                    predicate.lessThanOrEqualToEndTime(criteriaBuilder, root, new Date(endTimeTo)));
        }
        return predicates;
    }

    /**
     * create
     *
     * @param form
     * @return
     */
    public ChoiceActivityRule create(ChoiceActivityRuleForm form) {
        ChoiceActivityRule choiceActivityRule =
                ChoiceActivityRule.builder()
                        .activityName(form.getActivityName())
                        .activityFunction(form.getActivityFunction())
                        .obtainFrequency(form.getObtainFrequency())
                        .idType(form.getIdType())
                        .getType(form.getGetType())
                        .powerAmountRate(form.getPowerAmountRate())
                        .effectiveDate(form.getEffectiveDate())
                        .expiryDate(form.getExpiryDate())
                        .status(ChoiceActivityStatus.ACTIVE)
                        .createdBy(form.getCreatedBy())
                        .build();
        if (!StringUtils.isEmpty(form.getPowerAmount())) {
            choiceActivityRule.setPowerAmount(Long.parseLong(form.getPowerAmount()));
        }
        return customTransactionManager.save(choiceActivityRule);
    }

    @Override
    public ChoiceActivityRule findOne(Long id) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoiceActivityRule, ChoiceActivityRule>() {
                    @Override
                    public ChoiceActivityRule query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalId(criteriaBuilder, root, id));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public ChoiceActivityRule findOneByEffectiveDate(Long id) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoiceActivityRule, ChoiceActivityRule>() {
                    @Override
                    public ChoiceActivityRule query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalId(criteriaBuilder, root, id));
                        Date currentDate = new Date();
                        predicates.add(
                                criteriaBuilder.lessThanOrEqualTo(
                                        root.get(ChoiceActivityRule_.effectiveDate), currentDate));
                        predicates.add(
                                criteriaBuilder.greaterThan(
                                        root.get(ChoiceActivityRule_.expiryDate), currentDate));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public boolean existsActiveRecord(
            String activityName,
            ChoiceActivityFunction activityFunction,
            ChoiceActivityStatus status) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                List<Predicate> predicates = new ArrayList<>();

                                if (!ChoiceActivityFunction.FARM.equals(activityFunction)) {
                                    if (status != null) {
                                        predicates.add(
                                                predicate.equalStatus(
                                                        criteriaBuilder, root, status));
                                    }
                                    if (activityFunction != null) {
                                        predicates.add(
                                                predicate.equalActivityFunction(
                                                        criteriaBuilder, root, activityFunction));
                                    }
                                } else {
                                    if (activityName != null && !activityName.isEmpty()) {
                                        predicates.add(
                                                predicate.equalActivityName(
                                                        criteriaBuilder, root, activityName));
                                    }
                                    if (status != null) {
                                        predicates.add(
                                                predicate.equalStatus(
                                                        criteriaBuilder, root, status));
                                    }
                                    if (activityFunction != null) {
                                        predicates.add(
                                                predicate.equalActivityFunction(
                                                        criteriaBuilder, root, activityFunction));
                                    }
                                }

                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        predicates);
                            }
                        });
        return count > 0;
    }
}
