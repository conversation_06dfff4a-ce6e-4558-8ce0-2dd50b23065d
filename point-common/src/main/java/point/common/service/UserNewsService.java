package point.common.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.entity.UserNews;
import point.common.predicate.UserNewsPredicate;

@Service
public class UserNewsService extends EntityService<UserNews, UserNewsPredicate> {

    @Override
    public Class<UserNews> getEntityClass() {
        return UserNews.class;
    }

    public UserNews findByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserNews, UserNews>() {
                    @Override
                    public UserNews query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));

                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public UserNews create(Long userId) {
        System.out.println("UserNews-----------------");

        UserNews userNews = this.findByUserId(userId);
        if (userNews == null) {
            userNews = new UserNews();
            userNews.setUserId(userId);
        }
        return save(userNews);
    }

    public void saveReadId(Long userId, String readId) {
        UserNews userNews = this.findByUserId(userId);
        if (userNews == null) {
            userNews = new UserNews();
            userNews.setUserId(userId);
            userNews.setReadIds(readId);
            save(userNews);
            return;
        }
        String[] readIds = userNews.getReadIds().split(",");

        if (!Arrays.asList(readIds).contains(String.valueOf(readId))) {
            // Add
            ArrayList<Integer> newReadIdList = new ArrayList<Integer>();
            for (int i = 0; i < readIds.length; i++) {
                newReadIdList.add(Integer.valueOf(readIds[i]));
            }
            newReadIdList.add(Integer.valueOf(readId));

            // Sort
            Arrays.sort(newReadIdList.toArray());
            Integer[] sortReadIds = (Integer[]) newReadIdList.toArray(new Integer[0]);
            Arrays.sort(sortReadIds);

            // ArrayList To String[] convert
            ArrayList<String> sortReadIdList = new ArrayList<String>();
            for (int i = 0; i < sortReadIds.length; i++) {
                sortReadIdList.add(String.valueOf(sortReadIds[i]));
            }
            String[] newSortReadIds = (String[]) sortReadIdList.toArray(new String[0]);

            // save
            String saveReadIdsStr = String.join(",", newSortReadIds);
            userNews.setReadIds(saveReadIdsStr);
            save(userNews);
        }
    }
}
