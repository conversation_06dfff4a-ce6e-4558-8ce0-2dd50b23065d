package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.Authority;
import point.common.entity.UserAuthority;
import point.common.predicate.UserAuthorityPredicate;

@Service
public class UserAuthorityService extends EntityService<UserAuthority, UserAuthorityPredicate> {

    @Override
    public Class<UserAuthority> getEntityClass() {
        return UserAuthority.class;
    }

    public UserAuthority findAuthority(Long userId, Authority authority) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserAuthority, UserAuthority>() {
                    @Override
                    public UserAuthority query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(predicate.equalAuthority(criteriaBuilder, root, authority));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public UserAuthority findByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<UserAuthority, UserAuthority>() {
                    @Override
                    public UserAuthority query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
