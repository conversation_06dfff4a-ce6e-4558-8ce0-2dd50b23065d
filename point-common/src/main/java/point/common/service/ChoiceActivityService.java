package point.common.service;

import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.stream.Stream;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ChoiceActivityResultStatus;
import point.common.constant.ChoiceActivityType;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.entity.ChoiceActivity;
import point.common.entity.ChoiceActivity_;
import point.common.model.response.PageData;
import point.common.predicate.ChoiceActivityPredicate;
import point.common.util.DateRange;
import point.common.util.DateUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoiceActivityService extends EntityService<ChoiceActivity, ChoiceActivityPredicate> {

    @Override
    public Class<ChoiceActivity> getEntityClass() {
        return ChoiceActivity.class;
    }

    public PageData<ChoiceActivity> findByCondition(
            Long id,
            ChoiceActivityResultStatus status,
            Long totalVoteUsersFrom,
            Long totalVoteUsersTo,
            Long voteUsersFrom,
            Long voteUsersTo,
            Long totalVotePowerFrom,
            Long totalVotePowerTo,
            ChoiceActivityVoteResult voteResult,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                status,
                                                totalVoteUsersFrom,
                                                totalVoteUsersTo,
                                                voteUsersFrom,
                                                voteUsersTo,
                                                totalVotePowerFrom,
                                                totalVotePowerTo,
                                                voteResult));
                            }
                        });
        return new PageData<ChoiceActivity>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<ChoiceActivity, List<ChoiceActivity>>() {
                            @Override
                            public List<ChoiceActivity> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                status,
                                                totalVoteUsersFrom,
                                                totalVoteUsersTo,
                                                voteUsersFrom,
                                                voteUsersTo,
                                                totalVotePowerFrom,
                                                totalVotePowerTo,
                                                voteResult);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(ChoiceActivity_.id)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivity> root,
            Long id,
            ChoiceActivityResultStatus status,
            Long totalVoteUsersFrom,
            Long totalVoteUsersTo,
            Long voteUsersFrom,
            Long voteUsersTo,
            Long totalVotePowerFrom,
            Long totalVotePowerTo,
            ChoiceActivityVoteResult voteResult) {
        List<Predicate> predicates = new ArrayList<>();
        Predicate idPredicate = predicate.equalId(criteriaBuilder, root, id);
        if (idPredicate != null) {
            predicates.add(idPredicate);
        }

        Predicate statusPredicate = predicate.equalStatus(criteriaBuilder, root, status);
        if (statusPredicate != null) {
            predicates.add(statusPredicate);
        }

        Predicate totalVoteUsersPredicate =
                predicate.betweenTotalVoteUsers(
                        criteriaBuilder, root, totalVoteUsersFrom, totalVoteUsersTo);
        if (totalVoteUsersPredicate != null) {
            predicates.add(totalVoteUsersPredicate);
        }

        Predicate totalVotePowerPredicate =
                predicate.betweenTotalVotePower(
                        criteriaBuilder, root, totalVotePowerFrom, totalVotePowerTo);
        if (totalVotePowerPredicate != null) {
            predicates.add(totalVotePowerPredicate);
        }

        Predicate voteResultPredicate =
                predicate.equalVoteResult(criteriaBuilder, root, voteResult);
        if (voteResultPredicate != null) {
            predicates.add(voteResultPredicate);
        }

        Predicate electedUsersPredicate =
                predicate.betweenElectedUsers(
                        criteriaBuilder, root, voteUsersFrom, voteUsersTo, voteResult);
        if (electedUsersPredicate != null) {
            predicates.add(electedUsersPredicate);
        }
        return predicates;
    }

    public void updateChoiceActivityStatus() {
        DateRange dateRange = DateUnit.getCurrentDateRange();
        Date startDate = dateRange.getStartDate();
        Date endDate = dateRange.getEndDate();
        log.info(
                "startDate: {}, endDate: {}",
                DateFormatUtils.format(startDate, "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(endDate, "yyyy-MM-dd HH:mm:ss"));
        ChoiceActivity choiceActivity =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public ChoiceActivity query() {
                                List<Predicate> predicates = new ArrayList<>();
                                Predicate inactive =
                                        predicate.equalStatus(
                                                criteriaBuilder,
                                                root,
                                                ChoiceActivityResultStatus.INACTIVE);
                                Predicate voting =
                                        predicate.equalStatus(
                                                criteriaBuilder,
                                                root,
                                                ChoiceActivityResultStatus.VOTING);
                                predicates.add(criteriaBuilder.or(inactive, voting));
                                predicates.add(
                                        predicate.greaterThanOrEqualToCreatedAt(
                                                criteriaBuilder, root, startDate));
                                predicates.add(
                                        predicate.lessThanCreatedAt(
                                                criteriaBuilder, root, endDate));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });

        if (Objects.isNull(choiceActivity)) {
            return;
        }

        Date currentTime = new Date();
        log.info("currencyTime: {}", DateFormatUtils.format(currentTime, "yyyy-MM-dd HH:mm:ss"));
        log.info(
                "voteStartTime(): {}, voteEndTime(): {}",
                DateFormatUtils.format(choiceActivity.getVoteStartTime(), "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(choiceActivity.getVoteEndTime(), "yyyy-MM-dd HH:mm:ss"));
        log.info("choiceActivity.getStatus(): {}", choiceActivity.getStatus());
        if (ChoiceActivityResultStatus.INACTIVE.equals(choiceActivity.getStatus())
                && currentTime.after(choiceActivity.getVoteStartTime())
                && currentTime.before(choiceActivity.getVoteEndTime())) {
            choiceActivity.setStatus(ChoiceActivityResultStatus.VOTING);
            customTransactionManager.save(choiceActivity);
        } else if (ChoiceActivityResultStatus.VOTING.equals(choiceActivity.getStatus())
                && currentTime.after(choiceActivity.getVoteEndTime())) {
            choiceActivity.setStatus(ChoiceActivityResultStatus.COMPLETED);
            customTransactionManager.save(choiceActivity);
        }
    }

    public ChoiceActivity findChoiceActivityByToday() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceActivity query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalStatus(
                                        criteriaBuilder, root, ChoiceActivityResultStatus.VOTING));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public Optional<ChoiceActivity> findChoiceActivityWithoutIdOrderByIdDesc(Long activityId) {
        List<ChoiceActivity> choiceActivities =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<ChoiceActivity> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        Stream.of(
                                                        criteriaBuilder.notEqual(
                                                                root.get(ChoiceActivity_.id),
                                                                activityId))
                                                .toList(),
                                        0,
                                        1,
                                        criteriaBuilder.desc(root.get(ChoiceActivity_.id)));
                            }
                        });
        return Optional.ofNullable(
                CollectionUtils.isEmpty(choiceActivities) ? null : choiceActivities.get(0));
    }

    public ChoiceActivity findChoiceActivityByWorker(LocalDate date) {

        ZonedDateTime zonedDateTime = date.atStartOfDay(ZoneOffset.UTC);

        java.time.Instant instant = zonedDateTime.toInstant();

        Date endDate = Date.from(instant);
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceActivity query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalStatus(
                                        criteriaBuilder,
                                        root,
                                        ChoiceActivityResultStatus.COMPLETED));
                        // get yesterday activity
                        predicates.add(predicate.equalEndTime(criteriaBuilder, root, endDate));
                        predicates.add(
                                predicate.equalType(
                                        criteriaBuilder, root, ChoiceActivityType.BTC_PRICE));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public void updateBasePriceVotingDay(Date date, BigDecimal btcPrice) {
        List<ChoiceActivity> activities =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<ChoiceActivity> query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalVoteStartTime(criteriaBuilder, root, date));
                                return getResultList(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        for (ChoiceActivity activity : activities) {
            activity.setBasePriceVotingDay(btcPrice);
            save(activity);
        }
    }

    public void updateBasePriceElectionDay(Date date, BigDecimal btcPrice) {
        List<ChoiceActivity> activities =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<ChoiceActivity> query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalVoteStartTime(criteriaBuilder, root, date));
                                return getResultList(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        for (ChoiceActivity activity : activities) {
            activity.setBasePriceElectionDay(btcPrice);
            save(activity);
        }
    }

    public ChoiceActivity findLatestCompletedActivity() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceActivity query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalStatus(
                                        criteriaBuilder,
                                        root,
                                        ChoiceActivityResultStatus.COMPLETED));
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(ChoiceActivity_.voteEndTime)));
                    }
                });
    }
}
