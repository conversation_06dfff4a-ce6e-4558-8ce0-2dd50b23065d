package point.common.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.entity.User;
import point.common.model.response.UserLevelData;

@RequiredArgsConstructor
@Service
public class UserLevelService {

    public static UserLevelData set(User user) {
        UserLevelData uld = new UserLevelData();
        uld.setLevel(user.getLevel());
        return uld;
    }
}
