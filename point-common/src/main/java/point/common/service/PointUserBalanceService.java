package point.common.service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.constant.ErrorCode;
import point.common.entity.PointUser;
import point.common.exception.GameException;
import point.common.model.response.PointUserBalanceResponse;
import point.common.ponta.PontaBizInVokerApi;
import point.common.util.OptionalWithMessage;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointUserBalanceService {
    private static final String RESPONSE_CODE_KEY = "ST0274_tl";
    private static final String POINT_BALANCE_KEY = "NP0463_tl";
    private static final String SUCCESS_CODE_VALUE = "N000000000";
    private static final String ERROR_CODE_N058 = "N058";
    private static final String ERROR_CODE_G000 = "G000";

    private final PontaBizInVokerApi pontaBizInVokerApi;
    private final PointUserService pointUserService;

    public OptionalWithMessage<PointUserBalanceResponse> getPointBalance(
            Long userId, String CC0281_TL) {
        PointUser entity = pointUserService.findById(userId);
        if (Objects.isNull(entity)) {
            return OptionalWithMessage.empty(ErrorCode.GAME_USER_NOT_FOUND.getMessage());
        }

        Map<String, String> response;
        try {
            response = pontaBizInVokerApi.if001(entity.getPartnerMemberId(), CC0281_TL);
        } catch (GameException e) {
            log.error("Failed to get point user balance from ponta, error: {}", e.getMessage(), e);
            return OptionalWithMessage.empty(
                    ErrorCode.PONTA_GET_USER_POINT_BALANCE_FAILED.getMessage());
        }

        if (CollectionUtils.isEmpty(response)) {
            log.info(
                    "No response from ponta for userId: {}, memberId: {}",
                    userId,
                    entity.getPartnerMemberId());
            return OptionalWithMessage.empty(
                    ErrorCode.PONTA_GET_USER_POINT_BALANCE_FAILED.getMessage());
        }
        String responseCode = response.get(RESPONSE_CODE_KEY);
        if (SUCCESS_CODE_VALUE.equals(responseCode)) {
            String balance = response.get(POINT_BALANCE_KEY);
            try {
                PointUserBalanceResponse userBalanceResponse =
                        PointUserBalanceResponse.builder()
                                .userId(userId)
                                .memberId(entity.getPartnerMemberId())
                                .balance(new BigDecimal(balance))
                                .build();
                return OptionalWithMessage.of(userBalanceResponse);
            } catch (NumberFormatException e) {
                log.error(
                        "Failed to parse balance from ponta response, userId: {}, memberId: {}, balance: {}",
                        userId,
                        entity.getPartnerMemberId(),
                        balance,
                        e);
                return OptionalWithMessage.empty(
                        ErrorCode.PONTA_GET_USER_POINT_BALANCE_FAILED.getMessage());
            }
        } else if (responseCode.startsWith(ERROR_CODE_N058)
                || responseCode.startsWith(ERROR_CODE_G000)) {
            return OptionalWithMessage.empty(
                    ErrorCode.PONTA_RESPONSE_SUCCESS_CODE_N058_G000.getMessage());
        } else {
            return OptionalWithMessage.empty(
                    ErrorCode.PONTA_RESPONSE_ERROR_CODE.getMessage(
                            PontaBizInVokerApi.extractErrorPrefix(responseCode)));
        }
    }
}
