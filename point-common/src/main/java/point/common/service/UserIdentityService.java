package point.common.service;

import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.constant.UserIdType;
import point.common.entity.UserIdentity;
import point.common.predicate.UserIdentityPredicate;

@Service
@RequiredArgsConstructor
public class UserIdentityService extends EntityService<UserIdentity, UserIdentityPredicate> {

    @Override
    public Class<UserIdentity> getEntityClass() {
        return UserIdentity.class;
    }

    /**
     * create a new user identity
     *
     * @param idType the user id type
     * @param entityManager the entity manager
     * @return the userId
     */
    public UserIdentity create(UserIdType idType, EntityManager entityManager) throws Exception {
        UserIdentity entity = new UserIdentity();
        entity.setIdType(idType);
        return super.save(entity, entityManager);
    }
}
