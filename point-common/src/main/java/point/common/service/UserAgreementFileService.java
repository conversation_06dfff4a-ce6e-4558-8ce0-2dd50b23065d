package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.UserAgreementType;
import point.common.entity.UserAgreementFile;
import point.common.entity.UserAgreementFile_;
import point.common.predicate.UserAgreementFilePredicate;

@Service
public class UserAgreementFileService
        extends EntityService<UserAgreementFile, UserAgreementFilePredicate> {

    @Override
    public Class<UserAgreementFile> getEntityClass() {
        return UserAgreementFile.class;
    }

    public UserAgreementFile findByCondition(UserAgreementType userAgreementType, String version) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public UserAgreementFile query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (userAgreementType != null) {
                            predicates.add(
                                    predicate.equalUserAgreementType(
                                            criteriaBuilder, root, userAgreementType));
                        }
                        if (version != null) {
                            predicates.add(predicate.equalVersion(criteriaBuilder, root, version));
                        }
                        return getSingleResult(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(UserAgreementFile_.id)));
                    }
                });
    }

    public List<UserAgreementFile> findAll() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<UserAgreementFile> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(UserAgreementFile_.id)));
                    }
                });
    }

    public List<UserAgreementFile> findByAgreementType(UserAgreementType userAgreementType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<UserAgreementFile> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        if (userAgreementType != null) {
                            predicates.add(
                                    predicate.equalUserAgreementType(
                                            criteriaBuilder, root, userAgreementType));
                        }
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.asc(root.get(UserAgreementFile_.id)));
                    }
                });
    }

    public UserAgreementFile findMaxVersion(UserAgreementType userAgreementType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public UserAgreementFile query() {

                        List<Predicate> predicates = new ArrayList<>(1);
                        if (userAgreementType != null) {
                            predicates.add(
                                    predicate.equalUserAgreementType(
                                            criteriaBuilder, root, userAgreementType));
                        }

                        List<UserAgreementFile> resultList =
                                getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.desc(root.get(UserAgreementFile_.version)));
                        return resultList.isEmpty() ? null : resultList.get(0);
                    }
                });
    }
}
