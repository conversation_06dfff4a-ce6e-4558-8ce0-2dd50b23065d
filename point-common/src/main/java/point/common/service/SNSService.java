package point.common.service;

import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.config.SNSConfig;
import point.common.exception.HttpException;
import point.common.http.HttpManager;
import point.common.model.request.SNSRequestForm;
import point.common.model.response.SNSResponse;
import point.common.util.JsonUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class SNSService {

    private final SNSConfig snsConfig;

    private final HttpManager<SNSResponse> httpManager;

    public Optional<SNSResponse> postSenSNS(String message, String phoneNumber, String code)
            throws Exception {
        try {
            log.info("request SNS Link start");
            SNSRequestForm snsRequestForm =
                    SNSRequestForm.builder()
                            .to(phoneNumber)
                            .text(message)
                            .user_reference(code)
                            .build();
            log.info(
                    "request SNS Parameter: {}",
                    JsonUtil.encode(snsConfig.createEntityMap(snsRequestForm)));
            SNSResponse snsResponse =
                    httpManager.doPostJson(
                            snsConfig.getHost(),
                            snsConfig.createEntityMap(snsRequestForm),
                            snsConfig.createHeaderMap(),
                            null,
                            SNSResponse.class);
            log.info("response SNS Parameter: {}", JsonUtil.encode(snsResponse));
            return Optional.ofNullable(snsResponse);
        } catch (Exception e) {
            if (e instanceof HttpException) {
                log.error("SNS returns an exception: {}", ((HttpException) e).getDebugMessage());
            }
            return Optional.empty();
        }
    }
}
