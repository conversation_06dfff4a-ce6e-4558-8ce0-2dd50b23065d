package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.Exchange;
import point.common.constant.TradeType;
import point.common.entity.CoverOrderConfig;
import point.common.entity.CoverOrderConfig_;
import point.common.model.response.PageData;
import point.common.predicate.CoverOrderConfigPredicate;

@Service
public class CoverOrderConfigService
        extends EntityService<CoverOrderConfig, CoverOrderConfigPredicate> {

    @Override
    public Class<CoverOrderConfig> getEntityClass() {
        return CoverOrderConfig.class;
    }

    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<CoverOrderConfig> root,
            Long symbolId,
            Exchange exchange,
            Boolean enabled,
            TradeType type) {
        List<Predicate> predicates = new ArrayList<>();

        if (symbolId != null) {
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
        }

        if (exchange != null) {
            predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
        }

        if (enabled != null) {
            predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
        }

        if (type != null) {
            predicates.add(predicate.equalTradeType(criteriaBuilder, root, type));
        }

        return predicates;
    }

    public CoverOrderConfig findOne(
            Long symbolId, Exchange exchange, Boolean enabled, TradeType tradeType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<CoverOrderConfig, CoverOrderConfig>() {
                    @Override
                    public CoverOrderConfig query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                        predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
                        predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
                        predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<CoverOrderConfig> findAllByCondition(
            Long symbolId, Exchange exchange, Boolean enabled, TradeType type) {
        List<CoverOrderConfig> coverOrderConfigs =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<CoverOrderConfig, List<CoverOrderConfig>>() {

                            @Override
                            public List<CoverOrderConfig> query() {
                                List<Predicate> predicates =
                                        createPredicates(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                exchange,
                                                enabled,
                                                type);

                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(CoverOrderConfig_.id)));
                            }
                        });

        return coverOrderConfigs;
    }

    public PageData<CoverOrderConfig> findByConditionPageData(
            Long symbolId,
            Exchange exchange,
            Boolean enabled,
            Integer number,
            Integer size,
            TradeType type) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicates(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                exchange,
                                                enabled,
                                                type));
                            }
                        });
        return new PageData<CoverOrderConfig>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<CoverOrderConfig, List<CoverOrderConfig>>() {
                            @Override
                            public List<CoverOrderConfig> query() {
                                List<Predicate> predicates =
                                        createPredicates(
                                                criteriaBuilder,
                                                root,
                                                symbolId,
                                                exchange,
                                                enabled,
                                                type);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(CoverOrderConfig_.id)));
                            }
                        }));
    }
}
