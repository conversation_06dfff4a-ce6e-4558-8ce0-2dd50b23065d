package point.common.service;

import static point.common.entity.AbstractEntity_.createdAt;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.ChoiceGetType;
import point.common.constant.ChoiceObtainFrequency;
import point.common.constant.ChoicePowerTransferType;
import point.common.constant.UserIdType;
import point.common.entity.*;
import point.common.model.dto.MonsterDistributeDTO;
import point.common.model.response.PageData;
import point.common.predicate.ChoicePowerTransferPredicate;
import point.common.util.SqlUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoicePowerTransferService
        extends EntityService<ChoicePowerTransfer, ChoicePowerTransferPredicate> {

    private final ChoicePowerUserRelService choicePowerUserRelService;

    private final ChoicePowerService choicePowerService;

    private final PointUserService pointUserService;

    private final UserIdentityService userIdentityService;

    private final MonsterGrowthLevelService monsterGrowthLevelService;

    private final ChoicePowerSyncService choicePowerSyncService;

    private final UserMonsterInfoService userMonsterInfoService;

    @Override
    public Class<ChoicePowerTransfer> getEntityClass() {
        return ChoicePowerTransfer.class;
    }

    public PageData<ChoicePowerTransfer> findByCondition(
            Long userId,
            UserIdType idType,
            ChoicePowerTransferType transferType,
            Long choiceActivityRuleId,
            String partnerMemberId,
            Long dateFrom,
            Long dateTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                idType,
                                                transferType,
                                                choiceActivityRuleId,
                                                dateFrom,
                                                dateTo,
                                                partnerMemberId));
                            }
                        });
        return new PageData<ChoicePowerTransfer>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                ChoicePowerTransfer, List<ChoicePowerTransfer>>() {
                            @Override
                            public List<ChoicePowerTransfer> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                idType,
                                                transferType,
                                                choiceActivityRuleId,
                                                dateFrom,
                                                dateTo,
                                                partnerMemberId);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(
                                                root.get(ChoicePowerTransfer_.createdAt)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePowerTransfer> root,
            Long userId,
            UserIdType idType,
            ChoicePowerTransferType transferType,
            Long choiceActivityRuleId,
            Long dateFrom,
            Long dateTo,
            String partnerMemberId) {
        List<Predicate> predicates = new ArrayList<>();
        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }
        if (idType != null) {
            predicates.add(predicate.equalIdType(criteriaBuilder, root, idType));
        }

        if (transferType != null) {
            predicates.add(predicate.equalTransferType(criteriaBuilder, root, transferType));
        }

        if (choiceActivityRuleId != null) {
            predicates.add(
                    predicate.equalChoiceActivityRuleId(
                            criteriaBuilder, root, choiceActivityRuleId));
        }

        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(dateFrom)));
        }
        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
        }

        if (StringUtils.isNotBlank(partnerMemberId)) {
            predicates.add(predicate.likePartnerMemberId(criteriaBuilder, root, partnerMemberId));
        }
        return predicates;
    }

    public List<ChoicePowerTransfer> getChoicePowerTransferByUserId(List<Long> userIds) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<ChoicePowerTransfer> query() {
                        List<Predicate> predicates = new ArrayList<>(1);
                        predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                criteriaBuilder.desc(root.get(createdAt)));
                    }
                });
    }

    public void setChoicePowerTransferByRule() throws Exception {

        List<ChoicePowerSync> choicePowerSyncList = choicePowerSyncService.findByCondition(null);
        for (ChoicePowerSync choicePowerSync : choicePowerSyncList) {
            ChoicePowerTransfer choicePowerTransfer = new ChoicePowerTransfer();
            choicePowerTransfer.setUserId(choicePowerSync.getUserId());
            choicePowerTransfer.setAmount(choicePowerSync.getPowerAmount());
            choicePowerTransfer.setTransferType(ChoicePowerTransferType.IN);
            choicePowerTransfer.setChoiceActivityRuleId(choicePowerSync.getChoiceActivityRuleId());
            choicePowerTransfer.setDescription("transfer by ChoicePowerSyncWorker is day");
            customTransactionManager.execute(
                    entityManager -> {
                        save(choicePowerTransfer, entityManager);
                        choicePowerSync.setSynchronizeFlag(1);
                        choicePowerSyncService.save(choicePowerSync);
                    });
        }
        Set<Long> userIds =
                choicePowerSyncList.stream()
                        .map(ChoicePowerSync::getUserId)
                        .collect(Collectors.toSet());
        for (Long userId : userIds) {
            customTransactionManager.execute(
                    entityManager -> {
                        updateOrCreateChoicePower(userId, entityManager);
                    });
        }
    }

    /**
     * distribute monster power
     *
     * @throws Exception
     */
    public void distributeMonsterPower() throws Exception {
        customTransactionManager.execute(
                entityManager -> {
                    BigDecimal count = BigDecimal.valueOf(userMonsterInfoService.countAll());
                    if (BigDecimal.ZERO.compareTo(count) == 0) {
                        log.info("there is no user monster info to distribute power.");
                        return;
                    }
                    log.info("distribute monsters: " + count.longValue());
                    BigDecimal spread = BigDecimal.valueOf(5000L);
                    long pageCount = count.divide(spread, 0, RoundingMode.UP).longValue();

                    String baseSql = "findMonsterDistributeInfo";
                    LongStream.rangeClosed(1L, pageCount)
                            .mapToObj(
                                    l -> {
                                        long start = l == 1 ? 0 : (l - 1) * spread.longValue();
                                        var query = entityManager.createNamedQuery(baseSql);
                                        query.setParameter("limitSize", spread.longValue());
                                        query.setParameter("offsetValue", start);
                                        List<MonsterDistributeDTO> dtoList = query.getResultList();
                                        log.info(
                                                "monster group info limitSize: "
                                                        + spread.longValue()
                                                        + ", offsetValue:"
                                                        + start);
                                        return dtoList.stream()
                                                .map(this::buildSyncForMonster)
                                                .filter(Objects::nonNull)
                                                .toList();
                                    })
                            .forEach(
                                    syncl ->
                                            SqlUtil.batchInsert(
                                                    ChoicePowerSync.class, syncl, entityManager));
                });
    }

    private ChoicePowerSync buildSyncForMonster(MonsterDistributeDTO dto) {
        if (Objects.isNull(dto.getMonthlyPower()) || dto.getMonthlyPower() == 0) {
            log.info("invalid monster power info, id:" + dto.getMonsterId());
            return null;
        }
        return ChoicePowerSync.builder()
                .userId(dto.getUserId())
                .choiceActivityRuleId(dto.getRuleId())
                .activityFunction(ChoiceActivityFunction.FARM)
                .obtainFrequency(ChoiceObtainFrequency.MONTHLY)
                .getType(ChoiceGetType.FIXED)
                .powerAmount(dto.getMonthlyPower())
                .synchronizeFlag(0)
                .build();
    }

    public void setChoicePowerTransferByRuleForMonthly() throws Exception {

        try {
            this.distributeMonsterPower();
        } catch (Exception e) {
            log.info("Distribute monster power failed.", e);
        }

        List<ChoicePowerSync> choicePowerSyncMonthlyList =
                choicePowerSyncService.findByCondition(ChoiceObtainFrequency.MONTHLY);
        if (!choicePowerSyncMonthlyList.isEmpty()) {
            Map<String, Long> amountSumMap = new HashMap<>();
            for (ChoicePowerSync monthlySync : choicePowerSyncMonthlyList) {
                String key = monthlySync.getUserId() + "_" + monthlySync.getChoiceActivityRuleId();
                amountSumMap.put(
                        key, amountSumMap.getOrDefault(key, 0L) + monthlySync.getPowerAmount());
            }

            for (Map.Entry<String, Long> entry : amountSumMap.entrySet()) {
                customTransactionManager.execute(
                        em -> {
                            String[] keys = entry.getKey().split("_");
                            Long userIdMonthly = Long.parseLong(keys[0]);
                            Long choiceActivityRuleIdMonthly = Long.parseLong(keys[1]);
                            Long totalAmount = entry.getValue();
                            // -- comment out by jingshui.xiao
                            // https://backseat-inc.atlassian.net/browse/OFLM-1941
                            // cal calorie
                            //                            totalAmount =
                            // calCalorieByIncomeTotal(totalAmount);
                            // cal power by calorie
                            //                            totalAmount =
                            // calPowerByCalorie(totalAmount);
                            // -- comment out by jingshui.xiao
                            // https://backseat-inc.atlassian.net/browse/OFLM-1941

                            ChoicePowerTransfer choicePowerTransfer = new ChoicePowerTransfer();
                            choicePowerTransfer.setUserId(userIdMonthly);
                            choicePowerTransfer.setAmount(totalAmount);
                            choicePowerTransfer.setTransferType(ChoicePowerTransferType.IN);
                            choicePowerTransfer.setChoiceActivityRuleId(
                                    choiceActivityRuleIdMonthly);
                            choicePowerTransfer.setDescription(
                                    "transfer by ChoicePowerSyncWorker is MONTHLY");
                            this.save(choicePowerTransfer, em);
                        });
            }
            for (ChoicePowerSync choicePowerSync : choicePowerSyncMonthlyList) {
                customTransactionManager.execute(
                        em -> {
                            choicePowerSync.setSynchronizeFlag(1);
                            choicePowerSyncService.save(choicePowerSync, em);
                        });
            }

            Set<Long> userIds =
                    choicePowerSyncMonthlyList.stream()
                            .map(ChoicePowerSync::getUserId)
                            .collect(Collectors.toSet());

            customTransactionManager.execute(
                    em -> {
                        for (Long userId : userIds) {
                            updateOrCreateChoicePower(userId, em);
                        }
                    });
        }
    }

    private Long calPowerByCalorie(Long totalAmount) {

        List<MonsterGrowthLevel> monsterGrowthLevelList = monsterGrowthLevelService.findAll();
        Long votingTicket = 0L;
        for (int i = 0; i < monsterGrowthLevelList.size(); i++) {
            MonsterGrowthLevel level = monsterGrowthLevelList.get(i);
            if (totalAmount < level.getRequiredExperience()) {
                if (i == 0) {
                    votingTicket = level.getRule().getPowerAmount();
                } else {
                    votingTicket = monsterGrowthLevelList.get(i - 1).getRule().getPowerAmount();
                }
                break;
            }
            if (i == monsterGrowthLevelList.size() - 1) {
                votingTicket = level.getRule().getPowerAmount();
            }
        }
        return votingTicket;
    }

    private Long calCalorieByIncomeTotal(Long totalAmount) {
        if (totalAmount <= 4) {
            totalAmount = 0L;
        } else if (totalAmount >= 5 && totalAmount <= 50) {
            totalAmount = (long) (totalAmount * 0.10);
        } else if (totalAmount >= 51 && totalAmount <= 1000) {
            totalAmount = (long) (totalAmount * 0.20);
        } else if (totalAmount >= 1001 && totalAmount <= 10000) {
            totalAmount = (long) (totalAmount * 0.25);
        } else if (totalAmount >= 10001 && totalAmount <= 50000) {
            totalAmount = (long) (totalAmount * 0.30);
        } else if (totalAmount >= 50001 && totalAmount <= 100000) {
            totalAmount = (long) (totalAmount * 0.40);
        } else if (totalAmount > 100000) {
            totalAmount = (long) (totalAmount * 0.50);
        }
        return totalAmount;
    }

    private void insertIntoPowerTransfer(
            Long userId,
            Long amount,
            ChoicePowerTransferType transferType,
            Long choiceActivityRuleId,
            String description) {
        String insertSql =
                "INSERT INTO choice_power_transfer (user_id, amount, transfer_type, choice_activity_rule_id, description, created_at) "
                        + "VALUES (:userId, :amount, :transferType, :choiceActivityRuleId, :description, :createdAt)";
        MapSqlParameterSource insertParams = new MapSqlParameterSource();
        insertParams.addValue("userId", userId);
        insertParams.addValue("amount", amount);
        insertParams.addValue("transferType", transferType.name());
        insertParams.addValue("choiceActivityRuleId", choiceActivityRuleId);
        insertParams.addValue("description", description);
        insertParams.addValue("createdAt", new Date());
        customTransactionManager.multiUpdate(insertSql, insertParams);
    }

    private void updateSyncFlag(Long id) {
        String updateSql = "UPDATE choice_power_sync SET synchronize_flag = 1 WHERE id = :id";
        MapSqlParameterSource updateParams = new MapSqlParameterSource();
        updateParams.addValue("id", id);
        customTransactionManager.multiUpdate(updateSql, updateParams);
    }

    /** processUserChoicePower */
    private Long processUserChoicePower(Long userId, EntityManager entityManager) throws Exception {
        String querySql =
                "SELECT transfer_type, SUM(amount) as total_amount FROM choice_power_transfer WHERE user_id = "
                        + userId
                        + " GROUP BY transfer_type";
        List<Object[]> results =
                customTransactionManager.execute(
                        em -> {
                            return em.createNativeQuery(querySql).getResultList();
                        });

        return calculateNetAmount(results);
    }

    /** calculateNetAmount */
    private long calculateNetAmount(List<Object[]> results) {
        long totalIn = 0;
        long totalOut = 0;
        for (Object[] result : results) {
            String transferType = (String) result[0];
            long amount = ((Number) result[1]).longValue();
            if (ChoicePowerTransferType.IN.name().equals(transferType)) {
                totalIn += amount;
            } else if (ChoicePowerTransferType.OUT.name().equals(transferType)) {
                totalOut += amount;
            }
        }
        return totalIn - totalOut;
    }

    public void updateOrCreateChoicePower(Long userId, EntityManager entityManager)
            throws Exception {
        UserIdentity userIdentity = userIdentityService.findOne(userId);
        if (UserIdType.Invest.equals(userIdentity.getIdType())) {
            PointUser pointUser = pointUserService.findByUserId(userId);
            handleTwoUsers(
                    userId,
                    Objects.nonNull(pointUser) ? pointUser.getId() : null,
                    userIdentity.getIdType(),
                    entityManager);
        } else {
            PointUser userOperate = pointUserService.findOne(userId);
            if (userOperate != null) {
                handleTwoUsers(
                        userOperate.getUserId(), userId, userIdentity.getIdType(), entityManager);
            }
        }
    }

    private void handleTwoUsers(
            Long investUserId,
            Long operateUserId,
            UserIdType userIdType,
            EntityManager entityManager)
            throws Exception {
        ChoicePowerUserRel investChoicePowerUserRel =
                choicePowerUserRelService.getChoicePowerUserRel(
                        investUserId == null ? operateUserId : investUserId);
        ChoicePowerUserRel operateChoicePowerUserRel =
                choicePowerUserRelService.getChoicePowerUserRel(operateUserId);
        Long investPowerAmount =
                investUserId != null
                        ? processUserChoicePower(investUserId, entityManager)
                        : NumberUtils.LONG_ZERO;
        Long operatePowerAmount =
                operateUserId != null
                        ? processUserChoicePower(operateUserId, entityManager)
                        : NumberUtils.LONG_ZERO;
        long totalPowerAmount = investPowerAmount + operatePowerAmount;
        Long choicePowerId =
                UserIdType.Invest.equals(userIdType)
                        ? investChoicePowerUserRel.getChoicePowerId()
                        : operateChoicePowerUserRel.getChoicePowerId();
        log.info("choicePowerId: {}", choicePowerId);
        updateChoicePower(choicePowerId, totalPowerAmount);
    }

    private void updateChoicePower(Long choicePowerId, Long totalPowerAmount) throws Exception {
        customTransactionManager.execute(
                em -> {
                    ChoicePower choicePower = choicePowerService.findOne(choicePowerId);
                    choicePower.setAmount(totalPowerAmount);
                    choicePowerService.save(choicePower, em);
                });
    }

    public ChoicePowerTransfer findChoicePowerTransferByUserToday(List<Long> userIds) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoicePowerTransfer, ChoicePowerTransfer>() {
                    @Override
                    public ChoicePowerTransfer query() {
                        List<Predicate> predicates = new ArrayList<>(4);
                        predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
                        predicates.add(
                                predicate.equalTransferType(
                                        criteriaBuilder, root, ChoicePowerTransferType.OUT));
                        predicates.add(predicate.equalDescription(criteriaBuilder, root, "vote"));
                        predicates.add(
                                predicate.equalCreatedAtYYYYMMDD(
                                        criteriaBuilder, root, new Date()));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
