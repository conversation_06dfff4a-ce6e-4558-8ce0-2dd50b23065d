package point.common.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.entity.MonsterGrowthRule;
import point.common.predicate.MonsterGrowthRulePredicate;

@RequiredArgsConstructor
@Service
public class MonsterGrowthRuleService
        extends EntityService<MonsterGrowthRule, MonsterGrowthRulePredicate> {

    @Override
    public Class<MonsterGrowthRule> getEntityClass() {
        return MonsterGrowthRule.class;
    }
}
