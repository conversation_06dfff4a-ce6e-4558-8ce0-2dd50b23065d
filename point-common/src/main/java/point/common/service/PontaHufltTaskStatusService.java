package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.entity.PontaHulftTaskStatus;
import point.common.predicate.PontaHufltTaskStatusPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PontaHufltTaskStatusService
        extends EntityService<PontaHulftTaskStatus, PontaHufltTaskStatusPredicate> {

    @Override
    public Class<PontaHulftTaskStatus> getEntityClass() {
        return PontaHulftTaskStatus.class;
    }

    public PontaHulftTaskStatus findByCondition() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PontaHulftTaskStatus query() {
                        List<Predicate> predicates = new ArrayList<>();
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public PontaHulftTaskStatus findByCondition(PontaHulftTaskStatus.ParsingStatus parsingStatus) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public PontaHulftTaskStatus query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalsStatus(criteriaBuilder, root, parsingStatus));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
