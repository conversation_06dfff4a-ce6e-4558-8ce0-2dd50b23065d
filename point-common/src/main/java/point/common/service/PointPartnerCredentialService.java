package point.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.entity.PointPartnerCredential;
import point.common.predicate.PointPartnerCredentialPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointPartnerCredentialService
        extends EntityService<PointPartnerCredential, PointPartnerCredentialPredicate> {
    @Override
    public Class<PointPartnerCredential> getEntityClass() {
        return PointPartnerCredential.class;
    }
}
