package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.entity.YearlyReportCreateInfo;
import point.common.predicate.YearlyReportCreateInfoPredicate;

@RequiredArgsConstructor
@Service
@Slf4j
public class YearlyReportCreateInfoService
        extends EntityService<YearlyReportCreateInfo, YearlyReportCreateInfoPredicate> {

    @Override
    public Class<YearlyReportCreateInfo> getEntityClass() {
        return YearlyReportCreateInfo.class;
    }

    @Override
    protected void fetch(Root<YearlyReportCreateInfo> root) {
        super.fetch(root);
    }

    public List<YearlyReportCreateInfo> findByCondition(Integer year, String status, Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<YearlyReportCreateInfo, List<YearlyReportCreateInfo>>() {
                    @Override
                    public List<YearlyReportCreateInfo> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalYear(criteriaBuilder, root, year));
                        if (status != null) {
                            predicates.add(predicate.equalStatus(criteriaBuilder, root, status));
                        }
                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
