package point.common.service;

import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.constant.CommonConstants;
import point.common.constant.KycStatus;
import point.common.entity.UserEkycStatusChangeHistory;
import point.common.repos.UserEkycStatusChangeHistoryRepository;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class UserEkycStatusChangeHistoryService {

    private final UserEkycStatusChangeHistoryRepository repository;

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void create(
            Long userId,
            String url,
            Long applicantId,
            KycStatus beforeStatus,
            KycStatus afterStatus,
            String reason) {
        UserEkycStatusChangeHistory entity =
                UserEkycStatusChangeHistory.builder()
                        .url(url)
                        .applicantId(applicantId)
                        .userId(userId)
                        .beforeStatus(beforeStatus)
                        .afterStatus(afterStatus)
                        .reason(reason)
                        .build();
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        repository.save(entity);
    }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void createAntiSocialHistory(
            Long userId, KycStatus beforeStatus, KycStatus afterStatus, String reason) {
        UserEkycStatusChangeHistory entity =
                UserEkycStatusChangeHistory.builder()
                        .userId(userId)
                        .beforeStatus(beforeStatus)
                        .afterStatus(afterStatus)
                        .reason(reason)
                        .build();
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        repository.save(entity);
    }

    @Transactional(transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public List<UserEkycStatusChangeHistory> findByUserEkycStatusChangeHistory(
            Long userId, Long applicantId, KycStatus kycStatus) {
        return repository.findByUserIdAndApplicantIdAndAfterStatus(userId, applicantId, kycStatus);
    }
}
