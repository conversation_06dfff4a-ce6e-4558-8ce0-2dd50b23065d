package point.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import point.common.component.CustomLogger;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.component.QueryExecutorSum;
import point.common.component.RedisManager;
import point.common.constant.Currency;
import point.common.constant.ErrorCode;
import point.common.constant.FiatDepositStatus;
import point.common.entity.FiatDeposit;
import point.common.entity.FiatDeposit_;
import point.common.entity.GmoDeposit;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.FiatDepositPredicate;
import point.common.repos.GmoServiceRepository;

@RequiredArgsConstructor
@Service
@Slf4j
public class FiatDepositService extends EntityService<FiatDeposit, FiatDepositPredicate> {

    private static final CustomLogger logSevere =
            new CustomLogger(FiatDepositService.class.getName());

    private final AssetService assetService;

    private final GmoServiceRepository gmoServiceRepository;

    @Override
    public Class<FiatDeposit> getEntityClass() {
        return FiatDeposit.class;
    }

    @Override
    protected void fetch(Root<FiatDeposit> root) {
        super.fetch(root);
        root.fetch(FiatDeposit_.user, JoinType.LEFT);
    }

    @Autowired private RedisManager redisManager;

    private static String getLockKey(Long userId, Currency currency) {
        return "lock:asset:" + userId + ":" + currency;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<FiatDeposit> root,
            Long userId,
            FiatDepositStatus depositStatus,
            Long createdAtFrom,
            Long createdAtTo,
            Long updatedAtFrom,
            Long updatedAtTo) {
        List<Predicate> predicates = new ArrayList<>();

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (depositStatus != null) {
            predicates.add(predicate.equalDepositStatus(criteriaBuilder, root, depositStatus));
        }

        if (createdAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(
                            criteriaBuilder, root, new Date(createdAtFrom)));
        }

        if (createdAtTo != null) {
            predicates.add(
                    predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(createdAtTo)));
        }

        if (updatedAtFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToUpdatedAt(
                            criteriaBuilder, root, new Date(updatedAtFrom)));
        }

        if (updatedAtTo != null) {
            predicates.add(
                    predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(updatedAtTo)));
        }

        return predicates;
    }

    public PageData<FiatDeposit> findByConditionPageData(
            Long userId,
            FiatDepositStatus fiatDepositStatus,
            Long dateFrom,
            Long dateTo,
            Long updatedAtFrom,
            Long updatedAtTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                fiatDepositStatus,
                                                dateFrom,
                                                dateTo,
                                                updatedAtFrom,
                                                updatedAtTo));
                            }
                        });
        PageData<FiatDeposit> pageData =
                new PageData<FiatDeposit>(
                        number,
                        size,
                        count,
                        customTransactionManager.find(
                                getEntityClass(),
                                new QueryExecutorReturner<FiatDeposit, List<FiatDeposit>>() {
                                    @Override
                                    public List<FiatDeposit> query() {
                                        List<Predicate> predicates =
                                                getPredicatesOfFindByCondition(
                                                        criteriaBuilder,
                                                        root,
                                                        userId,
                                                        fiatDepositStatus,
                                                        dateFrom,
                                                        dateTo,
                                                        updatedAtFrom,
                                                        updatedAtTo);
                                        return getResultList(
                                                entityManager,
                                                criteriaQuery,
                                                root,
                                                predicates,
                                                number,
                                                size,
                                                criteriaBuilder.desc(root.get(FiatDeposit_.id)));
                                    }
                                }));
        for (FiatDeposit fiatDeposit : pageData.getContent()) {
            GmoDeposit gmoDeposit = gmoServiceRepository.findByFiatDepositId(fiatDeposit.getId());
            if (!ObjectUtils.isEmpty(gmoDeposit)) {
                fiatDeposit.setRemake(gmoDeposit.getRemarks());
            }
        }
        return pageData;
    }

    public List<FiatDeposit> findAllByCondition(
            Long userId,
            Long createdAtFrom,
            Long createdAtTo,
            Long updatedAtFrom,
            Long updatedAtTo,
            FiatDepositStatus depositStatus) {
        List<FiatDeposit> dep =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<FiatDeposit, List<FiatDeposit>>() {
                            @Override
                            public List<FiatDeposit> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                depositStatus,
                                                createdAtFrom,
                                                createdAtTo,
                                                updatedAtFrom,
                                                updatedAtTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.desc(root.get(FiatDeposit_.id)));
                            }
                        });
        return dep;
    }

    public List<FiatDeposit> findByCondition(
            Long userId,
            Long dateFrom,
            Long dateTo,
            Long updateFrom,
            Long updateTo,
            List<FiatDepositStatus> fiatDepositStatusList,
            Integer number,
            Integer size) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<FiatDeposit, List<FiatDeposit>>() {
                    @Override
                    public List<FiatDeposit> query() {
                        List<Predicate> predicates = new ArrayList<>();

                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }

                        if (dateFrom != null) {
                            predicates.add(
                                    predicate.greaterThanOrEqualToCreatedAt(
                                            criteriaBuilder, root, new Date(dateFrom)));
                        }

                        if (dateTo != null) {
                            predicates.add(
                                    predicate.lessThanCreatedAt(
                                            criteriaBuilder, root, new Date(dateTo)));
                        }

                        if (updateFrom != null) {
                            predicates.add(
                                    predicate.greaterThanOrEqualToUpdatedAt(
                                            criteriaBuilder, root, new Date(updateFrom)));
                        }

                        if (updateTo != null) {
                            predicates.add(
                                    predicate.lessThanUpdatedAt(
                                            criteriaBuilder, root, new Date(updateTo)));
                        }

                        if (!fiatDepositStatusList.isEmpty()) {
                            predicates.add(
                                    predicate.inFiatDepositStatus(
                                            criteriaBuilder, root, fiatDepositStatusList));
                        }
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                number,
                                size,
                                criteriaBuilder.desc(root.get(FiatDeposit_.id)));
                    }
                });
    }

    private Object[] sumExchange(
            EntityManager entityManager,
            CriteriaBuilder criteriaBuilder,
            CriteriaQuery<Object[]> criteriaQuery,
            Root<FiatDeposit> root,
            List<Predicate> predicates) {

        criteriaQuery.multiselect(
                criteriaBuilder.sum(root.get(FiatDeposit_.amount)),
                criteriaBuilder.sum(root.get(FiatDeposit_.fee)));

        if (!CollectionUtils.isEmpty(predicates)) {
            criteriaQuery.where(predicates.toArray(new Predicate[] {}));
        }

        try {
            return entityManager
                    .createQuery(criteriaQuery)
                    .setFirstResult(0)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    // 取引所サマリ
    public Object[] sumExchangeByCondition(
            FiatDepositStatus depositStatus, Long updatedAtFrom, Long updatedAtTo) {
        return customTransactionManager.sum(
                getEntityClass(),
                new QueryExecutorSum<FiatDeposit>() {
                    @Override
                    public Object[] query() {

                        List<Predicate> predicates =
                                getPredicatesOfFindByCondition(
                                        criteriaBuilder,
                                        root,
                                        null,
                                        depositStatus,
                                        null,
                                        null,
                                        updatedAtFrom,
                                        updatedAtTo);

                        return sumExchange(
                                entityManager, criteriaBuilder, criteriaQuery, root, predicates);
                    }
                });
    }

    public FiatDeposit found(
            Long userId, Long bankAccountId, BigDecimal amount, BigDecimal fee, String comment) {
        FiatDeposit fiatDeposit = new FiatDeposit();
        fiatDeposit.setUserId(userId);
        fiatDeposit.setBankAccountId(bankAccountId);
        fiatDeposit.setAmount(amount);
        fiatDeposit.setFee(fee);
        fiatDeposit.setComment(comment);
        return save(fiatDeposit);
    }

    public void apply(Long id) throws Exception {
        FiatDeposit fiatDeposit = findOne(id);

        if (fiatDeposit == null) {
            return;
        }

        customTransactionManager.execute(
                entityManager -> {
                    try {
                        fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);
                        save(fiatDeposit, entityManager);
                        assetService.update(
                                fiatDeposit.getUserId(),
                                Currency.JPY,
                                fiatDeposit.getAmount(),
                                BigDecimal.ZERO,
                                entityManager);
                    } catch (Exception e) {
                        logSevere.severe(getClass().getName(), e);
                    }
                });
    }

    public void applyWithComment(Long id, String comment) throws Exception {
        FiatDeposit fiatDeposit = findOne(id);
        if (fiatDeposit == null) {
            return;
        }
        if (!redisManager.executeWithLock(
                getLockKey(fiatDeposit.getUserId(), Currency.JPY),
                RedisManager.LockParams.LOCKKEY,
                () -> {
                    //    	return (FiatDeposit)
                    customTransactionManager.execute(
                            entityManager -> {
                                try {
                                    fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);
                                    fiatDeposit.setComment(comment);
                                    save(fiatDeposit, entityManager);
                                    assetService.updateWithExternalLock(
                                            fiatDeposit.getUserId(),
                                            Currency.JPY,
                                            fiatDeposit.getAmount(),
                                            BigDecimal.ZERO,
                                            entityManager);
                                } catch (Exception e) {
                                    logSevere.severe(getClass().getName(), e);
                                }
                                return fiatDeposit;
                            });
                })) {
            log.error(
                    getClass().getSimpleName()
                            + ",could not get lock. key: "
                            + getLockKey(fiatDeposit.getUserId(), Currency.JPY));
            throw new CustomException(
                    ErrorCode.LOCK_KEY, "AssetUpdate userId: " + fiatDeposit.getUserId());
        }
        ;
    }

    public void update(Long id, FiatDepositStatus status, String comment) throws Exception {
        FiatDeposit fiatDeposit = findOne(id);
        if (fiatDeposit == null) {
            return;
        }
        fiatDeposit.setFiatDepositStatus(status);
        fiatDeposit.setComment(comment);
        save(fiatDeposit);
    }
}
