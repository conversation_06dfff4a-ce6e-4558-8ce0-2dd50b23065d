package point.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.entity.UserCropStatusHistory;
import point.common.predicate.UserCropStatusHistoryPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCropStatusHistoryService
        extends EntityService<UserCropStatusHistory, UserCropStatusHistoryPredicate> {

    @Override
    public Class<UserCropStatusHistory> getEntityClass() {
        return UserCropStatusHistory.class;
    }
}
