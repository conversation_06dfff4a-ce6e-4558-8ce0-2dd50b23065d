package point.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ErrorCode;
import point.common.constant.ReportLabel;
import point.common.constant.TradeType;
import point.common.entity.CurrencyPairConfig;
import point.common.entity.FinancialAssetsDeviationUser;
import point.common.entity.FinancialAssetsDeviationUser_;
import point.common.entity.Symbol;
import point.common.entity.SystemConfig;
import point.common.entity.User;
import point.common.entity.UserInfo;
import point.common.exception.CustomException;
import point.common.model.response.PageData;
import point.common.predicate.FinancialAssetsDeviationUserPredicate;
import point.common.util.DateUnit;
import point.common.util.JsonUtil;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@Service
public class FinancialAssetsDeviationUserService
        extends EntityService<FinancialAssetsDeviationUser, FinancialAssetsDeviationUserPredicate> {

    private final CurrencyPairConfigService currencyPairConfigService;
    private final SymbolService symbolService;
    private final SystemConfigService systemConfigService;
    private final UserInfoService userInfoService;
    private final UserService userService;
    private final PosTradeService posTradeService;

    private String dbtradeType;

    private int CHECK_SPAN_HOURS = 0;
    private int TRADE_ASSET_AMOUNT = 0;
    private int TRADES_THRESHOLD = 0;
    private int FINANCIAL_ASSETS = 0;

    private final int MAX_CHECK_SPAN_HOURS = 40;

    @Override
    public Class<FinancialAssetsDeviationUser> getEntityClass() {
        return FinancialAssetsDeviationUser.class;
    }

    /*
     * 【運用】predicates作成メソッドは共通化して使用する
     * インデックス順でaddする
     */
    protected List<Predicate> createPredicates(
            CriteriaBuilder criteriaBuilder,
            Root<FinancialAssetsDeviationUser> root,
            Long userId,
            Date dateFrom,
            Date dateTo) {
        List<Predicate> predicates = new ArrayList<>();

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (dateFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, dateFrom));
        }

        if (dateTo != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, dateTo));
        }

        return predicates;
    }

    public PageData<FinancialAssetsDeviationUser> findByCondition(
            Long userId, Date dateFrom, Date dateTo, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        createPredicates(
                                                criteriaBuilder, root, userId, dateFrom, dateTo));
                            }
                        });

        return new PageData<FinancialAssetsDeviationUser>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                FinancialAssetsDeviationUser,
                                List<FinancialAssetsDeviationUser>>() {
                            @Override
                            public List<FinancialAssetsDeviationUser> query() {
                                List<Predicate> predicates =
                                        createPredicates(
                                                criteriaBuilder, root, userId, dateFrom, dateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(
                                                root.get(FinancialAssetsDeviationUser_.id)));
                            }
                        }));
    }

    public List<FinancialAssetsDeviationUser> findByCondition(
            Long userId, Date dateFrom, Date dateTo, boolean isAscending) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<
                        FinancialAssetsDeviationUser, List<FinancialAssetsDeviationUser>>() {
                    @Override
                    public List<FinancialAssetsDeviationUser> query() {
                        List<Predicate> predicates =
                                createPredicates(criteriaBuilder, root, userId, dateFrom, dateTo);
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                isAscending
                                        ? criteriaBuilder.asc(
                                                root.get(FinancialAssetsDeviationUser_.id))
                                        : criteriaBuilder.desc(
                                                root.get(FinancialAssetsDeviationUser_.id)));
                    }
                });
    }

    private void save(long userId, int overTrades, String dbtradeType, EntityManager entityManager)
            throws Exception {
        FinancialAssetsDeviationUser financialAssetsDeviationUser =
                new FinancialAssetsDeviationUser();
        financialAssetsDeviationUser.setUserId(userId);
        financialAssetsDeviationUser.setOverTrades(overTrades);
        financialAssetsDeviationUser.setTradeType(dbtradeType);
        save(financialAssetsDeviationUser, entityManager);
    }

    private void getThreshholds() throws Exception {
        SystemConfig checkSpanHoursConfig =
                systemConfigService.findByCondition(
                        null, null, "FINANCIAL_ASSETS_DEVIATION_CHECK_SPAN_HOURS");
        SystemConfig tradeAssetAmountConfig =
                systemConfigService.findByCondition(
                        null, null, "FINANCIAL_ASSETS_DEVIATION_TRADE_ASSET_AMOUNT");
        SystemConfig tradesThreshholdConfig =
                systemConfigService.findByCondition(
                        null, null, "FINANCIAL_ASSETS_DEVIATION_TRADES_THRESHOLD");
        SystemConfig financialAssetsConfig =
                systemConfigService.findByCondition(
                        null, null, "FINANCIAL_ASSETS_DEVIATION_USER_INFO");

        if (checkSpanHoursConfig == null
                || tradeAssetAmountConfig == null
                || tradesThreshholdConfig == null
                || financialAssetsConfig == null) {
            log.error("FinancialAssetsDeviationCheckerLog,system config not found");
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        // 検知対象の約定履歴の対象期間
        CHECK_SPAN_HOURS = Integer.parseInt(checkSpanHoursConfig.getValue());
        // 1約定の約定総額の円貨閾値(円)
        TRADE_ASSET_AMOUNT = Integer.parseInt(tradeAssetAmountConfig.getValue());
        // 約定回数の閾値
        TRADES_THRESHOLD = Integer.parseInt(tradesThreshholdConfig.getValue());
        // 金融資産のcode
        FINANCIAL_ASSETS = Integer.parseInt(financialAssetsConfig.getValue());

        // 300万未満の場合,  code < 3
        //     INCOME1(1, "100 万円未満"),
        //    INCOME2(2, "100 万円 ~ 300 万円"),
        // INCOME3(3, "300 万円 ~ 500 万円"),
        String financialAssetsKanji =
                ReportLabel.Income.valueOfCode(Integer.parseInt(financialAssetsConfig.getValue()))
                        .getKanjiName();

        log.info(
                "FinancialAssetsDeviationCheckerLog,CHECK_SPAN_HOURS,"
                        + CHECK_SPAN_HOURS
                        + ",TRADE_ASSET_AMOUNT,"
                        + TRADE_ASSET_AMOUNT
                        + ",TRADES_THRESHOLD,"
                        + TRADES_THRESHOLD
                        + ",FINANCIAL_ASSETS,"
                        + FINANCIAL_ASSETS
                        + ",financialAssetsKanji,"
                        + financialAssetsKanji);

        // 取引履歴アーカイブを対象外とするため、MAX2日弱
        if (CHECK_SPAN_HOURS > MAX_CHECK_SPAN_HOURS) {
            log.error("FinancialAssetsDeviationCheckerLog, CHECK_SPAN_HOURS should be shorter");
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }
    }

    // ユーザーID,約定件数のMapに追加(同一keyなら合算)
    private void addUserTradesMapFromObject(
            Map<Long, Integer> userTradesMap, List<Object[]> newUserTradeCountList) {
        for (Object[] newUserTradeCount : newUserTradeCountList) {
            if (userTradesMap.containsKey((Long) newUserTradeCount[0])) {
                // 同一ユーザーIDの場合は合算して置き換え
                userTradesMap.replace(
                        (Long) newUserTradeCount[0],
                        userTradesMap.get((Long) newUserTradeCount[0])
                                + Integer.parseInt(newUserTradeCount[1].toString()));
            } else {
                userTradesMap.put(
                        (Long) newUserTradeCount[0],
                        Integer.parseInt(newUserTradeCount[1].toString()));
            }
        }
        return;
    }

    private void countOverTradesPos(
            Symbol symbol,
            Map<Long, Integer> userTradesMap,
            List<Long> userListTradeUncapped,
            Long dateFrom) {
        // 当日中の直近{CHECK_SPAN_HOURS}の期間で、約定総額(円貨換算)が{TRADE_ASSET_AMOUNT}以上の
        // 約定件数のユーザーID別リスト取得
        List<Object[]> posTradeSumResult =
                posTradeService.sumOverTradesByCondition(
                        symbol.getId(),
                        BigDecimal.valueOf(TRADE_ASSET_AMOUNT),
                        userListTradeUncapped,
                        dateFrom,
                        null);

        log.info(
                "DBMITO,FinancialAssetsDeviationCheckerLog,symbolId,"
                        + symbol.getId()
                        + ",posTradeSumResult,"
                        + JsonUtil.encode(posTradeSumResult)
                        + ",trade_type,POS");

        if (!CollectionUtils.isEmpty(posTradeSumResult) && posTradeSumResult.get(0)[0] != null) {
            // 全通貨ペアでユーザーID,閾値金額以上の約定件数Mapにマージ
            addUserTradesMapFromObject(userTradesMap, posTradeSumResult);
            log.info(
                    "DBMITO,FinancialAssetsDeviationCheckerLog,symbolId,"
                            + symbol.getId()
                            + ",userTradesMap,"
                            + JsonUtil.encode(userTradesMap)
                            + ",trade_type,POS");
        }
    }

    private List<Long> mergeUserList(List<UserInfo> userInfoList) {
        List<Long> resultList = new ArrayList<>();
        userInfoList.stream().forEach(list -> resultList.add(list.getUserId()));
        return resultList;
    }

    public List<Long> execute(TradeType tradeType, Long now, List<Long> posSavedUserId)
            throws Exception {
        String logParam;
        if (tradeType != null) {
            logParam = tradeType.toString();
            dbtradeType = tradeType.getName();
        } else {
            logParam = "INVEST";
            dbtradeType = "INVEST";
        }
        List<CurrencyPairConfig> currencyPairConfigs = null;
        if (tradeType != null) {
            // 取引所または販売所をチェックする
            /** 有効な通貨ペアリスト取得 */
            currencyPairConfigs =
                    currencyPairConfigService.findAllByCondition(tradeType, null, true);
        } else {
            // 取引所と販売所をチェックする
            currencyPairConfigs =
                    currencyPairConfigService.findAllByCondition(TradeType.INVEST, null, true);
        }
        if (CollectionUtils.isEmpty(currencyPairConfigs)) {
            log.warn(
                    "FinancialAssetsDeviationCheckerLog,start,enabled currencyPairConfigs not found,trade_type,{}",
                    logParam);
            throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
        }

        /** 閾値取得 */
        getThreshholds();

        Long dateFrom = now - DateUnit.HOUR.getMillis() * CHECK_SPAN_HOURS;
        log.info(
                "FinancialAssetsDeviationCheckerLog,start,now,{},dateFrom,{},CHECK_SPAN_HOURS,{},tradeType,{}",
                new Date(now),
                new Date(dateFrom),
                CHECK_SPAN_HOURS,
                logParam);

        /** 運用口座取得(tradeUncappedで判定) */
        List<User> userListTradeUncapped = userService.findByTradeUncapped(true);
        List<Long> userIdListTradeUncapped = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userListTradeUncapped)) {
            userIdListTradeUncapped =
                    userListTradeUncapped.stream().map(User::getId).collect(Collectors.toList());
        }

        /** 判定①取引履歴 */
        // 直近{CHECK_SPAN_HOURS}の期間で、
        // 約定総額(円貨換算)が{TRADE_ASSET_AMOUNT}円以上の約定件数
        // 運用口座は対象外とする
        Map<Long, Integer> userTradesMap = new HashMap<>(); // ユーザーID,閾値金額以上の約定件数Map

        for (CurrencyPairConfig currencyPairConfig : currencyPairConfigs) {
            TradeType tradeTypeUse = null;
            // typeの設定
            if (tradeType != null) {
                tradeTypeUse = tradeType;
            } else {
                tradeTypeUse = currencyPairConfig.getTradeType();
            }
            // 有効な通貨ペアのsymbol(paramのsymbolはnull。取り直す)
            Symbol symbol =
                    symbolService.findByCondition(
                            tradeTypeUse, currencyPairConfig.getCurrencyPair());

            if (symbol == null) {
                log.error("FinancialAssetsDeviationCheckerLog, symbol is null");
                throw new CustomException(ErrorCode.COMMON_ERROR_NOT_FOUND);
            }

            if (TradeType.INVEST.equals(tradeTypeUse)) {
                // 取引履歴集計(当日中。運用口座・アーカイブは対象外)
                log.info("dateFrom:{}", DateFormatUtils.format(dateFrom, "yyyy-MM-dd HH:mm:ss"));
                countOverTradesPos(symbol, userTradesMap, userIdListTradeUncapped, dateFrom);
            }
        }

        List<Long> savedUserList = new ArrayList<>();
        // 閾値金額以上の約定が{TRADES_THRESHOLD}回以上のユーザーList作成
        List<Long> userList = new ArrayList<>();
        userTradesMap.entrySet().stream()
                .filter(map -> map.getValue() >= TRADES_THRESHOLD)
                .forEach(map -> userList.add(map.getKey()));

        log.info(
                "DBMITO,FinancialAssetsDeviationCheckerLog,userList,"
                        + JsonUtil.encode(userList)
                        + ",userTradeMap,"
                        + JsonUtil.encode(userTradesMap)
                        + ",tradeType,"
                        + logParam);

        if (CollectionUtils.isEmpty(userList)) {
            log.info(
                    "FinancialAssetsDeviationCheckerLog,no user over threshhold,trade_type,{}",
                    logParam);
            return savedUserList;
        }

        /** 判定②金融資産 */
        // かつ、金融資産が{FINANCIAL_ASSETS}万円未満
        // 例)金融資産300万未満の場合、INCOME2(2, "100 万円 ~ 300 万円")
        // => userInfo.financialAssets <= 2
        // ※テーブル登録無しの場合は対象外と判定

        // 件数少ないとみなし、user_id in(ユーザーリスト)を使用する
        // 個人とも判定する
        List<UserInfo> userInfoList =
                userInfoService.findByCondition(null, userList, FINANCIAL_ASSETS, null, true);

        List<Long> deviatedUserList = mergeUserList(userInfoList);

        // 検知対象ユーザー・閾値以上約定件数のMap抽出
        Map<Long, Integer> resultMap =
                userTradesMap.entrySet().stream()
                        .filter(map -> deviatedUserList.contains(map.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 取引所または販売所に乖離しているユーザーを除く
        if (tradeType == null) {
            Map<Long, Integer> resultMapPos = new HashMap<>();
            for (Map.Entry<Long, Integer> result : resultMap.entrySet()) {
                if (!posSavedUserId.contains(result.getKey())) {
                    resultMapPos.put(result.getKey(), result.getValue());
                }
            }
            /** 検知データ保存 */
            customTransactionManager.execute(
                    entityManager -> {
                        for (Map.Entry<Long, Integer> result : resultMapPos.entrySet()) {
                            save(result.getKey(), result.getValue(), dbtradeType, entityManager);
                        }
                    });
        } else {
            /** 検知データ保存 */
            customTransactionManager.execute(
                    entityManager -> {
                        for (Map.Entry<Long, Integer> result : resultMap.entrySet()) {
                            savedUserList.add(result.getKey());
                            save(result.getKey(), result.getValue(), dbtradeType, entityManager);
                        }
                    });
        }

        /** 検知時通知 */
        log.info("FinancialAssetsDeviationCheckerLog,deviated users found,trade_type,{}", logParam);

        return savedUserList;
    }
}
