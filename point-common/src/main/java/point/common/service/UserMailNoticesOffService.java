package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.component.CustomTransactionManager;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.constant.NoticesType;
import point.common.entity.UserMailNoticesOff;
import point.common.exception.CustomException;
import point.common.model.request.UserMailNoticesForm;
import point.common.model.response.UserMailnoticesResponse;
import point.common.repos.UserMailNoticesOffRepository;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
public class UserMailNoticesOffService {

    private final UserMailNoticesOffRepository userMailNoticesOffRepository;

    private final CustomTransactionManager customTransactionManager;

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void save(UserMailNoticesForm userMailNoticesForm) throws CustomException {
        UserMailNoticesOff userMailNoticesOff =
                userMailNoticesOffRepository
                        .findById(userMailNoticesForm.getId())
                        .orElseThrow(
                                () ->
                                        new CustomException(
                                                ErrorCode.REQUEST_ERROR_NOTICES_ID_NOT_FOUND));
        userMailNoticesOff.setUpdatedAt(new Date());
        userMailNoticesOff.setNoticesEnabled(userMailNoticesForm.getNoticesEnabled());
        userMailNoticesOff.setUserId(userMailNoticesOff.getUserId());
        userMailNoticesOff.setNoticesType(userMailNoticesForm.getNoticesType());
        userMailNoticesOffRepository.save(userMailNoticesOff);
    }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void create(Long userId, EntityManager entityManager) throws CustomException {
        if (StringUtils.isBlank(String.valueOf(userId))) {
            log.error("create notice param exception userId: {}", userId);
            throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_USER);
        }
        List<NoticesType> noticesTypeList =
                List.of(NoticesType.IMPORTANT_NOTICES_TYPE, NoticesType.EMAIL_MAGAZINE_TYPE);
        noticesTypeList.stream()
                .forEach(
                        noticesType -> {
                            UserMailNoticesOff userMailNoticesOff =
                                    UserMailNoticesOff.builder()
                                            .userId(userId)
                                            .noticesType(noticesType)
                                            .noticesEnabled(true)
                                            .build();
                            userMailNoticesOff.setCreatedAt(new Date());
                            try {
                                customTransactionManager.save(userMailNoticesOff, entityManager);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        });
    }

    public List<UserMailnoticesResponse> findByUserId(Long userId) {
        List<UserMailNoticesOff> userMailNoticesOffList =
                userMailNoticesOffRepository.findByUserId(userId);
        List<UserMailnoticesResponse> userMailnoticesResponseList = new ArrayList<>();
        userMailNoticesOffList.stream()
                .forEach(
                        userMailNoticesOff -> {
                            UserMailnoticesResponse userMailnoticesResponse =
                                    UserMailnoticesResponse.builder()
                                            .Id(userMailNoticesOff.getId())
                                            .noticesType(userMailNoticesOff.getNoticesType())
                                            .noticesEnabled(userMailNoticesOff.getNoticesEnabled())
                                            .build();
                            userMailnoticesResponseList.add(userMailnoticesResponse);
                        });
        return userMailnoticesResponseList;
    }
}
