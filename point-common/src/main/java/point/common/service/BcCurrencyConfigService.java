package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.entity.BcCurrencyConfig;
import point.common.entity.BcCurrencyConfig_;
import point.common.model.request.BcCurrencyConfigForm;
import point.common.model.response.PageData;
import point.common.predicate.BcCurrencyConfigPredicate;
import point.common.util.JsonUtil;

@Service
@RequiredArgsConstructor
public class BcCurrencyConfigService
        extends EntityService<BcCurrencyConfig, BcCurrencyConfigPredicate> {

    private static final String ABOLISHED = "ABOLISHED";
    private static final String ACTIVE = "ACTIVE";

    @Override
    public Class<BcCurrencyConfig> getEntityClass() {
        return BcCurrencyConfig.class;
    }

    public PageData<BcCurrencyConfig> findByConditionPageData(
            String active, String currencyType, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                List<Predicate> predicates = new ArrayList<>();
                                if (active != null) {
                                    predicates.add(
                                            predicate.equalActive(criteriaBuilder, root, active));
                                }
                                if (currencyType != null) {
                                    predicates.add(
                                            predicate.equalCurrencyType(
                                                    criteriaBuilder, root, currencyType));
                                }
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        predicates);
                            }
                        });

        List<BcCurrencyConfig> bcCurrencyConfigs =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<BcCurrencyConfig> query() {
                                List<Predicate> predicates = new ArrayList<>();
                                if (active != null) {
                                    predicates.add(
                                            predicate.equalActive(criteriaBuilder, root, active));
                                }
                                if (currencyType != null) {
                                    predicates.add(
                                            predicate.equalCurrencyType(
                                                    criteriaBuilder, root, currencyType));
                                }
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(BcCurrencyConfig_.active)),
                                        criteriaBuilder.desc(
                                                root.get(BcCurrencyConfig_.updatedAt)));
                            }
                        });
        return new PageData<>(number, size, count, bcCurrencyConfigs);
    }

    public BcCurrencyConfig create(String userName, BcCurrencyConfigForm currencyDataForm) {
        String currencyData = JsonUtil.encode(currencyDataForm.getCurrencyConfigForms());
        BcCurrencyConfig bcCurrencyConfig = new BcCurrencyConfig();
        bcCurrencyConfig.setCurrencyData(currencyData);
        bcCurrencyConfig.setCurrencyType(currencyDataForm.getCurrencyType());
        bcCurrencyConfig.setCreatedBy(userName);
        bcCurrencyConfig.setUpdatedBy(userName);
        return save(bcCurrencyConfig);
    }

    public BcCurrencyConfig update(Long id) {
        BcCurrencyConfig currencyConfig = this.findOne(id);
        currencyConfig.setActive(ABOLISHED);
        return this.save(currencyConfig);
    }

    public BcCurrencyConfig findOneByActive(String currencyType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public BcCurrencyConfig query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalActive(criteriaBuilder, root, ACTIVE));
                        predicates.add(
                                predicate.equalCurrencyType(criteriaBuilder, root, currencyType));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<BcCurrencyConfig> findAllByActive() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<BcCurrencyConfig> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalActive(criteriaBuilder, root, ACTIVE));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
