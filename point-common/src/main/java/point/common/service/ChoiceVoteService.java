package point.common.service;

import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.stream.Stream;
import javax.persistence.criteria.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.component.QueryExecutorSum;
import point.common.constant.ChoiceVoteResult;
import point.common.constant.UserIdType;
import point.common.entity.*;
import point.common.model.response.ChoiceRewardHistoryData;
import point.common.model.response.ChoiceRewardResponseData;
import point.common.model.response.GlobalApiResponse;
import point.common.model.response.PageData;
import point.common.predicate.ChoiceVotePredicate;
import point.common.repos.ChoiceRewardRepository;
import point.common.repos.ChoiceVoteRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoiceVoteService extends EntityService<ChoiceVote, ChoiceVotePredicate> {
    private final ChoiceVoteRepository choiceVoteRepository;

    private final ChoiceRewardRepository choiceRewardRepository;

    private final UserVoteRewardService userVoteRewardService;

    @Override
    public Class<ChoiceVote> getEntityClass() {
        return ChoiceVote.class;
    }

    public PageData<ChoiceVote> findByCondition(
            Long userId,
            ChoiceVoteResult voteResult,
            Integer withdrawalAllowed,
            Long activityId,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                voteResult,
                                                withdrawalAllowed,
                                                activityId));
                            }
                        });
        return new PageData<ChoiceVote>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<ChoiceVote, List<ChoiceVote>>() {
                            @Override
                            public List<ChoiceVote> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                voteResult,
                                                withdrawalAllowed,
                                                activityId);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(ChoiceVote_.VOTE_TIME)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceVote> root,
            Long userId,
            ChoiceVoteResult voteResult,
            Integer withdrawalAllowed,
            Long activityId) {
        List<Predicate> predicates = new ArrayList<>();
        Predicate idPredicate = predicate.equalUserId(criteriaBuilder, root, userId);
        if (idPredicate != null) {
            predicates.add(idPredicate);
        }
        Predicate voteResultPredicate =
                predicate.equalChoiceVoteResult(criteriaBuilder, root, voteResult);
        if (voteResultPredicate != null) {
            predicates.add(voteResultPredicate);
        }

        Predicate withdrawalAllowedPredicate =
                predicate.equalWithdrawalAllowed(criteriaBuilder, root, withdrawalAllowed);
        if (withdrawalAllowedPredicate != null) {
            predicates.add(withdrawalAllowedPredicate);
        }
        Predicate activityIdPredicate =
                predicate.equalActivityId(criteriaBuilder, root, activityId);
        if (activityIdPredicate != null) {
            predicates.add(activityIdPredicate);
        }
        return predicates;
    }

    public ChoiceVote findOneVoteByReward(ChoiceReward reward) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoiceVote, ChoiceVote>() {
                    @Override
                    public ChoiceVote query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalUserId(criteriaBuilder, root, reward.getUserId()));
                        predicates.add(
                                predicate.equalActivityId(
                                        criteriaBuilder, root, reward.getActivityId()));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public ChoiceVote findOneByUserIdAndToday(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoiceVote, ChoiceVote>() {
                    @Override
                    public ChoiceVote query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(
                                predicate.equalCreatedAtYYYYMMDD(
                                        criteriaBuilder, root, new Date()));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public Optional<ChoiceVote> findByUserIdsWithActivityId(List<Long> userIds, Long activityId) {
        ChoiceVote choiceVote =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public ChoiceVote query() {
                                List<Predicate> predicates = new ArrayList<>(1);
                                predicates.add(
                                        predicate.userIdsIn(
                                                criteriaBuilder, root, activityId, userIds));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        return Optional.ofNullable(choiceVote);
    }

    public List<ChoiceVote> findOneByActivity(Long activityId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<ChoiceVote> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalActivityId(criteriaBuilder, root, activityId));
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                0,
                                1000,
                                criteriaBuilder.asc(root.get(ChoiceVote_.USER_ID)));
                    }
                });
    }

    public long countByActivityId(Long activityId) {
        return customTransactionManager.count(
                getEntityClass(),
                new QueryExecutorCounter<>() {
                    @Override
                    public Long query() {
                        return count(
                                entityManager,
                                criteriaBuilder,
                                criteriaQuery,
                                root,
                                Stream.of(
                                                criteriaBuilder.equal(
                                                        root.get(ChoiceVote_.activityId),
                                                        activityId))
                                        .toList());
                    }
                });
    }

    public List<ChoiceVote> findOneByActivityId(Long activityId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<ChoiceVote> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalActivityId(criteriaBuilder, root, activityId));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public ResponseEntity<GlobalApiResponse<ChoiceRewardResponseData>> getUserVoteRewardHistory(
            UserIdType type, Pair<Long, Long> userIdPair, Integer yearMonth) {
        int year = yearMonth / 100;
        int month = yearMonth % 100;

        YearMonth target = YearMonth.of(year, month);
        int daysInMonth = target.lengthOfMonth();

        LocalDateTime startJST = LocalDateTime.of(year, month, 1, 0, 0, 0);

        LocalDateTime endJST = LocalDateTime.of(year, month, daysInMonth, 23, 59, 59);

        ZonedDateTime startZonedJST =
                startJST.atZone(ZoneId.of("Asia/Tokyo"))
                        .withZoneSameInstant(ZoneId.systemDefault());

        ZonedDateTime endZonedJST =
                endJST.atZone(ZoneId.of("Asia/Tokyo")).withZoneSameInstant(ZoneId.systemDefault());

        Date startDate = Date.from(startZonedJST.toInstant());

        Date endDate = Date.from(endZonedJST.toInstant());

        Long userId = type == UserIdType.Invest ? userIdPair.getLeft() : userIdPair.getRight();
        List<Long> userIds = new ArrayList<>(2);
        if (userIdPair.getLeft() != null) {
            userIds.add(userIdPair.getLeft());
        }

        if (userIdPair.getRight() != null) {
            userIds.add(userIdPair.getRight());
        }

        LocalTime currentTime = startZonedJST.toLocalTime();
        List<ChoiceRewardHistoryData> voteRewardHistory;
        int hour = currentTime.getHour();
        if (hour >= 7 && hour <= 9) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            endDate = calendar.getTime();
            voteRewardHistory =
                    choiceVoteRepository.getUserChoiceRewardHistory(userIds, startDate, endDate);
            ChoiceActivity activity = userVoteRewardService.findYesterdayActivity();
            ChoiceVote vote =
                    choiceVoteRepository.findByActivityIdAndUserIdIn(activity.getId(), userIds);
            if (vote != null) {
                ChoiceReward reward =
                        choiceRewardRepository.findByActivityIdAndUserIdIn(
                                activity.getId(), userIds);
                if (reward != null) {
                    voteRewardHistory.add(new ChoiceRewardHistoryData(reward, vote));
                } else {
                    voteRewardHistory.add(new ChoiceRewardHistoryData(vote));
                }
            }
        } else {
            voteRewardHistory =
                    choiceVoteRepository.getUserChoiceRewardHistory(userIds, startDate, endDate);
        }

        BigDecimal totalReward;

        totalReward =
                voteRewardHistory.stream()
                        .map(ChoiceRewardHistoryData::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);

        // Create a map to store vote history by day of month
        Map<Integer, ChoiceRewardHistoryData> voteHistoryMap =
                new HashMap<>(voteRewardHistory.size());
        for (ChoiceRewardHistoryData data : voteRewardHistory) {
            LocalDateTime voteTime =
                    data.getVoteTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            int dayOfMonth = voteTime.getDayOfMonth();
            voteHistoryMap.put(dayOfMonth, data);
        }

        // Iterate through each day of the month and fill in missing data
        List<ChoiceRewardHistoryData> result = new ArrayList<>(daysInMonth);
        for (int day = 1; day <= daysInMonth; day++) {
            if (voteHistoryMap.containsKey(day)) {
                result.add(voteHistoryMap.get(day));
            } else {
                ChoiceRewardHistoryData missingData = new ChoiceRewardHistoryData();
                missingData.setUserId(userId);
                missingData.setVoteResult(ChoiceVoteResult.NOT_VOTED);
                missingData.setAmount(BigDecimal.ZERO);
                result.add(missingData);
            }
        }
        ChoiceRewardResponseData response = new ChoiceRewardResponseData();
        response.setMonthRewardAmount(totalReward);
        response.setRewardHistory(result);
        return ResponseEntity.ok().body(new GlobalApiResponse<>(200, response));
    }

    public boolean existsByUserId(Long activityId, List<Long> userIds) {
        List<ChoiceVote> votes =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<ChoiceVote> query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.userIdsIn(
                                                criteriaBuilder, root, activityId, userIds));
                                return getResultList(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        return !CollectionUtils.isEmpty(votes);
    }

    public ChoiceVote findByUserWithActivity(Long userId, Long activityId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceVote query() {
                        List<Predicate> predicates = new ArrayList<>(2);
                        predicates.add(
                                predicate.equalActivityId(criteriaBuilder, root, activityId));
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public long sumVotePowerByActivityIdWithUserId(Long activityId, List<Long> userIds) {
        Object[] sum =
                customTransactionManager.sum(
                        getEntityClass(),
                        new QueryExecutorSum<>() {
                            @Override
                            public Object[] query() {
                                criteriaQuery.multiselect(
                                        criteriaBuilder.sum(root.get(ChoiceVote_.VOTE_POWER)));

                                Predicate[] predicates =
                                        Stream.of(
                                                        criteriaBuilder.equal(
                                                                root.get(ChoiceVote_.activityId),
                                                                activityId),
                                                        criteriaBuilder
                                                                .in(root.get(ChoiceVote_.USER_ID))
                                                                .value(userIds))
                                                .toArray(Predicate[]::new);
                                criteriaQuery.where(predicates);
                                try {
                                    Object singleResult =
                                            entityManager
                                                    .createQuery(criteriaQuery)
                                                    .setFirstResult(0)
                                                    .setMaxResults(1)
                                                    .getSingleResult();
                                    return new Object[] {singleResult};
                                } catch (Exception e) {
                                    log.error(
                                            "Failed to get sum of vote power, activityId: {}, userId: {}",
                                            activityId,
                                            userIds,
                                            e);
                                    return new Object[] {NumberUtils.LONG_ZERO};
                                }
                            }
                        });
        if (Objects.isNull(sum) || Objects.isNull(sum[0])) {
            return NumberUtils.LONG_ZERO;
        }
        return Long.parseLong(sum[0].toString());
    }
}
