package point.common.service;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import point.common.component.EkycProxy;
import point.common.component.SesManager;
import point.common.config.EKycConfig;
import point.common.constant.BpoStatus;
import point.common.constant.CommonConstants;
import point.common.constant.EkycErrorType;
import point.common.constant.ErrorCode;
import point.common.constant.KycMailStatus;
import point.common.constant.KycStatus;
import point.common.constant.MailNoreplyType;
import point.common.entity.MailNoreply;
import point.common.entity.User;
import point.common.entity.UserEkyc;
import point.common.entity.UserEkycStatusChangeHistory;
import point.common.entity.UserKyc;
import point.common.exception.CustomException;
import point.common.http.cb.HttpClient;
import point.common.repos.UserEkycRepository;
import point.common.repos.UserKycRepository;
import point.common.repos.UserRepository;
import point.common.util.JsonUtil;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
@RequiredArgsConstructor
public class UserEkycBpoResultService {

    private final UserEkycService userEkycService;
    private final UserEkycStatusChangeHistoryService userEkycStatusChangeHistoryService;
    private final UserEkycRequestHistoryService userEkycRequestHistoryService;
    private final UserEkycRepository repository;
    private final UserRepository userRepository;
    private final UserKycRepository userKycRepository;
    private final EKycConfig eKycConfig;
    private final SesManager sesManager;
    private final MailNoreplyService mailNoreplyService;
    private MailNoreply mailNoreply;
    private MailNoreply ekycSuccessMail;

    //  @PostConstruct
    //  public void init() {
    //    mailNoreply = mailNoreplyService.findOne(MailNoreplyType.EKYC_BPO_FAILED_ERROR);
    //    ekycSuccessMail =
    //        mailNoreplyService.findOne(MailNoreplyType.USER_KYC_STATUS_CHANGED_EKYC_SUCCESS);
    //  }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void fetchBpoResultWithUpdate() {
        log.info("start fetch ekyc status of users");

        // find user ekyc
        // find last
        List<User> users =
                userRepository.findByKycStatusInOrderByIdDesc(
                        List.of(
                                KycStatus.DOCUMENT_WAITING_APPLY,
                                KycStatus.DOCUMENT_RECEIVED,
                                KycStatus.DOCUMENT_CONFIRMING));
        if (CollectionUtils.isEmpty(users)) {
            log.info("no user need to fetch bpo result, skip this schedule");
            return;
        }

        // fetch bpo result
        for (User user : users) {
            UserEkyc userEkyc = repository.findTopByUserIdOrderByIdDesc(user.getId());
            if (userEkyc == null) {
                log.warn("the user {} has no ekyc record", user.getId());
                continue;
            }
            Long userId = userEkyc.getUserId();
            Long applicantId = userEkyc.getApplicantId();
            Long userEkycId = userEkyc.getId();
            KycStatus userKycStatus = user.getKycStatus();
            if (applicantId == null) {
                log.info("the user {} has no applicantId", userEkyc.getUserId());
                continue;
            }
            List<UserEkycStatusChangeHistory> ekycStatusChangeHistory =
                    userEkycStatusChangeHistoryService.findByUserEkycStatusChangeHistory(
                            user.getId(), userEkyc.getApplicantId(), KycStatus.DOCUMENT_RECEIVED);
            if (KycStatus.DOCUMENT_WAITING_APPLY.equals(user.getKycStatus())
                    && !CollectionUtils.isEmpty(ekycStatusChangeHistory)) {
                log.warn(
                        "this user ekyc url has been finished ,skip fetching  on BPO  side ,current kyc status is {},userId is {}",
                        user.getKycStatus(),
                        userId);
                continue;
            }
            log.info("start process user: {} applicantId: {}", userId, applicantId);
            Map<String, Object> resultMap;
            try {
                resultMap = this.getBpoResultByApplicantId(userId, applicantId);
            } catch (Exception e) {
                log.error(
                        "get bpo result by applicantId error. userId: {} applicantId: {}",
                        userId,
                        applicantId);
                continue;
            }

            try {
                // get status field
                if (resultMap.containsKey(CommonConstants.ERROR_CODE)) {
                    log.info("ekyc response error, skip to next user; responseMap: {}", resultMap);
                    continue;
                }

                String status =
                        resultMap.getOrDefault(CommonConstants.STATUS, StringUtils.EMPTY) != null
                                ? resultMap.get(CommonConstants.STATUS).toString()
                                : StringUtils.EMPTY;

                // get result field
                String result =
                        resultMap.get(CommonConstants.RESULT) != null
                                ? resultMap.get(CommonConstants.RESULT).toString()
                                : StringUtils.EMPTY;

                // EKYCStatus bpoStatus = KycStatus.from(status);
                BpoStatus bpoStatus = BpoStatus.from(status);
                if (BpoStatus.applying.equals(bpoStatus)) {
                    if (!userEkycService.hasValidEkycUrl(userEkyc)) {
                        // the ekyc_url of user is expired
                        log.info(
                                "the user ekyc_url is expired, userId: {}, applicantId: {}",
                                userId,
                                applicantId);
                        this.ekycUrlExpired(userId, userEkycId, applicantId, userKycStatus);
                        continue;
                    }
                    log.info(
                            "bpo.status: {}, waiting for the next schedule, userId: {}, applicantId: {}",
                            bpoStatus,
                            userId,
                            applicantId);
                    continue;
                } else if (BpoStatus.accepted.equals(bpoStatus)) {
                    this.processEkycResult(
                            resultMap, userId, userEkycId, applicantId, userKycStatus, user);
                } else if (BpoStatus.processing.equals(bpoStatus)) {
                    // process ekyc_result
                    boolean isEkycSuccess =
                            this.processEkycResult(
                                    resultMap,
                                    userId,
                                    userEkycId,
                                    applicantId,
                                    userKycStatus,
                                    user);
                    if (!isEkycSuccess) {
                        continue;
                    }
                    // process user kyc.status
                    this.bpoProcessing(userId, userEkycId, applicantId, userKycStatus, user);
                } else if (BpoStatus.completed.equals(bpoStatus)) {
                    // the result fields incorrect
                    if (!StringUtils.equalsAnyIgnoreCase(
                            result, CommonConstants.MATCH, CommonConstants.UNMATCH)) {
                        log.warn("bpo result incorrect, userId: {}, result: {}", userId, result);
                        continue;
                    }

                    // bpo success
                    if (CommonConstants.MATCH.equalsIgnoreCase(result)) {
                        log.info(
                                "bpo success, user status to completed, userId: {}, applicantId: {}",
                                userId,
                                applicantId);
                        this.bpoSuccess(userId, userEkycId, applicantId, userKycStatus);
                    } else {
                        log.info("bpo failed, userId: {}, applicantId: {}", userId, applicantId);
                        this.bpoFailed(userId, userEkycId, applicantId, userKycStatus, resultMap);
                    }
                } else {
                    log.warn(
                            "unknown bpo.status: {}, userId: {}, applicantId: {}",
                            bpoStatus,
                            userId,
                            applicantId);
                }
                log.info(
                        "finish process user: {} applicantId: {}, bpo.status: {}",
                        userId,
                        applicantId,
                        bpoStatus);
            } catch (Exception e) {
                log.error(
                        "process user ekyc result error, userId: {}, applicantId: {}, msg: {}",
                        userId,
                        applicantId,
                        e.getMessage());
                log.error(e.getMessage(), e);
            }
        }
        log.info("finish fetch ekyc status");
    }

    private void updateUserEkycStatus(Long userId, Long userEkycId, KycStatus status) {

        Optional<User> userOptional = userRepository.findById(userId);
        if (userOptional.isEmpty()) {
            log.error(
                    "cannot update user status to {}, because user is null by {}", status, userId);
            return;
        }

        User user = userOptional.get();
        KycStatus beforeStatus = user.getKycStatus();
        try {
            UserKyc userKyc = new UserKyc(userId);
            userKyc.setKycStatus(status);
            userKyc.setJudgingComment(StringUtils.EMPTY);
            userKyc.setAmlCftComment(StringUtils.EMPTY);
            userKyc.setCreatedAt(new Date());
            userKyc.setUpdatedAt(new Date());
            if (status.equals(KycStatus.DOCUMENT_RECEIVED)) {
                userKyc.setKycMailStatus(KycMailStatus.MAIL_SENT);
                userKyc.setMailSendAt(new Date());
            }
            userKyc.setOperator(CommonConstants.WORKER);
            userKyc.setUserInfoId(user.getUserInfoId());
            userKycRepository.save(userKyc);
            user.setKycStatus(status);
            user.setUserKycId(userKyc.getId());
            user.setUpdatedAt(new Date());
            userRepository.save(user);
        } catch (Exception e) {
            log.error(
                    "update user.kycStatus to {} error, userId: {}, userEkycId: {}, beforeStatus: {}, msg: {}",
                    status,
                    userId,
                    userEkycId,
                    beforeStatus,
                    e.getMessage());
            log.error(e.getMessage(), e);
        }
    }

    private void createUseEkycStatusHistory(
            Long userId,
            Long applicantId,
            String url,
            KycStatus beforeStatus,
            KycStatus afterStatus,
            String reason) {
        try {
            userEkycStatusChangeHistoryService.create(
                    userId, url, applicantId, beforeStatus, afterStatus, reason);
        } catch (Exception e) {
            log.error(
                    "save ekyc status history error, userId: {} applicantId: {}, url: {}, beforeStatus: {}, afterStatus: {}, reason: {}, msg: {}",
                    userId,
                    applicantId,
                    url,
                    beforeStatus,
                    afterStatus,
                    reason,
                    e.getMessage());
            log.error(e.getMessage(), e);
        }
    }

    private Map<String, Object> getBpoResultByApplicantId(Long userId, Long applicantId)
            throws IOException, CustomException {

        // build header
        List<String> reqHeaders = new ArrayList<>(3);
        reqHeaders.addAll(userEkycService.getSignatureHeader(StringUtils.EMPTY));
        reqHeaders.addAll(userEkycService.getAuthorizationToken());
        String url = String.format(eKycConfig.getBpoResultUrl(), applicantId);
        log.info(
                "get bpo result url: {} by {}; applicantId: {}, reqHeader: {}",
                url,
                userId,
                applicantId,
                reqHeaders);

        // send request with get result
        HttpClient.HttpResult result = EkycProxy.reqApi(url, reqHeaders, Collections.emptyMap());
        String returnPayload = result.content;
        // convert result to json
        Map<String, Object> resultMap = JsonUtil.decode(returnPayload, Map.class);
        if (CollectionUtils.isEmpty(resultMap)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_REQUEST_ERROR);
        }

        // remove large field that useless
        resultMap.remove(CommonConstants.MASKED_IMAGES);
        resultMap.remove(CommonConstants.APPLICANT_IMAGES);

        try {
            userEkycRequestHistoryService.bpoResultHistory(
                    userId,
                    JsonUtil.encode(reqHeaders),
                    StringUtils.EMPTY,
                    result.getRespHeaders().toString(),
                    JsonUtil.encode(resultMap),
                    url);
        } catch (Exception e) {
            log.error("save bpo result request history error,  message: {}", e.getMessage());
            log.error(e.getMessage(), e);
        }
        log.info("response payload: {}", resultMap);
        return resultMap;
    }

    /**
     * process ekyc_result
     *
     * @param resultMap
     * @param userId
     * @param userEkycId
     * @param applicantId
     * @param userKycStatus
     * @return false is ekyc failed
     */
    private boolean processEkycResult(
            Map<String, Object> resultMap,
            Long userId,
            Long userEkycId,
            Long applicantId,
            KycStatus userKycStatus,
            User user) {

        // Check if the kycStatus has been modified.
        if (KycStatus.DOCUMENT_CONFIRMING.equals(user.getKycStatus())
                || KycStatus.DOCUMENT_RECEIVED.equals(user.getKycStatus())) {
            return true;
        }

        Map<String, String> ekycResultMap =
                resultMap.get(CommonConstants.EKYC_RESULT) != null
                        ? (Map<String, String>) resultMap.get(CommonConstants.EKYC_RESULT)
                        : Collections.emptyMap();
        if (!CollectionUtils.isEmpty(ekycResultMap)) {
            // ekyc success
            log.info("ekyc result success, userId: {}, applicantId: {}", userId, applicantId);
            this.ekycSuccess(userId, userEkycId, applicantId, userKycStatus);
            // send mail for ekyc success
            this.sendEkycSuccessMail(user);
            return true;
        }
        log.warn("ekyc result failure, userId: {},applicantId: {}", userId, applicantId);
        return false;
    }

    private void sendEkycSuccessMail(User user) {
        try {
            ekycSuccessMail =
                    mailNoreplyService.findOne(
                            MailNoreplyType.USER_KYC_STATUS_CHANGED_EKYC_SUCCESS);
            sesManager.send(
                    ekycSuccessMail.getFromAddress(),
                    user.getEmail(),
                    ekycSuccessMail.getTitle(),
                    ekycSuccessMail.getContents());
        } catch (Exception e) {
            log.error(
                    "send ekyc success mail to {} error, msg: {}", user.getEmail(), e.getMessage());
            log.error(e.getMessage(), e);
        }
    }

    private void bpoProcessing(
            Long userId, Long userEkycId, Long applicantId, KycStatus userKycStatus, User user) {
        // Check if the kycStatus has been modified.
        if (KycStatus.DOCUMENT_CONFIRMING.equals(user.getKycStatus())) {
            return;
        }
        this.updateUserEkycStatus(userId, userEkycId, KycStatus.DOCUMENT_CONFIRMING);
        this.createUseEkycStatusHistory(
                userId,
                applicantId,
                eKycConfig.getBpoResultUrl(),
                userKycStatus,
                KycStatus.DOCUMENT_CONFIRMING,
                "[UserEkycResultJob]: bpo processing");
    }

    private void bpoSuccess(
            Long userId, Long userEkycId, Long applicantId, KycStatus userKycStatus) {
        // supplement kyc status for user - DOCUMENT_RECEIVED
        if (KycStatus.DOCUMENT_WAITING_APPLY.equals(userKycStatus)) {
            this.createUseEkycStatusHistory(
                    userId,
                    applicantId,
                    eKycConfig.getBpoResultUrl(),
                    userKycStatus,
                    KycStatus.DOCUMENT_RECEIVED,
                    "[UserEkycResultJob]: ekyc success");
        }
        // supplement kyc status for user - DOCUMENT_CONFIRMING
        if (KycStatus.DOCUMENT_RECEIVED.equals(userKycStatus)) {
            this.createUseEkycStatusHistory(
                    userId,
                    applicantId,
                    eKycConfig.getBpoResultUrl(),
                    userKycStatus,
                    KycStatus.DOCUMENT_CONFIRMING,
                    "[UserEkycResultJob]: bpo success");
            userKycStatus = KycStatus.DOCUMENT_CONFIRMING;
        }

        this.updateUserEkycStatus(userId, userEkycId, KycStatus.DOCUMENT_CONFIRMED);
        this.createUseEkycStatusHistory(
                userId,
                applicantId,
                eKycConfig.getBpoResultUrl(),
                userKycStatus,
                KycStatus.DOCUMENT_CONFIRMED,
                "[UserEkycResultJob]: bpo success");
    }

    private void bpoFailed(
            Long userId,
            Long userEkycId,
            Long applicantId,
            KycStatus userKycStatus,
            Map<String, Object> resultMap) {
        List<Map<String, String>> errors =
                resultMap.get(CommonConstants.VERIFICATION_ERRORS) != null
                        ? (List<Map<String, String>>)
                                resultMap.get(CommonConstants.VERIFICATION_ERRORS)
                        : null;

        if (CollectionUtils.isEmpty(errors)) {
            log.error("bpo failed and errors empty!");
            return;
        }

        List<String> codeList = new ArrayList<>();
        for (Map<String, String> error : errors) {
            codeList.add(error.get(CommonConstants.ERROR_CODE));
            log.info(
                    "errorCode: {} errorMessage: {}",
                    error.get(CommonConstants.ERROR_CODE),
                    error.get(CommonConstants.ERROR_MESSAGE));
        }

        // supplement kyc status for user - DOCUMENT_RECEIVED
        if (KycStatus.DOCUMENT_WAITING_APPLY.equals(userKycStatus)) {
            this.createUseEkycStatusHistory(
                    userId,
                    applicantId,
                    eKycConfig.getBpoResultUrl(),
                    userKycStatus,
                    KycStatus.DOCUMENT_RECEIVED,
                    "[UserEkycResultJob]: bpo failed");
        }
        // supplement kyc status for user - DOCUMENT_CONFIRMING
        if (KycStatus.DOCUMENT_RECEIVED.equals(userKycStatus)) {
            this.createUseEkycStatusHistory(
                    userId,
                    applicantId,
                    eKycConfig.getBpoResultUrl(),
                    userKycStatus,
                    KycStatus.DOCUMENT_CONFIRMING,
                    "[UserEkycResultJob]: bpo failed");
            userKycStatus = KycStatus.DOCUMENT_CONFIRMING;
        }

        StringBuilder content = new StringBuilder();
        List<EkycErrorType> errorTypeList = EkycErrorType.getEkycError(codeList);
        int num = 1;

        for (int i = 0; i <= errorTypeList.size() - 1; i++) {
            content.append(num)
                    .append(":")
                    .append("\n")
                    .append(errorTypeList.get(i).getDescription())
                    .append("\n");
            num++;
        }

        this.updateUserEkycStatus(userId, userEkycId, KycStatus.DOCUMENT_REJECTED);
        this.createUseEkycStatusHistory(
                userId,
                applicantId,
                eKycConfig.getBpoResultUrl(),
                userKycStatus,
                KycStatus.DOCUMENT_REJECTED,
                "[UserEkycResultJob]: bpo failed");

        mailNoreply = mailNoreplyService.findOne(MailNoreplyType.EKYC_BPO_FAILED_ERROR);
        MessageFormat format = new MessageFormat(mailNoreply.getContents());
        String mailContent = format.format(new String[] {content.toString()});

        Optional<User> userOptional = userRepository.findById(userId);
        if (userOptional.isEmpty()) {
            log.warn("send bpo fail mail fail, because the user not found by {}", userId);
            return;
        }
        User user = userOptional.get();
        try {
            sesManager.send(
                    mailNoreply.getFromAddress(),
                    user.getEmail(),
                    mailNoreply.getTitle(),
                    mailContent);
        } catch (Exception e) {
            log.error("send bpo fail mail to {} error, msg: {}", user.getEmail(), e.getMessage());
            log.error(e.getMessage(), e);
        }
    }

    private void ekycUrlExpired(
            Long userId, Long userEkycId, Long applicantId, KycStatus userKycStatus) {
        this.updateUserEkycStatus(userId, userEkycId, KycStatus.URL_EXPIRED);

        this.createUseEkycStatusHistory(
                userId,
                applicantId,
                eKycConfig.getBpoResultUrl(),
                userKycStatus,
                KycStatus.URL_EXPIRED,
                "[UserEkycResultJob]: ekyc_url expired");
    }

    private void ekycSuccess(
            Long userId, Long userEkycId, Long applicantId, KycStatus userKycStatus) {
        this.updateUserEkycStatus(userId, userEkycId, KycStatus.DOCUMENT_RECEIVED);

        this.createUseEkycStatusHistory(
                userId,
                applicantId,
                eKycConfig.getBpoResultUrl(),
                userKycStatus,
                KycStatus.DOCUMENT_RECEIVED,
                "[UserEkycResultJob]: ekyc success");
    }

    private void ekycFailed(
            Long userId, Long userEkycId, Long applicantId, KycStatus userKycStatus) {
        this.updateUserEkycStatus(userId, userEkycId, KycStatus.DOCUMENT_REJECTED);

        this.createUseEkycStatusHistory(
                userId,
                applicantId,
                eKycConfig.getBpoResultUrl(),
                userKycStatus,
                KycStatus.DOCUMENT_REJECTED,
                "[UserEkycResultJob]: ekyc failed");
    }
}
