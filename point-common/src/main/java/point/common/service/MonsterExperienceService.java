package point.common.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import point.common.constant.UserIdType;
import point.common.entity.MonsterFood;
import point.common.entity.MonsterGrowthRule;

@Slf4j
@Service
@RequiredArgsConstructor
public class MonsterExperienceService {
    private static final BigDecimal BONUS_RATE = new BigDecimal("1.1");
    private final MonsterGrowthRuleService monsterGrowthRuleService;
    private final MonsterFoodService monsterFoodService;
    private List<MonsterGrowthRule> cachedRules;

    public BigDecimal getBonusRate() {
        return BONUS_RATE;
    }

    public long calculateExperiencePoints(Long symbolId, UserIdType idType, BigDecimal income) {
        BigDecimal conversionRate = this.getConversionRate(income);
        Optional<MonsterFood> monsterFood = this.getMonsterFood(idType);
        return monsterFood
                .map(food -> calculateExperiencePoints(symbolId, income, conversionRate, food))
                .orElse(NumberUtils.LONG_ZERO);
    }

    public Pair<Long, Optional<MonsterFood>> calculateExperiencePointsWithFood(
            Long symbolId, UserIdType idType, BigDecimal income) {
        BigDecimal conversionRate = this.getConversionRate(income);
        Optional<MonsterFood> monsterFood = this.getMonsterFood(idType);
        long experiencePoints =
                monsterFood
                        .map(
                                food ->
                                        calculateExperiencePoints(
                                                symbolId, income, conversionRate, food))
                        .orElse(NumberUtils.LONG_ZERO);
        return Pair.of(experiencePoints, monsterFood);
    }

    public BigDecimal getConversionRate(BigDecimal income) {
        List<MonsterGrowthRule> rules = this.getCachedRules();
        return this.getCalculatedResult(income, rules);
    }

    private Optional<MonsterFood> getMonsterFood(UserIdType idType) {
        LocalDate currentDate = LocalDate.now();
        int year = currentDate.getYear();
        int weekOfYear = currentDate.get(WeekFields.ISO.weekOfWeekBasedYear());
        MonsterFood monsterFood =
                monsterFoodService.findByYearAndWeekOfYear(idType, year, weekOfYear);

        if (Objects.isNull(monsterFood)) {
            log.warn(
                    "No monster food found for year: {}, weekOfYear: {}, idType: {}",
                    year,
                    weekOfYear,
                    idType);
        }

        return Optional.ofNullable(monsterFood);
    }

    private long calculateExperiencePoints(
            Long symbolId, BigDecimal income, BigDecimal conversionRate, MonsterFood monsterFood) {
        BigDecimal baseExperience = income.multiply(conversionRate);
        if (monsterFood.getSymbolId().equals(symbolId)) {
            return baseExperience.multiply(BONUS_RATE).longValue();
        }
        return baseExperience.longValue();
    }

    private List<MonsterGrowthRule> getCachedRules() {
        if (cachedRules == null) {
            cachedRules = monsterGrowthRuleService.findAll();
        }
        return cachedRules;
    }

    private BigDecimal getCalculatedResult(BigDecimal income, List<MonsterGrowthRule> rules) {
        BigDecimal lastValidRate = BigDecimal.ZERO;
        for (MonsterGrowthRule rule : rules) {
            Long lowerBound = rule.getTotalProfitLossFrom();
            Long upperBound = rule.getTotalProfitLossTo();

            // 条件1：lowerBound 为 null 或 benefit >= lowerBound
            boolean isLowerValid =
                    (lowerBound == null) || (income.compareTo(new BigDecimal(lowerBound)) >= 0);
            // 条件2：upperBound 为 null 或 benefit <= upperBound
            boolean isUpperValid =
                    (upperBound == null) || (income.compareTo(new BigDecimal(upperBound)) <= 0);

            if (isLowerValid && isUpperValid) {
                lastValidRate = rule.getConversionRate(); // 更新为最后一条符合条件的规则
            }

            // 如果当前规则的 lowerBound > benefit，后续规则无需检查
            if (lowerBound != null && income.compareTo(new BigDecimal(lowerBound)) < 0) {
                break;
            }
        }
        return lastValidRate;
    }
}
