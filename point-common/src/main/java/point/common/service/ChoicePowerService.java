package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.UserIdType;
import point.common.entity.ChoicePower;
import point.common.model.response.PageData;
import point.common.predicate.ChoicePowerPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoicePowerService extends EntityService<ChoicePower, ChoicePowerPredicate> {

    @Override
    public Class<ChoicePower> getEntityClass() {
        return ChoicePower.class;
    }

    public PageData<ChoicePower> findByCondition(
            Long userId,
            String partnerMemberId,
            UserIdType idType,
            Integer powerAmountFrom,
            Integer powerAmountTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                partnerMemberId,
                                                idType,
                                                powerAmountFrom,
                                                powerAmountTo));
                            }
                        });
        return new PageData<ChoicePower>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<ChoicePower, List<ChoicePower>>() {
                            @Override
                            public List<ChoicePower> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                partnerMemberId,
                                                idType,
                                                powerAmountFrom,
                                                powerAmountTo);
                                criteriaQuery.select(root).distinct(true);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(
                                                root.join("choicePowerUserRels").get("userId")));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<ChoicePower> root,
            Long userId,
            String partnerMemberId,
            UserIdType idType,
            Integer powerAmountFrom,
            Integer powerAmountTo) {
        List<Predicate> predicates = new ArrayList<>();
        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }
        if (partnerMemberId != null) {
            predicates.add(predicate.equalPartnerMemberId(criteriaBuilder, root, partnerMemberId));
        }
        if (idType != null) {
            predicates.add(predicate.equalIdType(criteriaBuilder, root, idType));
        }
        if (powerAmountFrom != null || powerAmountTo != null) {
            predicates.add(
                    predicate.betweenPowerAmount(
                            criteriaBuilder, root, powerAmountFrom, powerAmountTo));
        }
        return predicates;
    }

    public ChoicePower findOneByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<ChoicePower, ChoicePower>() {
                    @Override
                    public ChoicePower query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
