package point.common.service;

import java.io.IOException;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.persistence.criteria.Predicate;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import point.common.component.CustomTransactionManager;
import point.common.component.EkycProxy;
import point.common.component.QueryExecutorReturner;
import point.common.component.SesManager;
import point.common.config.EKycConfig;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.constant.KycStatus;
import point.common.constant.MailNoreplyType;
import point.common.constant.PrefectureMapper;
import point.common.entity.MailNoreply;
import point.common.entity.User;
import point.common.entity.UserEkyc;
import point.common.entity.UserInfo;
import point.common.exception.CustomException;
import point.common.predicate.UserEkycPredicate;
import point.common.repos.UserEkycRepository;
import point.common.repos.UserRepository;
import point.common.util.FormatUtil;
import point.common.util.JsonUtil;
import point.common.util.KycUtil;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
@RequiredArgsConstructor
public class UserEkycService extends EntityService<UserEkyc, UserEkycPredicate> {

    private final UserEkycRepository repository;
    private final UserRepository userRepository;
    private final UserEkycRequestHistoryService userEkycRequestHistoryService;
    private final EKycConfig eKycConfig;
    private final SesManager sesManager;
    private final MailNoreplyService mailNoreplyService;
    private MailNoreply mailNoreply;
    @Autowired protected CustomTransactionManager customTransactionManager;

    //  @PostConstruct
    //  public void init() {
    //    mailNoreply = mailNoreplyService.findOne(MailNoreplyType.USER_EKYC_STARTED);
    //  }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public String createEkyc(Long userId) throws CustomException {
        User user =
                userRepository
                        .findById(userId)
                        .orElseThrow(
                                () -> new CustomException(ErrorCode.REQUEST_ERROR_USER_NOT_FOUND));

        UserInfo userInfo = user.getUserInfo();
        if (userInfo == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_NOT_FOUND);
        }

        if (KycStatus.DONE.equals(user.getKycStatus())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_ALREADY_COMPLETED);
        }

        // first apply: `KycStatus.DOCUMENT_WAITING_APPLY`
        // ekyc_url has expired: `KycStatus.URL_EXPIRED`
        // bo reject: `KycStatus.INFORMATION_REQUIRED` or `KycStatus.DOCUMENT_REJECTED`
        // only the following status can apply, otherwise an exception will be thrown
        if (!KycStatus.DOCUMENT_WAITING_APPLY.equals(user.getKycStatus())
                && !KycStatus.URL_EXPIRED.equals(user.getKycStatus())
                && !KycStatus.INFORMATION_REQUIRED.equals(user.getKycStatus())
                && !KycStatus.DOCUMENT_REJECTED.equals(user.getKycStatus())
                && !KycStatus.CHANGE_ATTR_APPLY_REQUESTED.equals(user.getKycStatus())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_INVALID_STATUS);
        }

        UserEkyc validUserEkyc = this.getValidUserEkyc(userId);
        if (validUserEkyc != null) {
            log.info("the user {} has a valid ekyc_url, which is returned directly", userId);
            return validUserEkyc.getUrl();
        }

        // generate referenceId
        String referenceId = StringUtils.join(userId, "T", System.currentTimeMillis());
        // build payload for applicantId
        String applicantIdPayload = this.buildApplicantIdPayload(userInfo, referenceId);

        // get applicantId
        Long applicantId;
        try {
            applicantId = this.getApplicantId(userId, applicantIdPayload);
        } catch (IOException e) {
            log.error("request applicantId fail userId: {}, msg: {}", userId, e.getMessage());
            log.error(e.getMessage(), e);
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_REQUEST_ERROR);
        }

        // create ekyc url
        String ekycUrlPayload = this.buildEkycUrlPayload(referenceId);
        String ekycUrl;

        try {
            ekycUrl = this.getEkycUrl(userId, ekycUrlPayload);
        } catch (IOException e) {
            log.error("request ekyc url fail userId: {}, msg: {}", userId, e.getMessage());
            log.error(e.getMessage(), e);
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_REQUEST_ERROR);
        }

        // create user_ekyc
        UserEkyc entity =
                UserEkyc.builder()
                        .userId(userId)
                        .docType(CommonConstants.DEFAULT_DOC_TYPE)
                        .referenceId(referenceId)
                        .applicantId(applicantId)
                        .url(ekycUrl)
                        .build();
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        repository.save(entity);

        //
        user.setKycStatus(KycStatus.DOCUMENT_WAITING_APPLY);
        userRepository.save(user);

        // send mail
        try {
            String validDate =
                    FormatUtil.formatJst(
                            new Date(System.currentTimeMillis() + eKycConfig.getEkycUrlValidTime()),
                            FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
            log.info("the user {} ekyc url validDate: {}", userId, validDate);
            mailNoreply = mailNoreplyService.findOne(MailNoreplyType.USER_EKYC_STARTED);
            MessageFormat messageFormat = new MessageFormat(this.mailNoreply.getContents());
            String[] args = new String[] {ekycUrl, validDate};
            String mailContent = messageFormat.format(args);
            sesManager.send(
                    this.mailNoreply.getFromAddress(),
                    user.getEmail(),
                    this.mailNoreply.getTitle(),
                    mailContent);
        } catch (Exception e) {
            log.error("send ekyc mail to {} error: {}", user.getEmail(), e.getMessage());
            log.error(e.getMessage(), e);
        }
        return ekycUrl;
    }

    public UserEkyc getValidUserEkyc(Long userId) {
        Date createdAt =
                Date.from(
                        LocalDateTime.now()
                                .minus(eKycConfig.getEkycUrlValidTime(), ChronoUnit.MILLIS)
                                .atZone(ZoneId.systemDefault())
                                .toInstant());
        return repository
                .findTopByUserIdAndCreatedAtGreaterThanEqualAndUrlNotNullAndApplicantIdNotNull(
                        userId, createdAt);
    }

    public boolean hasValidEkycUrl(Long userId) {
        return this.getValidUserEkyc(userId) != null;
    }

    public boolean hasValidEkycUrl(@NotNull UserEkyc userEkyc) {
        Date createdAt =
                Date.from(
                        LocalDateTime.now()
                                .minus(eKycConfig.getEkycUrlValidTime(), ChronoUnit.MILLIS)
                                .atZone(ZoneId.systemDefault())
                                .toInstant());
        return !StringUtils.isEmpty(userEkyc.getUrl())
                && userEkyc.getApplicantId() != null
                && userEkyc.getCreatedAt() != null
                && userEkyc.getCreatedAt().after(createdAt);
    }

    // -- private methods

    private void checkUserStatusWithEkycUrlValid(Long userId, KycStatus userKycStatus)
            throws CustomException {
        if (KycStatus.DONE.equals(userKycStatus)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_ALREADY_COMPLETED);
        }

        UserEkyc userEkyc = repository.findTopByUserIdOrderByIdDesc(userId);
        if (userEkyc == null || userEkyc.getApplicantId() == null) {
            return;
        }

        long current = System.currentTimeMillis();
        // The two apply times should be greater than {ekycUrlValidTime}
        long createdAt = userEkyc.getCreatedAt().toInstant().toEpochMilli();
        long ttl = current - createdAt;
        Long ekycUrlValidTime = eKycConfig.getEkycUrlValidTime();

        if (!KycStatus.DOCUMENT_REJECTED.equals(userKycStatus)
                && !StringUtils.isEmpty(userEkyc.getUrl())
                && ttl < ekycUrlValidTime) {
            log.info(
                    "the user {} request ekyc url interval is {} less than setting interval {}.",
                    userId,
                    ttl,
                    ekycUrlValidTime);
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_APPLY_FREQUENT);
        }
    }

    private Long getApplicantId(Long userId, String payload) throws IOException, CustomException {
        if (StringUtils.isEmpty(payload)) {
            // Normally it should not be null
            throw new CustomException(ErrorCode.COMMON_ERROR_SYSTEM_ERROR);
        }

        List<String> reqHeaders = new ArrayList<>(3);
        reqHeaders.addAll(this.getSignatureHeader(payload));
        reqHeaders.addAll(this.getAuthorizationToken());
        log.info(
                "get applicantId by {}; reqHeader: {} reqPayload: {}", userId, reqHeaders, payload);

        String returnPayload =
                EkycProxy.reqApi(
                        eKycConfig.getApplicantIdUrl(),
                        reqHeaders,
                        payload,
                        it ->
                                userEkycRequestHistoryService.applicationIdHistory(
                                        userId,
                                        JsonUtil.encode(reqHeaders),
                                        payload,
                                        JsonUtil.encode(it.getRespHeaders()),
                                        it.content,
                                        eKycConfig.getApplicantIdUrl()));

        log.info("response payload: {}", returnPayload);

        Map<String, String> result = JsonUtil.decode(returnPayload, Map.class);
        if (CollectionUtils.isEmpty(result)) {
            log.warn("empty result by ekyc returned, userId: {} payload: {}", userId, payload);
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_REQUEST_ERROR);
        }
        String applicantId =
                result.get(CommonConstants.APPLICANT_ID) != null
                        ? String.valueOf(result.get(CommonConstants.APPLICANT_ID))
                        : StringUtils.EMPTY;
        if (StringUtils.isEmpty(applicantId)) {
            String errorCode = result.get(CommonConstants.ERROR_CODE);
            log.info("request applicantId failed, errorCode: {}", errorCode);
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_REQUEST_ERROR);
        }

        log.info("get applicantId: {}, userId: {}", applicantId, userId);
        return Long.valueOf(applicantId);
    }

    private String getEkycUrl(Long userId, String payload) throws IOException, CustomException {

        if (StringUtils.isEmpty(payload)) {
            // TODO update error code
            throw new CustomException(ErrorCode.REQUEST_ERROR_ZIPCODE);
        }

        List<String> reqHeaders = new ArrayList<>(3);
        reqHeaders.addAll(this.getSignatureHeader(payload));
        reqHeaders.addAll(this.getAuthorizationToken());
        log.info("get ekycUrl by {}; reqHeader: {} reqPayload: {}", userId, reqHeaders, payload);

        String returnPayload =
                EkycProxy.reqApi(
                        eKycConfig.getEkycUrl(),
                        reqHeaders,
                        payload,
                        it ->
                                userEkycRequestHistoryService.ekycUrlHistory(
                                        userId,
                                        JsonUtil.encode(reqHeaders),
                                        payload,
                                        JsonUtil.encode(it.getRespHeaders()),
                                        it.content,
                                        eKycConfig.getApplicantIdUrl()));

        log.info("response payload: {}", returnPayload);

        Map<String, String> result = JsonUtil.decode(returnPayload, Map.class);
        if (CollectionUtils.isEmpty(result)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_URL_ISSUE_FAILED);
        }

        String ekycUrl = result.get(CommonConstants.EKYC_URL);
        if (StringUtils.isEmpty(ekycUrl)) {

            String errorCode = result.get(CommonConstants.ERROR_CODE);
            log.info("request ekycUrl, errorCode: {}", errorCode);
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_URL_ISSUE_FAILED);
        }

        return ekycUrl;
    }

    private String buildEkycUrlPayload(String referenceId) {
        return JsonUtil.encode(
                Map.of(
                        CommonConstants.APPLY_NO,
                        referenceId,
                        CommonConstants.API_AUTH_KEY,
                        eKycConfig.getApiAuthKey(),
                        CommonConstants.ENTERPRISE_ID,
                        eKycConfig.getEnterpriseId(),
                        CommonConstants.DOC_TYPE,
                        CommonConstants.DEFAULT_DOC_TYPE,
                        CommonConstants.TRANSITION_URL,
                        eKycConfig.getTransitionUrl()));
    }

    private String buildApplicantIdPayload(UserInfo userInfo, String referenceId)
            throws CustomException {

        if (userInfo.getLastName() == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_LAST_NAME);
        }

        if (userInfo.getFirstName() == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_FIRST_NAME);
        }

        String fullName = userInfo.getLastName() + userInfo.getFirstName();
        char[] chars = userInfo.getBirthday().toCharArray();
        String year = new String(Arrays.copyOfRange(chars, 0, 4));
        String month = new String(Arrays.copyOfRange(chars, 4, 6));
        String day = new String(Arrays.copyOfRange(chars, 6, 8));
        String birthday = year + "-" + month + "-" + day;

        String prefecture = userInfo.getPrefecture();
        String prefectureContent =
                PrefectureMapper.PREFECTURE_MAP.getOrDefault(prefecture, StringUtils.EMPTY);
        String city = userInfo.getCity();
        String town = userInfo.getAddress();
        String room = userInfo.getBuilding();
        String address = StringUtils.join(prefectureContent, city, town, room);

        return JsonUtil.encode(
                Map.of(
                        CommonConstants.REFERENCE_ID,
                        referenceId,
                        CommonConstants.NAME,
                        fullName,
                        CommonConstants.BIRTHDAY,
                        birthday,
                        CommonConstants.ADDRESS,
                        address));
    }

    List<String> getSignatureHeader(String payload) throws CustomException {
        String time =
                FormatUtil.formatJst(
                        new Date(), FormatUtil.FormatPattern.DATE_FORMAT_YYYY_T_MM_DD_HH_MM_SS_900);
        String signature = KycUtil.hmacSha256Signature(eKycConfig.getSecret(), payload, time);
        if (StringUtils.isEmpty(signature)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_EKYC_REQUEST_ERROR);
        }
        return List.of(CommonConstants.X_SIGNATURE, signature, CommonConstants.X_TIME, time);
    }

    List<String> getAuthorizationToken() {
        return List.of(
                CommonConstants.AUTHORIZATION,
                CommonConstants.AUTHORIZATION_VAL_PREFIX + eKycConfig.getToken());
    }

    @Override
    public Class<UserEkyc> getEntityClass() {
        return UserEkyc.class;
    }

    public List<UserEkyc> findOneByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<UserEkyc> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
