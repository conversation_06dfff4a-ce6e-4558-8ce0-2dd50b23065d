package point.common.service;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.component.DataSourceManager;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.entity.ChoiceVote;
import point.common.model.response.websocket.ChoiceActivityDataWrapper;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoiceActivityTotalUpdater {

    private final DataSourceManager dataSourceManager;

    @Transactional(transactionManager = "masterTransactionManager")
    public void processVote(ChoiceActivityDataWrapper dataWrapper) {
        ChoiceVote choiceVote = dataWrapper.getChoiceVote();
        log.info(
                "Processing activity ID: {}, voteId: {}, votePower: {}, direction: {}, action: {}, balanceVote: {}",
                choiceVote.getActivityId(),
                choiceVote.getId(),
                choiceVote.getVotePower(),
                choiceVote.getVoteDirection(),
                dataWrapper.getVoteAction(),
                dataWrapper.getBalanceVote());
        try {
            // Process vote based on a direction
            if (ChoiceActivityVoteResult.UP.equals(choiceVote.getVoteDirection())) {
                processUpVote(dataWrapper);
            } else {
                processDownVote(dataWrapper);
            }
        } catch (Exception e) {
            log.error(
                    "Failed to update activity({}) total, please see logs for details",
                    choiceVote.getActivityId(),
                    e);
        }
    }

    private void processUpVote(ChoiceActivityDataWrapper wrapper) {
        JdbcTemplate jdbcTemplate = dataSourceManager.getMasterJdbcTemplate();

        Map<String, Object> stats =
                jdbcTemplate.queryForMap(
                        "SELECT IFNULL(SUM(vote_power), 0) as total_power, COUNT(1) as total_users "
                                + "FROM choice_vote WHERE activity_id = ? AND vote_direction = ?",
                        wrapper.getChoiceVote().getActivityId(),
                        wrapper.getChoiceVote().getVoteDirection().name());

        jdbcTemplate.update(
                "UPDATE choice_activity SET "
                        + "total_vote_power_up = ?, "
                        + "total_vote_users_up = ?, "
                        + "updated_at = NOW() "
                        + "WHERE id = ?",
                stats.get("total_power"),
                stats.get("total_users"),
                wrapper.getChoiceVote().getActivityId());

        //        if (ChoiceActivityVoteAction.CREATE.equals(wrapper.getVoteAction())) {
        //            String sql =
        //                    "UPDATE choice_activity set total_vote_power_up =
        // IFNULL(total_vote_power_up, 0) + ?, total_vote_users_up = IFNULL(total_vote_users_up, 0)
        // + 1, updated_at = now() where id = ?";
        //            jdbcTemplate.update(
        //                    sql,
        //                    new Object[] {
        //                        wrapper.getChoiceVote().getVotePower(),
        //                        wrapper.getChoiceVote().getActivityId()
        //                    },
        //                    new int[] {Types.BIGINT, Types.BIGINT});
        //        } else {
        //            String sql =
        //                    "UPDATE choice_activity set total_vote_power_up =
        // IFNULL(total_vote_power_up, 0) + ?, updated_at = now() where id = ?";
        //            jdbcTemplate.update(
        //                    sql,
        //                    new Object[] {
        //                        wrapper.getBalanceVote(), wrapper.getChoiceVote().getActivityId()
        //                    },
        //                    new int[] {Types.BIGINT, Types.BIGINT});
        //        }
    }

    private void processDownVote(ChoiceActivityDataWrapper wrapper) {
        JdbcTemplate jdbcTemplate = dataSourceManager.getMasterJdbcTemplate();
        Map<String, Object> stats =
                jdbcTemplate.queryForMap(
                        "SELECT IFNULL(SUM(vote_power), 0) as total_power, COUNT(1) as total_users "
                                + "FROM choice_vote WHERE activity_id = ? AND vote_direction = ?",
                        wrapper.getChoiceVote().getActivityId(),
                        wrapper.getChoiceVote().getVoteDirection().name());

        jdbcTemplate.update(
                "UPDATE choice_activity SET "
                        + "total_vote_power_down = ?, "
                        + "total_vote_users_down = ?, "
                        + "updated_at = NOW() "
                        + "WHERE id = ?",
                stats.get("total_power"),
                stats.get("total_users"),
                wrapper.getChoiceVote().getActivityId());
        //        if (ChoiceActivityVoteAction.CREATE.equals(wrapper.getVoteAction())) {
        //            String sql =
        //                    "UPDATE choice_activity set total_vote_power_down =
        // IFNULL(total_vote_power_down, 0) + ?, total_vote_users_down =
        // IFNULL(total_vote_users_down, 0) + 1, updated_at = now() where id = ?";
        //            jdbcTemplate.update(
        //                    sql,
        //                    new Object[] {
        //                        wrapper.getChoiceVote().getVotePower(),
        //                        wrapper.getChoiceVote().getActivityId()
        //                    },
        //                    new int[] {Types.BIGINT, Types.BIGINT});
        //        } else {
        //            log.info("{}", wrapper.getBalanceVote());
        //            String sql =
        //                    "UPDATE choice_activity set total_vote_power_down =
        // IFNULL(total_vote_power_down, 0) + ?, updated_at = now() where id = ?";
        //            jdbcTemplate.update(
        //                    sql,
        //                    new Object[] {
        //                        wrapper.getBalanceVote(), wrapper.getChoiceVote().getActivityId()
        //                    },
        //                    new int[] {Types.BIGINT, Types.BIGINT});
        //        }
    }
}
