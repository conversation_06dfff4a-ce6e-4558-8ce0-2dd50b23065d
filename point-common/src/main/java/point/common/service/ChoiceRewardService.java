package point.common.service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.WithdrawStatusType;
import point.common.entity.ChoiceReward;
import point.common.model.request.ChoiceRewardSearchForm;
import point.common.model.response.ChoiceRewardPageData;
import point.common.model.response.PageData;
import point.common.predicate.ChoiceRewardPredicate;

@Service
@RequiredArgsConstructor
public class ChoiceRewardService extends EntityService<ChoiceReward, ChoiceRewardPredicate> {

    public PageData<ChoiceRewardPageData> findRewardByCondition(ChoiceRewardSearchForm form) {

        int num = form.getNumber();
        int size = form.getSize();
        final var condition = this.buildCondition(form);
        try {
            return customTransactionManager.execute(
                    entityManager -> {
                        long count = countByForm(entityManager, condition);

                        return new PageData<ChoiceRewardPageData>(
                                num,
                                size,
                                count,
                                getResultByForm(entityManager, condition, num, size));
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public ChoiceReward findTodayReward(List<Long> userId, Date date) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceReward query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.userIdIn(criteriaBuilder, root, userId));
                        predicates.add(predicate.equalRewardTime(criteriaBuilder, root, date));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public ChoiceReward findRewardByBetweenDates(List<Long> userId, Date start, Date end) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceReward query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.userIdIn(criteriaBuilder, root, userId));
                        predicates.add(
                                predicate.rewardTimeGreaterThan(criteriaBuilder, root, start));
                        predicates.add(predicate.rewardTimeLessThan(criteriaBuilder, root, end));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public ChoiceReward findRewardByActivityId(List<Long> userId, Long activityId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceReward query() {
                        List<Predicate> predicates = new ArrayList<>(2);
                        predicates.add(predicate.userIdIn(criteriaBuilder, root, userId));
                        predicates.add(
                                predicate.equalActivityId(criteriaBuilder, root, activityId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    protected long countByForm(
            EntityManager entityManager, Pair<String, Map<String, Object>> condition) {
        var baseSql =
                new StringBuilder("SELECT ")
                        .append("    count(cr.id)")
                        .append(" FROM choice_reward cr ")
                        .append(
                                " left join choice_vote vo on vo.user_id = cr.user_id and vo.activity_id = cr.activity_id ")
                        .append(" left join choice_power_user_rel pur on pur.user_id= cr.user_id ")
                        .append(" left join choice_power cp on pur.choice_power_id= cp.id ");
        var countQuery =
                entityManager.createNativeQuery(baseSql.append(condition.getFirst()).toString());
        var params = condition.getSecond();
        params.forEach(countQuery::setParameter);
        return ((BigInteger) countQuery.getSingleResult()).longValue();
    }

    protected List<ChoiceRewardPageData> getResultByForm(
            EntityManager entityManager,
            Pair<String, Map<String, Object>> condition,
            int num,
            int size) {
        var baseSql =
                new StringBuilder("SELECT ")
                        .append("    cr.id as rewardId,")
                        .append("    cr.user_id as userId,")
                        .append("    'updateId' as updateUserId,")
                        .append("    cr.amount as rewardAmount,")
                        .append("    cr.reward_time as rewardDate,")
                        .append("    cp.choice_p_amount as choicePAmount,")
                        .append("    CASE ")
                        .append(
                                "        WHEN cr.withdraw_status != 'PENDING' THEN cr.amount - ifNull(cr.remaining_amount,0) ")
                        .append("        ELSE null ")
                        .append("    END as withdrawAmount,")
                        .append(
                                "    IF(cr.withdraw_status in ('FULLY_WITHDRAWN', 'PARTIALLY_WITHDRAWN'), cr.updated_at, null)  as withdrawDate,")
                        .append("    cr.withdraw_status as withdrawStatus ")
                        .append("FROM choice_reward cr ")
                        .append(
                                "left join choice_vote vo on vo.user_id = cr.user_id and vo.activity_id = cr.activity_id ")
                        .append("left join choice_power_user_rel pur on pur.user_id= cr.user_id ")
                        .append("left join choice_power cp on pur.choice_power_id= cp.id ");
        baseSql.append(condition.getFirst()).append(" limit :size offset :over");
        var params = condition.getSecond();
        params.put("size", size);
        params.put("over", size * num);
        var query =
                entityManager.createNativeQuery(baseSql.toString(), "ChoiceRewardPageDataMapping");
        params.forEach(query::setParameter);
        return query.getResultList();
    }

    protected Pair<String, Map<String, Object>> buildCondition(ChoiceRewardSearchForm form) {
        var condition = new StringBuilder();
        condition.append("where 1=1 ");
        var params = new HashMap<String, Object>();
        if (Objects.nonNull(form.getUserId())) {
            condition.append(" AND cr.user_id=:user_id");
            params.put("user_id", form.getUserId());
        }

        //    if (Objects.nonNull(form.getUpdateUserId())) {
        //      sql.append(" AND vo.update_user_id=:update_user_id");
        //      params.put("update_user_id", form.getUpdateUserId());
        //    }

        if (Objects.nonNull(form.getRewardPointsFrom())) {
            condition.append(" AND cr.amount >= :rewardAmountFrom ");
            params.put("rewardAmountFrom", form.getRewardPointsFrom());
        }

        if (Objects.nonNull(form.getRewardPointsTo())) {
            condition.append(" AND cr.amount <= :rewardAmountTo ");
            params.put("rewardAmountTo", form.getRewardPointsTo());
        }

        if (Objects.nonNull(form.getRewardDateFrom())) {
            condition.append(" AND cr.reward_time >= :rewardTimeFrom ");
            params.put("rewardTimeFrom", new Date(form.getRewardDateFrom()));
        }

        if (Objects.nonNull(form.getRewardDateTo())) {
            condition.append(" AND cr.reward_time <= :rewardTimeTo");
            params.put("rewardTimeTo", new Date(form.getRewardDateTo()));
        }
        if (Objects.nonNull(form.getRewardBalanceFrom())) {
            condition.append(" AND cp.choice_p_amount >= :choicePAmountFrom");
            params.put("choicePAmountFrom", form.getRewardBalanceFrom());
        }
        if (Objects.nonNull(form.getRewardBalanceTo())) {
            condition.append(" AND cp.choice_p_amount <= :choicePAmountTo");
            params.put("choicePAmountTo", form.getRewardBalanceTo());
        }

        if (Objects.nonNull(form.getWithdrawalDateFrom())
                || Objects.nonNull(form.getWithdrawalDateTo())) {
            condition.append(" AND cr.withdraw_status != :withdrawStatusPending");
            params.put("withdrawStatusPending", WithdrawStatusType.PENDING.toString());
        }

        //
        if (Objects.nonNull(form.getWithdrawalDateFrom())) {
            condition.append(" AND cr.updated_at >= :withdrawDateFrom");
            params.put("withdrawDateFrom", new Date(form.getWithdrawalDateFrom()));
        }
        //
        if (Objects.nonNull(form.getWithdrawalDateTo())) {
            condition.append(" AND cr.updated_at <= :withdrawDateTo");
            params.put("withdrawDateTo", new Date(form.getWithdrawalDateTo()));
        }

        return Pair.of(condition.toString(), params);
    }

    @Override
    public Class<ChoiceReward> getEntityClass() {
        return ChoiceReward.class;
    }

    public ChoiceReward findByCondition(Long userId, Long activityId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public ChoiceReward query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(
                                predicate.equalActivityId(criteriaBuilder, root, activityId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<ChoiceReward> findByCondition(Long userId, List<WithdrawStatusType> withdrawType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<>() {
                    @Override
                    public List<ChoiceReward> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        predicates.add(
                                predicate.withdrawStatusTypeIn(
                                        criteriaBuilder, root, withdrawType));
                        predicates.add(predicate.lessThanExpireTime(criteriaBuilder, root));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
