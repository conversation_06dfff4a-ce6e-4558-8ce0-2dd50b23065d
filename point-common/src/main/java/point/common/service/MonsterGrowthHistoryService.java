package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.entity.*;
import point.common.model.response.PageData;
import point.common.predicate.MonsterGrowthHistoryPredicate;

@RequiredArgsConstructor
@Service
public class MonsterGrowthHistoryService
        extends EntityService<MonsterGrowthHistory, MonsterGrowthHistoryPredicate> {

    @Override
    public Class<MonsterGrowthHistory> getEntityClass() {
        return MonsterGrowthHistory.class;
    }

    public PageData<MonsterGrowthHistory> getTradeSummaryByCondition(
            Long userId,
            Long symbolId,
            Date dateFromDate,
            Date dateToDate,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<MonsterGrowthHistory>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                symbolId,
                                                dateFromDate,
                                                dateToDate,
                                                number,
                                                size));
                            }
                        });

        return new PageData<MonsterGrowthHistory>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                MonsterGrowthHistory, List<MonsterGrowthHistory>>() {
                            @Override
                            public List<MonsterGrowthHistory> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                symbolId,
                                                dateFromDate,
                                                dateToDate,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(UserMonsterInfo_.id)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<MonsterGrowthHistory> root,
            Long userId,
            Long symbolId,
            Date dateFromDate,
            Date dateToDate,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));

        if (symbolId != null) {
            predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
        }
        if (dateFromDate != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, dateFromDate));
        }
        if (dateToDate != null) {
            predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, dateToDate));
        }
        return predicates;
    }
}
