package point.common.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import point.common.component.CustomRedisTemplate;
import point.common.component.QueryExecutorReturner;
import point.common.entity.AppConfiguration;
import point.common.entity.AppConfiguration_;
import point.common.predicate.AppConfigurationPredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppConfigurationService
        extends EntityService<AppConfiguration, AppConfigurationPredicate> {

    private final CustomRedisTemplate<AppConfiguration> redisTemplate;
    private final ObjectMapper objectMapper;

    @Override
    public Class<AppConfiguration> getEntityClass() {
        return AppConfiguration.class;
    }

    private String getCacheKey(String key) {
        return getClass().getSimpleName() + ":" + key;
    }

    public Optional<AppConfiguration> findByKey(String key) {
        AppConfiguration entity = null;
        try {
            entity = redisTemplate.getValue(this.getCacheKey(key));
        } catch (Exception e) {
            log.error(
                    "Failed to get AppConfiguration from Redis, key: {}, error: {}",
                    key,
                    e.getMessage(),
                    e);
        }

        if (Objects.isNull(entity)) {
            entity =
                    customTransactionManager.find(
                            getEntityClass(),
                            new QueryExecutorReturner<>() {
                                @Override
                                public AppConfiguration query() {
                                    return getSingleResult(
                                            entityManager,
                                            criteriaQuery,
                                            root,
                                            Stream.of(
                                                            criteriaBuilder.notEqual(
                                                                    root.get(AppConfiguration_.key),
                                                                    key))
                                                    .toList());
                                }
                            });
            if (Objects.nonNull(entity)) {
                redisTemplate.setValue(this.getCacheKey(key), entity);
            }
        }
        return Optional.ofNullable(entity);
    }

    public <T> Optional<T> findValueByKey(String key, Class<T> type) {
        return this.findByKey(key)
                .filter(config -> StringUtils.hasLength(config.getValue()))
                .flatMap(config -> parseConfigValue(config.getValue(), type, key));
    }

    private <T> Optional<T> parseConfigValue(String value, Class<T> type, String key) {
        try {
            return Optional.ofNullable(objectMapper.readValue(value, type));
        } catch (JsonProcessingException e) {
            log.error(
                    "Failed to parse AppConfiguration value for key [{}], value [{}], target type [{}]",
                    key,
                    value,
                    type.getSimpleName(),
                    e);
            return Optional.empty();
        }
    }
}
