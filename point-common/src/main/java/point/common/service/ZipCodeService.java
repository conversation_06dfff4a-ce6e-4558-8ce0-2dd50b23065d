package point.common.service;

import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.entity.ZipCode;
import point.common.model.response.ZipCodeData;
import point.common.repos.ZipCodeRepository;

@Slf4j
@Service
@RequiredArgsConstructor
public class ZipCodeService {

    public final ZipCodeRepository zipCodeRepository;

    public Optional<ZipCodeData> search(String zipCode) {
        List<ZipCode> byZipCodes = zipCodeRepository.findByZipCodeOrderById(zipCode);
        if (byZipCodes != null && !byZipCodes.isEmpty()) {
            return Optional.of(new ZipCodeData(byZipCodes.get(0)));
        }
        return Optional.empty();
    }
}
