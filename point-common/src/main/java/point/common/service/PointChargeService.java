package point.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import point.common.entity.PointCharge;
import point.common.predicate.PointChargePredicate;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointChargeService extends EntityService<PointCharge, PointChargePredicate> {
    @Override
    public Class<PointCharge> getEntityClass() {
        return PointCharge.class;
    }
}
