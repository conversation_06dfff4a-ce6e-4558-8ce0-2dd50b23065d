package point.common.service;

import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SMSAttemptService {

    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${request.limit.count:5}")
    private Integer requestLimitCount;

    @Value("${request.limit.seconds:86400}")
    private Integer requestLimitSeconds;

    protected String getCacheKey(Long userId, String phoneNumber) {
        return "userSMSAttempt:phoneNumber:" + userId + ":" + phoneNumber;
    }

    public void clear(Long userId, String phoneNumber) {
        redisTemplate.delete(getCacheKey(userId, phoneNumber));
    }

    public boolean isLimitExceeded(Long userId, String phoneNumber) {
        String currentUserCacheKey = getCacheKey(userId, phoneNumber);
        Integer newNum = (Integer) redisTemplate.opsForValue().get(currentUserCacheKey);
        log.info(
                "requests count: {}, request limit: {} times, request limit: {} seconds",
                newNum,
                requestLimitCount,
                requestLimitSeconds);
        return newNum != null && newNum >= requestLimitCount;
    }

    public void countSmsSent(Long userId, String phoneNumber) {
        String currentUserCacheKey = getCacheKey(userId, phoneNumber);
        if (Boolean.FALSE.equals(redisTemplate.hasKey(currentUserCacheKey))) {
            redisTemplate
                    .opsForValue()
                    .set(currentUserCacheKey, 1, requestLimitSeconds, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().increment(currentUserCacheKey, 1);
        }
    }
}
