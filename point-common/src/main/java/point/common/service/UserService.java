package point.common.service;

import static point.common.entity.AbstractEntity_.updatedAt;

import com.google.common.collect.Lists;
import java.util.*;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.KycStatus;
import point.common.constant.UserStatus;
import point.common.entity.User;
import point.common.entity.UserKyc;
import point.common.entity.User_;
import point.common.model.response.PageData;
import point.common.predicate.UserPredicate;
import point.common.util.CollectionUtil;

@RequiredArgsConstructor
@Service
public class UserService extends EntityService<User, UserPredicate> implements UserDetailsService {

    @Override
    public Class<User> getEntityClass() {
        return User.class;
    }

    @Override
    public User loadUserByUsername(String username) throws UsernameNotFoundException {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, User>() {
                    @Override
                    public User query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalEmail(criteriaBuilder, root, username));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    /** use this method for update AuthenticationPrincipal together. */
    public User saveWithAuthenticationPrincipal(User user) {
        user = super.save(user);
        SecurityContextHolder.getContext()
                .setAuthentication(
                        new UsernamePasswordAuthenticationToken(
                                user, user.getPassword(), user.getAuthorities()));
        return user;
    }

    public User saveWithAuthenticationPrincipal(User user, EntityManager entityManager)
            throws Exception {
        user = super.save(user, entityManager);
        SecurityContextHolder.getContext()
                .setAuthentication(
                        new UsernamePasswordAuthenticationToken(
                                user, user.getPassword(), user.getAuthorities()));
        return user;
    }

    public User save(User user) {
        return super.save(user);
    }

    @Override
    protected void fetch(Root<User> root) {
        super.fetch(root);
        root.fetch(User_.authorities, JoinType.LEFT);
        root.fetch(User_.userInfo, JoinType.LEFT);
        root.fetch(User_.userKyc, JoinType.LEFT);
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<User> root,
            Long id,
            Long idFrom,
            Long idTo,
            String email,
            String firstName,
            String lastName,
            String firstKana,
            String lastKana,
            UserStatus userStatus,
            Long affiliateInfoId,
            String uuid,
            Boolean insideAccountFlg,
            KycStatus kycStatus,
            Long dateFrom,
            Long dateTo) {
        List<Predicate> predicates = new ArrayList<>();

        if (id != null) {
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
        } else {
            if (idFrom != null) {
                predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
            }

            if (idTo != null) {
                predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
            }

            if (!StringUtils.isEmpty(email)) {
                predicates.add(predicate.likeEmail(criteriaBuilder, root, email));
            }

            if (!StringUtils.isEmpty(firstName)) {
                predicates.add(predicate.likeFirstName(criteriaBuilder, root, firstName));
            }

            if (!StringUtils.isEmpty(lastName)) {
                predicates.add(predicate.likeLastName(criteriaBuilder, root, lastName));
            }

            if (!StringUtils.isEmpty(firstKana)) {
                predicates.add(predicate.likeFirstKana(criteriaBuilder, root, firstKana));
            }

            if (!StringUtils.isEmpty(lastKana)) {
                predicates.add(predicate.likeLastKana(criteriaBuilder, root, lastKana));
            }

            if (userStatus != null) {
                predicates.add(predicate.equalUserStatus(criteriaBuilder, root, userStatus));
            }

            if (affiliateInfoId != null) {
                predicates.add(
                        predicate.equalAffiliateInfoId(criteriaBuilder, root, affiliateInfoId));
            }

            if (!StringUtils.isEmpty(uuid)) {
                predicates.add(predicate.equalUuid(criteriaBuilder, root, uuid));
            }

            if (insideAccountFlg != null) {
                predicates.add(
                        predicate.isInsideAccountFlg(criteriaBuilder, root, insideAccountFlg));
            }

            if (kycStatus != null) {
                predicates.add(predicate.equalKycStatus(criteriaBuilder, root, kycStatus));
            }

            if (dateFrom != null) {
                predicates.add(
                        predicate.greaterThanOrEqualToCreatedAt(
                                criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
                predicates.add(
                        predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
            }
        }

        return predicates;
    }

    public PageData<User> findByCondition(
            Long id,
            Long idFrom,
            Long idTo,
            String email,
            String firstName,
            String lastName,
            String firstKana,
            String lastKana,
            UserStatus userStatus,
            Long affiliateInfoId,
            String uuid,
            Boolean insideAccountFlg,
            KycStatus kycStatus,
            Long dateFrom,
            Long dateTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                idFrom,
                                                idTo,
                                                email,
                                                firstName,
                                                lastName,
                                                firstKana,
                                                lastKana,
                                                userStatus,
                                                affiliateInfoId,
                                                uuid,
                                                insideAccountFlg,
                                                kycStatus,
                                                dateFrom,
                                                dateTo));
                            }
                        });

        return new PageData<User>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<User, List<User>>() {
                            @Override
                            public List<User> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                id,
                                                idFrom,
                                                idTo,
                                                email,
                                                firstName,
                                                lastName,
                                                firstKana,
                                                lastKana,
                                                userStatus,
                                                affiliateInfoId,
                                                uuid,
                                                insideAccountFlg,
                                                kycStatus,
                                                dateFrom,
                                                dateTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(updatedAt)));
                            }
                        }));
    }

    public User findByCondition(String email, KycStatus kycStatus) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, User>() {
                    @Override
                    public User query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
                        predicates.add(predicate.equalKycStatus(criteriaBuilder, root, kycStatus));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public User findByEmailAndStatus(String email, UserStatus userStatus) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, User>() {
                    @Override
                    public User query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
                        predicates.add(
                                predicate.equalUserStatus(criteriaBuilder, root, userStatus));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public User findByEmailAndNotStatus(String email, UserStatus userStatus) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, User>() {
                    @Override
                    public User query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
                        predicates.add(
                                predicate.notEqualUserStatus(criteriaBuilder, root, userStatus));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public User findByEmail(String email) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, User>() {
                    @Override
                    public User query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalEmail(criteriaBuilder, root, email));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByEnabled() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalEnabled(criteriaBuilder, root, true));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public User findByUserKyc(UserKyc userKyc) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, User>() {
                    @Override
                    public User query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalId(criteriaBuilder, root, userKyc.getUserId()));
                        predicates.add(
                                predicate.equalUserKycId(criteriaBuilder, root, userKyc.getId()));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByIdOrName(String query) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.likeFirstName(criteriaBuilder, root, query));
                        predicates.add(predicate.likeLastName(criteriaBuilder, root, query));
                        predicates.add(
                                predicate.equalId(criteriaBuilder, root, Long.parseLong(query)));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public User findById(Long id) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, User>() {
                    @Override
                    public User query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalId(criteriaBuilder, root, id));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByUpdatedAt(Long updatedAtFrom, Long updatedAtTo) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.greaterThanOrEqualToUpdatedAt(
                                        criteriaBuilder, root, new Date(updatedAtFrom)));
                        predicates.add(
                                predicate.lessThanUpdatedAt(
                                        criteriaBuilder, root, new Date(updatedAtTo)));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByTradeUncapped(boolean isTradeUncapped) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.isTradeUncapped(criteriaBuilder, root, isTradeUncapped));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByInsideAccountFlg(boolean isInsideAccountFlg) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.isInsideAccountFlg(
                                        criteriaBuilder, root, isInsideAccountFlg));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByStatus(UserStatus userStatus) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalUserStatus(criteriaBuilder, root, userStatus));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByKycStatus(KycStatus[] kycStatus) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.inKycStatus(criteriaBuilder, root, kycStatus));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByKycStatusAndUpdateAt(KycStatus[] kycStatus, Date updatedAt) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.inKycStatus(criteriaBuilder, root, kycStatus));
                        if (null != updatedAt) {
                            predicates.add(
                                    predicate.greaterThanOrEqualToUpdatedAt(
                                            criteriaBuilder, root, updatedAt));
                        }
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByKycStatusAndIds(KycStatus[] kycStatus, Set<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.inKycStatus(criteriaBuilder, root, kycStatus));
                        predicates.add(predicate.inId(root, Lists.newArrayList(ids)));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public List<User> findByCreatedAt(Long createdAtTo) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<User, List<User>>() {
                    @Override
                    public List<User> query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.lessThanCreatedAt(
                                        criteriaBuilder, root, new Date(createdAtTo)));
                        return getResultList(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
