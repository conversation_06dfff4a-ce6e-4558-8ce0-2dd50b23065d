package point.common.service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import point.common.constant.CommonConstants;
import point.common.constant.UserNoteTypeEnum;
import point.common.entity.UserNote;
import point.common.model.dto.UserNoteDTO;
import point.common.model.request.UserNoteCreateForm;
import point.common.predicate.UserNotePredicate;
import point.common.repos.UserNoteRepository;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
@RequiredArgsConstructor
public class UserNoteService extends EntityService<UserNote, UserNotePredicate> {

    private final UserNoteRepository repository;

    public Optional<UserNoteDTO> findByUserId(Long userId) {
        UserNote userNote =
                repository.findTopByUserIdAndNoteTypeOrderByCreatedAtDesc(
                        userId, UserNoteTypeEnum.DEFAULT);
        if (Objects.isNull(userNote)) {
            return Optional.empty();
        }
        return Optional.of(
                UserNoteDTO.builder()
                        .note(userNote.getNote())
                        .noteType(userNote.getNoteType())
                        .userId(userNote.getUserId())
                        .operator(userNote.getOperator())
                        .currentKycStatus(userNote.getCurrentKycStatus())
                        .build());
    }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void addUserNote(UserNoteCreateForm form) {
        UserNote userNote = new UserNote();
        userNote.setUserId(form.getUserId());
        userNote.setNoteType(form.getType());
        userNote.setOperator(form.getOperator());
        userNote.setCurrentKycStatus(form.getCurrentKycStatus());
        userNote.setNote(form.getNote());
        userNote.setCreatedAt(new Date());
        userNote.setUpdatedAt(new Date());
        repository.save(userNote);
    }

    public List<UserNoteDTO> findByList(Long userId) {
        List<UserNote> userNotes = repository.findByUserId(userId);
        return userNotes.stream()
                .map(
                        userNote ->
                                UserNoteDTO.builder()
                                        .note(userNote.getNote())
                                        .noteType(userNote.getNoteType())
                                        .userId(userNote.getUserId())
                                        .operator(userNote.getOperator())
                                        .currentKycStatus(userNote.getCurrentKycStatus())
                                        .createdAt(userNote.getCreatedAt())
                                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public Class<UserNote> getEntityClass() {
        return UserNote.class;
    }
}
