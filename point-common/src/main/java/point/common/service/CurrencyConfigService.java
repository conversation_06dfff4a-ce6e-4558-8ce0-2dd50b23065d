package point.common.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.Currency;
import point.common.constant.TradeType;
import point.common.entity.CurrencyConfig;
import point.common.entity.CurrencyConfig_;
import point.common.predicate.CurrencyConfigPredicate;

@Service
public class CurrencyConfigService extends EntityService<CurrencyConfig, CurrencyConfigPredicate> {

    @Override
    public Class<CurrencyConfig> getEntityClass() {
        return CurrencyConfig.class;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<CurrencyConfig> root,
            TradeType tradeType,
            Currency currency) {
        List<Predicate> predicates = new ArrayList<>();

        if (currency != null) {
            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
        }
        if (tradeType != null) {
            predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
        }

        return predicates;
    }

    public CurrencyConfig findByCurrency(Currency currency, TradeType tradeType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<CurrencyConfig, CurrencyConfig>() {
                    @Override
                    public CurrencyConfig query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
                        predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public CurrencyConfig findByCurrency(Currency currency, EntityManager entityManager) {
        return new QueryExecutorReturner<CurrencyConfig, CurrencyConfig>() {
            @Override
            public CurrencyConfig query() {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
                return getSingleResult(entityManager, criteriaQuery, root, predicates);
            }
        }.execute(getEntityClass(), entityManager);
    }

    public List<CurrencyConfig> findAllByCondition(
            TradeType tradeType, Currency currency, boolean enabled) {
        return this.findAllByCondition(tradeType, currency, enabled, null);
    }

    public List<CurrencyConfig> findAllByCondition(
            TradeType tradeType, Currency currency, boolean enabled, EntityManager entityManager) {
        return Objects.isNull(entityManager)
                ? customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {

                            @Override
                            public List<CurrencyConfig> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder, root, tradeType, currency);
                                predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(CurrencyConfig_.id)));
                            }
                        })
                : customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {

                            @Override
                            public List<CurrencyConfig> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder, root, tradeType, currency);
                                predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(CurrencyConfig_.id)));
                            }
                        },
                        entityManager);
    }

    public List<CurrencyConfig> findByConditionWithFirstSetFlg(
            TradeType tradeType, Currency currency, boolean enabled, boolean firstSetFlg) {
        List<CurrencyConfig> currencyConfig =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<CurrencyConfig, List<CurrencyConfig>>() {

                            @Override
                            public List<CurrencyConfig> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder, root, tradeType, currency);
                                predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
                                predicates.add(
                                        predicate.isFirstSet(criteriaBuilder, root, firstSetFlg));
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(CurrencyConfig_.id)));
                            }
                        });

        return currencyConfig;
    }

    public List<CurrencyConfig> findAllByCondition(TradeType tradeType, Currency currency) {
        List<CurrencyConfig> currencyConfig =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<CurrencyConfig, List<CurrencyConfig>>() {

                            @Override
                            public List<CurrencyConfig> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder, root, tradeType, currency);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(CurrencyConfig_.id)));
                            }
                        });

        return currencyConfig;
    }

    public List<CurrencyConfig> findByCondition(Date createdAt, boolean enabled) {
        List<CurrencyConfig> currencyConfig =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<CurrencyConfig, List<CurrencyConfig>>() {

                            @Override
                            public List<CurrencyConfig> query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.lessThanCreatedAt(
                                                criteriaBuilder, root, createdAt));
                                predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(CurrencyConfig_.id)));
                            }
                        });

        return currencyConfig;
    }
}
