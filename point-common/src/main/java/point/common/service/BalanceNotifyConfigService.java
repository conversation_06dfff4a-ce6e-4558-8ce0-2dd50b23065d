package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorReturner;
import point.common.constant.Exchange;
import point.common.entity.BalanceNotifyConfig;
import point.common.entity.BalanceNotifyConfig_;
import point.common.predicate.BalanceNotifyConfigPredicate;

@Service
public class BalanceNotifyConfigService
        extends EntityService<BalanceNotifyConfig, BalanceNotifyConfigPredicate> {

    @Override
    public Class<BalanceNotifyConfig> getEntityClass() {
        return BalanceNotifyConfig.class;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<BalanceNotifyConfig> root,
            String currency,
            Exchange exchange,
            Boolean enabled) {
        List<Predicate> predicates = new ArrayList<>();

        if (currency != null) {
            predicates.add(predicate.equalCurrency(criteriaBuilder, root, currency));
        }

        if (exchange != null) {
            predicates.add(predicate.equalExchange(criteriaBuilder, root, exchange));
        }
        if (enabled != null) {
            predicates.add(predicate.equalEnabled(criteriaBuilder, root, enabled));
        }
        return predicates;
    }

    public List<BalanceNotifyConfig> findAllByCondition(
            String currency, Exchange point, Boolean enabled) {
        List<BalanceNotifyConfig> balanceNotifyConfig =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                BalanceNotifyConfig, List<BalanceNotifyConfig>>() {

                            @Override
                            public List<BalanceNotifyConfig> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder, root, currency, point, enabled);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.asc(root.get(BalanceNotifyConfig_.id)));
                            }
                        });

        return balanceNotifyConfig;
    }
}
