package point.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import point.common.component.CustomLogger;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ErrorCode;
import point.common.entity.OnetimeBankAccount;
import point.common.entity.OnetimeBankAccount_;
import point.common.exception.GmoRequestException;
import point.common.model.response.PageData;
import point.common.model.response.Va;
import point.common.model.response.VaIssueResponse;
import point.common.predicate.OnetimeBankAccountPredicate;
import point.common.repos.OneTimeBankAccountServiceRepository;

@RequiredArgsConstructor
@Service
public class OnetimeBankAccountService
        extends EntityService<OnetimeBankAccount, OnetimeBankAccountPredicate> {
    @Autowired GmoService gmoService;

    @Autowired OneTimeBankAccountServiceRepository oneTimeBankAccountServiceRepository;
    private static final CustomLogger log = new CustomLogger(OnetimeBankAccount.class.getName());

    @Override
    public Class<OnetimeBankAccount> getEntityClass() {
        return OnetimeBankAccount.class;
    }

    @Override
    protected void fetch(Root<OnetimeBankAccount> root) {
        super.fetch(root);
        root.fetch(OnetimeBankAccount_.user, JoinType.LEFT);
    }

    public void issueMigrationUserVirtualAccount() throws GmoRequestException {
        List<Long> userIds = oneTimeBankAccountServiceRepository.findMigrationUser();
        for (int i = 0; i < userIds.size(); i++) {
            log.info(getClass().getName(), "issue virtual account for user:" + userIds.get(i) + "");
            OnetimeBankAccount onetimeBankAccount = issueVirtualAccount(userIds.get(i));
            log.info(
                    getClass().getName(),
                    "issue virtual account successfully,account number:"
                            + onetimeBankAccount.getAccountNumber());
        }
    }

    public OnetimeBankAccount issueVirtualAccount(Long userId) throws GmoRequestException {

        OnetimeBankAccount onetimeBankAccount = findOneByUserId(userId);

        if (onetimeBankAccount != null) {
            throw new GmoRequestException(
                    String.valueOf(ErrorCode.ONE_TIME_BANK_ACCOUNT_EXISTS.getCode()));
        }

        VaIssueResponse response = gmoService.issueAccount("2");
        if (ObjectUtils.isEmpty(response) || CollectionUtils.isEmpty(response.getVaList())) {
            throw new GmoRequestException(
                    "return result is empty, check the gmo create account interface");
        }
        Va va = response.getVaList().get(0);
        OnetimeBankAccount account = new OnetimeBankAccount();
        account.setAccountNumber(va.getVaAccountNumber());
        account.setBranchName(va.getVaBranchNameKana());
        account.setBranchCode(va.getVaBranchCode());
        account.setUserId(userId);
        account.setVaTypeCode(response.getVaTypeCode());
        account.setVaTypeName(response.getVaTypeName());
        account.setVaHolderNameKana(response.getVaHolderNameKana());
        account.setVaId(va.getVaId());
        save(account);
        return account;
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<OnetimeBankAccount> root,
            Long userId,
            Long accountNumber,
            String branchName,
            Integer number,
            Integer size) {
        List<Predicate> predicates = new ArrayList<>();

        if (userId != null) {
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
        }

        if (accountNumber != null) {
            predicates.add(predicate.equalAccountNumber(criteriaBuilder, root, accountNumber));
        }

        if (branchName != null) {
            predicates.add(predicate.likeBranchName(criteriaBuilder, root, branchName));
        }

        return predicates;
    }

    public PageData<OnetimeBankAccount> findByConditionPageData(
            Long userId, Long accountNumber, String branchName, Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                accountNumber,
                                                branchName,
                                                number,
                                                size));
                            }
                        });

        return new PageData<OnetimeBankAccount>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<OnetimeBankAccount, List<OnetimeBankAccount>>() {
                            @Override
                            public List<OnetimeBankAccount> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                accountNumber,
                                                branchName,
                                                number,
                                                size);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(root.get(OnetimeBankAccount_.id)));
                            }
                        }));
    }

    public List<OnetimeBankAccount> findAllByCondition(
            Long userId, Long accountNumber, String branchName) {
        List<OnetimeBankAccount> dep =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<OnetimeBankAccount, List<OnetimeBankAccount>>() {
                            @Override
                            public List<OnetimeBankAccount> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                userId,
                                                accountNumber,
                                                branchName,
                                                0,
                                                Integer.MAX_VALUE);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        criteriaBuilder.desc(root.get(OnetimeBankAccount_.id)));
                            }
                        });
        return dep;
    }

    public List<OnetimeBankAccount> findByCondition(
            Long userId, Long accountNumber, String branchName, Integer number, Integer size) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<OnetimeBankAccount, List<OnetimeBankAccount>>() {
                    @Override
                    public List<OnetimeBankAccount> query() {
                        List<Predicate> predicates = new ArrayList<>();

                        if (userId != null) {
                            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        }

                        if (accountNumber != null) {
                            predicates.add(
                                    predicate.equalAccountNumber(
                                            criteriaBuilder, root, accountNumber));
                        }

                        if (branchName != null) {
                            predicates.add(
                                    predicate.likeBranchName(criteriaBuilder, root, branchName));
                        }
                        return getResultList(
                                entityManager,
                                criteriaQuery,
                                root,
                                predicates,
                                number,
                                size,
                                criteriaBuilder.desc(root.get(OnetimeBankAccount_.id)));
                    }
                });
    }

    public OnetimeBankAccount findOneByUserId(Long userId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<OnetimeBankAccount, OnetimeBankAccount>() {
                    @Override
                    public OnetimeBankAccount query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public OnetimeBankAccount findOneByUserIdIsNull() {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<OnetimeBankAccount, OnetimeBankAccount>() {
                    @Override
                    public OnetimeBankAccount query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(predicate.nullOfUserId(criteriaBuilder, root));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public OnetimeBankAccount findOneByAccountNumber(String accountNumber) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<OnetimeBankAccount, OnetimeBankAccount>() {
                    @Override
                    public OnetimeBankAccount query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalAccountNumber(
                                        criteriaBuilder, root, Long.valueOf(accountNumber)));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }

    public OnetimeBankAccount assignNewBankAccount(Long userId) throws Exception {
        OnetimeBankAccount onetimeBankAccount = findOneByUserIdIsNull();

        if (onetimeBankAccount == null) {
            return onetimeBankAccount;
        }

        customTransactionManager.execute(
                entityManager -> {
                    try {
                        onetimeBankAccount.setUserId(userId);
                        save(onetimeBankAccount, entityManager);
                    } catch (Exception e) {
                        log.severe(getClass().getName(), e);
                    }
                });

        return onetimeBankAccount;
    }

    public OnetimeBankAccount findOneByAccountNumberAndVaId(String accountNumber, String vaId) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<OnetimeBankAccount, OnetimeBankAccount>() {
                    @Override
                    public OnetimeBankAccount query() {
                        List<Predicate> predicates = new ArrayList<>();
                        predicates.add(
                                predicate.equalAccountNumber(
                                        criteriaBuilder, root, Long.parseLong(accountNumber)));
                        predicates.add(predicate.equalVaId(criteriaBuilder, root, vaId));
                        return getSingleResult(entityManager, criteriaQuery, root, predicates);
                    }
                });
    }
}
