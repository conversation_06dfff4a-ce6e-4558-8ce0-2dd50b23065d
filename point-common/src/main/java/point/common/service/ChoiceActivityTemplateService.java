package point.common.service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.ChoiceActivityAutoCycle;
import point.common.constant.ChoiceActivityResultStatus;
import point.common.constant.ChoiceActivityStatus;
import point.common.entity.ChoiceActivity;
import point.common.entity.ChoiceActivityTemplate;
import point.common.entity.ChoiceActivityTemplate_;
import point.common.model.request.ChoiceActivityTemplateForm;
import point.common.model.response.PageData;
import point.common.predicate.ChoiceActivityTemplatePredicate;
import point.common.util.ChoiceActivityUtil;
import point.common.util.DateRange;
import point.common.util.DateUnit;
import point.common.util.JsonUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoiceActivityTemplateService
        extends EntityService<ChoiceActivityTemplate, ChoiceActivityTemplatePredicate> {

    @Autowired private ChoiceActivityService choiceActivityService;

    @Override
    public Class<ChoiceActivityTemplate> getEntityClass() {
        return ChoiceActivityTemplate.class;
    }

    public PageData<ChoiceActivityTemplate> findByCondition(
            ChoiceActivityStatus status,
            Long startTimeFrom,
            Long startTimeTo,
            Long endTimeFrom,
            Long endTimeTo,
            Integer number,
            Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                status,
                                                startTimeFrom,
                                                startTimeTo,
                                                endTimeFrom,
                                                endTimeTo));
                            }
                        });

        return new PageData<ChoiceActivityTemplate>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                ChoiceActivityTemplate, List<ChoiceActivityTemplate>>() {
                            @Override
                            public List<ChoiceActivityTemplate> query() {
                                List<Predicate> predicates =
                                        getPredicatesOfFindByCondition(
                                                criteriaBuilder,
                                                root,
                                                status,
                                                startTimeFrom,
                                                startTimeTo,
                                                endTimeFrom,
                                                endTimeTo);
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        predicates,
                                        number,
                                        size,
                                        criteriaBuilder.desc(
                                                root.get(ChoiceActivityTemplate_.updatedAt)));
                            }
                        }));
    }

    private List<Predicate> getPredicatesOfFindByCondition(
            CriteriaBuilder criteriaBuilder,
            Root<ChoiceActivityTemplate> root,
            ChoiceActivityStatus status,
            Long startTimeFrom,
            Long startTimeTo,
            Long endTimeFrom,
            Long endTimeTo) {
        List<Predicate> predicates = new ArrayList<>();

        if (status != null) {
            predicates.add(predicate.equalStatus(criteriaBuilder, root, status));
        }
        if (startTimeFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToStartTime(
                            criteriaBuilder, root, new Date(startTimeFrom)));
        }
        if (startTimeTo != null) {
            predicates.add(
                    predicate.lessThanStartTime(criteriaBuilder, root, new Date(startTimeTo)));
        }
        if (endTimeFrom != null) {
            predicates.add(
                    predicate.greaterThanOrEqualToEndTime(
                            criteriaBuilder, root, new Date(endTimeFrom)));
        }
        if (endTimeTo != null) {
            predicates.add(predicate.lessThanEndTime(criteriaBuilder, root, new Date(endTimeTo)));
        }
        return predicates;
    }

    /**
     * create
     *
     * @param form
     * @return
     */
    public ChoiceActivityTemplate create(ChoiceActivityTemplateForm form) {
        ChoiceActivityTemplate choiceActivityTemplate =
                ChoiceActivityTemplate.builder()
                        .activityType(form.getActivityType())
                        .voteStartTime(form.getVoteStartTime())
                        .voteEndTime(form.getVoteEndTime())
                        .startTime(form.getStartTime())
                        .endTime(form.getEndTime())
                        .votePowerLimit(form.getVotePowerLimit())
                        .status(form.getStatus())
                        .autoCycle(form.getAutoCycle())
                        .powerTotal(form.getPowerTotal())
                        .createdBy(form.getCreatedBy())
                        .updatedBy(form.getUpdatedBy())
                        .build();

        return customTransactionManager.save(choiceActivityTemplate);
    }

    public boolean existsByAutoCycleAndStatus(
            ChoiceActivityAutoCycle autoCycle, ChoiceActivityStatus choiceActivityStatus) {
        ChoiceActivityTemplate temp =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<
                                ChoiceActivityTemplate, ChoiceActivityTemplate>() {
                            @Override
                            public ChoiceActivityTemplate query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalAutoCycle(criteriaBuilder, root, autoCycle));
                                predicates.add(
                                        predicate.equalStatus(
                                                criteriaBuilder, root, choiceActivityStatus));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        if (temp != null) {
            return true;
        }
        return false;
    }

    public void setChoiceActivityByTemplate() {
        // 現在の日付を取得 (UTC)
        LocalDate currentDateUTC = LocalDate.now();
        log.info("現在の日付を取得 (UTC) currentDate: {}", currentDateUTC);

        LocalDate currentDate = DateUnit.getCurrentDateJST();
        log.info("現在の日付を取得 (JST) currentDate: {}", currentDate);
        // ChoiceActivityTemplate テーブルから活動の開始時間と終了時間を取得
        Optional<ChoiceActivityTemplate> templateOptional = this.findChoiceActivityTemplate();
        if (templateOptional.isEmpty()) {
            log.info("No choice activity template found skip to set for choice activity");
            return;
        }

        // テンプレートが存在する場合
        ChoiceActivityTemplate choiceActivityTemplate = templateOptional.get();
        log.info("choiceActivityTemplate: {}", JsonUtil.encode(choiceActivityTemplate));

        // voteStartTime と voteEndTime の文字列を取得
        String voteStartTimeStr = choiceActivityTemplate.getVoteStartTime(); // 例: "7:00"
        String voteEndTimeStr = choiceActivityTemplate.getVoteEndTime(); // 例: "23:59"

        // 時間のフォーマッターを定義
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("H:mm:ss");

        // voteStartTime と voteEndTime の文字列を LocalTime に変換
        LocalTime voteStartTime = LocalTime.parse(voteStartTimeStr, timeFormatter);
        LocalTime voteEndTime = LocalTime.parse(voteEndTimeStr, timeFormatter);

        // 現在の日付と時間を組み合わせて、LocalDateTime を作成（这里认为是 JST 时间）
        LocalDateTime startDateTimeJST = LocalDateTime.of(currentDate, voteStartTime);
        LocalDateTime endDateTimeJST = LocalDateTime.of(currentDate, voteEndTime);

        ZoneId jstZone = DateUnit.ZONE_ID_TOKYO;
        ZonedDateTime startZonedJST = startDateTimeJST.atZone(jstZone);
        ZonedDateTime endZonedJST = endDateTimeJST.atZone(jstZone);

        ZonedDateTime startZonedUTC = startZonedJST.withZoneSameInstant(ZoneOffset.UTC);
        ZonedDateTime endZonedUTC = endZonedJST.withZoneSameInstant(ZoneOffset.UTC);

        // LocalDateTime を Date に変換
        Date startDate = Date.from(startZonedUTC.toInstant());
        Date endDate = Date.from(endZonedUTC.toInstant());
        // ChoiceActivity をデータベースに保存
        this.saveChoiceActivity(choiceActivityTemplate, startDate, endDate);
    }

    // ChoiceActivityTemplate を取得するメソッド（ダミー実装）
    private Optional<ChoiceActivityTemplate> findChoiceActivityTemplate() {
        // データベースからテンプレートレコードを取得する処理
        // ここではダミーで値を返す
        ChoiceActivityTemplate choiceActivityTemplate =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public ChoiceActivityTemplate query() {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(
                                        predicate.equalAutoCycle(
                                                criteriaBuilder,
                                                root,
                                                ChoiceActivityAutoCycle.DAILY));
                                predicates.add(
                                        predicate.equalStatus(
                                                criteriaBuilder,
                                                root,
                                                ChoiceActivityStatus.ACTIVE));
                                Date now = new Date();
                                predicates.add(
                                        predicate.lessThanOrEqualToStartTime(
                                                criteriaBuilder, root, now));
                                predicates.add(
                                        predicate.greaterThanOrEqualToEndTime(
                                                criteriaBuilder, root, now));
                                return getSingleResult(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        return Optional.ofNullable(choiceActivityTemplate);
    }

    // ChoiceActivity をデータベースに保存するメソッド（ダミー実装）
    private void saveChoiceActivity(
            ChoiceActivityTemplate choiceActivityTemplate, Date startDate, Date endDate) {
        // データベース保存処理
        // ChoiceActivity を作成
        // lock time: 00:00:00 - 06:59:59
        DateRange lockDate = ChoiceActivityUtil.getDefaultChoiceActivityLockDate();
        choiceActivityService.save(
                ChoiceActivity.builder()
                        .name(ChoiceActivityAutoCycle.DAILY.name())
                        .activityType(choiceActivityTemplate.getActivityType())
                        .votePowerLimit(choiceActivityTemplate.getVotePowerLimit())
                        .status(ChoiceActivityResultStatus.INACTIVE)
                        .lockStartTime(lockDate.getStartDate())
                        .lockEndTime(lockDate.getEndDate())
                        .voteStartTime(startDate)
                        .voteEndTime(endDate)
                        .rewardPool(choiceActivityTemplate.getPowerTotal())
                        .symbol("JPY")
                        .build());
    }

    public Optional<List<ChoiceActivityTemplate>> findByStatus(ChoiceActivityStatus status) {
        List<ChoiceActivityTemplate> entities =
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<>() {
                            @Override
                            public List<ChoiceActivityTemplate> query() {
                                List<Predicate> predicates = new ArrayList<>(1);
                                predicates.add(
                                        predicate.equalStatus(criteriaBuilder, root, status));
                                return getResultList(
                                        entityManager, criteriaQuery, root, predicates);
                            }
                        });
        return Optional.ofNullable(entities);
    }

    public void refreshStatus() {
        Optional<List<ChoiceActivityTemplate>> entities =
                this.findByStatus(ChoiceActivityStatus.ACTIVE);
        if (entities.isEmpty()) {
            return;
        }
        List<ChoiceActivityTemplate> templates = entities.get();
        LocalDateTime now = DateUnit.getLocalDateTimeWithZoneId(DateUnit.ZONE_ID_UTC);
        for (ChoiceActivityTemplate entity : templates) {
            Date endTime = entity.getEndTime();
            LocalDateTime configuredEndTime =
                    DateUnit.convertToLocalDateTimeWithZoneId(endTime, ZoneId.systemDefault());
            if (now.isAfter(configuredEndTime)) {
                entity.setStatus(ChoiceActivityStatus.ABOLISH);
                this.save(entity);
            }
        }
    }
}
