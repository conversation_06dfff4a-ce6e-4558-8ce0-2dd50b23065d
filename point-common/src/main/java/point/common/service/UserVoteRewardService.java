package point.common.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import point.common.component.RedisManager;
import point.common.component.SesManager;
import point.common.constant.*;
import point.common.entity.*;
import point.common.model.response.VoteRewardEmailData;
import point.common.repos.*;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserVoteRewardService {
    private static final String VOTE_POWER_LOCK_PREFIX = "lock:power:user:";

    private final ChoiceActivityService choiceActivityService;
    private final ChoiceActivityRepository choiceActivityRepository;
    private final ChoicePowerRepository choicePowerRepository;
    private final ChoiceVoteRepository choiceVoteRepository;
    private final ChoicePowerUserRelService choicePowerUserRelService;
    private final ChoicePowerService choicePowerService;
    private final MailNoreplyService mailNoreplyService;
    private final SesManager sesManager;
    private final RedisManager redisManager;
    private final ApplicationEventPublisher eventPublisher;
    private final ChoiceRewardRepository choiceRewardRepository;
    private final UserRepository userRepository;

    @Value("${vote-reward.expire-date:1}")
    private int rewardExpireDate;

    @Value("${vote-reward.expire-unit:YEAR}")
    private String rewardExpireUnit;

    /**
     * 查找昨天的活动数据
     *
     * @return 昨天的活动数据，如果不存在返回null
     */
    public ChoiceActivity findYesterdayActivity() {
        ZonedDateTime nowUtc = ZonedDateTime.now(DateUnit.ZONE_ID_UTC);
        ZonedDateTime taskStartTime = nowUtc.withHour(22).withMinute(0).withSecond(0).withNano(0);
        ZonedDateTime taskEndTime =
                nowUtc.withHour(23).withMinute(59).withSecond(59).withNano(999_999_999);

        LocalDate queryDate =
                (nowUtc.isAfter(taskStartTime) && nowUtc.isBefore(taskEndTime))
                        ? nowUtc.toLocalDate()
                        : nowUtc.toLocalDate().minusDays(1);

        log.debug("Querying activity for date: {}", queryDate);
        return choiceActivityService.findChoiceActivityByWorker(queryDate);
    }

    /** 处理奖励过期 */
    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER)
    public void voteRewardExpiry() {
        log.info("Start processing expired vote rewards");

        List<ChoiceReward> expiredRewards = this.findExpiredRewards();
        if (CollectionUtils.isEmpty(expiredRewards)) {
            log.info("No expired rewards found");
            return;
        }

        log.info("Found {} expired rewards to process", expiredRewards.size());
        this.processExpiredRewards(expiredRewards);
    }

    public boolean calculateChoicePowerAfterExpire(Long userId, BigDecimal amount) {
        try {
            return redisManager.executeWithLock(
                    getLockKey(userId),
                    RedisManager.LockParams.WORKER,
                    () -> this.deductUserPower(userId, amount));
        } catch (Exception e) {
            log.warn(
                    "user:{} update reward expire failed,amount:{},error:{}",
                    userId,
                    amount,
                    e.getMessage(),
                    e);
            throw new RuntimeException(e);
        }
    }

    public void calculateChoicePowerAfterExpireByEntityManager(
            Long userId, BigDecimal amount, EntityManager entityManager) throws Exception {
        ChoicePowerUserRel userPower = choicePowerUserRelService.getChoicePowerUserRel(userId);
        ChoicePower choicePower = choicePowerService.findOne(userPower.getChoicePowerId());

        BigDecimal currentChoiceP =
                choicePower.getChoicePAmount() != null
                        ? choicePower.getChoicePAmount()
                        : BigDecimal.ZERO;
        BigDecimal afterChoiceP = currentChoiceP.subtract(amount);

        choicePower.setChoicePAmount(afterChoiceP);
        log.info(
                "user:{},decrease choiceP:{},before:{},after:{}",
                userId,
                amount,
                currentChoiceP,
                afterChoiceP);

        choicePowerService.save(choicePower, entityManager);
    }

    // -- helper methods --

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER,
            propagation = Propagation.REQUIRES_NEW,
            timeout = 60)
    public void handleNoVotesCase(ChoiceActivity activity, int priceComparison) {
        log.info("No votes found for activity[{}], setting default result", activity.getId());
        ChoiceActivityVoteResult voteResult =
                priceComparison > 0
                        ? ChoiceActivityVoteResult.UP
                        : priceComparison == 0
                                ? ChoiceActivityVoteResult.DRAW
                                : ChoiceActivityVoteResult.DOWN;
        activity.setVoteResult(voteResult);
        choiceActivityRepository.save(activity);
    }

    @Transactional(
            rollbackFor = Exception.class,
            transactionManager = CommonConstants.MASTER_TRANSACTION_MANAGER,
            propagation = Propagation.REQUIRES_NEW,
            timeout = 120)
    public void processRewards(
            ChoiceActivity activity, int priceComparison, List<ChoiceVote> votes) {
        List<ChoiceReward> rewardList = new ArrayList<>(votes.size());
        List<VoteRewardEmailData> emailDataList = new ArrayList<>();
        Long rewardPool = activity.getRewardPool();

        switch (priceComparison) {
            case 0:
                this.processDrawCase(activity, votes, rewardPool, rewardList, emailDataList);
                break;
            case 1:
                this.processWinnerCase(
                        activity,
                        votes,
                        rewardPool,
                        rewardList,
                        emailDataList,
                        ChoiceActivityVoteResult.UP,
                        activity.getTotalVotePowerUp());
                break;
            case -1:
                this.processWinnerCase(
                        activity,
                        votes,
                        rewardPool,
                        rewardList,
                        emailDataList,
                        ChoiceActivityVoteResult.DOWN,
                        activity.getTotalVotePowerDown());
                break;
        }

        if (!rewardList.isEmpty()) {
            long startTime = System.currentTimeMillis();
            choiceRewardRepository.saveAll(rewardList);
            long endTime = System.currentTimeMillis();
            log.info("rewardList size is {}, time is {}", rewardList.size(), endTime - startTime);
        }

        if (!emailDataList.isEmpty()) {
            eventPublisher.publishEvent(
                    new UserVoteRewardService.VoteRewardEmailEvent(emailDataList));
        }
    }

    private void processDrawCase(
            ChoiceActivity activity,
            List<ChoiceVote> votes,
            Long rewardPool,
            List<ChoiceReward> rewardList,
            List<VoteRewardEmailData> emailDataList) {
        log.info("Processing draw case for activity[{}]", activity.getId());
        Long totalPower =
                this.sumVotePower(activity.getTotalVotePowerUp(), activity.getTotalVotePowerDown());
        this.initRewards(votes, rewardPool, totalPower, rewardList, emailDataList);
        List<Long> voteIds = votes.stream().map(ChoiceVote::getId).toList();
        if (CollectionUtils.isNotEmpty(voteIds)) {
            this.updateVoteResultAllWin(activity.getId(), voteIds);
        }
        activity.setVoteResult(ChoiceActivityVoteResult.DRAW);
        choiceActivityRepository.save(activity);
    }

    private void processWinnerCase(
            ChoiceActivity activity,
            List<ChoiceVote> votes,
            Long rewardPool,
            List<ChoiceReward> rewardList,
            List<VoteRewardEmailData> emailDataList,
            ChoiceActivityVoteResult winnerDirection,
            Long totalPower) {
        log.info(
                "Processing winner case for activity[{}], winner: {}",
                activity.getId(),
                winnerDirection);

        List<ChoiceVote> winnerVotes = this.filterVotesByDirection(votes, winnerDirection);
        if (!winnerVotes.isEmpty()) {
            this.initRewards(winnerVotes, rewardPool, totalPower, rewardList, emailDataList);
        }
        List<Long> voteIds = votes.stream().map(ChoiceVote::getId).toList();
        if (CollectionUtils.isNotEmpty(voteIds)) {
            this.updateVoteResultsForWinnerLoser(
                    activity.getId(),
                    winnerDirection,
                    voteIds,
                    winnerDirection == ChoiceActivityVoteResult.UP
                            ? ChoiceActivityVoteResult.DOWN
                            : ChoiceActivityVoteResult.UP);
        }
        activity.setVoteResult(winnerDirection);
        choiceActivityRepository.save(activity);
    }

    /** 初始化奖励记录和邮件数据 */
    private void initRewards(
            List<ChoiceVote> votes,
            Long rewardPool,
            Long totalPowerAmount,
            List<ChoiceReward> rewardList,
            List<VoteRewardEmailData> emailDataList) {
        Date currentDate = new Date();
        Date expiryDate = this.calculateExpiryDate(currentDate);

        Map<Long, ChoicePower> userPowerMap = this.preloadUserPowerData(votes);

        for (ChoiceVote vote : votes) {
            try {
                this.processSingleVote(
                        vote,
                        rewardPool,
                        totalPowerAmount,
                        rewardList,
                        emailDataList,
                        currentDate,
                        expiryDate,
                        userPowerMap);
            } catch (Exception e) {
                log.error(
                        "Failed to process vote[{}] for user[{}]: {}",
                        vote.getId(),
                        vote.getUserId(),
                        e.getMessage(),
                        e);
            }
        }
    }

    private Map<Long, ChoicePower> preloadUserPowerData(List<ChoiceVote> votes) {
        Set<Long> userIds = new HashSet<>();
        for (ChoiceVote vote : votes) {
            userIds.add(vote.getUserId());
        }

        Map<Long, ChoicePower> powerMap = new HashMap<>(userIds.size());
        for (Long userId : userIds) {
            try {
                Optional<ChoicePower> choicePower = choicePowerRepository.findByUserId(userId);
                choicePower.ifPresent(power -> powerMap.put(userId, power));
            } catch (Exception e) {
                log.warn("Failed to load power data for user[{}]: {}", userId, e.getMessage());
            }
        }
        return powerMap;
    }

    private void processSingleVote(
            ChoiceVote vote,
            Long rewardPool,
            Long totalPowerAmount,
            List<ChoiceReward> rewardList,
            List<VoteRewardEmailData> emailDataList,
            Date currentDate,
            Date expiryDate,
            Map<Long, ChoicePower> userPowerMap) {

        vote.setVoteResult(ChoiceVoteResult.WIN);
        choiceVoteRepository.save(vote);

        BigDecimal rewardAmount =
                this.calculateVoteReward(rewardPool, totalPowerAmount, vote.getVotePower());
        if (log.isDebugEnabled()) {
            log.debug("Calculated reward for vote[{}]: {}", vote.getId(), rewardAmount);
        }

        ChoiceReward reward =
                ChoiceReward.builder()
                        .activityId(vote.getActivityId())
                        .userId(vote.getUserId())
                        .choiceRewardType(ChoiceRewardType.CHOICE_P)
                        .amount(rewardAmount)
                        .withdrawStatusType(WithdrawStatusType.PENDING)
                        .rewardTime(currentDate)
                        .expiryTime(expiryDate)
                        .build();
        rewardList.add(reward);

        this.prepareEmailData(vote, rewardAmount, currentDate, emailDataList);

        this.updateUserPower(vote.getUserId(), rewardAmount, userPowerMap);
    }

    private Date calculateExpiryDate(Date currentDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        VoteRewardExpireUnit expireUnit = VoteRewardExpireUnit.valueOf(rewardExpireUnit);
        calendar.add(expireUnit.getCalendarField(), rewardExpireDate);
        return calendar.getTime();
    }

    private void prepareEmailData(
            ChoiceVote vote,
            BigDecimal rewardAmount,
            Date currentDate,
            List<VoteRewardEmailData> emailDataList) {
        String email = userRepository.findUserEmail(vote.getUserId());
        if (StringUtils.isNotEmpty(email)) {
            VoteRewardEmailData data = new VoteRewardEmailData();
            data.setEmail(email);
            data.setType("Pontaビットコイン投票");
            data.setAmount(rewardAmount);
            data.setData(
                    FormatUtil.formatJst(
                            currentDate, FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
            emailDataList.add(data);
        }
    }

    private void updateUserPower(
            Long userId, BigDecimal rewardAmount, Map<Long, ChoicePower> userPowerMap) {
        ChoicePower power = userPowerMap.get(userId);
        if (Objects.nonNull(power)) {
            BigDecimal currentAmount =
                    Objects.nonNull(power.getChoicePAmount())
                            ? power.getChoicePAmount()
                            : BigDecimal.ZERO;
            BigDecimal newAmount = currentAmount.add(rewardAmount);
            power.setChoicePAmount(newAmount);
            choicePowerRepository.save(power);
            if (log.isDebugEnabled()) {
                log.debug(
                        "Updating power for user[{}]: {} -> {}", userId, currentAmount, newAmount);
            }
        } else {
            log.warn("No power record found for user[{}]", userId);
        }
    }

    /** 计算单个投票奖励 */
    private BigDecimal calculateVoteReward(
            Long rewardPool, Long totalPowerAmount, Long userVotePower) {
        if (totalPowerAmount == 0) {
            log.warn("Total power amount is zero, cannot calculate reward");
            return BigDecimal.ZERO;
        }

        return new BigDecimal(userVotePower)
                .divide(new BigDecimal(totalPowerAmount), 3, RoundingMode.DOWN)
                .multiply(new BigDecimal(rewardPool));
    }

    private List<ChoiceReward> findExpiredRewards() {
        return choiceRewardRepository.findChoiceRewardByExpiryTimeLessThanAndWithdrawStatusTypeIn(
                new Date(),
                List.of(WithdrawStatusType.PENDING, WithdrawStatusType.PARTIALLY_WITHDRAWN));
    }

    private void processExpiredRewards(List<ChoiceReward> rewards) {
        int successCount = 0;
        int failedCount = 0;

        for (ChoiceReward reward : rewards) {
            try {
                if (this.processSingleExpiredReward(reward)) {
                    successCount++;
                } else {
                    failedCount++;
                }
            } catch (Exception e) {
                log.error(
                        "Failed to process expired reward[{}]: {}",
                        reward.getId(),
                        e.getMessage(),
                        e);
                failedCount++;
            }
        }

        log.info(
                "Expired rewards processing completed: {} succeeded, {} failed",
                successCount,
                failedCount);
    }

    private boolean processSingleExpiredReward(ChoiceReward reward) {
        BigDecimal amount =
                Objects.nonNull(reward.getRemainingAmount())
                                && reward.getRemainingAmount().compareTo(BigDecimal.ZERO) > 0
                        ? reward.getRemainingAmount()
                        : reward.getAmount();
        try {
            if (!this.calculateChoicePowerAfterExpire(reward.getUserId(), amount)) {
                log.info("Failed to acquire lock for user[{}]", reward.getUserId());
                return false;
            }
        } catch (Exception e) {
            log.warn(
                    "Failed to deduct user power. user[{}]. please see log for details",
                    reward.getUserId());
            return false;
        }

        this.updateRewardStatus(reward);
        return true;
    }

    private void deductUserPower(Long userId, BigDecimal amount) {
        ChoicePowerUserRel userPower = choicePowerUserRelService.getChoicePowerUserRel(userId);
        if (Objects.isNull(userPower)) {
            log.warn("No power record found for user[{}]", userId);
            return;
        }

        ChoicePower power = choicePowerService.findOne(userPower.getChoicePowerId());
        if (Objects.isNull(power)) {
            log.warn("Power data not found for ID[{}]", userPower.getChoicePowerId());
            return;
        }

        BigDecimal currentAmount =
                Objects.nonNull(power.getChoicePAmount())
                        ? power.getChoicePAmount()
                        : BigDecimal.ZERO;
        BigDecimal newAmount = currentAmount.subtract(amount);
        power.setChoicePAmount(newAmount);

        log.info(
                "Deducted {} from user[{}] power: {} -> {}",
                amount,
                userId,
                currentAmount,
                newAmount);

        choicePowerRepository.save(power);
    }

    private void updateRewardStatus(ChoiceReward reward) {
        if (Objects.nonNull(reward.getRemainingAmount())
                && reward.getRemainingAmount().compareTo(BigDecimal.ZERO) > 0) {
            reward.setWithdrawStatusType(WithdrawStatusType.PARTIALLY_EXPIRED);
        } else {
            reward.setWithdrawStatusType(WithdrawStatusType.FULLY_EXPIRED);
        }
        choiceRewardRepository.save(reward);
    }

    private Long sumVotePower(Long upPower, Long downPower) {
        return Stream.of(upPower, downPower)
                .mapToLong(value -> Objects.nonNull(value) ? value : NumberUtils.LONG_ZERO)
                .sum();
    }

    private List<ChoiceVote> filterVotesByDirection(
            List<ChoiceVote> votes, ChoiceActivityVoteResult direction) {
        return votes.stream().filter(vote -> vote.getVoteDirection().equals(direction)).toList();
    }

    private void updateVoteResultsForWinnerLoser(
            Long activityId,
            ChoiceActivityVoteResult winnerDirection,
            List<Long> voteIds,
            ChoiceActivityVoteResult loserDirection) {
        this.updateVoteResultByDirection(
                ChoiceVoteResult.WIN, activityId, winnerDirection, voteIds);
        this.updateVoteResultByDirection(
                ChoiceVoteResult.LOSE, activityId, loserDirection, voteIds);
    }

    private void updateVoteResultByDirection(
            ChoiceVoteResult voteResult,
            Long activityId,
            ChoiceActivityVoteResult direction,
            List<Long> voteIds) {
        choiceVoteRepository.updateVoteResult(voteResult, activityId, direction, voteIds);
    }

    private void updateVoteResultAllWin(Long activityId, List<Long> voteIds) {
        choiceVoteRepository.updateVoteAllWin(ChoiceVoteResult.WIN, activityId, voteIds);
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void sendRewardEmails(UserVoteRewardService.VoteRewardEmailEvent event) {
        List<VoteRewardEmailData> dataList = event.getList();
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("No email data to send");
            return;
        }

        MailNoreply mailInfo =
                mailNoreplyService.findOne(MailNoreplyType.VOTE_REWARD_RESULT_NOTIFY);
        if (Objects.isNull(mailInfo)) {
            log.error("Email template not found for type: VOTE_REWARD_RESULT_NOTIFY");
            return;
        }

        log.info("Preparing to send {} reward emails", dataList.size());
        dataList.forEach(data -> sendSingleEmail(data, mailInfo));
    }

    private void sendSingleEmail(VoteRewardEmailData data, MailNoreply mailInfo) {
        try {
            String content =
                    new MessageFormat(mailInfo.getContents())
                            .format(
                                    new String[] {
                                        data.getType(), data.getData(), data.getAmount() + "p"
                                    });

            sesManager.send(
                    mailInfo.getFromAddress(), data.getEmail(), mailInfo.getTitle(), content);

            if (log.isDebugEnabled()) {
                log.debug("Successfully sent reward email to {}", data.getEmail());
            }
        } catch (Exception e) {
            log.error("Failed to send email to {}: {}", data.getEmail(), e.getMessage(), e);
        }
    }

    private static String getLockKey(Long userId) {
        return VOTE_POWER_LOCK_PREFIX + userId;
    }

    @Getter
    public static class VoteRewardEmailEvent {
        private final List<VoteRewardEmailData> list;

        public VoteRewardEmailEvent(List<VoteRewardEmailData> list) {
            this.list = list;
        }
    }
}
