package point.common.service;

import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import point.common.component.RedisManager;
import point.common.entity.ChoiceVote;
import point.common.model.response.websocket.ChoiceActivityDataWrapper;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChoiceActivityTotalService {

    private final RedisManager redisManager;
    private final AsyncTaskExecutor taskExecutor;
    private final ChoiceActivityTotalUpdater totalUpdater;

    public static String getLockKey(Long activityId, Long voteId) {
        return "choice:activity:total:" + activityId + ":" + voteId;
    }

    public void handleTotal(ChoiceActivityDataWrapper dataWrapper) {
        ChoiceVote choiceVote =
                Optional.ofNullable(dataWrapper)
                        .map(ChoiceActivityDataWrapper::getChoiceVote)
                        .orElseGet(
                                () -> {
                                    log.info("Choice vote is null, skipping processing");
                                    return null;
                                });

        if (Objects.isNull(choiceVote)
                || Objects.isNull(choiceVote.getActivityId())
                || Objects.isNull(choiceVote.getVotePower())
                || Objects.isNull(choiceVote.getVoteDirection())
                || Objects.isNull(choiceVote.getId())) {
            log.warn("Choice vote is null, skipping processing");
            return;
        }

        taskExecutor.execute(
                () -> {
                    try {
                        redisManager.executeWithLock(
                                getLockKey(choiceVote.getActivityId(), choiceVote.getId()),
                                RedisManager.LockParams.EXECUTE_CHOICE_ACTIVITY_TOTAL,
                                () -> totalUpdater.processVote(dataWrapper));
                    } catch (Exception e) {
                        log.error(
                                "Failed to handle total for activity ID: {}, vote ID: {}",
                                choiceVote.getActivityId(),
                                choiceVote.getId(),
                                e);
                    }
                });
    }
}
