package point.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.util.CollectionUtils;
import point.common.component.HistoricalTransactionManager;
import point.common.entity.BestPrice;
import point.common.entity.Symbol;
import point.common.model.BestPriceRowMapper;
import point.common.predicate.BestPricePredicate;

@Slf4j
public abstract class BestPriceService<
                E extends BestPrice,
                P extends BestPricePredicate<E>,
                R extends BestPriceRowMapper<E>>
        extends EntityService<E, P> {

    @Autowired protected HistoricalTransactionManager historicalTransactionManager;
    @Autowired protected SymbolService symbolService;

    public abstract void archive(Symbol symbol, BigDecimal bestAsk, BigDecimal bestBid);

    public abstract Class<R> getRowMapperClass();

    public R newRowMapper() {
        R rowMapper = null;

        try {
            rowMapper = getRowMapperClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("newRowMapper failed", e);
        }

        return rowMapper;
    }

    public E findFromHistory(Symbol symbol, Long id) {
        return historicalTransactionManager.findOneFromHistory(
                "select * from " + BestPrice.getTableName(symbol) + " where id = ?",
                id,
                newRowMapper());
    }

    public List<E> findAllFromHistory(Symbol symbol, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
        mapSqlParameterSource.addValue("ids", ids);
        return historicalTransactionManager.findFromHistory(
                "select * from " + BestPrice.getTableName(symbol) + " where id in (:ids)",
                mapSqlParameterSource,
                newRowMapper());
    }
}
