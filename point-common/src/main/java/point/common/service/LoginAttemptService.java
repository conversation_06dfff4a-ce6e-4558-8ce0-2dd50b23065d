package point.common.service;

import java.util.Date;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import point.common.component.CustomRedisTemplate;
import point.common.entity.LoginAttempt;

@RequiredArgsConstructor
@Service
public class LoginAttemptService {

    /*
     * ログイン失敗がこの回数を超えると、ユーザはロックされる
     * 負数の場合はInteger.MAXVALUEになる(実質的に何回間違えてもロックされない)
     */
    @Value("${point-common.account-lock.max-attempt:5}")
    private Integer maxAttempt;

    @Value("${point-common.account-lock.expire-seconds:0}")
    private Integer expireSeconds;

    @Value("${point-common.account-lock.redis-expire-minutes:60}")
    private Integer redisExpireMinutes;

    protected String getCacheKey(Long userId) {
        return "userloginAttempt:" + userId;
    }

    @Autowired private CustomRedisTemplate<LoginAttempt> loginAttemptRedisTemplate;

    public void clear(Long userId) {
        loginAttemptRedisTemplate.delete(getCacheKey(userId));
    }

    /**
     * @param userId
     */
    public boolean countUp(Long userId) {
        LoginAttempt loginAttempt = loginAttemptRedisTemplate.getValue(getCacheKey(userId));

        if (loginAttempt == null) {
            loginAttempt = new LoginAttempt(userId);
        } else {
            if (new Date().getTime() - loginAttempt.getTimestamp() > expireSeconds * 1000) {
                loginAttempt.setTimestamp(new Date().getTime());
                loginAttempt.setAttempt(loginAttempt.getAttempt() + 1);
            }
        }

        loginAttemptRedisTemplate.setValue(getCacheKey(userId), loginAttempt, redisExpireMinutes);

        return loginAttempt.getAttempt().intValue() >= maxAttempt;
    }

    /**
     * @param userId
     * @return bool
     */
    public boolean isLockExpired(Long userId) {
        LoginAttempt loginAttempt = loginAttemptRedisTemplate.getValue(getCacheKey(userId));
        // loginAttemptがない場合は管理画面のLockなため、expireしない
        if (loginAttempt == null) {
            return false;
        }
        if (expireSeconds > 0) {
            return expireSeconds * 1000 < new Date().getTime() - loginAttempt.getTimestamp();
        } else {
            return false;
        }
    }
}
