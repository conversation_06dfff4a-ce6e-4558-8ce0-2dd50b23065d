package point.common.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import point.common.component.QueryExecutorCounter;
import point.common.component.QueryExecutorReturner;
import point.common.constant.UserIdType;
import point.common.entity.MonsterBase;
import point.common.entity.MonsterBase_;
import point.common.model.response.PageData;
import point.common.predicate.MonsterBasePredicate;

@RequiredArgsConstructor
@Service
public class MonsterBaseService extends EntityService<MonsterBase, MonsterBasePredicate> {

    @Override
    public Class<MonsterBase> getEntityClass() {
        return MonsterBase.class;
    }

    public Optional<MonsterBase> findByIdType(UserIdType userIdType) {
        return customTransactionManager.find(
                getEntityClass(),
                new QueryExecutorReturner<MonsterBase, Optional<MonsterBase>>() {
                    @Override
                    public Optional<MonsterBase> query() {
                        List<Predicate> predicates = new ArrayList<>(1);
                        predicates.add(
                                criteriaBuilder.equal(root.get(MonsterBase_.ID_TYPE), userIdType));
                        return Optional.ofNullable(
                                getSingleResult(entityManager, criteriaQuery, root, predicates));
                    }
                });
    }

    public PageData<MonsterBase> findByCondition(Integer number, Integer size) {
        long count =
                customTransactionManager.count(
                        getEntityClass(),
                        new QueryExecutorCounter<>() {
                            @Override
                            public Long query() {
                                return count(
                                        entityManager,
                                        criteriaBuilder,
                                        criteriaQuery,
                                        root,
                                        new ArrayList<>());
                            }
                        });

        return new PageData<MonsterBase>(
                number,
                size,
                count,
                customTransactionManager.find(
                        getEntityClass(),
                        new QueryExecutorReturner<MonsterBase, List<MonsterBase>>() {
                            @Override
                            public List<MonsterBase> query() {
                                return getResultList(
                                        entityManager,
                                        criteriaQuery,
                                        root,
                                        new ArrayList<>(),
                                        number,
                                        size,
                                        criteriaBuilder.asc(root.get(MonsterBase_.id)));
                            }
                        }));
    }
}
