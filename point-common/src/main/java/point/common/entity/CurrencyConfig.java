package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.Currency;
import point.common.constant.TradeType;

@Entity
@NoArgsConstructor
@Table(name = "currency_config")
@ToString(callSuper = true, doNotUseGetters = true)
public class CurrencyConfig extends AbstractEntity {

    private static final long serialVersionUID = -244741863775650520L;

    @Getter
    @Setter
    @Column(name = "trade_type")
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Getter
    @Setter
    @Column(name = "currency")
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @Getter
    @Setter
    @Column(name = "deposit_fee", precision = 34, scale = 20, nullable = false)
    private BigDecimal depositFee = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "withdrawal_fee", precision = 34, scale = 20, nullable = false)
    private BigDecimal withdrawalFee = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "transaction_fee", precision = 34, scale = 20, nullable = false)
    private BigDecimal transactionFee = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "max_order_amount_per_day", precision = 34, scale = 20, nullable = false)
    private BigDecimal maxOrderAmountPerDay = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "min_deposit_amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal minDepositAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "min_withdrawal_amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal minWithdrawalAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "depositable", nullable = false)
    private boolean depositable = true;

    @Getter
    @Setter
    @Column(name = "withdrawable", nullable = false)
    private boolean withdrawable = true;

    @Getter
    @Setter
    @Column(name = "enabled", nullable = false)
    private boolean enabled = false;

    @Getter
    @Setter
    @Column(name = "firstSetFlg", nullable = false)
    private boolean firstSetFlg = true;

    @Getter
    @Setter
    @Column(name = "visible", nullable = false)
    private boolean isVisible = true;

    public CurrencyConfig(Currency currency) {
        this.currency = currency;
    }
}
