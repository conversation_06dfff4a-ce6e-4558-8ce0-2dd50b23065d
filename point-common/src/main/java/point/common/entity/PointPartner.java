package point.common.entity;

import java.util.Date;
import java.util.Objects;
import javax.persistence.*;
import lombok.*;
import point.common.constant.PointPartnerStatus;

@Getter
@Setter
@Entity
@Builder
@Table(name = "point_partner")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class PointPartner extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "partner_number", nullable = false)
    private String partnerNumber;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "effective_date", nullable = false)
    private Date effectiveDate;

    @Column(name = "expiry_date", nullable = false)
    private Date expiryDate;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private PointPartnerStatus status;

    @Column(name = "show_name", nullable = false)
    private String showName;

    @Column(name = "responsible_person")
    private String responsiblePerson;

    @Column(name = "phone_num")
    private String phoneNum;

    @Column(name = "mail")
    private String mail;

    @Column(name = "address")
    private String address;

    /** 1:運用 2:投資 3:運用／投資 */
    @Column(name = "scope")
    private Integer scope;

    @Column(name = "website_url")
    private String websiteUrl;

    @Column(name = "description")
    private String description;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    public boolean isAllowOperate() {
        return Objects.nonNull(this.scope) && (this.scope == 1 || this.scope == 3);
    }

    public boolean isAllowInvest() {
        return Objects.nonNull(this.scope) && (this.scope == 2 || this.scope == 3);
    }

    public boolean isActive() {
        return PointPartnerStatus.ACTIVE.equals(this.status);
    }

    public boolean isInactive() {
        Date currentDate = new Date();
        Date effectiveDate = this.getEffectiveDate();
        return PointPartnerStatus.INACTIVE.equals(this.status)
                || (Objects.nonNull(effectiveDate) && currentDate.before(effectiveDate));
    }

    public boolean isExpired() {
        Date currentDate = new Date();
        Date expiryDate = this.getExpiryDate();
        return PointPartnerStatus.ABOLISH.equals(this.status)
                || (Objects.nonNull(effectiveDate) && currentDate.after(expiryDate));
    }

    public boolean isValidate() {
        Date currentDate = new Date();
        Date effectiveDate = this.getEffectiveDate();
        Date expiryDate = this.getExpiryDate();
        return effectiveDate != null
                && expiryDate != null
                && !currentDate.before(effectiveDate)
                && !currentDate.after(expiryDate);
    }
}
