package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.CurrencyPair;
import point.common.constant.Environment;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(WorkerMaster.class)
public abstract class WorkerMaster_ extends AbstractEntity_ {

    public static volatile SingularAttribute<WorkerMaster, Environment> environment;
    public static volatile SingularAttribute<WorkerMaster, TradeType> tradeType;
    public static volatile SingularAttribute<WorkerMaster, CurrencyPair> currencyPair;
    public static volatile SingularAttribute<WorkerMaster, String> beanName;
    public static volatile SingularAttribute<WorkerMaster, Long> intervalMillis;
    public static volatile SingularAttribute<WorkerMaster, Boolean> enabled;

    public static final String ENVIRONMENT = "environment";
    public static final String TRADE_TYPE = "tradeType";
    public static final String CURRENCY_PAIR = "currencyPair";
    public static final String BEAN_NAME = "beanName";
    public static final String INTERVAL_MILLIS = "intervalMillis";
    public static final String ENABLED = "enabled";
}
