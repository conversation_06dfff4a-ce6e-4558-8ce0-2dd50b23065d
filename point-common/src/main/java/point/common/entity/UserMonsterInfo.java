package point.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import point.common.model.dto.MonsterDistributeDTO;

@SqlResultSetMapping(
        name = "MonsterDistributeMapping",
        classes =
                @ConstructorResult(
                        targetClass = MonsterDistributeDTO.class,
                        columns = {
                            @ColumnResult(name = "monsterId", type = Long.class),
                            @ColumnResult(name = "userId", type = Long.class),
                            @ColumnResult(name = "ruleId", type = Long.class),
                            @ColumnResult(name = "activityFunction", type = String.class),
                            @ColumnResult(name = "obtainFrequency", type = String.class),
                            @ColumnResult(name = "getType", type = String.class),
                            @ColumnResult(name = "powerAmount", type = Long.class)
                        }))
@NamedNativeQuery(
        name = "findMonsterDistributeInfo",
        query =
                """
                select
                    monster.id as monsterId,
                    monster.user_id as userId,
                    rule.id as ruleId,
                    rule.activity_function as activityFunction,
                    rule.obtain_frequency as obtainFrequency,
                    rule.get_Type as getType,
                    rule.power_amount as powerAmount
                from user_monster_info monster
                    left join monster_growth_level level on monster.level = level.id
                    left join choice_activity_rule rule on level.rule_id = rule.id
                limit :limitSize offset :offsetValue
                """,
        resultSetMapping = "MonsterDistributeMapping")
@Setter
@Getter
@Entity
@Table(name = "user_monster_info")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class UserMonsterInfo extends AbstractEntity {

    // ユーザーID
    @Column(name = "user_id")
    private Long userId;

    // モンスターID
    @Column(name = "monster_id")
    private Long monsterId;

    // 種別
    @Column(name = "id_type")
    private String idType;

    // レベル
    @Column(name = "level")
    private Long level;

    // 現在経験値
    @Column(name = "current_experience")
    private Long currentExperience;

    // 次のレベルの経験値
    @Column(name = "next_level_experience")
    private Long nextLevelExperience;

    // 生成日時
    @Column(name = "creation_date_time")
    private Date creationDateTime;

    // 月間報酬
    @Column(name = "monthly_power")
    private Long monthlyPower;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Getter
    @Setter
    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "monster_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private MonsterBase monsterBase;
}
