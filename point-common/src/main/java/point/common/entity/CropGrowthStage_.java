package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(CropGrowthStage.class)
public abstract class CropGrowthStage_ extends AbstractEntity_ {

    public static volatile SingularAttribute<CropGrowthStage, TradeType> tradeType;
    public static volatile SingularAttribute<CropGrowthStage, String> growthStageId;
    public static volatile SingularAttribute<CropGrowthStage, String> growthStage;
    public static volatile SingularAttribute<CropGrowthStage, Long> evaluationFrom;
    public static volatile SingularAttribute<CropGrowthStage, Long> evaluationTo;
    public static volatile SingularAttribute<CropGrowthStage, String> updatedBy;

    public static final String TRADE_TYPE = "tradeType";
    public static final String GROWTH_STAGE_ID = "growthStageId";
    public static final String GROWTH_STAGE = "growthStage";
    public static final String EVALUATION_FROM = "evaluationFrom";
    public static final String EVALUATION_TO = "evaluationTo";
    public static final String UPDATED_BY = "updatedBy";
}
