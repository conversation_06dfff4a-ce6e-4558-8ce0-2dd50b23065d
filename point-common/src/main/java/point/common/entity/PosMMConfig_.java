package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Exchange;
import point.common.predicate.PosMMConfigPredicate;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PosMMConfig.class)
public abstract class PosMMConfig_ extends AbstractEntity_ {

    public static volatile SingularAttribute<PosMMConfigPredicate, Long> symbol;

    public static volatile SingularAttribute<PosMMConfigPredicate, Exchange> exchange;
    public static volatile SingularAttribute<PosMMConfigPredicate, Boolean> enabled;
    public static volatile SingularAttribute<PosMMConfigPredicate, BigDecimal> quantity;

    public static final String SYMBOL = "symbol";
    public static final String EXCHANGE = "exchange";
    public static final String ENABLED = "enabled";
    public static final String QUANTITY = "quantity";
}
