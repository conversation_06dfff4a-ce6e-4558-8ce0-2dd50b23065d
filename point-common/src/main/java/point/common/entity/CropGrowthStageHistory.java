package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.TradeType;

@Setter
@Getter
@Entity
@Table(name = "crop_growth_stage_history")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class CropGrowthStageHistory extends AbstractEntity {

    @Column(name = "trade_type")
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Column(name = "befor_growth_stage")
    private String beforGrowthStage;

    @Column(name = "befor_evaluation")
    private String beforEvaluation;

    @Column(name = "after_growth_stage")
    private String afterGrowthStage;

    @Column(name = "after_evaluation")
    private String afterEvaluation;

    @Column(name = "created_by")
    private String createdBy;
}
