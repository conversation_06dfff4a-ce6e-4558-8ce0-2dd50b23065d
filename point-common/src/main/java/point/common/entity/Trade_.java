package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.OrderSide;
import point.common.constant.OrderType;
import point.common.constant.TradeAction;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Trade.class)
public abstract class Trade_ extends AbstractEntity_ {

    public static volatile SingularAttribute<Trade, Long> symbolId;
    public static volatile SingularAttribute<Trade, Long> userId;
    public static volatile SingularAttribute<Trade, OrderSide> orderSide;
    public static volatile SingularAttribute<Trade, OrderType> orderType;
    public static volatile SingularAttribute<Trade, BigDecimal> price;
    public static volatile SingularAttribute<Trade, BigDecimal> amount;
    public static volatile SingularAttribute<Trade, TradeAction> tradeAction;
    public static volatile SingularAttribute<Trade, Long> orderId;
    public static volatile SingularAttribute<Trade, BigDecimal> fee;

    public static final String SYMBOL_ID = "symbolId";
    public static final String USER_ID = "userId";
    public static final String ORDER_SIDE = "orderSide";
    public static final String ORDER_TYPE = "orderType";
    public static final String PRICE = "price";
    public static final String AMOUNT = "amount";
    public static final String TRADE_ACTION = "tradeAction";
    public static final String ORDER_ID = "orderId";
    public static final String FEE = "fee";
}
