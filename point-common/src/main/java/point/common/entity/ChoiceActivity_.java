package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.*;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoiceActivity.class)
public abstract class ChoiceActivity_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoiceActivity, String> name;
    public static volatile SingularAttribute<ChoiceActivity, ChoiceActivityType> activityType;
    public static volatile SingularAttribute<ChoiceActivity, Date> voteStartTime;
    public static volatile SingularAttribute<ChoiceActivity, Date> voteEndTime;
    public static volatile SingularAttribute<ChoiceActivity, Date> lockStartTime;
    public static volatile SingularAttribute<ChoiceActivity, Date> lockEndTime;
    public static volatile SingularAttribute<ChoiceActivity, BigDecimal> basePriceVotingDay;
    public static volatile SingularAttribute<ChoiceActivity, BigDecimal> basePriceElectionDay;
    public static volatile SingularAttribute<ChoiceActivity, Long> rewardPool;
    public static volatile SingularAttribute<ChoiceActivity, String> symbol;
    public static volatile SingularAttribute<ChoiceActivity, Long> totalVotePowerUp;
    public static volatile SingularAttribute<ChoiceActivity, Long> totalVotePowerDown;
    public static volatile SingularAttribute<ChoiceActivity, Long> votePowerLimit;
    public static volatile SingularAttribute<ChoiceActivity, ChoiceActivityResultStatus> status;
    public static volatile SingularAttribute<ChoiceActivity, Long> totalVoteUsersUp;
    public static volatile SingularAttribute<ChoiceActivity, Long> totalVoteUsersDown;
    public static volatile SingularAttribute<ChoiceActivity, Long> totalReward;
    public static volatile SingularAttribute<ChoiceActivity, ChoiceActivityVoteResult> voteResult;

    public static final String NAME = "name";
    public static final String ACTIVITY_TYPE = "activityType";
    public static final String VOTE_START_TIME = "voteStartTime";
    public static final String VOTE_END_TIME = "voteEndTime";
    public static final String LOCK_START_TIME = "lockStartTime";
    public static final String LOCK_END_TIME = "lockEndTime";
    public static final String BASE_PRICE = "basePrice";
    public static final String REWARD_POOL = "rewardPool";
    public static final String SYMBOL = "symbol";
    public static final String TOTAL_VOTE_POWER_UP = "totalVotePowerUp";
    public static final String TOTAL_VOTE_POWER_DOWN = "totalVotePowerDown";
    public static final String VOTE_POWER_LIMIT = "votePowerLimit";
    public static final String STATUS = "status";
    public static final String TOTAL_VOTE_USERS_UP = "totalVoteUsersUp";
    public static final String TOTAL_VOTE_USERS_DOWN = "totalVoteUsersDown";
    public static final String TOTAL_REWARD = "totalReward";
    public static final String VOTE_RESULT = "voteResult";
}
