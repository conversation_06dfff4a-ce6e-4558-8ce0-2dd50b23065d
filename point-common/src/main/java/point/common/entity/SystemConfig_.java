package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(SystemConfig.class)
public abstract class SystemConfig_ extends AbstractEntity_ {

    public static volatile SingularAttribute<SystemConfig, CurrencyPair> currencyPair;
    public static volatile SingularAttribute<SystemConfig, String> name;
    public static volatile SingularAttribute<SystemConfig, Currency> currency;
    public static volatile SingularAttribute<SystemConfig, String> value;

    public static final String CURRENCY_PAIR = "currencyPair";
    public static final String NAME = "name";
    public static final String CURRENCY = "currency";
    public static final String VALUE = "value";
}
