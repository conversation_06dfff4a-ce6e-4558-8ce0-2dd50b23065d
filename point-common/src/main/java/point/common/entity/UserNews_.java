package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserNews.class)
public abstract class UserNews_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserNews, Long> userId;
    public static volatile SingularAttribute<UserNews, String> readIds;

    public static final String USER_ID = "userId";
    public static final String READ_IDS = "readIds";
}
