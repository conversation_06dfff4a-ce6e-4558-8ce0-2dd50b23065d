package point.common.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import point.common.serializer.BigDecimalSerializer;

@Getter
@Setter
@MappedSuperclass
@ToString(callSuper = true, doNotUseGetters = true)
public abstract class Order extends AbstractEntity {

    public static String getTableName(Symbol symbol) {
        return symbol.getTradeType().toLowerCase() + "_order";
    }

    public static String getLockKey(Symbol symbol, Long id) {
        return "lock:" + ":" + getTableName(symbol) + ":" + id;
    }

    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @Column(name = "amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal amount;

    public Order setProperties(Long symbolId, Long userId, BigDecimal amount) {
        this.symbolId = symbolId;
        this.userId = userId;
        this.amount = amount;
        return this;
    }
}
