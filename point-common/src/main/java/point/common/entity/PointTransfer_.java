package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.HulftUploadStatusEnum;
import point.common.constant.PointTransferStatusEnum;
import point.common.constant.PointTransferTypeEnum;
import point.common.constant.UserIdType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PointTransfer.class)
public class PointTransfer_ extends AbstractEntity_ {

    public static volatile SingularAttribute<PointTransfer, Long> userId;
    public static volatile SingularAttribute<PointTransfer, Long> partnerId;
    public static volatile SingularAttribute<PointTransfer, UserIdType> userIdType;
    public static volatile SingularAttribute<PointTransfer, PointTransferTypeEnum> transferType;
    public static volatile SingularAttribute<PointTransfer, BigDecimal> amount;
    public static volatile SingularAttribute<PointTransfer, Date> requestTime;
    public static volatile SingularAttribute<PointTransfer, Date> transferTime;
    public static volatile SingularAttribute<PointTransfer, String> tradeNumber;
    public static volatile SingularAttribute<PointTransfer, PointTransferStatusEnum> status;
    public static volatile SingularAttribute<PointTransfer, HulftUploadStatusEnum> uploadStatus;
    public static volatile SingularAttribute<PointTransfer, Date> uploadTime;
    public static volatile SingularAttribute<PointTransfer, PointPartner> pointPartner;
    public static volatile SingularAttribute<PointTransfer, PointUser> pointUser;
    public static volatile SingularAttribute<PointTransfer, User> investUser;
}
