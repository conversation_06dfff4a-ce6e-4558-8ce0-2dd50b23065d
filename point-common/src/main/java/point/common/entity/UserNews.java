package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "user_news")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserNews extends AbstractEntity {

    private static final long serialVersionUID = -6722377105797603952L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "read_ids", nullable = false)
    private String readIds;
}
