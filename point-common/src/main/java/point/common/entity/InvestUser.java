package point.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import point.common.constant.KycStatus;
import point.common.constant.UserStatus;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "user")
@ToString(callSuper = true, doNotUseGetters = true)
public class InvestUser {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "email", nullable = false)
    private String email;

    @JsonIgnore
    @Column(name = "password")
    private String password;

    @JsonIgnore
    @Column(name = "anti_phishing_code")
    private String antiPhishingCode;

    @Column(name = "account_non_expired", nullable = false)
    private boolean accountNonExpired = true;

    @Column(name = "account_non_locked", nullable = false)
    private boolean accountNonLocked = true;

    @Column(name = "credentials_non_expired", nullable = false)
    private boolean credentialsNonExpired = true;

    @Column(name = "enabled", nullable = false)
    private boolean enabled = true;

    @Column(name = "user_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UserStatus userStatus = UserStatus.REVIEWING;

    @Column(name = "kyc_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private KycStatus kycStatus = KycStatus.WAITING_SET_PWD;

    @Column(name = "user_kyc_id", nullable = true)
    private Long userKycId;

    @Column(name = "level", nullable = false)
    private int level = 1;

    @Column(name = "user_info_id", nullable = true)
    private Long userInfoId;

    @Column(name = "inside_account_flg", nullable = false)
    private boolean insideAccountFlg = false;

    @Column(name = "trade_uncapped", nullable = false)
    private boolean tradeUncapped = false;

    @Column(name = "insider", nullable = false)
    private boolean insider = false;

    @Column(name = "risker", nullable = false)
    private boolean risker = false;

    @Column(name = "old_user_id", nullable = true)
    private Integer oldUserId;

    @Column(name = "session_id", nullable = true)
    private String sessionId;

    @Column(name = "affiliate_info_id", nullable = true)
    private Long affiliateInfoId;

    @Column(name = "uuid", nullable = true)
    private String uuid;

    @OneToMany(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private List<UserAuthority> authorities;

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_info_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private UserInfo userInfo;

    @JsonIgnore
    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_kyc_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private UserKyc userKyc;

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "affiliate_info_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private AffiliateInfo affiliateInfo;

    @Column(name = "created_at", nullable = false)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "updated_at")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    public InvestUser(String email) {
        this.email = email;
    }
}
