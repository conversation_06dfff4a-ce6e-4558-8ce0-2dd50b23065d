package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import lombok.*;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import point.common.model.response.UserPowerBalancePageData;

@SqlResultSetMapping(
        name = "UserPowerBalancePageDataMapping",
        classes =
                @ConstructorResult(
                        targetClass = UserPowerBalancePageData.class,
                        columns = {
                            @ColumnResult(name = "id", type = Long.class),
                            @ColumnResult(name = "userId", type = Long.class),
                            @ColumnResult(name = "partnerMemberId", type = String.class),
                            @ColumnResult(name = "name", type = String.class),
                            @ColumnResult(name = "partnerNumber", type = String.class),
                            @ColumnResult(name = "idType", type = String.class),
                            @ColumnResult(name = "amount", type = BigDecimal.class)
                        }))
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "choice_power_user_rel")
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoicePowerUserRel extends AbstractEntity {

    @Column(name = "choice_power_id", nullable = false)
    private Long choicePowerId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    @ToString.Exclude
    @Fetch(FetchMode.JOIN)
    private UserIdentity userIdentity;

    @ManyToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Fetch(FetchMode.JOIN)
    private PointUser operatorPointUser;

    @ManyToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "user_id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Fetch(FetchMode.JOIN)
    private PointUser investPointUser;
}
