package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import point.common.model.dto.CurrencyPairDTO;

@Entity
@SqlResultSetMapping(
        name = "CurrencyPairMapping",
        classes =
                @ConstructorResult(
                        targetClass = CurrencyPairDTO.class,
                        columns = {
                            @ColumnResult(name = "id", type = Long.class),
                            @ColumnResult(name = "trade_type", type = String.class),
                            @ColumnResult(name = "currency_pair", type = String.class),
                            @ColumnResult(name = "min_order_amount", type = BigDecimal.class),
                            @ColumnResult(name = "max_order_amount", type = BigDecimal.class),
                            @ColumnResult(name = "pos_slippage_percent", type = BigDecimal.class),
                            @ColumnResult(name = "pos_spread_percent", type = BigDecimal.class),
                            @ColumnResult(name = "enabled", type = Boolean.class)
                        }))
@NamedNativeQuery(
        name = "findCurrencyPairs",
        query =
                """
    SELECT symbol.id, symbol.trade_type, symbol.currency_pair,
           cpc.min_order_amount, cpc.max_order_amount,
           cpc.pos_slippage_percent, cpc.pos_spread_percent, cpc.enabled
    FROM symbol
    INNER JOIN currency_pair_config cpc
    ON symbol.currency_pair = cpc.currency_pair AND symbol.trade_type = cpc.trade_type
    WHERE symbol.trade_type = :tradeType AND cpc.enabled = 1 AND symbol.visible = 1
    """,
        resultSetMapping = "CurrencyPairMapping")
public class SymbolEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
}
