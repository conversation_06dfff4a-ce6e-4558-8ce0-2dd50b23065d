package point.common.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.Currency;
import point.common.constant.TradeType;
import point.common.serializer.BigDecimalSerializer;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "exchange_summary")
@ToString(callSuper = true, doNotUseGetters = true)
public class ExchangeSummary extends AbstractEntity {

    private static final long serialVersionUID = 8299489298687748835L;

    @Column(name = "target_at", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date targetAt;

    @Column(name = "currency", nullable = false)
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @Column(name = "current_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal currentAmount = BigDecimal.ZERO;

    @Column(name = "jpy_conversion", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal jpyConversion = BigDecimal.ZERO;

    @Column(name = "deposit_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositAmount = BigDecimal.ZERO;

    @Column(name = "deposit_amount_jpy", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositAmountJpy = BigDecimal.ZERO;

    @Column(name = "deposit_fee", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositFee = BigDecimal.ZERO;

    @Column(name = "deposit_fee_jpy", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositFeeJpy = BigDecimal.ZERO;

    @Column(name = "withdrawal_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal withdrawalAmount = BigDecimal.ZERO;

    @Column(name = "withdrawal_amount_jpy", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal withdrawalAmountJpy = BigDecimal.ZERO;

    @Column(name = "withdrawal_fee", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal withdrawalFee = BigDecimal.ZERO;

    @Column(name = "withdrawal_fee_jpy", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal withdrawalFeeJpy = BigDecimal.ZERO;

    @Column(name = "transaction_fee", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal transactionFee = BigDecimal.ZERO;

    @Column(name = "transaction_fee_jpy", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal transactionFeeJpy = BigDecimal.ZERO;

    @Column(
            name = "simple_market_pos_trade_buy_amount",
            nullable = false,
            precision = 34,
            scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal simpleMarketPosTradeBuyAmount = BigDecimal.ZERO;

    @Column(
            name = "simple_market_pos_trade_buy_amount_jpy",
            nullable = false,
            precision = 34,
            scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal simpleMarketPosTradeBuyAmountJpy = BigDecimal.ZERO;

    @Column(
            name = "simple_market_pos_trade_sell_amount",
            nullable = false,
            precision = 34,
            scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal simpleMarketPosTradeSellAmount = BigDecimal.ZERO;

    @Column(
            name = "simple_market_pos_trade_sell_amount_jpy",
            nullable = false,
            precision = 34,
            scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal simpleMarketPosTradeSellAmountJpy = BigDecimal.ZERO;

    @Column(name = "simple_market_pos_trade_fee", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal simpleMarketPosTradeFee = BigDecimal.ZERO;

    @Column(name = "simple_market_pos_trade_fee_jpy", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal simpleMarketPosTradeFeeJpy = BigDecimal.ZERO;

    @Column(name = "expiration_not_continue_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal expirationNotContinueAmount = BigDecimal.ZERO;

    @Column(name = "expiration_not_continue_reward", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal expirationNotContinueReward = BigDecimal.ZERO;

    @Column(name = "cancel_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal cancelAmount = BigDecimal.ZERO;

    @Column(name = "expiration_continue_reward", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal expirationContinueReward = BigDecimal.ZERO;

    @Column(name = "trade_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;
}
