package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@MappedSuperclass
@ToString(callSuper = true, doNotUseGetters = true)
public abstract class BestPrice extends AbstractEntity {

    private static final long serialVersionUID = -1420006231123824101L;

    public static String getTableName(Symbol symbol) {
        return symbol.getTradeType().toLowerCase()
                + "_best_price_"
                + symbol.getCurrencyPair().toLowerCase();
    }

    @Getter
    @Setter
    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    @Getter
    @Setter
    @Column(name = "best_ask", precision = 34, scale = 20, nullable = false)
    private BigDecimal bestAsk;

    @Getter
    @Setter
    @Column(name = "best_bid", precision = 34, scale = 20, nullable = false)
    private BigDecimal bestBid;

    public BestPrice setProperties(Long symbolId, BigDecimal bestAsk, BigDecimal bestBid) {
        this.symbolId = symbolId;
        this.bestAsk = bestAsk;
        this.bestBid = bestBid;
        return this;
    }
}
