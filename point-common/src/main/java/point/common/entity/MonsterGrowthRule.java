package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Entity
@Table(name = "monster_growth_rule")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class MonsterGrowthRule extends AbstractEntity {

    // 売却時の合計損益From
    @Column(name = "total_profit_loss_from")
    private Long totalProfitLossFrom;

    // 売却時の合計損益To
    @Column(name = "total_profit_loss_to")
    private Long totalProfitLossTo;

    // 变换割合
    @Column(name = "conversion_rate", nullable = false)
    private BigDecimal conversionRate;

    // 獲得できる経験值(EXP)
    @Column(name = "experience_gain", nullable = false)
    private String experienceGain;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;
}
