package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MonsterGrowthRule.class)
public abstract class MonsterGrowthRule_ extends AbstractEntity_ {

    public static volatile SingularAttribute<MonsterGrowthRule, String> totalProfitLossFrom;
    public static volatile SingularAttribute<MonsterGrowthRule, String> totalProfitLossTo;
    public static volatile SingularAttribute<MonsterGrowthRule, String> conversionRate;
    public static volatile SingularAttribute<MonsterGrowthRule, String> experienceGain;
    public static volatile SingularAttribute<MonsterGrowthRule, String> createdBy;
    public static volatile SingularAttribute<MonsterGrowthRule, String> updatedBy;

    public static final String TOTAL_PROFIT_LOSS_FROM = "totalProfitLossFrom";
    public static final String TOTAL_PROFIT_LOSS_TO = "totalProfitLossTo";
    public static final String CONVERSION_RATE = "conversionRate";
    public static final String EXPERIENCE_GAIN = "experienceGain";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
}
