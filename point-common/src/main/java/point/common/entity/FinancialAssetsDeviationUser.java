package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "financial_assets_deviation_user")
@ToString(callSuper = true, doNotUseGetters = true)
public class FinancialAssetsDeviationUser extends AbstractEntity {

    private static final long serialVersionUID = 8299489842687748835L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "over_trades", nullable = false)
    private Integer overTrades;

    @Getter
    @Setter
    @Column(name = "trade_type", nullable = true)
    private String tradeType;
}
