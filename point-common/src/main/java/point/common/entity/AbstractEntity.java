package point.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@MappedSuperclass
@ToString(callSuper = true, doNotUseGetters = true)
public abstract class AbstractEntity implements Serializable {

    private static final long serialVersionUID = 7903066238019538561L;

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Getter
    @Setter
    @Column(name = "id")
    private Long id;

    @Getter
    @Setter
    @Column(name = "created_at", nullable = false)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Getter
    @Setter
    @Column(name = "updated_at")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof AbstractEntity)) {
            return false;
        }

        return getId().equals(((AbstractEntity) obj).getId());
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @JsonIgnore
    public String getFormattedCreatedAt() {
        return FormatUtil.formatJst(createdAt, FormatPattern.YYYY_MM_DD_HH_MM_SS_S);
    }

    @JsonIgnore
    public String getFormattedUpdatedAt() {
        return FormatUtil.formatJst(updatedAt, FormatPattern.YYYY_MM_DD_HH_MM_SS_S);
    }

    @JsonIgnore
    public boolean isNew() {
        return createdAt == null;
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = new Date();
    }

    @PrePersist
    public void prePersist() {
        createdAt = new Date();
        updatedAt = new Date();
    }
}
