package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(SameIpUser.class)
public abstract class SameIpUser_ extends AbstractEntity_ {

    public static volatile SingularAttribute<SameIpUser, String> ipAddress;
    public static volatile SingularAttribute<SameIpUser, String> userIds;

    public static final String IP_ADDRESS = "ipAddress";
    public static final String USER_IDS = "userIds";
}
