package point.common.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import javax.persistence.*;
import lombok.*;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "point_user")
@ToString(callSuper = true, doNotUseGetters = true)
public class PointUser extends AbstractEntity {

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "partner_id", nullable = false)
    private Long partnerId;

    @Column(name = "partner_member_id", nullable = false)
    private String partnerMemberId;

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "id", referencedColumnName = "id", insertable = false, updatable = false)
    @ToString.Exclude
    @Fetch(FetchMode.JOIN)
    private UserIdentity userIdentity;

    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "partner_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    @ToString.Exclude
    @Fetch(FetchMode.JOIN)
    private PointPartner pointPartner;

    @Getter
    @Setter
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id", // PointUser FK
            referencedColumnName = "id",
            insertable = false,
            updatable = false, // User PK
            foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @JsonBackReference
    @ToString.Exclude
    private User user;
}
