package point.common.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.*;
import point.common.serializer.BigDecimalSerializer;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Builder
@Table(name = "ponta_convert_worker")
@ToString(callSuper = true, doNotUseGetters = true)
public class PontaConvertWorker extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "trade_number", nullable = false)
    private String tradeNumber;

    @Column(name = "contents", nullable = false, columnDefinition = "TEXT")
    private String contents;

    @Column(name = "flag", nullable = false)
    private Integer flag;

    @Column(name = "point_amount", precision = 34, scale = 20, nullable = false)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal pointAmount;
}
