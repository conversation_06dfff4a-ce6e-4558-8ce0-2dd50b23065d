package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(AppConfiguration.class)
public abstract class AppConfiguration_ extends AbstractEntity_ {

    public static volatile SingularAttribute<AppConfiguration, String> key;
    public static volatile SingularAttribute<AppConfiguration, String> value;

    public static final String KEY = "key";
    public static final String VALUE = "value";
}
