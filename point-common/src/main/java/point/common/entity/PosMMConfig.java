package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import point.common.constant.Exchange;

@Entity
@Setter
@Getter
@Table(name = "pos_market_maker_config")
public class PosMMConfig extends AbstractEntity {

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.EAGER)
    @JoinColumn(
            name = "symbol_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    private Symbol symbol;

    @Column(name = "mm")
    @Enumerated(EnumType.STRING)
    private Exchange exchange;

    private boolean enabled;

    private BigDecimal quantity;
}
