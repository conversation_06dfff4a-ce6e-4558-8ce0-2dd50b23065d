package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(OnetimeBankAccount.class)
public abstract class OnetimeBankAccount_ extends AbstractEntity_ {

    public static volatile SingularAttribute<OnetimeBankAccount, Long> branchCode;
    public static volatile SingularAttribute<OnetimeBankAccount, String> branchName;
    public static volatile SingularAttribute<OnetimeBankAccount, Long> accountNumber;
    public static volatile SingularAttribute<OnetimeBankAccount, Long> userId;
    public static volatile SingularAttribute<OnetimeBankAccount, User> user;

    public static volatile SingularAttribute<OnetimeBankAccount, String> vaTypeCode;
    public static volatile SingularAttribute<OnetimeBankAccount, String> vaTypeName;
    public static volatile SingularAttribute<OnetimeBankAccount, String> vaHolderNameKana;
    public static volatile SingularAttribute<OnetimeBankAccount, String> vaId;

    public static final String BRANCH_CODE = "branchCode";
    public static final String BRANCH_NAME = "branchName";
    public static final String ACCOUNT_NUMBER = "accountNumber";
    public static final String USER_ID = "userId";
    public static final String USER = "user";

    public static final String VA_TYPE_CODE = "vaTypeCode";
    public static final String VA_TYPE_NAME = "vaTypeName";
    public static final String VA_HOLDER_NAME_KANA = "vaHolderNameKana";
    public static final String VA_ID = "vaId";
}
