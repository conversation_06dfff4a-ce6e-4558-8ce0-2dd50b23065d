package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.ChoiceGetType;
import point.common.constant.ChoiceObtainFrequency;
import point.common.constant.UserIdType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoiceActivityRule.class)
public abstract class ChoiceActivityRule_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoiceActivityRule, String> activityName;
    public static volatile SingularAttribute<ChoiceActivityRule, ChoiceActivityFunction>
            activityFunction;
    public static volatile SingularAttribute<ChoiceActivityRule, ChoiceGetType> getType;
    public static volatile SingularAttribute<ChoiceActivityRule, BigDecimal> powerAmountRate;
    public static volatile SingularAttribute<ChoiceActivityRule, ChoiceObtainFrequency>
            obtainFrequency;
    public static volatile SingularAttribute<ChoiceActivityRule, Long> powerAmount;
    public static volatile SingularAttribute<ChoiceActivityRule, Date> effectiveDate;
    public static volatile SingularAttribute<ChoiceActivityRule, Date> expiryDate;
    public static volatile SingularAttribute<ChoiceActivityRule, String> createdBy;
    public static volatile SingularAttribute<ChoiceActivityRule, String> status;
    public static volatile SingularAttribute<ChoiceActivityRule, String> updatedBy;
    public static volatile SingularAttribute<ChoiceActivityRule, UserIdType> idType;
    public static volatile SingularAttribute<ChoiceActivityRule, Integer> ordered;

    public static final String ACTIVITY_NAME = "activityName";
    public static final String ACTIVITY_FUNCTION = "activityFunction";
    public static final String GET_TYPE = "getType";
    public static final String POWER_AMOUNT_RATE = "powerAmountRate";
    public static final String OBTAIN_FREQUENCY = "obtainFrequency";
    public static final String POWER_AMOUNT = "powerAmount";
    public static final String EFFECTIVE_DATE = "effectiveDate";
    public static final String EXPIRY_DATE = "expiryDate";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
    public static final String STATUS = "status";
    public static final String ORDERED = "ordered";
}
