package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MonsterGrowthHistory.class)
public abstract class MonsterGrowthHistory_ extends AbstractEntity_ {
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> userId;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> monsterId;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> symbolId;
    public static volatile SingularAttribute<MonsterGrowthHistory, BigDecimal> income;
    public static volatile SingularAttribute<MonsterGrowthHistory, BigDecimal> conversionRate;
    public static volatile SingularAttribute<MonsterGrowthHistory, BigDecimal> bonusRate;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> weekFood;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> experienceEarned;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> currentExperience;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> nextLevelExperience;
    public static volatile SingularAttribute<MonsterGrowthHistory, BigDecimal> growthRate;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> levelBefore;
    public static volatile SingularAttribute<MonsterGrowthHistory, Long> levelAfter;
    public static volatile SingularAttribute<MonsterGrowthHistory, String> createdBy;
    public static volatile SingularAttribute<MonsterGrowthHistory, String> updatedBy;
    public static volatile SingularAttribute<MonsterGrowthHistory, MonsterBase> monsterBase;

    public static final String USER_ID = "userId";
    public static final String MONSTER_ID = "monsterId";
    public static final String SYMBOL_ID = "symbolId";
    public static final String INCOME = "income";
    public static final String CONVERSION_RATE = "conversionRate";
    public static final String BONUS_RATE = "bonusRate";
    public static final String WEEK_FOOD = "weekFood";
    public static final String EXPERIENCE_EARNED = "experienceEarned";
    public static final String CURRENT_EXPERIENCE = "currentExperience";
    public static final String NEXT_LEVEL_EXPERIENCE = "nextLevelExperience";
    public static final String GROWTH_RATE = "growthRate";
    public static final String LEVEL_BEFORE = "levelBefore";
    public static final String LEVEL_AFTER = "levelAfter";
}
