package point.common.entity;

import javax.persistence.*;
import lombok.*;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.ChoiceGetType;
import point.common.constant.ChoiceObtainFrequency;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Builder
@Table(name = "choice_power_sync")
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoicePowerSync extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "choice_activity_rule_id", nullable = false)
    private Long choiceActivityRuleId;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "activity_function",
            nullable = false,
            columnDefinition =
                    "ENUM('LOGIN_OPERATION_COURSE','PASS_OPERATION_QUIZ','LOGIN_INVESTMENT_COURSE','PASS_INVESTMENT_QUIZ','OPEN_INVESTMENT_ACCOUNT','FARM','BUY_SELL_TRADE','ELECTION_RESULTS_OF_VOTE','CROP_HARVESTED_SHARED')")
    private ChoiceActivityFunction activityFunction;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "obtain_frequency",
            nullable = false,
            columnDefinition = "ENUM('DAILY','ONCE','MONTHLY','EACH_DAY','EACH_TIME')")
    private ChoiceObtainFrequency obtainFrequency;

    @Enumerated(EnumType.STRING)
    @Column(name = "get_Type", nullable = false, columnDefinition = "ENUM('FIXED','PROPORTIONAL')")
    private ChoiceGetType getType;

    @Column(name = "power_amount", nullable = false)
    private Long powerAmount;

    @Column(name = "synchronize_flag")
    private Integer synchronizeFlag;
}
