package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "investment_purpose_deviation_user")
@ToString(callSuper = true, doNotUseGetters = true)
public class InvestmentPurposeDeviationUser extends AbstractEntity {

    private static final long serialVersionUID = 8299489877587748835L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "trade_type", nullable = true)
    private String tradeType;

    @Getter
    @Setter
    @Column(name = "trades", nullable = false)
    private Integer trades;
}
