package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.UserIdType;

@Setter
@Getter
@Entity
@NoArgsConstructor
@Table(name = "user_identity")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserIdentity extends AbstractEntity {

    @Enumerated(value = EnumType.STRING)
    @Column(name = "id_type", nullable = false)
    private UserIdType idType;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id", referencedColumnName = "id")
    @ToString.Exclude
    private User investUser;
}
