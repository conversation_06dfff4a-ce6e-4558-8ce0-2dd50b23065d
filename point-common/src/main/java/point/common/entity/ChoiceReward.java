package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import javax.persistence.*;
import lombok.*;
import point.common.constant.ChoiceRewardType;
import point.common.constant.WithdrawStatusType;
import point.common.model.response.ChoiceRewardPageData;

@SqlResultSetMapping(
        name = "ChoiceRewardPageDataMapping",
        classes =
                @ConstructorResult(
                        targetClass = ChoiceRewardPageData.class,
                        columns = {
                            @ColumnResult(name = "rewardId", type = Long.class),
                            @ColumnResult(name = "userId", type = Long.class),
                            //                        @ColumnResult(name = "updateUserId", type =
                            // Long.class),
                            @ColumnResult(name = "rewardAmount", type = BigDecimal.class),
                            @ColumnResult(name = "rewardDate", type = Date.class),
                            @ColumnResult(name = "choicePAmount", type = BigDecimal.class),
                            @ColumnResult(name = "withdrawAmount", type = BigDecimal.class),
                            @ColumnResult(name = "withdrawDate", type = Date.class)
                        }))
@Getter
@Setter
@Entity
@Builder
@Table(name = "choice_reward")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoiceReward extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "activity_id", nullable = false)
    private Long activityId;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Column(name = "remaining_amount", nullable = false)
    private BigDecimal remainingAmount;

    @Enumerated(EnumType.STRING)
    @Column(name = "reward_type", columnDefinition = "ENUM('CHOICE_P')")
    private ChoiceRewardType choiceRewardType;

    @Column(name = "reward_time", nullable = false)
    private Date rewardTime;

    @Column(name = "expiry_time", nullable = false)
    private Date expiryTime;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "withdraw_status",
            columnDefinition =
                    "ENUM('FULLY_WITHDRAWN','PARTIALLY_WITHDRAWN','FULLY_EXPIRED','PARTIALLY_EXPIRED','PENDING')")
    private WithdrawStatusType withdrawStatusType;

    // use for SqlUtil.batchInsert
    //    @Column(name = "created_at", nullable = false)
    //    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    //    @Temporal(TemporalType.TIMESTAMP)
    //    private Date createdAt;
    //
    //    @Column(name = "updated_at")
    //    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    //    @Temporal(TemporalType.TIMESTAMP)
    //    private Date updatedAt;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChoiceReward that = (ChoiceReward) o;
        return Objects.equals(getId(), that.getId())
                && Objects.equals(userId, that.userId)
                && Objects.equals(activityId, that.activityId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), userId, activityId);
    }
}
