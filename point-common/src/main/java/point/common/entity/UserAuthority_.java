package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Authority;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserAuthority.class)
public abstract class UserAuthority_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserAuthority, Authority> authority;
    public static volatile SingularAttribute<UserAuthority, Long> userId;

    public static final String AUTHORITY = "authority";
    public static final String USER_ID = "userId";
}
