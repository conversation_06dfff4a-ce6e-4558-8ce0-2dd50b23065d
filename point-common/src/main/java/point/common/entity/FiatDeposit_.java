package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.FiatDepositStatus;
import point.common.constant.FiatDepositSubStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(FiatDeposit.class)
public abstract class FiatDeposit_ extends AbstractEntity_ {

    public static volatile SingularAttribute<FiatDeposit, Long> userId;
    public static volatile SingularAttribute<FiatDeposit, Long> bankAccountId;
    public static volatile SingularAttribute<FiatDeposit, Long> onetimeBankAccountId;
    public static volatile SingularAttribute<FiatDeposit, BigDecimal> amount;
    public static volatile SingularAttribute<FiatDeposit, BigDecimal> fee;
    public static volatile SingularAttribute<FiatDeposit, FiatDepositStatus> fiatDepositStatus;
    public static volatile SingularAttribute<FiatDeposit, String> comment;
    public static volatile SingularAttribute<FiatDeposit, User> user;
    public static volatile SingularAttribute<FiatDeposit, OnetimeBankAccount> onetimeBankAccount;
    public static volatile SingularAttribute<FiatDeposit, PointUser> pointUser;
    public static volatile SingularAttribute<FiatDeposit, FiatDepositSubStatus>
            fiatDepositSubStatus;

    public static final String USER_ID = "userId";
    public static final String BANK_ACCOUNT_ID = "bankAccountId";
    public static final String ONETIME_BANK_ACCOUNT_ID = "onetimeBankAccountId";
    public static final String AMOUNT = "amount";
    public static final String FEE = "fee";
    public static final String FIAT_DEPOSIT_STATUS = "fiatDepositStatus";
    public static final String COMMENT = "comment";
    public static final String BANK_ACCOUNT = "bankAccount";
    public static final String USER = "user";
    public static final String ONETIME_BANK_ACCOUNT = "onetimeBankAccount";
    public static final String POINT_USER = "pointUser";
}
