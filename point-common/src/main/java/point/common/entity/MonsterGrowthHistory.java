package point.common.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import point.common.serializer.BigDecimalSerializer;

@Setter
@Getter
@Entity
@Table(name = "monster_growth_history")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class MonsterGrowthHistory extends AbstractEntity {

    private static final long serialVersionUID = -6424284956090933377L;

    // ユーザーID
    @Column(name = "user_id")
    private Long userId;

    // モンスターID
    @Column(name = "monster_id")
    private Long monsterId;

    // 通貨
    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    // 売却損益
    @Column(name = "income")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal income;

    // 変換割合
    @Column(name = "conversion_rate")
    private BigDecimal conversionRate;

    // ボーナス割合
    @Column(name = "bonus_rate")
    private BigDecimal bonusRate;

    // 今週食べたいもの
    @Column(name = "week_food")
    private Long weekFood;

    // 獲得経験値
    @Column(name = "experience_earned")
    private Long experienceEarned;

    // 現在経験値
    @Column(name = "current_experience")
    private Long currentExperience;

    // 次のレベルの経験値
    @Column(name = "next_level_experience")
    private Long nextLevelExperience;

    // 成長比率
    @Column(name = "growth_rate")
    private BigDecimal growthRate;

    // レベル(食前)
    @Column(name = "level_before")
    private Long levelBefore;

    // レベル(食後)
    @Column(name = "level_after")
    private Long levelAfter;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Getter
    @Setter
    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "monster_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private MonsterBase monsterBase;
}
