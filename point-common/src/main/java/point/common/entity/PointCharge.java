package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "point_charge")
@ToString(callSuper = true, doNotUseGetters = true)
public class PointCharge extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "point_user_id", nullable = false)
    private Long pointUserId;

    @ManyToOne
    @JoinColumn(name = "partner_id", nullable = false)
    private PointPartner partner;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Column(name = "charge_time", nullable = false)
    private Date chargeTime;
}
