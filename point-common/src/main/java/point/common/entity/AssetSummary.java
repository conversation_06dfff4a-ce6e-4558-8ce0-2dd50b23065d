package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.Currency;
import point.common.serializer.BigDecimalSerializer;

@Entity
@NoArgsConstructor
@Table(name = "asset_summary")
@ToString(callSuper = true, doNotUseGetters = true)
public class AssetSummary extends AbstractEntity {

    private static final long serialVersionUID = 7292022661970539948L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "target_at", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date targetAt;

    @Getter
    @Setter
    @Column(name = "currency", nullable = false)
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @Getter
    @Setter
    @Column(name = "current_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal currentAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "jpy_conversion", nullable = false, precision = 34, scale = 20)
    private BigDecimal jpyConversion = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "deposit_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "deposit_amount_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal depositAmountJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "deposit_fee", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositFee = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "deposit_fee_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal depositFeeJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "withdrawal_amount", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal withdrawalAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "withdrawal_amount_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal withdrawalAmountJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "withdrawal_fee", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal withdrawalFee = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "withdrawal_fee_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal withdrawalFeeJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "transaction_fee", nullable = false, precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal transactionFee = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "transaction_fee_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal transactionFeeJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "pos_trade_buy_amount", nullable = false, precision = 34, scale = 20)
    private BigDecimal posTradeBuyAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "pos_trade_buy_amount_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal posTradeBuyAmountJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "pos_trade_sell_amount", nullable = false, precision = 34, scale = 20)
    private BigDecimal posTradeSellAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "pos_trade_sell_amount_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal posTradeSellAmountJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "pos_trade_fee", nullable = false, precision = 34, scale = 20)
    private BigDecimal posTradeFee = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "pos_trade_fee_jpy", nullable = false, precision = 34, scale = 20)
    private BigDecimal posTradeFeeJpy = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "expiration_not_continue_amount", nullable = false, precision = 34, scale = 20)
    private BigDecimal expirationNotContinueAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "expiration_not_continue_reward", nullable = false, precision = 34, scale = 20)
    private BigDecimal expirationNotContinueReward = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "cancel_amount", nullable = false, precision = 34, scale = 20)
    private BigDecimal cancelAmount = BigDecimal.ZERO;

    @Getter
    @Setter
    @Column(name = "expiration_continue_reward", nullable = false, precision = 34, scale = 20)
    private BigDecimal expirationContinueReward = BigDecimal.ZERO;

    @JsonIgnore
    public AssetKey getAssetKey() {
        return new AssetKey(userId, currency);
    }
}
