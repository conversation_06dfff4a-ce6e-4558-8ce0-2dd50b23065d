package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(InvestmentPurposeDeviationUser.class)
public abstract class InvestmentPurposeDeviationUser_ extends AbstractEntity_ {

    public static volatile SingularAttribute<InvestmentPurposeDeviationUser, Long> userId;
    public static volatile SingularAttribute<InvestmentPurposeDeviationUser, Integer> trades;
    public static volatile SingularAttribute<InvestmentPurposeDeviationUser, String> type;
    public static volatile SingularAttribute<InvestmentPurposeDeviationUser, String> tradeType;

    public static final String TYPE = "type";
    public static final String USER_ID = "userId";
    public static final String TRADES = "trades";
}
