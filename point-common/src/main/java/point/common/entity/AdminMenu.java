package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

@Entity
@NoArgsConstructor
@Table(name = "admin_menu")
@Where(clause = "enable =1")
@Getter
@Setter
public class AdminMenu extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JsonIgnore
    @OneToMany(mappedBy = "parent")
    private List<AdminMenu> children;

    @ManyToOne
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "pid", insertable = false, updatable = false)
    private AdminMenu parent;

    private Long pid;

    @Column(name = "menu_name")
    private String menuName;

    @Column(name = "menu_key")
    private String menuKey;

    private boolean enable;

    @JsonIgnore
    @ManyToMany(mappedBy = "menus")
    private List<AdminRole> roles;

    @Column(name = "menu_group")
    private String menuGroup;
}
