package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(BankAccount.class)
public abstract class BankAccount_ extends AbstractEntity_ {

    public static volatile SingularAttribute<BankAccount, Long> userId;
    public static volatile SingularAttribute<BankAccount, User> user;
    public static volatile SingularAttribute<BankAccount, Long> bankId;
    public static volatile SingularAttribute<BankAccount, Bank> bank;
    public static volatile SingularAttribute<BankAccount, String> accountType;
    public static volatile SingularAttribute<BankAccount, String> accountNumber;
    public static volatile SingularAttribute<BankAccount, String> accountName;
    public static volatile SingularAttribute<BankAccount, Boolean> deleted;

    public static final String USER_ID = "userId";
    public static final String USER = "user";
    public static final String BANK_ID = "bankId";
    public static final String BANK = "bank";
    public static final String ACCOUNT_TYPE = "accountType";
    public static final String ACCOUNT_NUMBER = "accountNumber";
    public static final String ACCOUNT_NAME = "accountName";
    public static final String DELETED = "deleted";
}
