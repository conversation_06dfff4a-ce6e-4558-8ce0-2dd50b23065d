package point.common.entity;

import java.math.BigDecimal;
import java.util.List;
import javax.persistence.*;
import lombok.*;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "choice_power")
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoicePower extends AbstractEntity {

    @Column(name = "amount", nullable = false)
    private Long amount;

    @Column(name = "choice_p_amount")
    private BigDecimal choicePAmount;

    @OneToMany(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "choice_power_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    @ToString.Exclude
    @Fetch(FetchMode.JOIN)
    private List<ChoicePowerUserRel> choicePowerUserRels;
}
