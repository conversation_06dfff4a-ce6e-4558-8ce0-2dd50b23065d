package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.MfaType;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "user_mfa")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserMfa extends AbstractEntity {

    private static final long serialVersionUID = -6722377105797603952L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "mfa_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private MfaType mfaType = MfaType.EMAIL;

    @Getter
    @Setter
    @Column(name = "secret_key")
    private String secretKey;

    @Getter
    @Setter
    @Column(name = "authenticated", nullable = false)
    private boolean authenticated = true;

    public boolean isGoogle() {
        return this.mfaType == MfaType.GOOGLE;
    }

    public boolean isSms() {
        return this.mfaType == MfaType.SMS;
    }
}
