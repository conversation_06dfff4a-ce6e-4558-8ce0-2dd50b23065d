package point.common.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_antisocial_check")
public class UserAntisocialCheck extends AbstractEntity {

    @Basic
    @Column(name = "user_id")
    private Long userId;

    @Basic
    @Column(name = "user_info_id")
    private Long userInfoId;

    @Basic
    @Column(name = "kyc_flag")
    private String kycFlag;

    @Basic
    @Column(name = "reference_id")
    private String referenceId;

    @Basic
    @Column(name = "check_group_key")
    private String checkGroupKey;
}
