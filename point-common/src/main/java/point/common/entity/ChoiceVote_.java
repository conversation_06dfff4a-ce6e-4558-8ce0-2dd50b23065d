package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.constant.ChoiceVoteResult;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoiceVote.class)
public abstract class ChoiceVote_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoiceVote, Long> userId;
    public static volatile SingularAttribute<ChoiceVote, Long> activityId;
    public static volatile SingularAttribute<ChoiceVote, ChoiceVoteResult> voteResult;
    public static volatile SingularAttribute<ChoiceVote, ChoiceActivityVoteResult> voteDirection;
    public static volatile SingularAttribute<ChoiceVote, Long> votePower;
    public static volatile SingularAttribute<ChoiceVote, Date> voteTime;
    public static volatile SingularAttribute<ChoiceVote, String> description;
    public static volatile SingularAttribute<ChoiceVote, Integer> withdrawalAllowed;
    public static volatile SingularAttribute<ChoiceVote, UserIdentity> userIdentity;
    public static volatile SingularAttribute<ChoiceVote, ChoiceReward> choiceReward;

    public static final String USER_ID = "userId";
    public static final String ACTIVITY_ID = "activityId";
    public static final String VOTE_RESULT = "voteResult";
    public static final String VOTE_DIRECTION = "voteDirection";
    public static final String VOTE_POWER = "votePower";
    public static final String VOTE_TIME = "voteTime";
    public static final String DESCRIPTION = "description";
    public static final String WITHDRAW_ALALLOWED = "withdrawalAllowed";
}
