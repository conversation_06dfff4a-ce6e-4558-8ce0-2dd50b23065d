package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.Exchange;
import point.common.constant.TradeType;

@Entity
@NoArgsConstructor
@Table(name = "cover_order_config")
@ToString(callSuper = true, doNotUseGetters = true)
public class CoverOrderConfig extends AbstractEntity {

    private static final long serialVersionUID = 8299489298682538835L;

    @Getter
    @Setter
    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    @Getter
    @Setter
    @Column(name = "exchange", nullable = false)
    @Enumerated(EnumType.STRING)
    private Exchange exchange;

    @Getter
    @Setter
    @Column(name = "range_price_percent", precision = 34, scale = 20, nullable = false)
    private BigDecimal rangePricePercent;

    @Getter
    @Setter
    @Column(name = "min_order_amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal minOrderAmount;

    @Getter
    @Setter
    @Column(name = "enabled", nullable = false)
    private boolean enabled = false;

    @Getter
    @Setter
    @Column(name = "trade_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Getter
    @Setter
    @Column(name = "order_amount_percent")
    private BigDecimal orderAmountPercent;
}
