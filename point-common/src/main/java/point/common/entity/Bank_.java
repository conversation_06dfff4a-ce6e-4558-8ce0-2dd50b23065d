package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Bank.class)
public abstract class Bank_ extends AbstractEntity_ {

    public static volatile SingularAttribute<Bank, String> bankCode;
    public static volatile SingularAttribute<Bank, String> branchCode;
    public static volatile SingularAttribute<Bank, String> bankNameKana;
    public static volatile SingularAttribute<Bank, String> bankName;
    public static volatile SingularAttribute<Bank, String> branchNameKana;
    public static volatile SingularAttribute<Bank, String> branchName;

    public static final String BANK_CODE = "bankCode";
    public static final String BRANCH_CODE = "branchCode";
    public static final String BANK_NAME_KANA = "bankNameKana";
    public static final String BANK_NAME = "bankName";
    public static final String BRANCH_NAME_KANA = "branchNameKana";
    public static final String BRANCH_NAME = "branchName";
}
