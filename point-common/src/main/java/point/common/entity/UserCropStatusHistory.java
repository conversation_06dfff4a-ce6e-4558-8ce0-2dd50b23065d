package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.TradeType;

@Setter
@Getter
@Entity
@Table(name = "user_crop_status_history")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class UserCropStatusHistory extends AbstractEntity {

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "asset_id")
    private Long assetId;

    @Column(name = "trade_type")
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Column(name = "before_growth_stage_id")
    private Integer beforeGrowthStageId;

    @Column(name = "after_growth_stage_id")
    private Integer afterGrowthStageId;

    @Column(name = "profit_loss_amount")
    private Integer profitLossAmount;
}
