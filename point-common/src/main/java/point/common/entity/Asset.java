package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import point.common.constant.Currency;
import point.common.serializer.BigDecimalSerializer;

@Entity
@Table(name = "asset")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class Asset extends AbstractEntity {

    private static final long serialVersionUID = -8572975693891497381L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "currency", nullable = false)
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @Getter
    @Setter
    @Column(name = "onhand_amount", precision = 34, scale = 20, nullable = false)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal onhandAmount;

    @Getter
    @Setter
    @Column(name = "locked_amount", precision = 34, scale = 20, nullable = false)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal lockedAmount;

    @JsonIgnore
    public BigDecimal getScaledOnhandAmount() {
        return currency.getScaledAmount(onhandAmount);
    }

    @JsonIgnore
    public BigDecimal getScaledLockedAmount() {
        return currency.getScaledAmount(lockedAmount);
    }

    @Getter
    @Setter
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false,
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @Fetch(FetchMode.JOIN)
    private UserIdentity userIdentity;

    @Getter
    @Setter
    @Column(name = "eval_profit_loss_amt", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmt;

    @Getter
    @Setter
    @Column(name = "eval_profit_loss_amt_rate", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal evalProfitLossAmtRate;

    @Getter
    @Setter
    @Column(name = "avg_acq_unit_price", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal avgAcqUnitPrice;

    @Getter
    @Setter
    @Column(name = "asset_total", precision = 34, scale = 20)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal assetTotal;
}
