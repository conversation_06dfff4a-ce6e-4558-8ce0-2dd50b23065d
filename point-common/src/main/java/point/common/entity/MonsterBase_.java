package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.UserIdType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MonsterBase.class)
public abstract class MonsterBase_ extends AbstractEntity_ {

    public static volatile SingularAttribute<MonsterBase, String> name;
    public static volatile SingularAttribute<MonsterBase, String> imgUrl;
    public static volatile SingularAttribute<MonsterBase, String> createdBy;
    public static volatile SingularAttribute<MonsterBase, String> updatedBy;
    public static volatile SingularAttribute<MonsterBase, UserIdType> idType;
    public static volatile SingularAttribute<MonsterBase, Long> maxLevel;

    public static final String NAME = "name";
    public static final String IMG_URL = "imgUrl";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
    public static final String ID_TYPE = "idType";
    public static final String MAX_LEVEL = "maxLevel";
}
