package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.userdetails.UserDetails;
import point.common.constant.KycStatus;
import point.common.constant.UserStatus;

@Entity
@NoArgsConstructor
@Table(name = "user")
@ToString(callSuper = true, doNotUseGetters = true)
public class User extends AbstractEntity implements UserDetails, CredentialsContainer {

    private static final long serialVersionUID = -1318929503160277144L;

    @Getter
    @Setter
    @Column(name = "email", nullable = false)
    private String email;

    @JsonIgnore
    @Getter
    @Setter
    @Column(name = "password")
    private String password;

    @JsonIgnore
    @Getter
    @Setter
    @Column(name = "anti_phishing_code")
    private String antiPhishingCode;

    @Getter
    @Setter
    @Column(name = "account_non_expired", nullable = false)
    private boolean accountNonExpired = true;

    @Getter
    @Setter
    @Column(name = "account_non_locked", nullable = false)
    private boolean accountNonLocked = true;

    @Getter
    @Setter
    @Column(name = "credentials_non_expired", nullable = false)
    private boolean credentialsNonExpired = true;

    @Getter
    @Setter
    @Column(name = "enabled", nullable = false)
    private boolean enabled = true;

    @Getter
    @Setter
    @Column(name = "user_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UserStatus userStatus = UserStatus.REVIEWING;

    @Getter
    @Setter
    @Column(name = "kyc_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private KycStatus kycStatus = KycStatus.WAITING_SET_PWD;

    @Getter
    @Setter
    @Column(name = "user_kyc_id", nullable = true)
    private Long userKycId;

    @Getter
    @Setter
    @Column(name = "level", nullable = false)
    private int level = 1;

    @Getter
    @Setter
    @Column(name = "user_info_id", nullable = true)
    private Long userInfoId;

    @Getter
    @Setter
    @Column(name = "inside_account_flg", nullable = false)
    private boolean insideAccountFlg = false;

    @Getter
    @Setter
    @Column(name = "trade_uncapped", nullable = false)
    private boolean tradeUncapped = false;

    @Getter
    @Setter
    @Column(name = "insider", nullable = false)
    private boolean insider = false;

    @Getter
    @Setter
    @Column(name = "risker", nullable = false)
    private boolean risker = false;

    @Getter
    @Setter
    @Column(name = "old_user_id", nullable = true)
    private Integer oldUserId;

    @Getter
    @Setter
    @Column(name = "session_id", nullable = true)
    private String sessionId;

    @Getter
    @Setter
    @Column(name = "affiliate_info_id", nullable = true)
    private Long affiliateInfoId;

    @Getter
    @Setter
    @Column(name = "uuid", nullable = true)
    private String uuid;

    @Getter
    @Setter
    @OneToMany(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private List<UserAuthority> authorities;

    @Getter
    @Setter
    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_info_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private UserInfo userInfo;

    @Getter
    @Setter
    @JsonIgnore
    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_kyc_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private UserKyc userKyc;

    @Getter
    @Setter
    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "affiliate_info_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private AffiliateInfo affiliateInfo;

    @Getter
    @Setter
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id", referencedColumnName = "id", insertable = false, updatable = false)
    @ToString.Exclude
    @Fetch(FetchMode.JOIN)
    private UserIdentity userIdentity;

    @Getter
    @Setter
    @OneToOne(mappedBy = "user", cascade = CascadeType.DETACH, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @NotFound(action = NotFoundAction.IGNORE)
    @JsonManagedReference
    private PointUser pointUser;

    public User(String email, String password) {
        this.email = email;
        this.password = password;
    }

    public User(String email) {
        this.email = email;
    }

    @Override
    public void eraseCredentials() {
        password = null;
    }

    @Override
    public String getUsername() {
        return email;
    }

    public boolean isActive() {
        return userStatus == UserStatus.ACTIVE;
    }

    public boolean isTradable() {
        return (switch (this.userStatus) {
            case ACTIVE, UNWITHDRAWABLE -> true;
            default -> false;
        });
    }

    public boolean isAllowedToLogin() {
        return (switch (this.userStatus) {
            case REVIEWING, ACTIVE, UNTRADABLE, UNWITHDRAWABLE -> true;
            default -> false;
        });
    }

    @JsonIgnore
    public String getName() {
        return userInfo.getLastName() + " " + userInfo.getFirstName();
    }
}
