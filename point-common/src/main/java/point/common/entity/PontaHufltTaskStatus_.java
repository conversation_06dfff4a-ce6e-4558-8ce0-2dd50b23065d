package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PontaHulftTaskStatus.class)
public abstract class PontaHufltTaskStatus_ extends AbstractEntity_ {
    public static volatile SingularAttribute<PontaHulftTaskStatus, String> taskName;
    public static volatile SingularAttribute<
                    PontaHulftTaskStatus, PontaHulftTaskStatus.ParsingStatus>
            parsingStatus;
    public static volatile SingularAttribute<PontaHulftTaskStatus, String> errorMessage;
    public static volatile SingularAttribute<PontaHulftTaskStatus, String> sendContent;
    public static volatile SingularAttribute<PontaHulftTaskStatus, java.util.Date> parsingStartTime;
    public static volatile SingularAttribute<PontaHulftTaskStatus, java.util.Date> parsingEndTime;
}
