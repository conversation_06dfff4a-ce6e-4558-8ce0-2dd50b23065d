package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.MfaType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserMfa.class)
public abstract class UserMfa_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserMfa, Long> userId;
    public static volatile SingularAttribute<UserMfa, MfaType> mfaType;
    public static volatile SingularAttribute<UserMfa, String> secretKey;
    public static volatile SingularAttribute<UserMfa, Boolean> authenticated;

    public static final String USER_ID = "userId";
    public static final String MFA_TYPE = "mfaType";
    public static final String SECRET_KEY = "secretKey";
    public static final String AUTHENTICATED = "authenticated";
}
