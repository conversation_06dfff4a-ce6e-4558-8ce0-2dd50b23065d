package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(CurrencyConfig.class)
public abstract class CropGrowthStageHistory_ extends AbstractEntity_ {

    public static volatile SingularAttribute<CropGrowthStageHistory, TradeType> tradeType;
    public static volatile SingularAttribute<CropGrowthStageHistory, String> beforGrowthStage;
    public static volatile SingularAttribute<CropGrowthStageHistory, String> beforEvaluation;
    public static volatile SingularAttribute<CropGrowthStageHistory, String> afterGrowthStage;
    public static volatile SingularAttribute<CropGrowthStageHistory, String> afterEvaluation;
    public static volatile SingularAttribute<CropGrowthStageHistory, String> createdBy;

    public static final String TRADE_TYPE = "tradeType";
    public static final String BEFOR_GROWTH_STAGE = "beforGrowthStage";
    public static final String BEFOR_EVALUATION = "beforEvaluation";
    public static final String AFTER_GROWTH_STAGE = "afterGrowthStage";
    public static final String AFTER_EVALUATION = "afterEvaluation";
    public static final String CREATED_BY = "createdBy";
}
