package point.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.AntisocialStatus;
import point.common.constant.KycMailStatus;
import point.common.constant.KycStatus;
import point.common.constant.KycType;

@Entity
@Table(name = "user_kyc")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class UserKyc extends AbstractEntity {

    private static final long serialVersionUID = 7148773614961557271L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "kyc_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private KycType kycType = KycType.NONE;

    @Getter
    @Setter
    @Column(name = "kyc_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private KycStatus kycStatus = KycStatus.WAITING_SET_PWD;

    @Getter
    @Setter
    @Column(name = "kyc_mail_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private KycMailStatus kycMailStatus = KycMailStatus.NONE;

    @Getter
    @Setter
    @Column(name = "mail_send_at", nullable = true)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date mailSendAt;

    @Getter
    @Setter
    @Column(name = "judging_comment", nullable = false)
    private String judgingComment = "";

    @Getter
    @Setter
    @Column(name = "aml_cft_comment", nullable = false)
    private String amlCftComment = "";

    @Getter
    @Setter
    @Column(name = "antisocial_status")
    @Enumerated(EnumType.STRING)
    private AntisocialStatus antisocialStatus = AntisocialStatus.BEFORE_EXAMINATION;

    @Getter @Setter private String operator;

    @Getter
    @Setter
    @Column(name = "email")
    private String email;

    @Getter
    @Setter
    @Column(name = "user_info_id")
    private Long userInfoId;

    @Getter
    @Setter
    @Column(name = "change_type")
    private String changeType;

    public UserKyc(Long userId) {

        this.userId = userId;
    }
}
