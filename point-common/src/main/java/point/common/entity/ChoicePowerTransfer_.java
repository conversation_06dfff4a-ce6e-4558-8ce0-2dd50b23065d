package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.ChoicePowerTransferType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoicePowerTransfer.class)
public abstract class ChoicePowerTransfer_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoicePowerTransfer, Long> userId;
    public static volatile SingularAttribute<ChoicePowerTransfer, Long> amount;
    public static volatile SingularAttribute<ChoicePowerTransfer, ChoicePowerTransferType>
            transferType;
    public static volatile SingularAttribute<ChoicePowerTransfer, Long> choiceActivityRuleId;
    public static volatile SingularAttribute<ChoicePowerTransfer, String> description;
    public static volatile SingularAttribute<ChoicePowerTransfer, PointUser> operatorPointUser;
    public static volatile SingularAttribute<ChoicePowerTransfer, User> investPointUser;
    public static volatile SingularAttribute<ChoicePowerTransfer, UserIdentity> userIdentity;

    public static final String USER_ID = "userId";
    public static final String AMOUNT = "amount";
    public static final String TRANSFER_TYPE = "transferType";
    public static final String CHOICE_ACTIVITY_RULE_ID = "choiceActivityRuleId";
    public static final String DESCRIPTION = "description";
    public static final String OPERATOR_POINT_USER = "operatorPointUser";
    public static final String INVEST_POINT_USER = "investPointUser";
}
