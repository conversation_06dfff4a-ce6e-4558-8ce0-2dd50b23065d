package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import point.common.constant.WithdrawType;

@Getter
@Setter
@Entity
@Builder
@Table(name = "choice_p_withdrawal")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoicePWithdrawal extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "target_user_id", nullable = false)
    private Long targetUserId;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Column(name = "withdraw_time", nullable = false)
    private Date withdrawTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "withdraw_type", columnDefinition = "ENUM('POINT,'JPY')")
    private WithdrawType withdrawType;

    @Column(name = "description", nullable = false)
    private String description;
}
