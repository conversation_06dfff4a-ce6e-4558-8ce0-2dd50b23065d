package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;

@Entity
@NoArgsConstructor
@Table(name = "system_config")
@ToString(callSuper = true, doNotUseGetters = true)
public class SystemConfig extends AbstractEntity {

    private static final long serialVersionUID = 2290685754127081121L;

    @Getter
    @Setter
    @Column(name = "currency")
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @Getter
    @Setter
    @Column(name = "currency_pair")
    @Enumerated(EnumType.STRING)
    private CurrencyPair currencyPair;

    @Getter
    @Setter
    @Column(name = "name", nullable = false)
    private String name;

    @Getter
    @Setter
    @Column(name = "value", nullable = false)
    private String value;

    public SystemConfig(Currency currency, CurrencyPair currencyPair, String name, String value) {
        this.currency = currency;
        this.currencyPair = currencyPair;
        this.name = name;
        this.value = value;
    }
}
