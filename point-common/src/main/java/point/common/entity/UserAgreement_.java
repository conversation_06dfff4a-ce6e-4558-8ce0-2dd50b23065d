package point.common.entity;

import javax.persistence.metamodel.SingularAttribute;
import point.common.constant.UserAgreementType;

public class UserAgreement_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserAgreement, Long> userId;
    public static volatile SingularAttribute<UserAgreement, UserAgreementType> userAgreementType;
    public static volatile SingularAttribute<UserAgreement, String> version;
    public static volatile SingularAttribute<UserAgreement, Boolean> enabled;

    public static final String USER_ID = "userId";
    public static final String USER_AGREEMENT_TYPE = "userAgreementType";
    public static final String VERSION = "version";
    public static final String ENABLED = "enabled";
}
