package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Currency;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ExchangeSummary.class)
public abstract class ExchangeSummary_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ExchangeSummary, Date> targetAt;
    public static volatile SingularAttribute<ExchangeSummary, Currency> currency;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> currentAmount;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> jpyConversion;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositAmount;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositAmountJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositFee;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> depositFeeJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalAmount;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalAmountJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalFee;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> withdrawalFeeJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> transactionFee;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> transactionFeeJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
            simpleMarketPosTradeBuyAmount;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
            simpleMarketPosTradeBuyAmountJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
            simpleMarketPosTradeSellAmount;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
            simpleMarketPosTradeSellAmountJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> simpleMarketPosTradeFee;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
            simpleMarketPosTradeFeeJpy;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
            expirationNotContinueReward;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> cancelAmount;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal> expirationContinueReward;
    public static volatile SingularAttribute<ExchangeSummary, TradeType> tradeType;
    public static volatile SingularAttribute<ExchangeSummary, BigDecimal>
            expirationNotContinueAmount;

    public static final String TARGET_AT = "targetAt";
    public static final String CURRENCY = "currency";
    public static final String CURRENT_AMOUNT = "currentAmount";
    public static final String JPY_CONVERSION = "jpyConversion";
    public static final String DEPOSIT_AMOUNT = "depositAmount";
    public static final String DEPOSIT_AMOUNT_JPY = "depositAmountJpy";
    public static final String DEPOSIT_FEE = "depositFee";
    public static final String DEPOSIT_FEE_JPY = "depositFeeJpy";
    public static final String WITHDRAWAL_AMOUNT = "withdrawalAmount";
    public static final String WITHDRAWAL_AMOUNT_JPY = "withdrawalAmountJpy";
    public static final String WITHDRAWAL_FEE = "withdrawalFee";
    public static final String WITHDRAWAL_FEE_JPY = "withdrawalFeeJpy";
    public static final String TRANSACTION_FEE = "transactionFee";
    public static final String TRANSACTION_FEE_JPY = "transactionFeeJpy";
    public static final String SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT = "simpleMarketPosTradeBuyAmount";
    public static final String SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT_JPY =
            "simpleMarketPosTradeBuyAmountJpy";
    public static final String SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT =
            "simpleMarketPosTradeSellAmount";
    public static final String SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT_JPY =
            "simpleMarketPosTradeSellAmountJpy";
    public static final String SIMPLE_MARKET_POS_TRADE_FEE = "simpleMarketPosTradeFee";
    public static final String SIMPLE_MARKET_POS_TRADE_FEE_JPY = "simpleMarketPosTradeFeeJpy";
    public static final String EXPIRATION_NOT_CONTINUE_REWARD = "expirationNotContinueReward";
    public static final String CANCEL_AMOUNT = "cancelAmount";
    public static final String EXPIRATION_CONTINUE_REWARD = "expirationContinueReward";
}
