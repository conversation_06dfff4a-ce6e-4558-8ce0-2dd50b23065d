package point.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "point_transfer_status_history")
@ToString(callSuper = true, doNotUseGetters = true)
public class PointTransferStatusHistory extends AbstractEntity {

    @ManyToOne
    @JoinColumn(name = "transfer_id", nullable = false)
    private PointTransfer transfer;

    @Column(name = "previous_status")
    private String previousStatus;

    @Column(name = "current_status", nullable = false)
    private String currentStatus;

    @Column(name = "changed_at", nullable = false)
    private Date changedAt;

    @Column(name = "remarks")
    private String remarks;
}
