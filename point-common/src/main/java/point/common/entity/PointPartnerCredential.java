package point.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "point_partner_credential")
@ToString(callSuper = true, doNotUseGetters = true)
public class PointPartnerCredential extends AbstractEntity {

    @ManyToOne
    @JoinColumn(name = "point_partner_id", nullable = false)
    private PointPartner pointPartner;

    @Column(name = "api_key")
    private String apiKey;

    @Column(name = "secret_key")
    private String secretKey;

    @Column(name = "expired_at")
    private Date expiredAt;
}
