package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "user_ekyc_batch_request")
public class UserEkycBatchRequest extends AbstractEntity {

    @Column(name = "status")
    private String status;

    @Column(name = "headers")
    private String headers;

    @Column(name = "request_url")
    private String requestUrl;

    @Column(name = "response_body")
    private String responseBody;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "applicant_id")
    private Long applicantId;
}
