package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Exchange;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(BalanceNotifyConfig.class)
public abstract class BalanceNotifyConfig_ extends AbstractEntity_ {

    public static volatile SingularAttribute<BalanceNotifyConfig, String> currency;
    public static volatile SingularAttribute<BalanceNotifyConfig, Exchange> targetPos;
    public static volatile SingularAttribute<BalanceNotifyConfig, Boolean> enabled;
    public static volatile SingularAttribute<BalanceNotifyConfig, BigDecimal> limitBalancePercent;
    public static volatile SingularAttribute<BalanceNotifyConfig, String> mailTo;
    public static volatile SingularAttribute<BalanceNotifyConfig, BigDecimal> defaultBalance;

    public static final String CURRENCY = "currency";
    public static final String TARGET_POS = "targetPos";
    public static final String ENABLED = "enabled";
}
