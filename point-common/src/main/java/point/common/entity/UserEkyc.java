package point.common.entity;

import javax.persistence.*;
import lombok.*;
import point.common.constant.KycStatus;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "user_ekyc")
public class UserEkyc extends AbstractEntity {

    @Column(name = "doc_type")
    private String docType;

    @Column(name = "url")
    private String url;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private KycStatus status;

    @Column(name = "reference_id")
    private String referenceId;

    @Column(name = "applicant_id")
    private Long applicantId;

    @Column(name = "user_id")
    private Long userId;
}
