package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.WithdrawType;

@StaticMetamodel(ChoicePWithdrawal.class)
public class ChoiceRewardWithdrawal_ extends AbstractEntity_ {
    public static volatile SingularAttribute<ChoicePWithdrawal, Long> userId;
    public static volatile SingularAttribute<ChoicePWithdrawal, Long> activityId;
    public static volatile SingularAttribute<ChoicePWithdrawal, BigDecimal> amount;
    public static volatile SingularAttribute<ChoicePWithdrawal, Date> withdrawTime;
    public static volatile SingularAttribute<ChoicePWithdrawal, WithdrawType> withdrawType;
    public static volatile SingularAttribute<ChoicePWithdrawal, String> description;
}
