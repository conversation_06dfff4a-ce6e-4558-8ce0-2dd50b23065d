package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Currency;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(CurrencyConfig.class)
public abstract class CurrencyConfig_ extends AbstractEntity_ {

    public static volatile SingularAttribute<CurrencyConfig, BigDecimal> depositFee;
    public static volatile SingularAttribute<CurrencyConfig, BigDecimal> withdrawalFee;
    public static volatile SingularAttribute<CurrencyConfig, BigDecimal> transactionFee;
    public static volatile SingularAttribute<CurrencyConfig, BigDecimal> maxOrderAmountPerDay;
    public static volatile SingularAttribute<CurrencyConfig, BigDecimal> minDepositAmount;
    public static volatile SingularAttribute<CurrencyConfig, BigDecimal> minWithdrawalAmount;
    public static volatile SingularAttribute<CurrencyConfig, Boolean> depositable;
    public static volatile SingularAttribute<CurrencyConfig, Boolean> withdrawable;
    public static volatile SingularAttribute<CurrencyConfig, Currency> currency;
    public static volatile SingularAttribute<CurrencyConfig, Boolean> enabled;
    public static volatile SingularAttribute<CurrencyConfig, Boolean> firstSetFlg;
    public static volatile SingularAttribute<CurrencyConfig, TradeType> tradeType;
    public static volatile SingularAttribute<CurrencyConfig, Boolean> isVisible;

    public static final String DEPOSIT_FEE = "depositFee";
    public static final String TRANSACTION_FEE = "transactionFee";
    public static final String MAX_ORDER_AMOUNT_PER_DAY = "maxOrderAmountPerDay";
    public static final String MIN_DEPOSIT_AMOUNT = "minDepositAmount";
    public static final String MIN_WITHDRAWAL_AMOUNT = "minWithdrawalAmount";
    public static final String DEPOSITABLE = "depositable";
    public static final String WITHDRAWABLE = "withdrawable";
    public static final String CURRENCY = "currency";
    public static final String ENABLED = "enabled";
    public static final String TRADETYPE = "tradeType";
    public static final String IS_VISIBLE = "isVisible";
}
