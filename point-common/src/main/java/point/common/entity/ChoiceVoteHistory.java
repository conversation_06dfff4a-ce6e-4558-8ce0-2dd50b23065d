package point.common.entity;

import com.fasterxml.jackson.annotation.JsonView;
import javax.persistence.*;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "choice_vote_history")
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoiceVoteHistory extends AbstractEntity {

    @Column(name = "vote_id", nullable = false)
    private Long voteId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "activity_id", nullable = false)
    private Long activityId;

    @Column(name = "before_vote_power", nullable = false)
    private Long beforeVotePower;

    @Column(name = "after_vote_power", nullable = false)
    private Long afterVotePower;

    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "vote_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    @ToString.Exclude
    private ChoiceVote choiceVote;

    @JsonView(Views.WithUserIdentity.class)
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    @ToString.Exclude
    private UserIdentity userIdentity;
}
