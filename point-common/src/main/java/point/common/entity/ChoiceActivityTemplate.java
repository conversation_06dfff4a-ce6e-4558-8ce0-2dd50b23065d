package point.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.*;
import point.common.constant.ChoiceActivityAutoCycle;
import point.common.constant.ChoiceActivityStatus;
import point.common.constant.ChoiceActivityType;

@Getter
@Setter
@Entity
@Builder
@Table(name = "choice_activity_template")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoiceActivityTemplate extends AbstractEntity {

    @Enumerated(EnumType.STRING)
    @Column(
            name = "activity_type",
            nullable = false,
            columnDefinition = "ENUM('BTC_PRICE', 'OTHER_ASSET')")
    private ChoiceActivityType activityType;

    @Column(name = "vote_start_time", nullable = false)
    private String voteStartTime;

    @Column(name = "vote_end_time", nullable = false)
    private String voteEndTime;

    @Column(name = "start_time", nullable = false)
    private Date startTime;

    @Column(name = "end_time", nullable = false)
    private Date endTime;

    @Column(name = "vote_power_limit", nullable = false, columnDefinition = "BIGINT DEFAULT 1")
    private Long votePowerLimit;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "status",
            nullable = false,
            columnDefinition = "ENUM('ACTIVE', 'ABOLISH') DEFAULT 'ACTIVE'")
    private ChoiceActivityStatus status;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "auto_cycle",
            nullable = false,
            columnDefinition = "ENUM('DAILY', 'MONTHLY') DEFAULT 'DAILY'")
    private ChoiceActivityAutoCycle autoCycle;

    @Column(name = "power_total", nullable = false, columnDefinition = "BIGINT DEFAULT 10000")
    private Long powerTotal;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;
}
