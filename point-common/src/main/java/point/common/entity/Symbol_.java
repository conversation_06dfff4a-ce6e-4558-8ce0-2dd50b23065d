package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Symbol.class)
public abstract class Symbol_ extends AbstractEntity_ {

    public static volatile SingularAttribute<Symbol, TradeType> tradeType;
    public static volatile SingularAttribute<Symbol, CurrencyPair> currencyPair;
    public static volatile SingularAttribute<Symbol, Date> startAt;
    public static volatile SingularAttribute<Symbol, Date> endAt;
    public static volatile SingularAttribute<Symbol, Boolean> isVisible;

    public static final String TRADE_TYPE = "tradeType";
    public static final String CURRENCY_PAIR = "currencyPair";
    public static final String START_AT = "startAt";
    public static final String END_AT = "endAt";
    public static final String IS_VISIBLE = "isVisible";
}
