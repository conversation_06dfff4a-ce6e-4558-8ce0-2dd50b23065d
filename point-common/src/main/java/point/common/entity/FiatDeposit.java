package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import point.common.constant.FiatDepositStatus;
import point.common.constant.FiatDepositSubStatus;

@Entity
@Table(name = "fiat_deposit")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class FiatDeposit extends AbstractEntity {

    private static final long serialVersionUID = -1742465857593293667L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "bank_account_id")
    private Long bankAccountId;

    @Getter
    @Setter
    @Column(name = "onetime_bank_account_id")
    private Long onetimeBankAccountId;

    @Getter
    @Setter
    @Column(name = "amount", precision = 34, scale = 20, nullable = false)
    // @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;

    @Getter
    @Setter
    @Column(name = "fee", precision = 34, scale = 20, nullable = false)
    // @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal fee;

    @Getter
    @Setter
    @Column(name = "fiat_deposit_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private FiatDepositStatus fiatDepositStatus;

    @Getter
    @Setter
    @Column(name = "fiat_deposit_sub_status")
    @Enumerated(EnumType.STRING)
    private FiatDepositSubStatus fiatDepositSubStatus;

    @Getter
    @Setter
    @Column(name = "comment")
    private String comment;

    @Getter
    @Setter
    @ManyToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Fetch(FetchMode.JOIN)
    private User user;

    @Getter
    @Setter
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "onetime_bank_account_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private OnetimeBankAccount onetimeBankAccount;

    @Getter @Setter @Transient private String remake;

    @Getter
    @Setter
    @ManyToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Fetch(FetchMode.JOIN)
    private PointUser pointUser;

    public FiatDeposit(User user, BankAccount bank_account, BigDecimal amount, String comment) {
        userId = user.getId();
        this.amount = amount;
        this.comment = comment;
    }

    @JsonIgnore
    public BigDecimal nextBalance(BigDecimal balance) {
        return balance.add(amount);
    }

    @JsonIgnore
    public BigDecimal ownNextBalance(BigDecimal balance) {
        return balance.add(amount).subtract(fee);
    }
}
