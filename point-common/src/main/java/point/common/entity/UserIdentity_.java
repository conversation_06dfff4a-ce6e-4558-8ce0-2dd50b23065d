package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.UserIdType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserIdentity.class)
public abstract class UserIdentity_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserIdentity, Long> id;
    public static volatile SingularAttribute<UserIdentity, Long> userId;
    public static volatile SingularAttribute<UserIdentity, UserIdType> idType;
    public static volatile SingularAttribute<UserIdentity, User> investUser;

    public static final String ID = "id";
    public static final String USER_ID = "userId";
    public static final String ID_TYPE = "idType";
}
