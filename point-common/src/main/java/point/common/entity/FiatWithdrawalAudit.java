package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.FiatWithdrawalStatus;

@Entity
@Table(name = "fiat_withdrawal_audit")
@ToString(callSuper = true, doNotUseGetters = true)
public class FiatWithdrawalAudit extends AbstractEntity {

    @Getter
    @Setter
    @Column(name = "fiat_withdrawal_id")
    private Long fiatWithdrawalId;

    @Getter
    @Setter
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private FiatWithdrawalStatus status;

    @Getter
    @Setter
    @Column(name = "created_by")
    private String createdBy;
}
