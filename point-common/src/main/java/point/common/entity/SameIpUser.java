package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "same_ip_user")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class SameIpUser extends AbstractEntity {

    private static final long serialVersionUID = -7416671196554065701L;

    @Getter
    @Setter
    @Column(name = "ip_address", nullable = false)
    private String ipAddress;

    @Getter
    @Setter
    @Column(name = "user_ids", nullable = false)
    private String userIds;

    public SameIpUser(String ipAddress, String userIds) {
        this.ipAddress = ipAddress;
        this.userIds = userIds;
    }
}
