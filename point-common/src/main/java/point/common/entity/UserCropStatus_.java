package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserCropStatus.class)
public class UserCropStatus_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserCropStatus, Long> userId;
    public static volatile SingularAttribute<UserCropStatus, Long> assetId;
    public static volatile SingularAttribute<UserCropStatus, TradeType> tradeType;
    public static volatile SingularAttribute<UserCropStatus, Integer> growthStageId;
    public static volatile SingularAttribute<UserCropStatus, Integer> profitLossAmount;

    public static final String USER_ID = "userId";
    public static final String ASSET_ID = "assetId";
    public static final String TRADE_TYPE = "tradeType";
    public static final String GROWTH_STAGE_ID = "growthStageId";
    public static final String PROFIT_LOSS_AMOUNT = "profitLossAmount";
}
