package point.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeAction;
import point.common.constant.TradeType;

@Setter
@Getter
@Entity
@NoArgsConstructor
@Table(name = "currency_pair_config")
@ToString(callSuper = true, doNotUseGetters = true)
public class CurrencyPairConfig extends AbstractEntity {

    @Serial private static final long serialVersionUID = 3353097981369567845L;

    @Column(name = "trade_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Column(name = "currency_pair", nullable = false)
    @Enumerated(EnumType.STRING)
    private CurrencyPair currencyPair;

    @Column(name = "min_order_amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal minOrderAmount;

    @Column(name = "max_order_amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal maxOrderAmount;

    @Column(name = "max_active_order_amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal maxActiveOrderAmount;

    @Column(name = "limit_price_range_rate", precision = 34, scale = 20, nullable = false)
    private BigDecimal limitPriceRangeRate;

    @Column(name = "market_price_range_rate", precision = 34, scale = 20, nullable = false)
    private BigDecimal marketPriceRangeRate;

    @Column(name = "market_amount_range_rate", precision = 34, scale = 20, nullable = false)
    private BigDecimal marketAmountRangeRate;

    @Column(name = "maker_trade_fee_percent", precision = 34, scale = 20, nullable = false)
    private BigDecimal makerTradeFeePercent;

    @Column(name = "taker_trade_fee_percent", precision = 34, scale = 20, nullable = false)
    private BigDecimal takerTradeFeePercent;

    @Column(name = "tradable", nullable = false)
    private boolean tradable = true;

    @Column(name = "enabled", nullable = false)
    private boolean enabled = false;

    @Column(name = "circuit_break_updated_at")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date circuitBreakUpdatedAt;

    @Column(name = "circuit_break_percent", precision = 34, scale = 20)
    private BigDecimal circuitBreakPercent;

    @Column(name = "circuit_break_check_timespan")
    private Long circuitBreakCheckTimespan;

    @Column(name = "circuit_break_stop_timespan")
    private Long circuitBreakStopTimespan;

    @Column(name = "simple_market_spread_percent", precision = 34, scale = 20)
    private BigDecimal simpleMarketSpreadPercent;

    @Column(name = "simple_market_fee_percent", precision = 34, scale = 20)
    private BigDecimal simpleMarketFeePercent;

    @Column(name = "spike_percent", precision = 34, scale = 20, nullable = true)
    private BigDecimal spikePercent;

    @Column(name = "spike_minutes", nullable = true)
    private Integer spikeMinutes;

    @Column(name = "spike_count", nullable = true)
    private Integer spikeCount;

    @Column(name = "wash_trading_check_span_hours", nullable = true)
    private Integer washTradingCheckSpanHours;

    @Column(name = "wash_trading_percent_threshold", precision = 34, scale = 20, nullable = true)
    private BigDecimal washTradingPercentThreshold;

    @Column(name = "high_value_trader_check_span_hours", nullable = true)
    private Integer highValueTraderCheckSpanHours;

    @Column(name = "same_ip_check_span_hours", nullable = true)
    private Integer sameIpCheckSpanHours;

    @Column(name = "same_ip_threshold", nullable = true)
    private Integer sameIpThreshold;

    @Column(
            name = "high_value_trader_pos_order_limit_amount_threshold",
            precision = 34,
            scale = 20,
            nullable = true)
    private BigDecimal highValueTraderPosOrderLimitAmountThreshold;

    @Column(
            name = "high_value_trader_pos_trade_market_amount_threshold",
            precision = 34,
            scale = 20,
            nullable = true)
    private BigDecimal highValueTraderPosTradeMarketAmountThreshold;

    @Column(name = "high_value_trader_count_threshold", nullable = true)
    private Integer highValueTraderCountThreshold;

    @Column(name = "pos_spread_percent", precision = 34, scale = 20, nullable = true)
    private BigDecimal posSpreadPercent;

    @Column(name = "pos_slippage_percent", precision = 34, scale = 20, nullable = true)
    private BigDecimal posSlippagePercent;

    public CurrencyPairConfig(TradeType tradeType, CurrencyPair currencyPair) {
        this.tradeType = tradeType;
        this.currencyPair = currencyPair;
    }

    @JsonIgnore
    public BigDecimal getTradeFeePercent(TradeAction tradeAction) {
        return tradeAction.isMaker() ? makerTradeFeePercent : takerTradeFeePercent;
    }
}
