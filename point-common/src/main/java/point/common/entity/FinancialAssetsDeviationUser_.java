package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(FinancialAssetsDeviationUser.class)
public abstract class FinancialAssetsDeviationUser_ extends AbstractEntity_ {

    public static volatile SingularAttribute<FinancialAssetsDeviationUser, Long> userId;
    public static volatile SingularAttribute<FinancialAssetsDeviationUser, Integer> overTrades;
    public static volatile SingularAttribute<FinancialAssetsDeviationUser, String> tradeType;

    public static final String USER_ID = "userId";
    public static final String OVER_TRADES = "overTrades";
}
