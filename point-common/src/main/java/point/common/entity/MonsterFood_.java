package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Currency;
import point.common.constant.UserIdType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MonsterFood.class)
public abstract class MonsterFood_ extends AbstractEntity_ {

    public static volatile SingularAttribute<MonsterFood, UserIdType> type;
    public static volatile SingularAttribute<MonsterFood, Currency> currency;
    public static volatile SingularAttribute<MonsterFood, Long> symbolId;
    public static volatile SingularAttribute<MonsterFood, Long> year;
    public static volatile SingularAttribute<MonsterFood, Long> week;
    public static volatile SingularAttribute<MonsterFood, String> createdBy;
    public static volatile SingularAttribute<MonsterFood, String> updatedBy;

    public static final String TYPE = "type";
    public static final String CURRENCY = "currency";
    public static final String SYMBOL_ID = "symbolId";
    public static final String YEAR = "year";
    public static final String WEEK = "week";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
}
