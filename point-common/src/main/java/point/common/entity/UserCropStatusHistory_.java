package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserCropStatusHistory.class)
public class UserCropStatusHistory_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserCropStatusHistory, Long> userId;
    public static volatile SingularAttribute<UserCropStatusHistory, Long> assetId;
    public static volatile SingularAttribute<UserCropStatusHistory, TradeType> tradeType;
    public static volatile SingularAttribute<UserCropStatusHistory, Integer> beforeGrowthStageId;
    public static volatile SingularAttribute<UserCropStatusHistory, Integer> afterGrowthStageId;
    public static volatile SingularAttribute<UserCropStatusHistory, Integer> profitLossAmount;

    public static final String USER_ID = "userId";
    public static final String ASSET_ID = "assetId";
    public static final String TRADE_TYPE = "tradeType";
    public static final String BEFORE_GROWTH_STAGE_ID = "beforeGrowthStageId";
    public static final String AFTER_GROWTH_STAGE_ID = "afterGrowthStageId";
    public static final String PROFIT_LOSS_AMOUNT = "profitLossAmount";
}
