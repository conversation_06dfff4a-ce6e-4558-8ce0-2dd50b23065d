package point.common.entity;

import java.sql.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.NewsType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(User.class)
public abstract class News_ extends AbstractEntity_ {

    public static volatile SingularAttribute<News, NewsType> newsType;
    public static volatile SingularAttribute<News, String> title;
    public static volatile SingularAttribute<News, String> contents;
    public static volatile SingularAttribute<News, String> link;
    public static volatile SingularAttribute<News, Date> date;
    public static volatile SingularAttribute<News, Boolean> enabled;

    public static final String NEWS_TYPE = "news_type";
    public static final String TITLE = "title";
    public static final String CONTENTS = "contents";
    public static final String LINK = "link";
    public static final String DATE = "date";
    public static final String ENABLED = "enabled";
}
