package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PontaConvertWorker.class)
public abstract class PontaConvertWorker_ extends AbstractEntity_ {

    public static volatile SingularAttribute<PontaConvertWorker, Long> userId;
    public static volatile SingularAttribute<PontaConvertWorker, String> tradeNumber;
    public static volatile SingularAttribute<PontaConvertWorker, String> contents;
    public static volatile SingularAttribute<PontaConvertWorker, Integer> flag;
    public static volatile SingularAttribute<PontaConvertWorker, BigDecimal> pointAmount;

    public static final String USER_ID = "userId";
    public static final String TRADE_NUMBER = "tradeNumber";
    public static final String CONTENTS = "contents";
    public static final String FLAG = "flag";
    public static final String PointAmount = "pointAmount";
}
