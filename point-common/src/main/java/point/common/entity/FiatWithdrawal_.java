package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.FiatWithdrawalStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(FiatWithdrawal.class)
public abstract class FiatWithdrawal_ extends AbstractEntity_ {

    public static volatile SingularAttribute<FiatWithdrawal, Long> userId;
    public static volatile SingularAttribute<FiatWithdrawal, BigDecimal> amount;
    public static volatile SingularAttribute<FiatWithdrawal, BigDecimal> fee;
    public static volatile SingularAttribute<FiatWithdrawal, String> comment;
    public static volatile SingularAttribute<FiatWithdrawal, Long> bankAccountId;
    public static volatile SingularAttribute<FiatWithdrawal, FiatWithdrawalStatus>
            fiatWithdrawalStatus;
    public static volatile SingularAttribute<FiatWithdrawal, User> user;
    public static volatile SingularAttribute<FiatWithdrawal, String> applyNo;
    public static volatile SingularAttribute<FiatWithdrawal, String> itemId;
    public static volatile SingularAttribute<FiatWithdrawal, BankAccount> bankAccount;
    public static volatile SingularAttribute<FiatWithdrawal, String> createdBy;
    public static volatile SingularAttribute<FiatWithdrawal, String> updatedBy;
    public static volatile SingularAttribute<FiatWithdrawal, PointUser> pointUser;

    public static final String USER_ID = "userId";
    public static final String AMOUNT = "amount";
    public static final String FEE = "fee";
    public static final String COMMENT = "comment";
    public static final String BANK_ACCOUNT_ID = "bankAccountId";
    public static final String FIAT_WITHDRAWAL_STATUS = "fiatWithdrawalStatus";
    public static final String USER = "user";
    public static final String APPLY_NO = "applyNo";
    public static final String BANK_ACCOUNT = "bankAccount";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
    public static final String POINT_USER = "pointUser";
}
