package point.common.entity;

import java.math.BigDecimal;
import javax.persistence.*;
import lombok.NoArgsConstructor;

@lombok.Getter
@lombok.Setter
@NoArgsConstructor
@Entity
@Table(name = "bc_order_currency_split")
public class BcOrderCurrencySplit extends AbstractEntity {

    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    @Column(name = "amount", nullable = false, precision = 34, scale = 20)
    private BigDecimal amount;

    @Column(name = "order_id", nullable = false)
    private long orderId;

    @Column(name = "trade_id", nullable = false)
    private long tradeId;
}
