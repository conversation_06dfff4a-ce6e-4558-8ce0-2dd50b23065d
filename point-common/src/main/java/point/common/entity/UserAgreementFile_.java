package point.common.entity;

import javax.persistence.metamodel.SingularAttribute;
import point.common.constant.UserAgreementType;

public class UserAgreementFile_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserAgreementFile, UserAgreementType>
            userAgreementType;
    public static volatile SingularAttribute<UserAgreementFile, String> version;

    public static final String USER_AGREEMENT_TYPE = "userAgreementType";
    public static final String VERSION = "version";
}
