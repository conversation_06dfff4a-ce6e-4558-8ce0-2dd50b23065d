package point.common.entity;

import java.util.Date;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gmo_deposit")
@ToString(callSuper = true, doNotUseGetters = true)
public class GmoDeposit extends AbstractEntity {

    @Column(name = "item_key")
    private String itemKey;

    @Column(name = "fiat_deposit_id")
    private Long fiatDepositId;

    @Column(name = "transaction_date", nullable = false)
    private Date transactionDate;

    @Column(name = "va_account_name_kana")
    private String vaAccountNameKana;

    @Column(name = "remitter_name_kana")
    private String remitterNameKana;

    @Column(name = "payment_bank_name")
    private String paymentBankName;

    @Column(name = "payment_branch_name")
    private String paymentBranchName;

    @Column(name = "partner_name")
    private String partnerName;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "deposit_amount", nullable = false)
    private Long depositAmount;

    @Column(name = "va_id")
    private String vaId;

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "fiat_deposit_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private FiatDeposit fiatDeposit;
}
