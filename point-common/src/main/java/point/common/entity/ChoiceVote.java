package point.common.entity;

import com.fasterxml.jackson.annotation.JsonView;
import java.util.Date;
import java.util.Objects;
import javax.persistence.*;
import lombok.*;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.constant.ChoiceVoteResult;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "choice_vote")
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoiceVote extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "activity_id", nullable = false)
    private Long activityId;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "vote_result",
            nullable = false,
            columnDefinition = "ENUM('WIN', 'LOSE', 'PENDING') DEFAULT 'PENDING'")
    private ChoiceVoteResult voteResult;

    @Enumerated(EnumType.STRING)
    @Column(name = "vote_direction", nullable = false, columnDefinition = "ENUM('UP', 'DOWN')")
    private ChoiceActivityVoteResult voteDirection;

    @Column(name = "vote_power", nullable = false)
    private Long votePower;

    @Column(name = "vote_time")
    private Date voteTime;

    @Column(name = "description", length = 256)
    private String description;

    @Column(name = "withdrawal_allowed", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer withdrawalAllowed;

    @JsonView(Views.WithUserIdentity.class)
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.EAGER)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    @ToString.Exclude
    private UserIdentity userIdentity;

    @OneToOne(optional = true)
    @JoinColumns({
        @JoinColumn(
                name = "user_id",
                referencedColumnName = "user_id",
                insertable = false,
                updatable = false),
        @JoinColumn(
                name = "activity_id",
                referencedColumnName = "activity_id",
                insertable = false,
                updatable = false)
    })
    private ChoiceReward choiceReward;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChoiceVote that = (ChoiceVote) o;
        return Objects.equals(getId(), that.getId())
                && Objects.equals(userId, that.userId)
                && Objects.equals(activityId, that.activityId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), userId, activityId);
    }
}
