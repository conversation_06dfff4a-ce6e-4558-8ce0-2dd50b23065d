package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.KycStatus;
import point.common.constant.UserStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(User.class)
public abstract class User_ extends AbstractEntity_ {

    public static volatile SingularAttribute<User, String> password;
    public static volatile SingularAttribute<User, String> antiPhishingCode;
    public static volatile SingularAttribute<User, UserStatus> userStatus;
    public static volatile SingularAttribute<User, KycStatus> kycStatus;
    public static volatile SingularAttribute<User, Integer> userKycId;
    public static volatile SingularAttribute<User, UserKyc> userKyc;
    public static volatile SingularAttribute<User, Boolean> credentialsNonExpired;
    public static volatile SingularAttribute<User, Boolean> accountNonExpired;
    public static volatile SingularAttribute<User, String> email;
    public static volatile SingularAttribute<User, Boolean> enabled;
    public static volatile ListAttribute<User, UserAuthority> authorities;
    public static volatile SingularAttribute<User, Boolean> accountNonLocked;
    public static volatile SingularAttribute<User, Integer> level;
    public static volatile SingularAttribute<User, Integer> userInfoId;
    public static volatile SingularAttribute<User, UserInfo> userInfo;
    public static volatile SingularAttribute<User, Boolean> insideAccountFlg;
    public static volatile SingularAttribute<User, Boolean> tradeUncapped;
    public static volatile SingularAttribute<User, Boolean> insider;
    public static volatile SingularAttribute<User, Boolean> risker;
    public static volatile SingularAttribute<User, Integer> oldUserId;
    public static volatile SingularAttribute<User, Long> affiliateInfoId;
    public static volatile SingularAttribute<User, String> uuid;
    public static volatile SingularAttribute<User, AffiliateInfo> affiliateInfo;
    public static volatile SingularAttribute<User, String> sessionId;
    public static volatile SingularAttribute<User, UserIdentity> userIdentity;
    public static volatile SingularAttribute<User, PointUser> pointUser;

    public static final String PASSWORD = "password";
    public static final String ANTI_PHISHING_CODE = "antiPhishingCode";
    public static final String USER_STATUS = "userStatus";
    public static final String KYC_STATUS = "kycStatus";
    public static final String USER_KYC_ID = "userKycId";
    public static final String USER_KYC = "userKyc";
    public static final String CREDENTIALS_NON_EXPIRED = "credentialsNonExpired";
    public static final String ACCOUNT_NON_EXPIRED = "accountNonExpired";
    public static final String EMAIL = "email";
    public static final String ENABLED = "enabled";
    public static final String AUTHORITIES = "authorities";
    public static final String ACCOUNT_NON_LOCKED = "accountNonLocked";
    public static final String LEVEL = "level";
    public static final String USER_INFO_ID = "userInfoId";
    public static final String USER_INFO = "userInfo";
    public static final String TRADE_UNCAPPED = "trade_uncapped";
    public static final String INSIDER = "insider";
    public static final String RISKER = "risker";
    public static final String OLD_USER_ID = "oldUserId";
    public static final String AFFILIATE_INFO_ID = "affiliateInfoId";
    public static final String UUID = "uuid";
}
