package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.TradeType;

@Setter
@Getter
@Entity
@Table(name = "crop_growth_stage")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class CropGrowthStage extends AbstractEntity {

    @Column(name = "trade_type")
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Column(name = "growth_stage_id")
    private Long growthStageId;

    @Column(name = "growth_stage")
    private String growthStage;

    @Column(name = "evaluation_from", nullable = false)
    private Long evaluationFrom;

    @Column(name = "evaluation_to", nullable = false)
    private Long evaluationTo;

    @Column(name = "updated_by")
    private String updatedBy;
}
