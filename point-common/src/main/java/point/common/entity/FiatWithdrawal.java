package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import point.common.constant.FiatWithdrawalStatus;

@Entity
@Table(name = "fiat_withdrawal")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class FiatWithdrawal extends AbstractEntity {

    private static final long serialVersionUID = -7416671196554065701L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal amount;

    @Getter
    @Setter
    @Column(name = "fee", precision = 34, scale = 20, nullable = false)
    private BigDecimal fee;

    @Getter
    @Setter
    @Column(name = "comment")
    private String comment;

    @Getter
    @Setter
    @Column(name = "bank_account_id", nullable = false)
    private Long bankAccountId;

    @Getter
    @Setter
    @Column(name = "fiat_withdrawal_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private FiatWithdrawalStatus fiatWithdrawalStatus = FiatWithdrawalStatus.APPROVING;

    @Getter
    @Setter
    @Column(name = "apply_no")
    private String applyNo;

    @Getter
    @Setter
    @Column(name = "item_id")
    private String itemId;

    @Getter
    @Setter
    @ManyToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Fetch(FetchMode.JOIN)
    private User user;

    @Getter
    @Setter
    @OneToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "bank_account_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Fetch(FetchMode.JOIN)
    private BankAccount bankAccount;

    @Getter
    @Setter
    @Column(name = "created_by")
    private String createdBy;

    @Getter
    @Setter
    @Column(name = "updated_by")
    private String updatedBy;

    @Getter
    @Setter
    @ManyToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Fetch(FetchMode.JOIN)
    private PointUser pointUser;

    public FiatWithdrawal(User user, BankAccount bankAccount, BigDecimal amount, BigDecimal fee) {
        userId = user.getId();
        bankAccountId = bankAccount.getId();
        this.amount = amount;
        this.fee = fee;
    }

    @JsonIgnore
    public BigDecimal nextBalance(BigDecimal balance) {
        return balance.subtract(amount);
    }

    @JsonIgnore
    public BigDecimal ownNextBalance(BigDecimal balance) {
        return balance.subtract(amount);
    }
}
