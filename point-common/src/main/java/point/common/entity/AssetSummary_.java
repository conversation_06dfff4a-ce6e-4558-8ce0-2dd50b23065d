package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Currency;

@Generated(value = "Dali", date = "2020-07-10T14:47:00.569+0900")
@StaticMetamodel(AssetSummary.class)
public class AssetSummary_ extends AbstractEntity_ {

    public static volatile SingularAttribute<AssetSummary, Long> userId;
    public static volatile SingularAttribute<AssetSummary, Date> targetAt;
    public static volatile SingularAttribute<AssetSummary, Currency> currency;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> currentAmount;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> jpyConversion;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> depositAmount;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> depositAmountJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> depositFee;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> depositFeeJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> withdrawalAmount;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> withdrawalAmountJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> withdrawalFee;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> withdrawalFeeJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> transactionFee;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> posTradeBuyAmount;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> posTradeBuyAmountJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> posTradeSellAmount;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> posTradeSellAmountJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> posTradeFee;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> posTradeFeeJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> expirationNotContinueReward;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> expirationContinueReward;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> transactionFeeJpy;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> expirationNotContinueAmount;
    public static volatile SingularAttribute<AssetSummary, BigDecimal> cancelAmount;

    public static final String USER_ID = "userId";
    public static final String TARGET_AT = "targetAt";
    public static final String CURRENCY = "currency";
    public static final String CURRENT_AMOUNT = "currentAmount";
    public static final String JPY_CONVERSION = "jpyConversion";
    public static final String DEPOSIT_AMOUNT = "depositAmount";
    public static final String DEPOSIT_AMOUNT_JPY = "depositAmountJpy";
    public static final String DEPOSIT_FEE = "depositFee";
    public static final String DEPOSIT_FEE_JPY = "depositFeeJpy";
    public static final String WITHDRAWAL_AMOUNT = "withdrawalAmount";
    public static final String WITHDRAWAL_AMOUNT_JPY = "withdrawalAmountJpy";
    public static final String WITHDRAWAL_FEE = "withdrawalFee";
    public static final String WITHDRAWAL_FEE_JPY = "withdrawalFeeJpy";
    public static final String TRANSACTION_FEE = "transactionFee";
    public static final String TRANSACTION_FEE_JPY = "transactionFeeJpy";
}
