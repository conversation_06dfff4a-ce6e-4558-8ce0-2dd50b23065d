package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(CurrencyPairConfig.class)
public abstract class CurrencyPairConfig_ extends AbstractEntity_ {

    public static volatile SingularAttribute<CurrencyPairConfig, TradeType> tradeType;
    public static volatile SingularAttribute<CurrencyPairConfig, CurrencyPair> currencyPair;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> minOrderAmount;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> maxOrderAmount;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> maxActiveOrderAmount;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> limitPriceRangeRate;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> marketPriceRangeRate;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> marketAmountRangeRate;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> makerTradeFeePercent;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> takerTradeFeePercent;
    public static volatile SingularAttribute<CurrencyPairConfig, Boolean> tradable;
    public static volatile SingularAttribute<CurrencyPairConfig, Boolean> enabled;
    public static volatile SingularAttribute<CurrencyPairConfig, Date> circuitBreakUpdatedAt;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> circuitBreakPercent;
    public static volatile SingularAttribute<CurrencyPairConfig, Long> circuitBreakCheckTimespan;
    public static volatile SingularAttribute<CurrencyPairConfig, Long> circuitBreakStopTimespan;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal>
            simpleMarketSpreadPercent;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> simpleMarketFeePercent;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> spikePercent;
    public static volatile SingularAttribute<CurrencyPairConfig, Integer> spikeMinutes;
    public static volatile SingularAttribute<CurrencyPairConfig, Integer> spikeCount;
    public static volatile SingularAttribute<CurrencyPairConfig, Integer> washTradingCheckSpanHours;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal>
            washTradingPercentThreshold;
    public static volatile SingularAttribute<CurrencyPairConfig, Integer>
            highValueTraderCheckSpanHours;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal>
            highValueTraderPosOrderLimitAmountThreshold;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal>
            highValueTraderPosTradeMarketAmountThreshold;
    public static volatile SingularAttribute<CurrencyPairConfig, Integer>
            highValueTraderCountThreshold;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> posSpreadPercent;
    public static volatile SingularAttribute<CurrencyPairConfig, BigDecimal> posSlippagePercent;
    public static volatile SingularAttribute<CurrencyPairConfig, Integer> sameIpCheckSpanHours;
    public static volatile SingularAttribute<CurrencyPairConfig, Integer> sameIpThreshold;

    public static final String TRADE_TYPE = "tradeType";
    public static final String CURRENCY_PAIR = "currencyPair";
    public static final String MIN_ORDER_AMOUNT = "minOrderAmount";
    public static final String MAX_ORDER_AMOUNT = "maxOrderAmount";
    public static final String MAX_ACTIVE_ORDER_AMOUNT = "maxActiveOrderAmount";
    public static final String LIMIT_PRICE_RANGE_RATE = "limitPriceRangeRate";
    public static final String MARKET_PRICE_RANGE_RATE = "marketPriceRangeRate";
    public static final String MARKET_AMOUNT_RANGE_RATE = "marketAmountRangeRate";
    public static final String MAKER_TRADE_FEE_PERCENT = "makerTradeFeePercent";
    public static final String TAKER_TRADE_FEE_PERCENT = "takerTradeFeePercent";
    public static final String TRADABLE = "tradable";
    public static final String ENABLED = "enabled";
    public static final String CIRCUIT_BREAK_UPDATED_AT = "circuitBreakUpdatedAt";
    public static final String CIRCUIT_BREAK_PERCENT = "circuitBreakPercent";
    public static final String CIRCUIT_BREAK_CHECK_TIMESPAN = "circuitBreakCheckTimespan";
    public static final String CIRCUIT_BREAK_STOP_TIMESPAN = "circuitBreakStopTimespan";
    public static final String SIMPLE_MARKET_SPREAD_PERCENT = "simpleMarketSpreadPercent";
    public static final String SIMPLE_MARKET_FEE_PERCENT = "simpleMarketFeePercent";
    public static final String SPIKE_PERCENT = "spikePercent";
    public static final String SPIKE_MINUTES = "spikeMinutes";
    public static final String SPIKE_COUNT = "spikeCount";
    public static final String WASH_TRADING_CHECK_SPAN_HOURS = "washTradingCheckSpanHours";
    public static final String WASH_TRADING_PERCENT_THRESHOLD = "washTradingPercentThreshold";
    public static final String high_value_trader_check_span_hours = "highValueTraderCheckSpanHours";
    public static final String HIGH_VALUE_TRADER_POS_ORDER_LIMIT_AMOUNT_THRESHOLD =
            "highValueTraderPosOrderLimitAmountThreshold";
    public static final String HIGH_VALUE_TRADER_POS_TRADE_MARKET_AMOUNT_THRESHOLD =
            "highValueTraderPosTradeMarketAmountThreshold";
    public static final String high_value_trader_count_threshold = "highValueTraderCountThreshold";
}
