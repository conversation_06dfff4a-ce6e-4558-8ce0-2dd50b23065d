package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.CandlestickType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Candlestick.class)
public abstract class Candlestick_ extends AbstractEntity_ {

    public static volatile SingularAttribute<Candlestick, Long> symbolId;
    public static volatile SingularAttribute<Candlestick, CandlestickType> candlestickType;
    public static volatile SingularAttribute<Candlestick, Date> targetAt;
    public static volatile SingularAttribute<Candlestick, BigDecimal> open;
    public static volatile SingularAttribute<Candlestick, BigDecimal> high;
    public static volatile SingularAttribute<Candlestick, BigDecimal> low;
    public static volatile SingularAttribute<Candlestick, BigDecimal> close;
    public static volatile SingularAttribute<Candlestick, BigDecimal> volume;
    public static volatile SingularAttribute<Candlestick, Boolean> fixed;

    public static final String SYMBOL_ID = "symbolId";
    public static final String CANDLESTICK_TYPE = "candlestickType";
    public static final String TARGET_AT = "targetAt";
    public static final String OPEN = "open";
    public static final String HIGH = "high";
    public static final String LOW = "low";
    public static final String CLOSE = "close";
    public static final String VOLUME = "volume";
    public static final String FIXED = "fixed";
}
