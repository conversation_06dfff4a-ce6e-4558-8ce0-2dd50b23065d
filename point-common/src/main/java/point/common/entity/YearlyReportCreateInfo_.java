package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(YearlyReportCreateInfo.class)
public abstract class YearlyReportCreateInfo_ extends AbstractEntity_ {

    public static volatile SingularAttribute<YearlyReportCreateInfo, Long> userId;
    public static volatile SingularAttribute<YearlyReportCreateInfo, Integer> year;
    public static volatile SingularAttribute<YearlyReportCreateInfo, String> status;
    public static volatile SingularAttribute<YearlyReportCreateInfo, String> errorMsg;
    public static volatile SingularAttribute<YearlyReportCreateInfo, Boolean> downloadFlg;
    public static volatile SingularAttribute<YearlyReportCreateInfo, String> createdBy;
    public static volatile SingularAttribute<YearlyReportCreateInfo, String> updatedBy;
    public static volatile SingularAttribute<YearlyReportCreateInfo, Date> latestDownloadTime;
    public static volatile SingularAttribute<YearlyReportCreateInfo, String> s3FileName;

    public static final String USER_ID = "userId";
    public static final String YEAR = "year";
    public static final String STATUS = "status";
    public static final String ERROR_MSG = "errorMsg";
    public static final String DOWNLOAD_FLG = "downloadFlg";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
    public static final String LATEST_DOWNLOAD_TIME = "latestDownloadTime";
    public static final String S3_FILE_NAME = "s3FileName";
}
