package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserLoginInfo.class)
public abstract class UserLoginInfo_ extends AbstractEntity_ {
    public static volatile SingularAttribute<UserLoginInfo, String> userId;
    public static volatile SingularAttribute<UserLoginInfo, String> ipAddress;
    public static volatile SingularAttribute<UserLoginInfo, Boolean> inJapan;
    public static volatile SingularAttribute<UserLoginInfo, UserIdentity> userIdentity;
    public static volatile SingularAttribute<UserLoginInfo, User> user;

    public static final String USER_ID = "userId";
    public static final String IP_ADDRESS = "ipAddress";
    public static final String IN_JAPAN = "inJapan";
}
