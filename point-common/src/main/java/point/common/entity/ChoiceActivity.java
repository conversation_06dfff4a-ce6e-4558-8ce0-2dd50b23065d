package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import point.common.constant.ChoiceActivityResultStatus;
import point.common.constant.ChoiceActivityType;
import point.common.constant.ChoiceActivityVoteResult;

@Getter
@Setter
@Entity
@Builder
@Table(name = "choice_activity")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoiceActivity extends AbstractEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Enumerated(EnumType.STRING)
    @Column(name = "activity_type", columnDefinition = "ENUM('BTC_PRICE', 'OTHER_ASSET')")
    private ChoiceActivityType activityType;

    @Column(name = "vote_start_time", nullable = false)
    private Date voteStartTime;

    @Column(name = "vote_end_time", nullable = false)
    private Date voteEndTime;

    @Column(name = "lock_start_time", nullable = false)
    private Date lockStartTime;

    @Column(name = "lock_end_time", nullable = false)
    private Date lockEndTime;

    @Column(name = "base_price_voting_day")
    private BigDecimal basePriceVotingDay;

    @Column(name = "base_price_election_day")
    private BigDecimal basePriceElectionDay;

    @Column(name = "reward_pool")
    private Long rewardPool;

    @Column(name = "symbol", nullable = false, columnDefinition = "varchar(255) DEFAULT 'JPY'")
    private String symbol;

    @Column(name = "total_vote_power_up", nullable = false, columnDefinition = "bigint DEFAULT 0")
    private Long totalVotePowerUp;

    @Column(name = "total_vote_power_down", nullable = false, columnDefinition = "bigint DEFAULT 0")
    private Long totalVotePowerDown;

    @Column(name = "vote_power_limit", nullable = false, columnDefinition = "bigint DEFAULT 10000")
    private Long votePowerLimit;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "status",
            columnDefinition =
                    "ENUM('VOTING','CALCULATING_POWER','DISTRIBUTING_REWARDS','COMPLETED') DEFAULT 'VOTING'")
    private ChoiceActivityResultStatus status;

    @Column(name = "total_vote_users_up", columnDefinition = "bigint DEFAULT 0")
    private Long totalVoteUsersUp;

    @Column(name = "total_vote_users_down", columnDefinition = "bigint DEFAULT 0")
    private Long totalVoteUsersDown;

    @Enumerated(EnumType.STRING)
    @Column(name = "vote_result", columnDefinition = "ENUM('UP','DOWN','DRAW') DEFAULT NULL")
    private ChoiceActivityVoteResult voteResult;

    public boolean isInValidTime() {
        Date now = new Date();
        return now.after(voteStartTime) && now.before(voteEndTime);
    }

    public boolean isInLockTime() {
        Date now = new Date();
        return now.after(lockStartTime) && now.before(lockEndTime);
    }
}
