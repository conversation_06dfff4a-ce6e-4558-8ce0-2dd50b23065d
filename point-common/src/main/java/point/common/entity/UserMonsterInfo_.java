package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.UserIdType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserMonsterInfo.class)
public abstract class UserMonsterInfo_ extends AbstractEntity_ {
    public static volatile SingularAttribute<UserMonsterInfo, Long> userId;
    public static volatile SingularAttribute<UserMonsterInfo, Long> monsterId;
    public static volatile SingularAttribute<UserMonsterInfo, UserIdType> idType;
    public static volatile SingularAttribute<UserMonsterInfo, Long> level;
    public static volatile SingularAttribute<UserMonsterInfo, Long> currentExperience;
    public static volatile SingularAttribute<UserMonsterInfo, Long> nextLevelExperience;
    public static volatile SingularAttribute<UserMonsterInfo, Date> creationDateTime;
    public static volatile SingularAttribute<UserMonsterInfo, Long> monthlyPower;
    public static volatile SingularAttribute<UserMonsterInfo, String> createdBy;
    public static volatile SingularAttribute<UserMonsterInfo, String> updatedBy;
    public static volatile SingularAttribute<UserMonsterInfo, MonsterBase> monsterBase;

    public static final String USER_ID = "userId";
    public static final String MONSTER_ID = "monsterId";
    public static final String ID_TYPE = "idType";
    public static final String LEVEL = "level";
    public static final String CURRENT_EXPERIENCE = "currentExperience";
    public static final String NEXT_LEVEL_EXPERIENCE = "nextLevelExperience";
    public static final String CREATION_DATE_TIME = "creationDateTime";
    public static final String MONTHLY_POWER = "monthlyPower";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
}
