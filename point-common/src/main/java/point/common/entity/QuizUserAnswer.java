package point.common.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "quiz_user_answer")
public class QuizUserAnswer extends AbstractEntity {

    @Column(name = "quiz_id", nullable = false)
    private Long quizId;

    @Column(name = "quiz_num", nullable = false)
    private String quizNum;

    @Column(name = "quiz_title", nullable = false)
    private String quizTitle;

    @Column(name = "user_answer", nullable = false)
    private String userAnswer;

    @Column(name = "correct_answer", nullable = false)
    private String correctAnswer;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "answer_date", nullable = false)
    private Date answerDate;
}
