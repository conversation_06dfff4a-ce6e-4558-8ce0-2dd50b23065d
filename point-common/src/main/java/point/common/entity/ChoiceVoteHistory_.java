package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoiceVoteHistory.class)
public abstract class ChoiceVoteHistory_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoiceVoteHistory, Long> voteId;
    public static volatile SingularAttribute<ChoiceVoteHistory, Long> userId;
    public static volatile SingularAttribute<ChoiceVoteHistory, Long> activityId;
    public static volatile SingularAttribute<ChoiceVoteHistory, Long> beforeVotePower;
    public static volatile SingularAttribute<ChoiceVoteHistory, Long> afterVotePower;
    public static volatile SingularAttribute<ChoiceVoteHistory, UserIdentity> userIdentity;
    public static volatile SingularAttribute<ChoiceVoteHistory, ChoiceVote> choiceVote;

    public static final String VOTE_ID = "voteId";
    public static final String USER_ID = "userId";
    public static final String ACTIVITY_ID = "activityId";
    public static final String BEFORE_VOTE_POWER = "beforeVotePower";
    public static final String AFTER_VOTE_POWER = "afterVotePower";
    public static final String CHOICE_VOTE = "choiceVote";
}
