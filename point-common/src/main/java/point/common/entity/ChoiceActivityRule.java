package point.common.entity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;
import javax.persistence.*;
import lombok.*;
import point.common.constant.*;

@Getter
@Setter
@Entity
@Builder
@Table(name = "choice_activity_rule")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoiceActivityRule extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "activity_name", nullable = false)
    private String activityName;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "activity_function",
            nullable = false,
            columnDefinition =
                    "ENUM('LOGIN_OPERATION_COURSE','PASS_QUIZ','LOGIN_INVESTMENT_COURSE','OPEN_INVESTMENT_ACCOUNT','FARM','BUY_SELL_TRADE','ELECTION_RESULTS_OF_VOTE','CROP_HARVESTED_SHARED')")
    private ChoiceActivityFunction activityFunction;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "obtain_frequency",
            nullable = false,
            columnDefinition = "ENUM('DAILY','ONCE','MONTHLY','EACH_DAY','EACH_TIME')")
    private ChoiceObtainFrequency obtainFrequency;

    @Enumerated(EnumType.STRING)
    @Column(name = "id_type", nullable = false, columnDefinition = "ENUM('Invest','Operate')")
    private UserIdType idType;

    @Enumerated(EnumType.STRING)
    @Column(name = "get_type", nullable = false, columnDefinition = "ENUM('FIXED','PROPORTIONAL')")
    private ChoiceGetType getType;

    @Column(name = "power_amount")
    private Long powerAmount;

    @Column(name = "power_amount_rate", precision = 10, scale = 2)
    private BigDecimal powerAmountRate;

    @Column(name = "effective_date", nullable = false)
    private Date effectiveDate;

    @Column(name = "expiry_date", nullable = false)
    private Date expiryDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, columnDefinition = "ENUM('ACTIVE','ABOLISH')")
    private ChoiceActivityStatus status;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "ordered", nullable = false)
    private Integer ordered;

    public boolean isActive() {
        Date currentDate = new Date();
        return effectiveDate != null
                && expiryDate != null
                && !currentDate.before(effectiveDate)
                && !currentDate.after(expiryDate);
    }

    public String getAmountRateDisplayValue() {
        if (Objects.equals(ChoiceActivityFunction.BUY_SELL_TRADE, activityFunction)) {
            return "取引金額の"
                    + this.powerAmountRate
                            .multiply(new BigDecimal("100"))
                            .setScale(0, RoundingMode.DOWN)
                    + "%";
        }
        return Objects.isNull(this.powerAmountRate) ? null : this.powerAmountRate.toString();
    }
}
