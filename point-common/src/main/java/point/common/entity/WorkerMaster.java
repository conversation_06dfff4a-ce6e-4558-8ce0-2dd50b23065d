package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.CurrencyPair;
import point.common.constant.Environment;
import point.common.constant.TradeType;

@Entity
@NoArgsConstructor
@Table(name = "worker_master")
@ToString(callSuper = true, doNotUseGetters = true)
public class WorkerMaster extends AbstractEntity {

    private static final long serialVersionUID = -4695105606182919751L;

    @Getter
    @Setter
    @Column(name = "environment", nullable = false)
    @Enumerated(EnumType.STRING)
    private Environment environment;

    @Getter
    @Setter
    @Column(name = "bean_name", nullable = false)
    private String beanName;

    @Getter
    @Setter
    @Column(name = "trade_type")
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Getter
    @Setter
    @Column(name = "currency_pair")
    @Enumerated(EnumType.STRING)
    private CurrencyPair currencyPair;

    @Getter
    @Setter
    @Column(name = "interval_millis")
    private Long intervalMillis;

    @Getter
    @Setter
    @Column(name = "enabled", nullable = false)
    private boolean enabled = false;
}
