package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MonsterGrowthLevel.class)
public abstract class MonsterGrowthLevel_ extends AbstractEntity_ {
    public static volatile SingularAttribute<MonsterGrowthLevel, Long> requiredExperience;
    public static volatile SingularAttribute<MonsterGrowthLevel, Long> votingTicket;
    public static volatile SingularAttribute<MonsterGrowthLevel, String> createdBy;
    public static volatile SingularAttribute<MonsterGrowthLevel, String> updatedBy;
    public static volatile SingularAttribute<MonsterGrowthLevel, Long> ruleId;
    public static volatile SingularAttribute<MonsterGrowthLevel, ChoiceActivityRule> rule;

    public static final String REQUIRED_EXPERIENCE = "requiredExperience";
    public static final String VOTING_TICKET = "votingTicket";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
}
