package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(BcCurrencyConfig.class)
public abstract class BcCurrencyConfig_ extends AbstractEntity_ {

    public static volatile SingularAttribute<BcCurrencyConfig, String> currencyData;
    public static volatile SingularAttribute<BcCurrencyConfig, String> active;
    public static volatile SingularAttribute<BcCurrencyConfig, String> currencyType;
    public static volatile SingularAttribute<BcCurrencyConfig, String> createdBy;
    public static volatile SingularAttribute<BcCurrencyConfig, String> updatedBy;
}
