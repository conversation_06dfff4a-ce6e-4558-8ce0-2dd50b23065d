package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@Table(name = "bc_currency_config")
@ToString(callSuper = true, doNotUseGetters = true)
public class BcCurrencyConfig extends AbstractEntity {

    @Column(name = "currency_data")
    private String currencyData;

    @Column(name = "active")
    private String active = "ACTIVE";

    @Column(name = "currency_type")
    private String currencyType;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;
}
