package point.common.entity;

import javax.persistence.*;
import lombok.*;
import point.common.constant.KycStatus;
import point.common.constant.UserNoteTypeEnum;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "user_note")
public class UserNote extends AbstractEntity {

    @Column(name = "user_id")
    private Long userId;

    private String note;

    @Column(name = "note_type")
    @Enumerated(EnumType.STRING)
    private UserNoteTypeEnum noteType;

    @Column(name = "current_kyc_status")
    @Enumerated(EnumType.STRING)
    private KycStatus currentKycStatus;

    private String operator;
}
