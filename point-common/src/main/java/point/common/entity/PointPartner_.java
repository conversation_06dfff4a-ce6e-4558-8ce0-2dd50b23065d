package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.PointPartnerStatus;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PointPartner.class)
public abstract class PointPartner_ extends AbstractEntity_ {

    public static volatile SingularAttribute<PointPartner, String> partnerNumber;
    public static volatile SingularAttribute<PointPartner, String> name;
    public static volatile SingularAttribute<PointPartner, Date> effectiveDate;
    public static volatile SingularAttribute<PointPartner, Date> expiryDate;
    public static volatile SingularAttribute<PointPartner, PointPartnerStatus> status;
    public static volatile SingularAttribute<PointPartner, String> showName;
    public static volatile SingularAttribute<PointPartner, String> responsiblePerson;
    public static volatile SingularAttribute<Point<PERSON><PERSON><PERSON>, String> phoneNum;
    public static volatile SingularAttribute<PointPartner, String> mail;
    public static volatile SingularAttribute<PointPartner, String> address;
    public static volatile SingularAttribute<PointPartner, Integer> scope;
    public static volatile SingularAttribute<PointPartner, String> websiteUrl;
    public static volatile SingularAttribute<PointPartner, String> description;
    public static volatile SingularAttribute<PointPartner, String> createdBy;
    public static volatile SingularAttribute<PointPartner, String> updatedBy;

    public static final String ID = "id";
    public static final String PARTNER_NUMBER = "partnerNumber";
    public static final String NAME = "name";
    public static final String EFFECTIVE_DATE = "effectiveDate";
    public static final String EXPIRY_DATE = "expiryDate";
    public static final String STATUS = "status";
    public static final String SHOW_NAME = "showName";
    public static final String RESPONSIBLE_PERSON = "responsiblePerson";
    public static final String PHONE_NUM = "phoneNum";
    public static final String MAIL = "mail";
    public static final String ADDRESS = "address";
    public static final String SCOPE = "scope";
    public static final String WEBSITE_URL = "websiteUrl";
    public static final String DESCRIPTION = "description";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
}
