package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.*;
import lombok.*;
import point.common.constant.ChoicePowerTransferType;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "choice_power_transfer")
@ToString(callSuper = true, doNotUseGetters = true)
public class ChoicePowerTransfer extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "amount", nullable = false)
    private Long amount;

    @Enumerated(EnumType.STRING)
    @Column(name = "transfer_type", nullable = false, columnDefinition = "ENUM('IN', 'OUT')")
    private ChoicePowerTransferType transferType;

    @Column(name = "choice_activity_rule_id", nullable = false)
    private Long choiceActivityRuleId;

    @Column(name = "description", length = 256)
    private String description;

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false)
    @ToString.Exclude
    @JsonIgnore
    private UserIdentity userIdentity;

    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @ToString.Exclude
    @JsonIgnore
    private PointUser operatorPointUser;

    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @ToString.Exclude
    @JsonIgnore
    private User investPointUser;
}
