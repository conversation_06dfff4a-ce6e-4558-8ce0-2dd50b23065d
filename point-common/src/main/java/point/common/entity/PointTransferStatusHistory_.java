package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.PointTransferStatusEnum;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PointTransferStatusHistory.class)
public class PointTransferStatusHistory_ extends AbstractEntity_ {
    public static volatile SingularAttribute<PointTransferStatusHistory, PointTransfer> transfer;
    public static volatile SingularAttribute<PointTransferStatusHistory, PointTransferStatusEnum>
            previousStatus;
    public static volatile SingularAttribute<PointTransferStatusHistory, PointTransferStatusEnum>
            currentStatus;
    public static volatile SingularAttribute<PointTransferStatusHistory, String> remarks;
    public static volatile SingularAttribute<PointTransferStatusHistory, Date> changedAt;
}
