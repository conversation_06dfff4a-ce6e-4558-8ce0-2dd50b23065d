package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.SharedRecordType;
import point.common.constant.SharedTargetType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(SharedHist.class)
public abstract class SharedHist_ extends AbstractEntity_ {

    public static volatile SingularAttribute<SharedHist, Long> recordId;
    public static volatile SingularAttribute<SharedHist, SharedRecordType> sharedRecordType;
    public static volatile SingularAttribute<SharedHist, Date> sharedDay;
    public static volatile SingularAttribute<SharedHist, Long> ruleId;
    public static volatile SingularAttribute<SharedHist, ChoiceActivityFunction>
            choiceActivityFunction;
    public static volatile SingularAttribute<SharedHist, SharedTargetType> sharedTargetType;
    public static volatile SingularAttribute<SharedHist, Long> powerAmount;
    public static volatile SingularAttribute<SharedHist, Long> userId;

    public static final String USER_ID = "userId";
    public static final String SHARED_RECORD_TYPE = "sharedRecordType";
    public static final String SHARED_TARGET_TYPE = "sharedTargetType";
    public static final String SHARED_DAY = "sharedDay";
}
