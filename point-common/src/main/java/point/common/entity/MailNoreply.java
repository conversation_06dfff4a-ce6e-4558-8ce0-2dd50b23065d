package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import point.common.constant.LineFeed;
import point.common.constant.MailNoreplyType;

@Entity
@NoArgsConstructor
@Table(name = "mail_noreply")
@ToString(callSuper = true, doNotUseGetters = true)
public class MailNoreply extends AbstractEntity {

    private static final long serialVersionUID = -6722377105797603952L;

    @Getter
    @Setter
    @Column(name = "mail_noreply_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private MailNoreplyType mailNoreplyType;

    @Getter
    @Setter
    @Column(name = "from_address", nullable = false)
    private String fromAddress;

    @Getter
    @Setter
    @Column(name = "title", nullable = false)
    private String title;

    @Getter
    @Setter
    @Column(name = "contents", nullable = false)
    private String contents;

    public String getContents(String antiPhishingCode, String code, LineFeed lineFeed) {
        String contents = new String(this.contents);

        if (!StringUtils.isEmpty(antiPhishingCode)) {
            contents = contents + lineFeed.getValue() + "アンチフィッシングコード " + antiPhishingCode;
        }

        if (!StringUtils.isEmpty(code)) {
            contents = contents.replace("$code", code);
        }

        return contents;
    }
}
