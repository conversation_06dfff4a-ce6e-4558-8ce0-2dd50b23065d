package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "affiliate_info")
@ToString(callSuper = true, doNotUseGetters = true)
public class AffiliateInfo extends AbstractEntity {

    private static final long serialVersionUID = 3643980084099700186L;

    @Getter
    @Setter
    @Column(name = "affiliate_name", nullable = false)
    private String affiliateName;

    @Getter
    @Setter
    @Column(name = "identify", nullable = false)
    private String identify;
}
