package point.common.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "user_ekyc_request_history")
public class UserEkycRequestHistory extends AbstractEntity {

    @Column(name = "type")
    private String type;

    @Column(name = "request_time")
    private Date requestTime;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "response_header_json")
    private String responseHeaderJson;

    @Column(name = "response_entity_json")
    private String responseEntityJson;

    @Column(name = "request_header_json")
    private String requestHeaderJson;

    @Column(name = "request_entity_json")
    private String requestEntityJson;

    @Column(name = "success")
    private Boolean success;

    @Column(name = "url")
    private String url;
}
