package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.UserAgreementType;

@Setter
@Getter
@Entity
@Table(name = "user_agreement_file")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class UserAgreementFile extends AbstractEntity {

    @Column(name = "user_agreement_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UserAgreementType userAgreementType;

    @Column(name = "version")
    private Integer version;

    public UserAgreementFile(UserAgreementType userAgreementType, Integer version) {
        this.userAgreementType = userAgreementType;
        this.version = version;
    }
}
