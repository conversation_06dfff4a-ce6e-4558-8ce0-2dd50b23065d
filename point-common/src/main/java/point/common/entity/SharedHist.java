package point.common.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.SharedRecordType;
import point.common.constant.SharedTargetType;

@Getter
@Setter
@Entity
@Builder
@Table(name = "shared_hist")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class SharedHist extends AbstractEntity {

    @Column(name = "record_id", nullable = false)
    private Long recordId;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "record_type",
            nullable = false,
            columnDefinition =
                    "ENUM('INVEST_TRADE_HIST_SHARED','OPERATE_TRADE_HIST_SHARED','REWARD_HIST_SHARED')")
    private SharedRecordType sharedRecordType;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "shared_d", nullable = false)
    private Date sharedDay;

    @Enumerated(EnumType.STRING)
    @Column(name = "target_type", nullable = false, columnDefinition = "ENUM('TWITTER_X')")
    private SharedTargetType sharedTargetType;

    @Column(name = "rule_id")
    private Long ruleId;

    @Enumerated(EnumType.STRING)
    @Column(
            name = "choice_activity_function",
            columnDefinition =
                    "ENUM('LOGIN_OPERATION_COURSE','PASS_QUIZ','LOGIN_INVESTMENT_COURSE','OPEN_INVESTMENT_ACCOUNT','FARM','BUY_SELL_TRADE','ELECTION_RESULTS_OF_VOTE','CROP_HARVESTED_SHARED')")
    private ChoiceActivityFunction choiceActivityFunction;

    @Column(name = "power_amount")
    private Long powerAmount;
}
