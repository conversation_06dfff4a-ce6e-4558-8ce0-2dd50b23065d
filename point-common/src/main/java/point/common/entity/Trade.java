package point.common.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.OrderSide;
import point.common.constant.TradeAction;
import point.common.serializer.BigDecimalSerializer;

@MappedSuperclass
@ToString(callSuper = true, doNotUseGetters = true)
public abstract class Trade extends AbstractEntity {

    private static final long serialVersionUID = -5272495511073907204L;

    public static String getTableName(Symbol symbol) {
        return symbol.getTradeType().toLowerCase()
                + "_trade_"
                + symbol.getCurrencyPair().toLowerCase();
    }

    @Getter
    @Setter
    @Column(name = "symbol_id", nullable = false)
    private Long symbolId;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "order_side", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderSide orderSide;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Column(name = "price", precision = 34, scale = 20, nullable = false)
    private BigDecimal price;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Column(name = "amount", precision = 34, scale = 20, nullable = false)
    private BigDecimal amount;

    @Getter
    @Setter
    @Column(name = "trade_action", nullable = false)
    @Enumerated(EnumType.STRING)
    private TradeAction tradeAction;

    @Getter
    @Setter
    @Column(name = "order_id", nullable = false)
    private Long orderId;

    @Getter
    @Setter
    @JsonSerialize(using = BigDecimalSerializer.class)
    @Column(name = "fee", precision = 34, scale = 20, nullable = false)
    private BigDecimal fee;

    public Trade setProperties(
            Long symbolId,
            Long userId,
            OrderSide orderSide,
            BigDecimal price,
            BigDecimal amount,
            TradeAction tradeAction,
            Long orderId) {
        this.symbolId = symbolId;
        this.userId = userId;
        this.orderSide = orderSide;
        this.price = price;
        this.amount = amount;
        this.tradeAction = tradeAction;
        this.orderId = orderId;
        return this;
    }
}
