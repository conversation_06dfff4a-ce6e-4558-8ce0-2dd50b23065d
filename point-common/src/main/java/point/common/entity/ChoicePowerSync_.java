package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.ChoiceActivityFunction;
import point.common.constant.ChoiceGetType;
import point.common.constant.ChoiceObtainFrequency;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoicePowerSync.class)
public abstract class ChoicePowerSync_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoicePowerSync, Long> userId;
    public static volatile SingularAttribute<ChoicePowerSync, Long> choiceActivityRuleId;
    public static volatile SingularAttribute<ChoicePowerSync, ChoiceActivityFunction>
            activityFunction;
    public static volatile SingularAttribute<ChoicePowerSync, ChoiceObtainFrequency>
            obtainFrequency;
    public static volatile SingularAttribute<ChoicePowerSync, ChoiceGetType> getType;
    public static volatile SingularAttribute<ChoicePowerSync, Long> powerAmount;
    public static volatile SingularAttribute<ChoicePowerSync, Integer> synchronizeFlag;

    public static final String USER_ID = "userId";
    public static final String CHOICE_ACTIVITY_RULE_ID = "choiceActivityRuleId";
    public static final String ACTIVITY_FUNCTION = "activityFunction";
    public static final String OBTAIN_FREQUENCY = "obtainFrequency";
    public static final String GET_TYPE = "getType";
    public static final String POWER_AMOUNT = "powerAmount";
    public static final String SYNCHRONIZE_FLAG = "synchronizeFlag";
}
