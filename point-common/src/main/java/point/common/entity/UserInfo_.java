package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Country;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserInfo.class)
public abstract class UserInfo_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserInfo, Long> userId;
    public static volatile SingularAttribute<UserInfo, String> firstName;
    public static volatile SingularAttribute<UserInfo, String> lastName;
    public static volatile SingularAttribute<UserInfo, String> firstKana;
    public static volatile SingularAttribute<UserInfo, String> lastKana;
    public static volatile SingularAttribute<UserInfo, String> nationality;
    public static volatile SingularAttribute<UserInfo, String> zipCode;
    public static volatile SingularAttribute<UserInfo, String> prefecture;
    public static volatile SingularAttribute<UserInfo, String> city;
    public static volatile SingularAttribute<UserInfo, String> address;
    public static volatile SingularAttribute<UserInfo, String> building;
    public static volatile SingularAttribute<UserInfo, String> birthday;
    public static volatile SingularAttribute<UserInfo, Integer> gender;
    public static volatile SingularAttribute<UserInfo, String> phoneNumber;
    public static volatile SingularAttribute<UserInfo, Integer> occupation;
    public static volatile SingularAttribute<UserInfo, Integer> industry;
    public static volatile SingularAttribute<UserInfo, String> workPlace;
    public static volatile SingularAttribute<UserInfo, String> position;
    public static volatile SingularAttribute<UserInfo, Integer> income;
    public static volatile SingularAttribute<UserInfo, Integer> financialAssets;
    public static volatile SingularAttribute<UserInfo, Integer> purpose;
    public static volatile SingularAttribute<UserInfo, Integer> cryptoInsider;
    public static volatile SingularAttribute<UserInfo, Integer> investmentPurposes;
    public static volatile SingularAttribute<UserInfo, Integer> cryptoExperience;
    public static volatile SingularAttribute<UserInfo, Integer> fxExperience;
    public static volatile SingularAttribute<UserInfo, Integer> fundExperience;
    public static volatile SingularAttribute<UserInfo, Integer> stocksExperience;
    public static volatile SingularAttribute<UserInfo, Integer> applicationHistory;
    public static volatile SingularAttribute<UserInfo, String> applicationHistoryOther;
    public static volatile SingularAttribute<UserInfo, Boolean> foreignPeps;
    public static volatile SingularAttribute<UserInfo, Country> country;
    public static volatile SingularAttribute<UserInfo, Date> residenceCardExpiredAt;
    public static volatile SingularAttribute<UserInfo, String> residenceStatus;
    public static volatile SingularAttribute<UserInfo, String> insider;
    public static volatile SingularAttribute<UserInfo, Integer> priceFrom;
    public static volatile SingularAttribute<UserInfo, String> antisocialStatus;

    public static final String USER_ID = "userId";
    public static final String FIRST_NAME = "firstName";
    public static final String LAST_NAME = "lastName";
    public static final String FIRST_KANA = "firstKana";
    public static final String LAST_KANA = "lastKana";
    public static final String NATIONALITY = "nationality";
    public static final String ZIP_CODE = "zipCode";
    public static final String PREFECTURE = "prefecture";
    public static final String CITY = "city";
    public static final String ADDRESS = "address";
    public static final String BUILDING = "building";
    public static final String BIRTHDAY = "birthday";
    public static final String GENDER = "gender";
    public static final String PHONE_NUMBER = "phoneNumber";
    public static final String OCCUPATION = "occupation";
    public static final String INDUSTRY = "industry";
    public static final String WORK_PLACE = "workPlace";
    public static final String POSITION = "position";
    public static final String INCOME = "income";
    public static final String FINANCIAL_ASSETS = "financialAssets";
    public static final String PURPOSES = "purposes";
    public static final String CRYPTO_INSIDER = "cryptoInsider";
    public static final String INVESTMENT_PURPOSES = "investmentPurposes";
    public static final String CRYPTO_EXPERIENCE = "cryptoExperience";
    public static final String FX_EXPERIENCE = "fxExperience";
    public static final String FUND_EXPERIENCE = "fundExperience";
    public static final String STOCKS_EXPERIENCE = "stocksExperience";
    public static final String APPLICATION_HISTORY = "applicationHistory";
    public static final String APPLICATION_HISTORY_OTHER = "applicationHistoryOther";
    public static final String FOREIGN_PEPS = "foreignPeps";
    public static final String COUNTRY = "country";
    public static final String RESIDENCE_CARD_EXPIRED_AT = "residenceCardExpiredAt";
    public static final String RESIDENCE_STATUS = "residenceStatus";
}
