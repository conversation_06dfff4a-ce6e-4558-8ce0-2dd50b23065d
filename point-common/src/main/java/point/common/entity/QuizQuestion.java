package point.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Where;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "quiz_question")
@Where(clause = "enable = 1")
public class QuizQuestion extends AbstractEntity {

    @Column(name = "quiz_num", nullable = false)
    private String quizNum;

    @Column(name = "title", nullable = false)
    private String title;

    @Column(name = "answer", nullable = false)
    private String answer;

    @Column(name = "correct_answer_content", nullable = false)
    private String correctAnswerContent;

    @Column(name = "option_A_content", nullable = false)
    private String optionA;

    @Column(name = "option_B_content", nullable = false)
    private String optionB;

    @Column(name = "option_C_content", nullable = false)
    private String optionC;

    @Column(name = "option_D_content", nullable = false)
    private String optionD;

    @Column(name = "option_A_wrong_reason")
    private String optionAWrongReason;

    @Column(name = "option_B_wrong_reason")
    private String optionBWrongReason;

    @Column(name = "option_C_wrong_reason")
    private String optionCWrongReason;

    @Column(name = "option_D_wrong_reason")
    private String optionDWrongReason;

    @Column(name = "published_at", nullable = false)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date publishedAt;

    @Column(name = "enable", nullable = false)
    private boolean enable;

    @Column(name = "created_user", nullable = false)
    private String createdUser;

    @Column(name = "updated_user", nullable = false)
    private String updatedUser;
}
