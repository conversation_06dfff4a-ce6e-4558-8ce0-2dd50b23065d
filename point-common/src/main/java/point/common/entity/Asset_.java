package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.Currency;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Asset.class)
public abstract class Asset_ extends AbstractEntity_ {

    public static volatile SingularAttribute<Asset, BigDecimal> lockedAmount;
    public static volatile SingularAttribute<Asset, BigDecimal> onhandAmount;
    public static volatile SingularAttribute<Asset, Currency> currency;
    public static volatile SingularAttribute<Asset, Long> userId;
    public static volatile SingularAttribute<Asset, UserIdentity> userIdentity;
    public static volatile SingularAttribute<Asset, BigDecimal> assetTotal;
    public static volatile SingularAttribute<Asset, BigDecimal> evalProfitLossAmt;
    public static volatile SingularAttribute<Asset, BigDecimal> evalProfitLossAmtRate;
    public static volatile SingularAttribute<Asset, BigDecimal> avgAcqUnitPrice;

    public static final String LOCKED_AMOUNT = "lockedAmount";
    public static final String ONHAND_AMOUNT = "onhandAmount";
    public static final String CURRENCY = "currency";
    public static final String USER_ID = "userId";
}
