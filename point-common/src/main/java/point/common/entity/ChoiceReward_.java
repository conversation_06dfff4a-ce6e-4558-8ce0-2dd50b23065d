package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.*;

@StaticMetamodel(ChoiceReward.class)
public class ChoiceReward_ extends AbstractEntity {

    public static volatile SingularAttribute<ChoiceReward, Long> userId;
    public static volatile SingularAttribute<ChoiceReward, Long> activityId;
    public static volatile SingularAttribute<ChoiceReward, BigDecimal> amount;
    public static volatile SingularAttribute<ChoiceReward, ChoiceRewardType> choiceRewardType;
    public static volatile SingularAttribute<ChoiceReward, Date> rewardTime;
    public static volatile SingularAttribute<ChoiceReward, Date> expiryTime;
    public static volatile SingularAttribute<ChoiceReward, WithdrawStatusType> withdrawStatusType;
    public static volatile SingularAttribute<ChoiceReward, BigDecimal> remainingAmount;
}
