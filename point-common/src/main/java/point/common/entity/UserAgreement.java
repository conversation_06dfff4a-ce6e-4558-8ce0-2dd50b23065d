package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.UserAgreementType;

@Entity
@Table(name = "user_agreement")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class UserAgreement extends AbstractEntity {

    private static final long serialVersionUID = -1192077393415643820L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "user_agreement_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UserAgreementType userAgreementType;

    @Getter
    @Setter
    @Column(name = "version", nullable = false)
    private Integer version;

    @Getter
    @Setter
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

    public UserAgreement(Long userId, UserAgreementType userAgreementType, Integer version) {
        this.userId = userId;
        this.userAgreementType = userAgreementType;
        this.version = version;
        this.enabled = true;
    }
}
