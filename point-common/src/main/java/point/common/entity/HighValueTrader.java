package point.common.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;

@Entity
@Table(name = "high_value_trader")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class HighValueTrader extends AbstractEntity {

    private static final long serialVersionUID = 687981302642535333L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "trade_type", nullable = true)
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Getter
    @Setter
    @Column(name = "currency_pair", nullable = true)
    @Enumerated(EnumType.STRING)
    private CurrencyPair currencyPair;

    @Getter
    @Setter
    @Column(name = "pos_order_limits", nullable = false)
    private Integer posOrderLimits;

    @Getter
    @Setter
    @Column(name = "pos_trade_markets", nullable = false)
    private Integer posTradeMarkets;

    @Getter
    @Setter
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private User user;
}
