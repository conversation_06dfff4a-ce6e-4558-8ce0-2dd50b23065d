package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.AntisocialStatus;
import point.common.constant.KycMailStatus;
import point.common.constant.KycStatus;
import point.common.constant.KycType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserKyc.class)
public abstract class UserKyc_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserKyc, Long> userId;
    public static volatile SingularAttribute<UserKyc, KycType> kycType;
    public static volatile SingularAttribute<UserKyc, KycStatus> kycStatus;
    public static volatile SingularAttribute<UserKyc, KycMailStatus> kycMailStatus;
    public static volatile SingularAttribute<UserKyc, Date> mailSendAt;
    public static volatile SingularAttribute<UserKyc, String> judgingComment;
    public static volatile SingularAttribute<UserKyc, String> amlCftComment;
    public static volatile SingularAttribute<UserKyc, AntisocialStatus> antisocialStatus;
    public static volatile SingularAttribute<UserKyc, String> email;
    public static volatile SingularAttribute<UserKyc, String> operator;
    public static volatile SingularAttribute<UserKyc, String> changeType;
    public static volatile SingularAttribute<UserKyc, Long> userInfoId;

    public static final String USER_ID = "userId";
    public static final String KYC_TYPE = "kycType";
    public static final String KYC_STATUS = "kycStatus";
    public static final String KYC_MAIL_STATUS = "kycMailStatus";
    public static final String MAIL_SEND_AT = "mailSendAt";
    public static final String JUDGING_COMMENT = "judgingComment";
    public static final String AML_CFT_COMMENT = "amlCftComment";
    public static final String ANTISOCIAL_STATUS = "antisocialStatus";
}
