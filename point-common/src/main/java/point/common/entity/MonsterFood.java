package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.Currency;
import point.common.constant.UserIdType;

@Setter
@Getter
@Entity
@Table(name = "monster_food")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class MonsterFood extends AbstractEntity {

    // 種別：Invest | Operate
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, columnDefinition = "ENUM('Invest','Operate')")
    private UserIdType type;

    // 通貨
    @Column(name = "currency")
    @Enumerated(EnumType.STRING)
    private Currency currency;

    // シンボルID
    @Column(name = "symbol_id")
    private Long symbolId;

    // 年
    @Column(name = "year")
    private Integer year;

    // 週
    @Column(name = "week")
    private Integer week;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;
}
