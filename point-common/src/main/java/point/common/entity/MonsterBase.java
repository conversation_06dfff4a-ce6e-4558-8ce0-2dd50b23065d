package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.UserIdType;

@Setter
@Getter
@Entity
@Table(name = "monster_base")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class MonsterBase extends AbstractEntity {

    // モンスター名称
    @Column(name = "name")
    private String name;

    // 種別
    @Column(name = "id_type")
    @Enumerated(EnumType.STRING)
    private UserIdType idType;

    // モンスターIMG-URL
    @Column(name = "img_url")
    private String imgUrl;

    // 最高レベル
    @Column(name = "max_level")
    private Long maxLevel;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;
}
