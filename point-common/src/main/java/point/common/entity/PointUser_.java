package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(PointUser.class)
public abstract class PointUser_ extends AbstractEntity_ {

    public static volatile SingularAttribute<PointUser, Long> userId;
    public static volatile SingularAttribute<PointUser, Long> partnerId;
    public static volatile SingularAttribute<PointUser, String> partnerMemberId;
    public static volatile SingularAttribute<PointUser, PointPartner> pointPartner;
    public static volatile SingularAttribute<PointUser, UserIdentity> userIdentity;
    public static volatile SingularAttribute<PointUser, User> user;

    public static final String USER_ID = "userId";
    public static final String PARTNER_ID = "partnerId";
    public static final String PARTNER_MEMBER_ID = "partnerMemberId";
    public static final String POINT_PARTNER = "pointPartner";
    public static final String USER_IDENTITY = "userIdentity";
}
