package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.ChoiceActivityAutoCycle;
import point.common.constant.ChoiceActivityStatus;
import point.common.constant.ChoiceActivityType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoiceActivityTemplate.class)
public abstract class ChoiceActivityTemplate_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoiceActivityTemplate, ChoiceActivityType>
            activityType;
    public static volatile SingularAttribute<ChoiceActivityTemplate, String> voteStartTime;
    public static volatile SingularAttribute<ChoiceActivityTemplate, String> voteEndTime;
    public static volatile SingularAttribute<ChoiceActivityTemplate, Date> startTime;
    public static volatile SingularAttribute<ChoiceActivityTemplate, Date> endTime;
    public static volatile SingularAttribute<ChoiceActivityTemplate, Long> votePowerLimit;
    public static volatile SingularAttribute<ChoiceActivityTemplate, ChoiceActivityStatus> status;
    public static volatile SingularAttribute<ChoiceActivityTemplate, ChoiceActivityAutoCycle>
            autoCycle;
    public static volatile SingularAttribute<ChoiceActivityTemplate, Long> powerTotal;
    public static volatile SingularAttribute<ChoiceActivityRule, String> createdBy;
    public static volatile SingularAttribute<ChoiceActivityRule, String> updatedBy;

    public static final String ACTIVITY_TYPE = "activityType";
    public static final String VOTE_START_TIME = "voteStartTime";
    public static final String VOTE_END_TIME = "voteEndTime";
    public static final String START_TIME = "startTime";
    public static final String END_TIME = "endTime";
    public static final String VOTE_POWER_LIMIT = "votePowerLimit";
    public static final String STATUS = "status";
    public static final String AUTO_CYCLE = "autoCycle";
    public static final String POWER_TOTAL = "powerTotal";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
}
