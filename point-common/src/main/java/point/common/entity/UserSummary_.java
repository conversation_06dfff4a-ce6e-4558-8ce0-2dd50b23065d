package point.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(UserSummary.class)
public abstract class UserSummary_ extends AbstractEntity_ {

    public static volatile SingularAttribute<UserSummary, Date> targetAt;
    public static volatile SingularAttribute<UserSummary, Long> users;
    public static volatile SingularAttribute<UserSummary, Long> userKycDone;
    public static volatile SingularAttribute<UserSummary, Long> userKycAccountOpeningDone;

    public static final String TARGET_AT = "targetAt";
    public static final String USERS = "users";
    public static final String USER_KYC_DONE = "userKycDone";
    public static final String USER_KYC_ACCOUNT_OPENING_DONE = "userKycAccountOpeningDone";
}
