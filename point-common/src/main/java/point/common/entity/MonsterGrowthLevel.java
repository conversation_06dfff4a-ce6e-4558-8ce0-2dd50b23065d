package point.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Entity
@Table(name = "monster_growth_level")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class MonsterGrowthLevel extends AbstractEntity {

    // 必要な経験
    @Column(name = "required_experience")
    private Long requiredExperience;

    // 投票チケット
    @Column(name = "rule_id")
    private Long ruleId;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Getter
    @Setter
    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(
            name = "rule_id", // PointUser FK
            referencedColumnName = "id",
            insertable = false,
            updatable = false, // User PK
            foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ChoiceActivityRule rule;
}
