package point.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import point.common.constant.Country;
import point.common.model.request.UserInfoForm;
import point.common.util.DateUnit;
import point.common.util.TransliteratorUtil;

@Entity
@NoArgsConstructor
@Table(name = "user_info")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserInfo extends AbstractEntity {

    private static final long serialVersionUID = -8955673772046322349L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "first_name", nullable = false)
    private String firstName;

    @Getter
    @Setter
    @Column(name = "last_name", nullable = false)
    private String lastName;

    @Getter
    @Setter
    @Column(name = "first_kana", nullable = false)
    private String firstKana;

    @Getter
    @Setter
    @Column(name = "last_kana", nullable = false)
    private String lastKana;

    @Getter
    @Setter
    @Column(name = "nationality", nullable = false)
    private String nationality;

    @Getter
    @Setter
    @Column(name = "zip_code", nullable = false)
    private String zipCode;

    @Getter
    @Setter
    @Column(name = "prefecture", nullable = false)
    private String prefecture;

    @Getter
    @Setter
    @Column(name = "city", nullable = false)
    private String city;

    @Getter
    @Setter
    @Column(name = "address", nullable = false)
    private String address;

    @Getter
    @Setter
    @Column(name = "building")
    private String building;

    @Getter
    @Setter
    @Column(name = "birthday", nullable = false)
    private String birthday;

    @Getter
    @Setter
    @Column(name = "gender")
    private Integer gender;

    @Getter
    @Setter
    @Column(name = "phone_number", nullable = false)
    private String phoneNumber;

    @Getter
    @Setter
    @Column(name = "occupation", nullable = false)
    private Integer occupation;

    @Getter
    @Setter
    @Column(name = "industry", nullable = false)
    private Integer industry;

    @Getter
    @Setter
    @Column(name = "work_place")
    private String workPlace;

    @Getter
    @Setter
    @Column(name = "position", nullable = false)
    private String position;

    @Getter
    @Setter
    @Column(name = "priceFrom", nullable = false)
    private Integer priceFrom;

    @Getter
    @Setter
    @Column(name = "income", nullable = false)
    private Integer income;

    @Getter
    @Setter
    @Column(name = "financial_assets", nullable = false)
    private Integer financialAssets;

    @Getter
    @Setter
    @Column(name = "purpose", nullable = false)
    private Integer purpose;

    @Getter
    @Setter
    @Column(name = "investment_purposes", nullable = false)
    private Integer investmentPurposes;

    @Getter
    @Setter
    @Column(name = "crypto_experience", nullable = false)
    private Integer cryptoExperience;

    @Getter
    @Setter
    @Column(name = "fx_experience", nullable = false)
    private Integer fxExperience;

    @Getter
    @Setter
    @Column(name = "stocks_experience", nullable = false)
    private Integer stocksExperience;

    @Getter
    @Setter
    @Column(name = "fund_experience", nullable = false)
    private Integer fundExperience;

    @Getter
    @Setter
    @Column(name = "application_history", nullable = false)
    private Integer applicationHistory;

    @Getter
    @Setter
    @Column(name = "application_history_other")
    private String applicationHistoryOther;

    @Getter
    @Setter
    @Column(name = "foreign_peps", nullable = false)
    private boolean foreignPeps;

    @Getter
    @Setter
    @Column(name = "country")
    @Enumerated(EnumType.STRING)
    private Country country;

    @Getter
    @Setter
    @Column(name = "antisocial_status", nullable = false)
    private String antisocialStatus;

    @Getter
    @Setter
    @Column(name = "residence_status")
    private String residenceStatus;

    @Getter
    @Setter
    @Column(name = "residence_card_expired_at", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date residenceCardExpiredAt;

    @Getter
    @Setter
    @Column(name = "insider", nullable = false)
    private boolean insider = false;

    public UserInfo(Long userId) {
        this.userId = userId;
    }

    public UserInfo setProperties(UserInfoForm form) throws Exception {
        this.firstName = TransliteratorUtil.halfWidthToFullWidth(form.getFirstName());
        this.lastName = TransliteratorUtil.halfWidthToFullWidth(form.getLastName());
        this.firstKana = TransliteratorUtil.halfWidthToFullWidth(form.getFirstKana());
        this.lastKana = TransliteratorUtil.halfWidthToFullWidth(form.getLastKana());
        this.zipCode = form.getZipCode();
        this.nationality = form.getNationality();
        this.prefecture = form.getPrefecture();
        this.city = TransliteratorUtil.halfWidthToFullWidth(form.getCity());
        this.address = TransliteratorUtil.halfWidthToFullWidth(form.getAddress());

        if (!StringUtils.isEmpty(form.getBuilding())) {
            this.building = TransliteratorUtil.halfWidthToFullWidth(form.getBuilding());
        } else {
            this.building = null;
        }

        this.birthday = form.getBirthday();
        this.gender = Integer.parseInt(form.getGender());
        this.phoneNumber = form.getPhoneNumber();
        this.occupation = Integer.parseInt(form.getOccupation());

        if (!StringUtils.isEmpty(form.getIndustry())) {
            this.industry = Integer.parseInt(form.getIndustry());
        } else {
            this.industry = null;
        }

        if (!StringUtils.isEmpty(form.getWorkPlace())) {
            this.workPlace = TransliteratorUtil.halfWidthToFullWidth(form.getWorkPlace());
        } else {
            this.workPlace = null;
        }
        if (!StringUtils.isEmpty(form.getPosition())) {
            this.position = TransliteratorUtil.halfWidthToFullWidth(form.getPosition());
        } else {
            this.position = null;
        }
        if (!StringUtils.isEmpty(form.getPriceFrom())) {
            this.priceFrom = Integer.parseInt(form.getPriceFrom());
        } else {
            this.priceFrom = null;
        }

        this.income = Integer.parseInt(form.getIncome());
        this.financialAssets = Integer.parseInt(form.getFinancialAssets());
        this.purpose = Integer.parseInt(form.getPurpose());
        this.investmentPurposes = Integer.parseInt(form.getInvestmentPurposes());
        this.cryptoExperience = Integer.parseInt(form.getCryptoExperience());
        this.fxExperience = Integer.parseInt(form.getFxExperience());
        this.stocksExperience = Integer.parseInt(form.getStocksExperience());
        this.fundExperience = Integer.parseInt(form.getFundExperience());
        this.applicationHistory = Integer.parseInt(form.getApplicationHistory());
        this.applicationHistoryOther =
                !StringUtils.isEmpty(form.getApplicationHistoryOther())
                        ? TransliteratorUtil.halfWidthToFullWidth(form.getApplicationHistoryOther())
                        : null;
        this.foreignPeps = Boolean.parseBoolean(form.getForeignPeps());
        if (!StringUtils.isEmpty(form.getCountry())) {
            this.country = Country.valueOfName(form.getCountry());
        } else {
            this.country = null;
        }

        this.residenceCardExpiredAt =
                StringUtils.isBlank(form.getResidenceCardExpiredAt())
                        ? null
                        : DateUnit.toFormatStrToDate(form.getResidenceCardExpiredAt());

        this.residenceStatus =
                StringUtils.isBlank(form.getResidenceCardExpiredAt())
                        ? null
                        : form.getResidenceStatus().trim();

        return this;
    }
}
