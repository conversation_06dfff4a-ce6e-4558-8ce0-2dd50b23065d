package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import point.common.constant.MailNoreplyType;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MailNoreply.class)
public abstract class MailNoreply_ extends AbstractEntity_ {
    public static volatile SingularAttribute<MailNoreply, MailNoreplyType> mailNoreplyType;
    public static volatile SingularAttribute<MailNoreply, String> fromAddress;
    public static volatile SingularAttribute<MailNoreply, String> title;
    public static volatile SingularAttribute<MailNoreply, String> contents;

    public static final String MAIL_NOREPLY_TYPE = "mailNoreplyType";
    public static final String FROM_ADDRESS = "fromAddress";
    public static final String TITLE = "title";
    public static final String CONTENTS = "contents";
}
