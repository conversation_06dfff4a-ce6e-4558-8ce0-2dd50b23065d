package point.common.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "user_summary")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserSummary extends AbstractEntity {

    private static final long serialVersionUID = -8778937891595714900L;

    @Getter
    @Setter
    @Column(name = "target_at", nullable = false)
    private Date targetAt;

    @Getter
    @Setter
    @Column(name = "users", nullable = false)
    private Long users = 0L;

    @Getter
    @Setter
    @Column(name = "user_kyc_done", nullable = false)
    private Long userKycDone = 0L;

    @Getter
    @Setter
    @Column(name = "user_kyc_account_opening_done", nullable = false)
    private Long userKycAccountOpeningDone = 0L;

    public UserSummary(Date targetAt) {

        this.targetAt = targetAt;
    }
}
