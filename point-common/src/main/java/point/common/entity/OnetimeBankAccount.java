package point.common.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Entity
@Table(name = "onetime_bank_account")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class OnetimeBankAccount extends AbstractEntity {

    private static final long serialVersionUID = -8572975693891497381L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Getter
    @Setter
    @Column(name = "branch_code", nullable = false)
    private String branchCode;

    @Getter
    @Setter
    @Column(name = "branch_name", nullable = false)
    private String branchName;

    @Getter
    @Setter
    @Column(name = "account_number", nullable = false)
    private String accountNumber;

    @Getter
    @Setter
    @Column(name = "va_type_code")
    private String vaTypeCode;

    @Getter
    @Setter
    @Column(name = "va_type_name")
    private String vaTypeName;

    @Getter
    @Setter
    @Column(name = "va_holder_name_kana")
    private String vaHolderNameKana;

    @Getter
    @Setter
    @Column(name = "va_id")
    private String vaId;

    @Getter
    @Setter
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private User user;
}
