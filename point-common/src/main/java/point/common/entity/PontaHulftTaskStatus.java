package point.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "point_hulft_task_status")
public class PontaHulftTaskStatus extends AbstractEntity {

    @Column(name = "task_name", nullable = false, length = 180)
    private String taskName;

    @Enumerated(EnumType.STRING) // 将枚举类型存储为字符串
    @Column(name = "parsing_status", columnDefinition = "ENUM('PENDING','SUCCESS','FAILED')")
    private ParsingStatus parsingStatus;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "send_content", columnDefinition = "TEXT")
    private String sendContent;

    @Column(name = "parsing_start_time")
    private Date parsingStartTime;

    @Column(name = "parsing_end_time")
    private Date parsingEndTime;

    public enum ParsingStatus {
        PENDING,
        SUCCESS,
        FAILED
    }
}
