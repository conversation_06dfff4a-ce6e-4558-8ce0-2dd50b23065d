package point.common.entity;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoicePowerUserRel.class)
public abstract class ChoicePowerUserRel_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoicePowerUserRel, Long> choicePowerId;
    public static volatile SingularAttribute<ChoicePowerUserRel, Long> userId;
    public static volatile SingularAttribute<ChoicePowerUserRel, UserIdentity> userIdentity;
    public static volatile SingularAttribute<ChoicePowerUserRel, PointUser> operatorPointUser;
    public static volatile SingularAttribute<ChoicePowerUserRel, PointUser> investPointUser;

    public static final String CHOICE_POWER_ID = "choicePowerId";
    public static final String USER_ID = "userId";
    public static final String USER_IDENTITY = "userIdentity";
    public static final String OPERATOR_POINT_USER = "operatorPointUser";
    public static final String INVEST_POINT_USER = "investPointUser";
}
