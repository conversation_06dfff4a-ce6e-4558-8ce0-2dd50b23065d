package point.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import point.common.constant.Authority;

@Entity
@NoArgsConstructor
@Table(name = "user_authority")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserAuthority extends AbstractEntity implements GrantedAuthority {

    private static final long serialVersionUID = 3517585844586741165L;

    @Getter
    @Setter
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Setter
    @Column(name = "authority", nullable = false)
    @Enumerated(EnumType.STRING)
    private Authority authority;

    public UserAuthority(Long userId, Authority authority) {
        this.userId = userId;
        this.authority = authority;
    }

    public String getAuthority() {
        return this.authority.name();
    }

    public boolean isPersonal() {
        return authority == Authority.PERSONAL;
    }

    public boolean isSpecial() {
        return authority == Authority.SPECIAL;
    }
}
