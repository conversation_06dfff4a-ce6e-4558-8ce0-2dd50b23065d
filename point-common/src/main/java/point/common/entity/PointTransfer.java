package point.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import point.common.constant.HulftUploadStatusEnum;
import point.common.constant.PointTransferStatusEnum;
import point.common.constant.PointTransferTypeEnum;
import point.common.constant.UserIdType;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "point_transfer")
public class PointTransfer extends AbstractEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "partner_id", nullable = false)
    private Long partnerId;

    @Column(name = "id_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UserIdType userIdType;

    @Column(name = "transfer_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private PointTransferTypeEnum transferType;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Column(name = "fee")
    private BigDecimal fee;

    @Column(name = "request_time", nullable = false)
    private Date requestTime;

    @Column(name = "transfer_time", nullable = false)
    private Date transferTime;

    @Column(name = "trade_number", nullable = false)
    private String tradeNumber;

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private PointTransferStatusEnum status;

    @Column(name = "upload_status")
    @Enumerated(EnumType.STRING)
    private HulftUploadStatusEnum uploadStatus;

    @Column(name = "upload_time")
    private Date uploadTime;

    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "partner_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    private PointPartner pointPartner;

    @OneToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    @NotFound(action = NotFoundAction.IGNORE)
    private PointUser pointUser;

    @OneToOne(cascade = CascadeType.DETACH)
    @JoinColumn(
            name = "user_id",
            referencedColumnName = "id",
            foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT),
            insertable = false,
            updatable = false)
    @Fetch(FetchMode.JOIN)
    @NotFound(action = NotFoundAction.IGNORE)
    private User investUser;
}
