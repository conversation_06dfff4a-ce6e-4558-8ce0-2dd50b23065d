package point.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.ListAttribute;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(ChoicePower.class)
public abstract class ChoicePower_ extends AbstractEntity_ {

    public static volatile SingularAttribute<ChoicePower, Integer> amount;
    public static volatile SingularAttribute<ChoicePower, BigDecimal> choicePAmount;
    public static volatile ListAttribute<ChoicePower, ChoicePowerUserRel> choicePowerUserRels;

    public static final String AMOUNT = "amount";
    public static final String CHOICE_POWER_USER_RELS = "choicePowerUserRels";
}
