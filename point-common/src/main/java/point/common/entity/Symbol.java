package point.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import point.common.constant.CurrencyPair;
import point.common.constant.TradeType;

@Entity
@NoArgsConstructor
@Table(name = "symbol")
@ToString(callSuper = true, doNotUseGetters = true)
public class Symbol extends AbstractEntity {

    private static final long serialVersionUID = -6374111581110251808L;

    @Getter
    @Setter
    @Column(name = "trade_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private TradeType tradeType;

    @Getter
    @Setter
    @Column(name = "currency_pair", nullable = false)
    @Enumerated(EnumType.STRING)
    private CurrencyPair currencyPair;

    @Getter
    @Setter
    @Column(name = "visible", nullable = false)
    private Boolean isVisible;

    public Symbol setProperties(TradeType tradeType, CurrencyPair currencyPair) {
        this.tradeType = tradeType;
        this.currencyPair = currencyPair;
        return this;
    }

    @JsonIgnore
    public String getName() {
        return tradeType + "_" + currencyPair;
    }

    @JsonIgnore
    public String getCurrencyPairName() {
        return currencyPair.getName();
    }

    @JsonIgnore
    public String getBaseCurrencyName() {
        return currencyPair.getBaseCurrency().getName();
    }
}
