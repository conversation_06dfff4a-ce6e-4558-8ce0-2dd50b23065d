package point.common.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.crossstore.ChangeSetPersister.NotFoundException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import point.common.constant.ErrorCode;
import point.common.exception.CustomException;
import point.common.exception.GameException;
import point.common.exception.RefreshTokenException;
import point.common.model.response.ErrorData;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(
            Exception e, Object body, HttpHeaders headers, HttpStatus status, WebRequest request) {
        if (!(body instanceof ErrorData)) {
            body =
                    new ErrorData(
                            ErrorCode.COMMON_ERROR_SYSTEM_ERROR,
                            ErrorCode.COMMON_ERROR_SYSTEM_ERROR.getMessage());
        }

        logException(e);

        return new ResponseEntity<>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex,
            HttpHeaders headers,
            HttpStatus status,
            WebRequest request) {
        ErrorData errorData =
                new ErrorData(
                        ErrorCode.REQUEST_ERROR_PARAMETER_ERROR,
                        ErrorCode.REQUEST_ERROR_PARAMETER_ERROR.getMessage());
        return handleExceptionInternal(ex, errorData, headers, status, request);
    }

    private void logException(Exception e) {
        if (e instanceof CustomException) {
            // CustomExceptionかつdebugMessageがセットされていれば出力する。
            if (((CustomException) e).hasDebugMessage()) {
                log.warn(
                        e.getClass().getName() + ": " + ((CustomException) e).getDebugMessage(), e);
            }
            // CustomExceptionかつdebugが無効ならばスタックトレースを出力しない。
            if (!log.isDebugEnabled()) {
                log.warn("response with errorCode: " + e.getMessage());
                return;
            }
        }
        // スタックトレースを出力する。
        log.warn(e.getClass().getName() + ": " + e.getMessage(), e);
    }

    @ExceptionHandler(value = CustomException.class)
    public ResponseEntity<Object> handleCustomException(CustomException e, WebRequest request) {
        ErrorData errorData;
        if (!CollectionUtils.isEmpty(e.getParams())) {
            errorData = new ErrorData(e.getErrorCode(), e.getParams());
        } else {
            errorData = new ErrorData(e.getErrorCode());
        }
        return handleExceptionInternal(
                e, errorData, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, request);
    }

    @ExceptionHandler(value = GameException.class)
    public ResponseEntity<Object> handleCustomException(GameException e, WebRequest request) {
        ErrorData errorData;
        if (!CollectionUtils.isEmpty(e.getParams())) {
            errorData = new ErrorData(e.getErrorCode(), e.getParams());
        } else {
            errorData = new ErrorData(e.getErrorCode(), e.getErrorMessage());
        }
        return handleExceptionInternal(
                e, errorData, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, request);
    }

    @ExceptionHandler(value = RefreshTokenException.class)
    public ResponseEntity<Object> handleRefreshTokenException(
            RefreshTokenException e, WebRequest request) {
        return handleExceptionInternal(
                e,
                new ErrorData(e.getErrorCode()),
                new HttpHeaders(),
                HttpStatus.UNAUTHORIZED,
                request);
    }

    @ExceptionHandler(value = NotFoundException.class)
    public ResponseEntity<Object> handleNotFoundException(NotFoundException e, WebRequest request) {
        return handleExceptionInternal(
                e,
                new ErrorData(ErrorCode.COMMON_ERROR_NOT_FOUND),
                new HttpHeaders(),
                HttpStatus.NOT_FOUND,
                request);
    }

    @ExceptionHandler(value = MaxUploadSizeExceededException.class)
    public ResponseEntity<Object> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException e, WebRequest request) {
        return handleExceptionInternal(
                e,
                new ErrorData(ErrorCode.REQUEST_ERROR_EXCEED_MAXIMUM_UPLOAD_SIZE),
                new HttpHeaders(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                request);
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<Object> handleOtherException(Exception e, WebRequest request) {
        return handleExceptionInternal(
                e,
                new ErrorData(
                        ErrorCode.COMMON_ERROR_SYSTEM_ERROR,
                        ErrorCode.COMMON_ERROR_SYSTEM_ERROR.getMessage()),
                new HttpHeaders(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                request);
    }
}
