package point.common.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.AuthenticationException;
import point.common.constant.ErrorCode;

@Setter
@Getter
public class PontaAuthenticationException extends AuthenticationException {

    private ErrorCode errorCode;

    /**
     * NOTE: Please ensure the errorCode is defined in the messages.properties
     *
     * @param errorCode ErrorCode
     */
    public PontaAuthenticationException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public PontaAuthenticationException(ErrorCode errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
    }

    public PontaAuthenticationException(ErrorCode errorCode, String errorMsg, Throwable cause) {
        super(errorMsg, cause);
        this.errorCode = errorCode;
    }

    public PontaAuthenticationException(String errorMsg, Throwable cause) {
        super(errorMsg, cause);
    }
}
