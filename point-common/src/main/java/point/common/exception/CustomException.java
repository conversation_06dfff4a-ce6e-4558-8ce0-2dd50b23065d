package point.common.exception;

import java.util.List;
import lombok.Getter;
import point.common.constant.ErrorCode;

public class CustomException extends Exception {

    private static final long serialVersionUID = 63997398575474269L;
    protected String debugMessage;
    @Getter protected List<Object> params;
    @Getter private ErrorCode errorCode;

    public CustomException(ErrorCode errorCode, List<Object> params) {
        super();
        this.errorCode = errorCode;
        this.params = params;
    }

    public CustomException(ErrorCode errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public CustomException(ErrorCode errorCode, String message) {
        super(message);
        this.debugMessage = message;
        this.errorCode = errorCode;
    }

    public CustomException(ErrorCode errorCode, Exception e) {
        super(e);
        this.errorCode = errorCode;
    }

    public CustomException(Exception e) {
        super(e);
        this.errorCode =
                e instanceof CustomException
                        ? ((CustomException) e).getErrorCode()
                        : ErrorCode.COMMON_ERROR_SYSTEM_ERROR;
    }

    public String getMessage() {
        return errorCode.toString() + "(" + errorCode.getCode() + ")";
    }

    public String getDebugMessage() {
        return this.getMessage() + " debugMessage: " + this.debugMessage;
    }

    public boolean hasDebugMessage() {
        return this.debugMessage != null && !this.debugMessage.isBlank();
    }
}
