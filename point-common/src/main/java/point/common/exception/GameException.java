package point.common.exception;

import lombok.Getter;
import point.common.constant.ErrorCode;

public class GameException extends CustomException {

    private static final long serialVersionUID = -3319948512678274712L;
    @Getter private String errorMessage;

    public GameException(ErrorCode errorCode, String errorMessage) {
        super(errorCode);
        this.errorMessage = errorMessage;
    }
}
