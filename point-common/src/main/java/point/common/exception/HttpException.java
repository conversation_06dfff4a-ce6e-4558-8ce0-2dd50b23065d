package point.common.exception;

import lombok.Getter;
import point.common.constant.ErrorCode;

public class HttpException extends CustomException {

    private static final long serialVersionUID = -3319948512678274712L;
    @Getter private Integer statusCode = null;

    public HttpException(Exception e) {
        super(e);
    }

    public HttpException(ErrorCode errorCode) {
        super(errorCode);
    }

    public HttpException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public HttpException(ErrorCode errorCode, String message, Integer statusCode) {
        super(errorCode, message);
        this.statusCode = statusCode;
    }

    public HttpException(ErrorCode errorCode, Exception e) {
        super(errorCode, e);
    }
}
