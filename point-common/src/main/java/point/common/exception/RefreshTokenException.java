package point.common.exception;

import lombok.Getter;
import point.common.constant.ErrorCode;

public class RefreshTokenException extends Exception {

    protected String debugMessage;

    @Getter private ErrorCode errorCode;

    public RefreshTokenException(ErrorCode errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public RefreshTokenException(ErrorCode errorCode, String message) {
        super(message);
        this.debugMessage = message;
        this.errorCode = errorCode;
    }

    public RefreshTokenException(ErrorCode errorCode, Exception e) {
        super(e);
        this.errorCode = errorCode;
    }

    public RefreshTokenException(Exception e) {
        super(e);
        this.errorCode =
                e instanceof RefreshTokenException
                        ? ((RefreshTokenException) e).getErrorCode()
                        : ErrorCode.COMMON_ERROR_SYSTEM_ERROR;
    }

    public String getMessage() {
        return errorCode.toString() + "(" + errorCode.getCode() + ")";
    }

    public String getDebugMessage() {
        return this.getMessage() + " debugMessage: " + this.debugMessage;
    }

    public boolean hasDebugMessage() {
        return this.debugMessage != null && !this.debugMessage.isBlank();
    }
}
