package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.UserMailNoticesOff;

public interface UserMailNoticesOffRepository
        extends JpaRepository<UserMailNoticesOff, Long>,
                JpaSpecificationExecutor<UserMailNoticesOff> {

    List<UserMailNoticesOff> findByUserId(Long userId);
}
