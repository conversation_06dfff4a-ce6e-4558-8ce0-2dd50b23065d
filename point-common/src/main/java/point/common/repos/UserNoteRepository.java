package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.constant.UserNoteTypeEnum;
import point.common.entity.UserNote;

public interface UserNoteRepository
        extends JpaRepository<UserNote, Long>, JpaSpecificationExecutor<UserNote> {

    UserNote findTopByUserIdAndNoteTypeOrderByCreatedAtDesc(
            Long userId, UserNoteTypeEnum userNoteTypeEnum);

    List<UserNote> findByUserId(Long userId);
}
