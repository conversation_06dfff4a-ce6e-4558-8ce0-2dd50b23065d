package point.common.repos;

import java.util.Date;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.constant.ChoiceVoteResult;
import point.common.entity.ChoiceVote;
import point.common.model.response.ChoiceRewardHistoryData;

public interface ChoiceVoteRepository
        extends JpaRepository<ChoiceVote, Long>, JpaSpecificationExecutor<ChoiceVote> {
    @Modifying
    @Query(
            "UPDATE ChoiceVote u SET u.voteResult = :result, u.updatedAt = now() WHERE u.activityId = :activityId and u.voteDirection=:direction and u.id in :ids")
    int updateVoteResult(
            @Param("result") ChoiceVoteResult result,
            @Param("activityId") Long activityId,
            @Param("direction") ChoiceActivityVoteResult direction,
            @Param("ids") List<Long> voteIds);

    @Modifying
    @Query(
            "UPDATE ChoiceVote u SET u.voteResult = :result, u.updatedAt = now() WHERE u.activityId = :activityId and u.id in :ids")
    int updateVoteAllWin(
            @Param("result") ChoiceVoteResult result,
            @Param("activityId") Long activityId,
            @Param("ids") List<Long> voteIds);

    @Query(
            value =
                    """
                        select new point.common.model.response.ChoiceRewardHistoryData(cr.id, cv.userId, cv.voteResult, cr.amount, cv.voteTime, cv.voteDirection, cr.withdrawStatusType,cr.remainingAmount)
                        from ChoiceVote cv left join ChoiceReward cr on cv.userId = cr.userId and cv.activityId = cr.activityId
                        where cv.userId in (:userId)  AND cv.voteTime BETWEEN :startDate AND :endDate
            """)
    List<ChoiceRewardHistoryData> getUserChoiceRewardHistory(
            @Param("userId") List<Long> userIds,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    @Query(
            nativeQuery = true,
            value =
                    """
                      SELECT

                            CASE
                                WHEN p.user_id IS NOT NULL THEN u.email
                                WHEN p.user_id IS NULL AND p.id IS NOT NULL THEN NULL
                                ELSE u2.email
                                END AS email
                      FROM
                      choice_vote b
                      LEFT JOIN
                      point_user p ON b.user_id = p.id
                      LEFT JOIN
                      user u ON p.user_id = u.id
                      LEFT JOIN
                      user u2 ON b.user_id = u2.id
                      WHERE
                      b.id = :voteId
                  """)
    String getWinVoteUserEmail(@Param("voteId") Long voteId);

    ChoiceVote findByActivityIdAndUserIdIn(Long activityId, List<Long> userId);

    List<ChoiceVote> findByActivityId(Long activityId);

    Long countChoiceVoteByActivityId(Long activityId);

    Page<ChoiceVote> findByActivityIdOrderByIdAsc(Long activityId, Pageable pageable);

    Page<ChoiceVote> findByActivityIdAndIdGreaterThanOrderByIdAsc(
            Long activityId, Long id, Pageable pageable);
}
