package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import point.common.entity.OnetimeBankAccount;

public interface OneTimeBankAccountServiceRepository
        extends JpaRepository<OnetimeBankAccount, Long> {

    @Query(
            nativeQuery = true,
            value =
                    "select id from user where kyc_status in ('ACCOUNT_OPENING_DONE','DONE')  and id not in (select user_id from onetime_bank_account);")
    List<Long> findMigrationUser();
}
