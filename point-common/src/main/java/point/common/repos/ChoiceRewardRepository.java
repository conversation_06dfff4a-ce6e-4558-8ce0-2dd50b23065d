package point.common.repos;

import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.constant.WithdrawStatusType;
import point.common.entity.ChoiceReward;

public interface ChoiceRewardRepository
        extends JpaRepository<ChoiceReward, Long>, JpaSpecificationExecutor<ChoiceReward> {
    List<ChoiceReward> findChoiceRewardByExpiryTimeLessThanAndWithdrawStatusTypeIn(
            Date date, List<WithdrawStatusType> statusList);

    List<ChoiceReward> findChoiceRewardByUserIdInAndExpiryTimeLessThanAndWithdrawStatusTypeIn(
            List<Long> userId, Date date, List<WithdrawStatusType> statusList);

    ChoiceReward findByActivityIdAndUserIdIn(Long activityId, List<Long> userId);
}
