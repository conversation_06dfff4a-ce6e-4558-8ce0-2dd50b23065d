package point.common.repos;

import java.util.Date;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.QuizQuestionPublishedRecord;

public interface QuizQuestionPublishedRecordRepository
        extends JpaRepository<QuizQuestionPublishedRecord, Long>,
                JpaSpecificationExecutor<QuizQuestionPublishedRecord> {

    QuizQuestionPublishedRecord findByPublishedDate(Date publishedDate);
}
