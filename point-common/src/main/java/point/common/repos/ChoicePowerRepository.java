package point.common.repos;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import point.common.entity.ChoicePower;

public interface ChoicePowerRepository
        extends JpaRepository<ChoicePower, Long>, JpaSpecificationExecutor<ChoicePower> {

    @Query(
            value =
                    """
            SELECT cp
            FROM ChoicePower cp
            WHERE cp.id = (SELECT choicePowerId FROM ChoicePowerUserRel WHERE userId = :userId)
            """)
    Optional<ChoicePower> findByUserId(@Param("userId") Long userId);

    @Query(
            value =
                    """
            SELECT cp
            FROM ChoicePower cp
            WHERE cp.id in (SELECT choicePowerId FROM ChoicePowerUserRel WHERE userId in (:userIds))
            """)
    List<ChoicePower> findByUserIds(@Param("userIds") Set<Long> userId);
}
