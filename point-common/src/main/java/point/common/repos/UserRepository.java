package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import point.common.constant.KycStatus;
import point.common.entity.User;

public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    List<User> findByKycStatusAndUserInfoIdNotNull(KycStatus kycStatus);

    List<User> findByKycStatusInOrderByIdDesc(List<KycStatus> status);

    List<User> findByIdIn(List<Long> id);

    @Query(
            """

                    SELECT COALESCE(u.email, pu.user.email)
                    FROM User u
                    LEFT JOIN PointUser pu ON u.id = pu.user.id
                    WHERE u.id = :userId OR pu.id = :userId
        """)
    String findUserEmail(@Param("userId") Long userId);
}
