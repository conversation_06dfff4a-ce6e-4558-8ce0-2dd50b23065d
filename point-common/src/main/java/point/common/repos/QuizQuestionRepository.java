package point.common.repos;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.QuizQuestion;

public interface QuizQuestionRepository
        extends JpaRepository<QuizQuestion, Long>, JpaSpecificationExecutor<QuizQuestion> {

    boolean existsByQuizNum(String quizNum);

    boolean existsByTitle(String title);
}
