package point.common.repos;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.QuizUserAnswer;

public interface QuizUserAnswerRepository
        extends JpaRepository<QuizUserAnswer, Long>, JpaSpecificationExecutor<QuizUserAnswer> {
    Optional<QuizUserAnswer> findByAnswerDateAndUserId(Date answerDate, Long userId);

    List<QuizUserAnswer> findByAnswerDateAndUserIdIn(Date answerDate, List<Long> userId);

    List<QuizUserAnswer> findByAnswerDateAndUserIdInAndQuizId(
            Date answerDate, List<Long> userId, Long quizId);
}
