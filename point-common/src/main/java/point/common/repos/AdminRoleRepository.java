package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.AdminRole;

public interface AdminRoleRepository
        extends JpaRepository<AdminRole, Long>, JpaSpecificationExecutor<AdminRole> {
    List<AdminRole> findAllByEnableTrueOrderByIdAsc();

    AdminRole findByRoleName(String name);
}
