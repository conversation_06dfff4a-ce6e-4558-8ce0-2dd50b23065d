package point.common.repos;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.AppConfiguration;

public interface AppConfigurationRepository
        extends JpaRepository<AppConfiguration, Long>, JpaSpecificationExecutor<AppConfiguration> {
    AppConfiguration findFirstByKey(String key);
}
