package point.common.repos;

import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.UserEkyc;

public interface UserEkycRepository
        extends JpaRepository<UserEkyc, Long>, JpaSpecificationExecutor<UserEkyc> {

    List<UserEkyc> findTopByStatusInOrderByIdDesc(List<String> status);

    List<UserEkyc> findTopByUserIdInOrderByIdDesc(List<Long> userIdList);

    UserEkyc findTopByUserIdOrderByIdDesc(Long userId);

    Boolean existsByUserIdAndCreatedAtGreaterThanEqualAndUrlNotNullAndApplicantIdNotNull(
            Long userId, Date createdAt);

    UserEkyc findTopByUserIdAndCreatedAtGreaterThanEqualAndUrlNotNullAndApplicantIdNotNull(
            Long userId, Date createdAt);
}
