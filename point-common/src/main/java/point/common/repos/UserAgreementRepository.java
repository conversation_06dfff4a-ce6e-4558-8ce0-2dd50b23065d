package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import point.common.entity.UserAgreement;
import point.common.model.dto.UserAgreementDTO;

public interface UserAgreementRepository
        extends JpaRepository<UserAgreement, Long>, JpaSpecificationExecutor<UserAgreement> {

    @Query(
            value =
                    "select new point.common.model.dto.UserAgreementDTO(updatedAt, userAgreementType) from UserAgreement where enabled = true and userId = :userId")
    List<UserAgreementDTO> findByUserId(@Param("userId") Long userId);
}
