package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.entity.ZipCode;

public interface ZipCodeRepository
        extends JpaRepository<ZipCode, Long>, JpaSpecificationExecutor<ZipCode> {

    ZipCode findByZipCode(String zipCode);

    List<ZipCode> findByZipCodeOrderById(String zipCode);
}
