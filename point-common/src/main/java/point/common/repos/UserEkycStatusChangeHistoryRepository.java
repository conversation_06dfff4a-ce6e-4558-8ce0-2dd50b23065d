package point.common.repos;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import point.common.constant.KycStatus;
import point.common.entity.UserEkycStatusChangeHistory;

public interface UserEkycStatusChangeHistoryRepository
        extends JpaRepository<UserEkycStatusChangeHistory, Long>,
                JpaSpecificationExecutor<UserEkycStatusChangeHistory> {

    List<UserEkycStatusChangeHistory> findByUserIdAndApplicantIdAndAfterStatus(
            Long userId, Long applicantId, KycStatus kycStatus);
}
