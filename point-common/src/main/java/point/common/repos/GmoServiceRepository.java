package point.common.repos;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import point.common.entity.GmoDeposit;

public interface GmoServiceRepository extends JpaRepository<GmoDeposit, Long> {

    @Query(nativeQuery = true, value = "select max(item_key)  from gmo_deposit  limit 1")
    String gmoLatestItemKey();

    GmoDeposit findByFiatDepositId(Long id);
}
