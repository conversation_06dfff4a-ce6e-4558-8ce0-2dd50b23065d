package point.common.ponta;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import point.common.config.PontaConfig;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;

public final class PontaRedirectUtil {

    private PontaRedirectUtil() {}

    public static String getSuccessUri(PontaConfig pontaConfig, String client) {
        if (StringUtils.hasText(client) && CommonConstants.GAME.equalsIgnoreCase(client.trim())) {
            return pontaConfig.getGameLoginSuccessUrl();
        }
        return pontaConfig.getLoginSuccessUrl();
    }

    public static String getFailureUri(
            PontaConfig pontaConfig, String client, String partnerNumber) {
        String baseUrl =
                isGameClient(client)
                        ? pontaConfig.getGameLoginFailureUrl()
                        : pontaConfig.getLoginFailureUrl();

        return StringUtils.hasLength(partnerNumber)
                ? appendPartnerNumber(baseUrl, partnerNumber)
                : baseUrl;
    }

    public static String getFailureUriWithError(
            PontaConfig pontaConfig, String client, ErrorCode errorCode) {
        return UriComponentsBuilder.fromUriString(getFailureUri(pontaConfig, client, null))
                .queryParam(CommonConstants.ERROR, errorCode.getCode())
                .queryParam(
                        CommonConstants.ERROR_MESSAGE,
                        URLEncoder.encode(errorCode.getMessage(), StandardCharsets.UTF_8))
                .build()
                .toUriString();
    }

    private static boolean isGameClient(String client) {
        return StringUtils.hasText(client) && CommonConstants.GAME.equalsIgnoreCase(client.trim());
    }

    private static String appendPartnerNumber(String baseUrl, String partnerNumber) {
        return UriComponentsBuilder.fromUriString(baseUrl)
                .queryParam(CommonConstants.PONTA_PARTNER_NUMBER, partnerNumber)
                .build()
                .toUriString();
    }
}
