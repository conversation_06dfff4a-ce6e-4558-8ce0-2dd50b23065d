package point.common.ponta;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TradeNumberGenerator {

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String COUNTER_KEY = "trade_number_counter";

    public String generateTradeNumber() {
        Long number = redisTemplate.opsForValue().increment(COUNTER_KEY);
        return String.format("%08d", number);
    }
}
