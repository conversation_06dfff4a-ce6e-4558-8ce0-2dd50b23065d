package point.common.ponta;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.net.ssl.SSLContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import point.common.config.PontaConfig;
import point.common.constant.ErrorCode;
import point.common.entity.PontaConvertWorker;
import point.common.exception.GameException;
import point.common.model.request.PontaForm;
import point.common.util.EnvironmentUtil;
import point.common.util.JsonUtil;

@Slf4j
@Component
public class PontaBizInVokerApi {

    private static final String SUCCESS_CODE_VALUE = "N000000000";
    private static final String RESPONSE_CODE_KEY = "ST0274_tl";
    private static final String ERROR_CODE_KEY = "E000000000";
    private static final Integer CONNECT_TIME_OUT = 1500;

    private final XmlMapper xmlMapper = new XmlMapper();

    private final PontaConfig pontaConfig;
    private final RestTemplate restTemplate;

    public PontaBizInVokerApi(
            Environment environment,
            PontaConfig pontaConfig,
            ApplicationEventPublisher eventPublisher) {
        this.pontaConfig = pontaConfig;
        String activeProfile = EnvironmentUtil.getActiveProfileOrDefault(environment);
        try {
            String password =
                    new String(
                            pontaConfig.getKeystoreBizPassword().getBytes(StandardCharsets.UTF_8),
                            StandardCharsets.UTF_8);
            KeyStore keyStore = KeyStore.getInstance("JKS");

            String formattedKeyStoreName =
                    String.format("ponta/ponta-keystore-%s.jks", activeProfile);
            log.info("Loading keystore: {}", formattedKeyStoreName);
            ClassPathResource resource = new ClassPathResource(formattedKeyStoreName);
            keyStore.load(resource.getInputStream(), password.toCharArray());

            SSLContext sslContext =
                    SSLContextBuilder.create()
                            .loadKeyMaterial(keyStore, password.toCharArray())
                            .build();

            SSLConnectionSocketFactory sslConnectionSocketFactory =
                    new SSLConnectionSocketFactory(sslContext);

            CloseableHttpClient httpClient =
                    HttpClients.custom().setSSLSocketFactory(sslConnectionSocketFactory).build();

            HttpComponentsClientHttpRequestFactory requestFactory =
                    new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);

            requestFactory.setConnectTimeout(CONNECT_TIME_OUT);
            requestFactory.setReadTimeout(CONNECT_TIME_OUT);

            this.restTemplate = new RestTemplate(requestFactory);

            List<HttpMessageConverter<?>> messageConverters =
                    new ArrayList<>(this.restTemplate.getMessageConverters());
            messageConverters.add(new MappingJackson2XmlHttpMessageConverter());
            this.restTemplate.setMessageConverters(messageConverters);

        } catch (UnrecoverableKeyException
                | CertificateException
                | NoSuchAlgorithmException
                | KeyManagementException
                | KeyStoreException
                | IOException e) {
            log.error("Failed to create RestTemplate with custom SSL configuration", e);
            throw new RuntimeException(e);
        }
    }

    public Map<String, String> if001(String memberId, String CC0281_TL) throws GameException {
        // build body
        RequestBodyUtil.Rpc requestBody = RequestBodyUtil.if001(memberId, CC0281_TL);
        String paramsXml = RequestBodyUtil.Rpc.rpcTo31Xml(requestBody);
        String encodedData = "DATA=" + URLEncoder.encode(paramsXml, StandardCharsets.UTF_8);
        // set header
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "webif/1.00 IF-001");
        headers.setContentLength(encodedData.getBytes().length);
        headers.setConnection("close");
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // build entity
        HttpEntity<String> requestEntity = new HttpEntity<>(encodedData, headers);

        // send request
        String response;
        try {
            log.info("memberId: {}, requestBody: {}", memberId, encodedData);
            response =
                    restTemplate.postForObject(
                            pontaConfig.getWebIfUrl(), requestEntity, String.class);
            log.info("if001 response:{}", response);
        } catch (Exception e) {
            log.error(
                    "Failed to send request to Ponta for get ポイント照会, errorMessage: {}",
                    e.getMessage(),
                    e);
            throw new GameException(
                    ErrorCode.PONTA_REQUEST_WEB_IF_001_ERROR,
                    ErrorCode.PONTA_REQUEST_WEB_IF_001_ERROR.getMessage());
        }

        try {
            PontaResponseUtil.Rpc rpc = xmlMapper.readValue(response, PontaResponseUtil.Rpc.class);
            Map<String, String> responseMap = rpc.getParamsAsMap();
            log.info("if002 responseMap: {}", JsonUtil.encode(responseMap));
            return responseMap;
        } catch (Exception e) {
            log.error(
                    "Failed to decode xml that ponta returned, response: {}, errorMessage: {}",
                    response,
                    e.getMessage(),
                    e);
            throw new GameException(
                    ErrorCode.PONTA_REQUEST_WEB_IF_001_ERROR,
                    ErrorCode.PONTA_REQUEST_WEB_IF_001_ERROR.getMessage());
        }
    }

    public Map<String, String> if003(PontaForm pontaForm) throws GameException {
        log.info("pontaForm: {}", JsonUtil.encode(pontaForm));
        // build body
        RequestBodyUtil.Rpc requestBody = RequestBodyUtil.if003(pontaForm);
        String paramsXml = RequestBodyUtil.Rpc.rpcTo31Xml(requestBody);
        String encodedData = "DATA=" + URLEncoder.encode(paramsXml, StandardCharsets.UTF_8);
        // set header
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "webif/1.00 IF-003");
        headers.setContentLength(encodedData.getBytes().length);
        headers.setConnection("close");
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // build entity
        HttpEntity<String> requestEntity = new HttpEntity<>(encodedData, headers);

        // send request
        String response;
        try {
            log.info("pointAmount: {}, requestBody: {}", pontaForm.getPointAmount(), encodedData);
            response =
                    restTemplate.postForObject(
                            pontaConfig.getWebIfUrl(), requestEntity, String.class);
            log.info("success if004 response:{}", response);
        } catch (Exception e) {
            log.warn(
                    "Failed to send request to Ponta for get memberId, errorMessage: {}",
                    e.getMessage(),
                    e);
            Map<String, String> responseMap = new HashMap<>();
            responseMap.put(ERROR_CODE_KEY, encodedData);
            return responseMap;
        }

        try {
            PontaResponseUtil.Rpc rpc = xmlMapper.readValue(response, PontaResponseUtil.Rpc.class);
            Map<String, String> responseMap = rpc.getParamsAsMap();
            log.info("success if004 responseMap: {}", JsonUtil.encode(responseMap));
            return responseMap;
        } catch (Exception e) {
            log.error(
                    "Failed to decode xml that ponta returned, response: {}, errorMessage: {}",
                    response,
                    e.getMessage(),
                    e);
            throw new GameException(
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR,
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR.getMessage());
        }
    }

    public static boolean isSuccess(Map<String, String> responseMap) {
        if (CollectionUtils.isEmpty(responseMap)) {
            return true;
        }
        return !SUCCESS_CODE_VALUE.equals(responseMap.get(RESPONSE_CODE_KEY));
    }

    public static String extractErrorPrefix(String errorCode) {
        if (errorCode != null && errorCode.length() >= 4) {
            return "(" + errorCode.substring(0, 4) + ")";
        }
        return "(UNKN)";
    }

    public Map<String, String> reSendif003(PontaConvertWorker pontaForm) throws GameException {
        log.info("PontaConvertWorker: {}", JsonUtil.encode(pontaForm));
        // build body
        String encodedData = pontaForm.getContents();
        // set header
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "webif/1.00 IF-003");
        headers.setContentLength(encodedData.getBytes().length);
        headers.setConnection("close");
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // build entity
        HttpEntity<String> requestEntity = new HttpEntity<>(encodedData, headers);

        // send request
        String response;
        try {
            log.info(
                    "PontaConvertWorker ponta worker sync userId: {}, tradeNumber:{},requestBody: {}",
                    pontaForm.getUserId(),
                    pontaForm.getTradeNumber(),
                    encodedData);
            response =
                    restTemplate.postForObject(
                            pontaConfig.getWebIfUrl(), requestEntity, String.class);
            log.info("PontaConvertWorker if004 response:{}", response);
        } catch (Exception e) {
            log.error(
                    "PontaConvertWorker Failed to send request to Ponta for get memberId, errorMessage: {}",
                    e.getMessage(),
                    e);
            throw new GameException(
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR,
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR.getMessage());
        }

        try {
            PontaResponseUtil.Rpc rpc = xmlMapper.readValue(response, PontaResponseUtil.Rpc.class);
            Map<String, String> responseMap = rpc.getParamsAsMap();
            log.info("PontaConvertWorker if004 responseMap: {}", JsonUtil.encode(responseMap));
            return responseMap;
        } catch (Exception e) {
            log.error(
                    "PontaConvertWorker Failed to decode xml that ponta returned, response: {}, errorMessage: {}",
                    response,
                    e.getMessage(),
                    e);
            throw new GameException(
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR,
                    ErrorCode.PONTA_REQUEST_WEB_IF_003_ERROR.getMessage());
        }
    }
}
