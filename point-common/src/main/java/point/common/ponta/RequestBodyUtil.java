package point.common.ponta;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.*;
import point.common.model.request.PontaForm;
import point.common.util.DateUnit;

public class RequestBodyUtil {

    // get accessToken api
    private static final int IF_1507 = 1507;
    // get memberId api
    private static final int IF_1511 = 1511;
    // get ポイント照会（上り）
    private static final String IF_0001 = "0001";
    // get ポイント付与利用（上り）
    private static final String IF_0003 = "0003";
    // default version that fixed value
    private static final String VERSION = "1.0";
    // 取引先コード
    private static final String CC0281_TL = "106240";
    private static final String CH0103_TL = "12";
    private static final String SC0291_TL_0001 = "100000000005";
    private static final String SC0291_TL_0003 = "100000000001";
    private static final String ALL0 = "0";

    public static Rpc if1507(String credentials, String secretCode) {
        Rpc rpc = Rpc.builder().id(String.valueOf(IF_1507)).version(VERSION).build(); // ID バージョン

        // current date
        String yyyyMMdd = DateUnit.getYYMMDDString(LocalDate.now());
        String hhMMSS = DateUnit.getHHMMSSString(LocalDateTime.now());

        rpc.addParam(
                Param.builder()
                        .key("ID0001_tl")
                        .type("string")
                        .value("1507") // fix: 1507 ID
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("VE0006_tl")
                        .type("string")
                        .value("001") // fix: 001
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("YD0286_tl")
                        .type("string")
                        .value(yyyyMMdd) // YYYYMMDD 取引先営業日
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("OD0293_tl")
                        .type("string")
                        .value(yyyyMMdd) // YYYYMMDD 取引先発生年月日
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("OT0292_tl")
                        .type("string")
                        .value(hhMMSS) // HH24MISS 取引先発生時刻
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("CH0103_tl")
                        .type("string")
                        .value(CH0103_TL) // 12:web
                        .build());
        rpc.addParam(Param.builder().key("CC0281_tl").type("string").value(CC0281_TL).build());
        rpc.addParam(Param.builder().key("BC0290_tl").type("string").value("000000").build());
        rpc.addParam(Param.builder().key("SC0291_tl").type("string").value("000000000000").build());
        rpc.addParam(
                Param.builder().key("PO0280_tl").type("string").value("0000000000000").build());
        rpc.addParam(
                Param.builder()
                        .key("SI0005_tl")
                        .type("string")
                        .value(credentials) // サイト認証情報
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("WE0007_tl")
                        .type("string")
                        .value(secretCode) // WEB認証情報
                        .build());

        return rpc;
    }

    public static Rpc if1511(String credentials, String accessToken) {
        Rpc rpc = Rpc.builder().id(String.valueOf(IF_1511)).version(VERSION).build();

        // current date
        String yyyyMMdd = DateUnit.getYYMMDDString(LocalDate.now());
        String hhMMSS = DateUnit.getHHMMSSString(LocalDateTime.now());

        rpc.addParam(
                Param.builder()
                        .key("ID0001_tl")
                        .type("string")
                        .value("1511") // fix: 1511
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("VE0006_tl")
                        .type("string")
                        .value("001") // fix: 001
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("YD0286_tl")
                        .type("string")
                        .value(yyyyMMdd) // YYYYMMDD
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("OD0293_tl")
                        .type("string")
                        .value(yyyyMMdd) // YYYYMMDD
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("OT0292_tl")
                        .type("string")
                        .value(hhMMSS) // HH24MISS
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("CH0103_tl")
                        .type("string")
                        .value(CH0103_TL) // 12:web
                        .build());
        rpc.addParam(Param.builder().key("CC0281_tl").type("string").value(CC0281_TL).build());
        rpc.addParam(Param.builder().key("BC0290_tl").type("string").value("000000").build());
        rpc.addParam(Param.builder().key("SC0291_tl").type("string").value("000000000000").build());
        rpc.addParam(
                Param.builder().key("PO0280_tl").type("string").value("0000000000000").build());
        rpc.addParam(
                Param.builder()
                        .key("SI0005_tl")
                        .type("string")
                        .value(credentials) // サイト認証情報
                        .build());
        rpc.addParam(
                Param.builder()
                        .key("AT0010_tl")
                        .type("string")
                        .value(accessToken) // アクセストークン
                        .build());
        return rpc;
    }

    public static Rpc if001(String memberId, String CC0281TL) {
        Rpc rpc = Rpc.builder().id(IF_0001).version(VERSION).build();
        // current date
        String yyyyMMdd = DateUnit.getYYMMDDString(LocalDate.now());
        String hhMMSS = DateUnit.getHHMMSSString(LocalDateTime.now());

        // ID fix: "0001"
        addParam(rpc, "ID0011_tl", IF_0001);

        // バージョン インターフェースバージョン ※初期値:001
        addParam(rpc, "VE0107_tl", "001");

        // ポイント取引内容の区分 010:照会
        addParam(rpc, "DK0124_tl", "010");

        // ポイント変動区分 ポイント取引に対する変動内容 011:ポイント照会
        addParam(rpc, "HK0146_tl", "011");

        // 取引先営業日 YYYYMMDD 取引先の営業日付
        addParam(rpc, "YD0286_tl", yyyyMMdd);

        // 取引先発生年月日 YYYYMMDD データ作成日付（コンピュータ日付）をセット
        addParam(rpc, "OD0293_tl", yyyyMMdd);

        // 取引先発生時刻 HH24MISS データ作成時刻（コンピュータ時刻）をセット
        addParam(rpc, "OT0292_tl", hhMMSS);

        // チャネル区分 取引対象チャネルをセット 02:WEB（PC)　03:WEB（携帯）04:センター　06:キオスク端末
        addParam(rpc, "CH0103_tl", CH0103_TL);

        // 取引先コード 共通Ｐ内でユニークな取引先コード（共通Pが払い出した取引先コード）
        addParam(rpc, "CC0281_tl", CC0281TL);

        // 取引先支部コード 取引先内でユニークな取引先の支部コード
        addParam(rpc, "BC0290_tl", "000000");

        // 取引先店舗コード 取引先内でユニークな取引先の店舗コード
        addParam(rpc, "SC0291_tl", SC0291_TL_0001);

        // 取引先POSNO/端末NO
        addParam(rpc, "PO0280_tl", "0000000000000");

        // 取引番号
        addParam(rpc, "DN0297_tl", "00000000");

        // 会員ID 共通P内でユニークな共通会員ID（提携社ローカル会員IDとは異なる）※会員カードのバーコードNO ※疎通確認用の会員ID”00000888888886888800”
        addParam(rpc, "MI0177_tl", memberId);

        return rpc;
    }

    public static Rpc if003(PontaForm pontaForm) {
        Rpc rpc = Rpc.builder().id(IF_0003).version(VERSION).build();

        // current date
        String yyyyMMdd = DateUnit.getYYMMDDString(LocalDate.now());
        String hhMMSS = DateUnit.getHHMMSSString(LocalDateTime.now());

        // ID fix: "0001"
        addParam(rpc, "ID0011_tl", IF_0003);

        // バージョン インターフェースバージョン ※初期値:001
        addParam(rpc, "VE0107_tl", "001");

        // ポイント取引区分 ポイント取引内容の区分　030:付与・利用050:ポイント引継
        addParam(rpc, "DK0124_tl", "030");

        // ポイント変動区分 ポイント取引に対する変動内容　031:ポイント付与　032:ポイント利用　033:ポイント付与＋ポイント利用
        // 　051:ポイント引継（随時移行）※グループメンバによる0ポイント利用時は「ポイント利用」を使用の事
        addParam(rpc, "HK0146_tl", pontaForm.getType().getCode());

        // 取引先営業日 YYYYMMDD 取引先の営業日付
        addParam(rpc, "YD0286_tl", yyyyMMdd);

        // 取引先発生年月日 YYYYMMDD データ作成日付（コンピュータ日付）をセット
        addParam(rpc, "OD0293_tl", yyyyMMdd);

        // 取引先発生時刻 HH24MISS データ作成時刻（コンピュータ時刻）をセット
        addParam(rpc, "OT0292_tl", hhMMSS);

        // チャネル区分 取引対象チャネルをセット 02:WEB（PC)　03:WEB（携帯）04:センター　06:キオスク端末
        addParam(rpc, "CH0103_tl", CH0103_TL);

        // 取引先コード 共通Ｐ内でユニークな取引先コード（共通Pが払い出した取引先コード）
        addParam(rpc, "CC0281_tl", pontaForm.getCC0281_TL());

        // 取引先支部コード
        addParam(rpc, "BC0290_tl", "000000");

        // 取引先店舗コード 取引先内でユニークな取引先の店舗コード
        addParam(rpc, "SC0291_tl", SC0291_TL_0003);

        // 取引先POSNO/端末NO
        addParam(rpc, "PO0280_tl", "0000000000000");

        // 取引番号 取引先内でユニークな購買取引を識別する番号
        addParam(rpc, "DN0297_tl", pontaForm.getTradeNumber());

        // 会員ID 共通P内でユニークな共通会員ID（提携社ローカル会員IDとは異なる） ※会員カードのバーコードNO
        addParam(rpc, "MI0177_tl", pontaForm.getMemberId());

        // ポイント種別1 (TY0126_tl): ポイント種別を指定（1:通常）
        addParam(rpc, "TY0126_tl", "0");

        // ポイント色区分1 (PD0133_tl): ポイント色を指定（01:売上、02:来店、03:ボーナス/キャンペーン、05:交換、07:買取、09:アフィリエイト）
        addParam(rpc, "PD0133_tl", "00");

        // ポイント付与符号1 (PS0141_tl): 0:プラス値、1:マイナス値。ポイント引継データのみマイナス値を許可
        addParam(rpc, "PS0141_tl", "0");

        // ポイント付与数1 (NP0138_tl): ポイント付与数をセット。小数点以下不可
        addParam(rpc, "NP0138_tl", "0");

        // ポイント種別2 (TY0127_tl): ポイント種別1省略時、指定不可
        addParam(rpc, "TY0127_tl", "0");

        // ポイント色区分2 (PD0134_tl): ポイント色区分1省略時、指定不可
        addParam(rpc, "PD0134_tl", "00");

        // ポイント付与符号2 (PS0142_tl): 0:プラス値
        addParam(rpc, "PS0142_tl", "0");

        // ポイント付与数2 (NP0139_tl): ポイント付与数1省略時、指定不可
        addParam(rpc, "NP0139_tl", "0");

        // ポイント種別3 (TY0128_tl): ポイント種別1および2省略時、指定不可
        addParam(rpc, "TY0128_tl", "0");

        // ポイント色区分3 (PD0135_tl): ポイント色区分1および2省略時、指定不可
        addParam(rpc, "PD0135_tl", "00");

        // ポイント付与符号3 (PS0143_tl): 0:プラス値
        addParam(rpc, "PS0143_tl", "0");

        // ポイント付与数3 (NP0140_tl): ポイント付与1および2省略時、指定不可
        addParam(rpc, "NP0140_tl", "0");

        // 来店ポイント符号 (PS0457_tl): 0:プラス値
        addParam(rpc, "PS0457_tl", "0");

        // 来店ポイント数 (NP0456_tl): 来店によるポイント付与数。小数点以下不可
        addParam(rpc, "NP0456_tl", "0");

        // 売上点数符号 (TS0428_tl): 0:プラス値
        addParam(rpc, "TS0428_tl", "0");

        // 売上点数 (TC0427_tl): 取引における売上点数。小数点以下不可
        addParam(rpc, "TC0427_tl", "0");

        // 売上金額符号 (KS0424_tl): 0:プラス値
        addParam(rpc, "KS0424_tl", "0");

        // 売上金額 (NK0423_tl): 取引における売上金額。小数点以下不可
        addParam(rpc, "NK0423_tl", "0");

        // 売上対象外金額符号 (KS0426_tl): 0:プラス値
        addParam(rpc, "KS0426_tl", "0");

        // 売上対象外金額 (NK0425_tl): 提携社の売上とならない預かり金額（例:公共料金）
        addParam(rpc, "NK0425_tl", "0");

        // レシート取引番号 (DN0162_tl): レシートNoをセット。取引番号と同値でも可
        addParam(rpc, "DN0162_tl", pontaForm.getTradeNumber());

        // ポイント利用区分 (UK0151_tl): 0:未設定
        addParam(rpc, "UK0151_tl", "1");

        // ポイント利用符号 (US0154_tl): 0:プラス値
        addParam(rpc, "US0154_tl", "0");

        // ポイント利用数 (NP0152_tl): ポイントの利用数。小数点以下不可
        addParam(rpc, "NP0152_tl", pontaForm.getPointAmount());

        // 利用判定日 (JD0471_tl): YYYYMMDD形式
        addParam(rpc, "JD0471_tl", yyyyMMdd);

        // ポイント交換取引先コード (CD0120_tl): 将来用の拡張項目
        addParam(rpc, "CD0120_tl", "");

        // ポイントプレゼント会員ID (MI0117_tl): 将来用の拡張項目
        addParam(rpc, "MI0117_tl", "");

        // クーポンコード1 (QC0050_tl): 将来用の拡張項目
        addParam(rpc, "QC0050_tl", "");

        // クーポン数符号1 (QS0060_tl): 0:プラス値
        addParam(rpc, "QS0060_tl", "0");

        // クーポン数1 (NQ0055_tl): 将来用の拡張項目
        addParam(rpc, "NQ0055_tl", "0");

        // クーポンコード2 (QC0051_tl): クーポンコード1省略時、指定不可
        addParam(rpc, "QC0051_tl", "");

        // クーポン数符号2 (QS0061_tl): 0:プラス値
        addParam(rpc, "QS0061_tl", "0");

        // クーポン数2 (NQ0056_tl): クーポン数1省略時、指定不可
        addParam(rpc, "NQ0056_tl", "0");

        // クーポンコード3 (QC0052_tl): クーポンコード1、2省略時、指定不可
        addParam(rpc, "QC0052_tl", "");

        // クーポン数符号3 (QS0062_tl): 0:プラス値
        addParam(rpc, "QS0062_tl", "0");

        // クーポン数3 (NQ0057_tl): クーポン数1～2省略時、指定不可
        addParam(rpc, "NQ0057_tl", "0");

        // クーポンコード4 (QC0053_tl): クーポンコード1～3省略時、指定不可
        addParam(rpc, "QC0053_tl", "");

        // クーポン数符号4 (QS0063_tl): 0:プラス値
        addParam(rpc, "QS0063_tl", "0");

        // クーポン数4 (NQ0058_tl): クーポン数1～3省略時、指定不可
        addParam(rpc, "NQ0058_tl", "0");

        // クーポンコード5 (QC0054_tl): クーポンコード1～4省略時、指定不可
        addParam(rpc, "QC0054_tl", "");

        // クーポン数符号5 (QS0064_tl): 0:プラス値
        addParam(rpc, "QS0064_tl", "0");

        // クーポン数5 (NQ0059_tl): クーポン数1～4省略時、指定不可
        addParam(rpc, "NQ0059_tl", "0");

        return rpc;
    }

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JacksonXmlRootElement(localName = "rpc")
    public static class Rpc {
        @JacksonXmlProperty(isAttribute = true)
        private String id;

        @JacksonXmlProperty(isAttribute = true)
        private String version;

        @JacksonXmlElementWrapper(useWrapping = false)
        @JacksonXmlProperty(localName = "param")
        private List<Param> params;

        public void addParam(Param param) {
            if (this.params == null) {
                this.params = new ArrayList<>();
            }
            this.params.add(param);
        }

        public static String rpcToXml(Rpc rpc) {
            StringBuilder xml = new StringBuilder();
            xml.append("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\" ?>");
            xml.append("<rpc id=\"")
                    .append(rpc.getId())
                    .append("\" version=\"")
                    .append(rpc.getVersion())
                    .append("\">");
            for (Param param : rpc.getParams()) {
                xml.append("<param key=\"")
                        .append(param.getKey())
                        .append("\" type=\"")
                        .append(param.getType())
                        .append("\" value=\"")
                        .append(param.getValue())
                        .append("\" />");
            }
            xml.append("</rpc>");
            return xml.toString();
        }

        public static String rpcTo31Xml(Rpc rpc) {
            StringBuilder xml = new StringBuilder();
            xml.append("<?xml version=\"1.0\" encoding=\"Windows-31J\" ?>");
            xml.append("<rpc id=\"")
                    .append(rpc.getId())
                    .append("\" version=\"")
                    .append(rpc.getVersion())
                    .append("\">");
            for (Param param : rpc.getParams()) {
                xml.append("<param key=\"")
                        .append(param.getKey())
                        .append("\" type=\"")
                        .append(param.getType())
                        .append("\" value=\"")
                        .append(param.getValue())
                        .append("\" />");
            }
            xml.append("</rpc>");
            return xml.toString();
        }
    }

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Param {
        @JacksonXmlProperty(isAttribute = true)
        private String key;

        @JacksonXmlProperty(isAttribute = true)
        private String type;

        @JacksonXmlProperty(isAttribute = true)
        private String value;
    }

    private static void addParam(Rpc rpc, String key, String value) {
        rpc.addParam(Param.builder().key(key).type("string").value(value).build());
    }
}
