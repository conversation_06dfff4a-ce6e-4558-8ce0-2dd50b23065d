package point.common.ponta;

import org.springframework.stereotype.Component;

@Component
public class PointTransactionBuilder {

    private static final String FILE_ID = "0007"; // ファイルID
    private static final String FILE_ID_VERSION = "001"; // ファイルIDバージョン
    private static final String MODE_DIVISION = "0"; // モード区分
    private static final String RECORD_IDENTIFICATION = "2"; // レコード識別

    private static final String REAL_ID = "0003"; // ID
    private static final String REAL_VERSION = "001"; // バージョン

    private static final String POINT_TRANSACTION_DIVISION = "030"; // ポイント取引区分
    private static final String POINT_CHANGE_DIVISION = "031"; // ポイント変動区分

    private static final String CHANNEL_DIVISION = "12"; // チャネル区分
    private static final String PARTNER_BRANCH_CODE = "000000"; // 取引先支部コード
    private static final String PARTNER_STORE_CODE = "100000000002"; // 取引先店舗コード
    private static final String PARTNER_POS_TERMINAL_NO = "0000000000000"; // 取引先POSNO/端末NO
    // private static final String TRANSACTION_NUMBER = "00010101";  // 取引番号
    private static final String POINT_TYPE_1 = "1"; // ポイント種別1
    private static final String POINT_COLOR_DIVISION_1 = "01"; // ポイント色区分1
    private static final String POINT_GRANT_SIGN_1 = "0"; // ポイント付与符号1
    // private static final String POINT_GRANT_NUMBER_1 = "01000000"; // ポイント付与数1
    private static final String POINT_TYPE_2 = "0"; // ポイント種別2
    private static final String POINT_COLOR_DIVISION_2 = "00"; // ポイント色区分2
    private static final String POINT_GRANT_SIGN_2 = "0"; // ポイント付与符号2
    private static final String POINT_GRANT_NUMBER_2 = "00000000"; // ポイント付与数2
    private static final String POINT_TYPE_3 = "0"; // ポイント種別3
    private static final String POINT_COLOR_DIVISION_3 = "00"; // ポイント色区分3
    private static final String POINT_GRANT_SIGN_3 = "0"; // ポイント付与符号3
    private static final String POINT_GRANT_NUMBER_3 = "00000000"; // ポイント付与数3
    private static final String VISIT_POINT_SIGN = "0"; // 来店ポイント符号
    private static final String VISIT_POINT_NUMBER = "00000000"; // 来店ポイント数
    private static final String SALES_POINT_SIGN = "0"; // 売上点数符号
    private static final String SALES_POINT_NUMBER = "00000"; // 売上点数
    private static final String SALES_AMOUNT_SIGN = "0"; // 売上金額符号
    private static final String SALES_AMOUNT = "00000000"; // 売上金額
    private static final String NON_SALES_AMOUNT_SIGN = "0"; // 売上対象外金額符号
    private static final String NON_SALES_AMOUNT = "00000000"; // 売上対象外金額
    // private static final String RECEIPT_TRANSACTION_NUMBER = "00010101";  // レシート取引番号
    private static final String POINT_USAGE_DIVISION = "0"; // ポイント利用区分
    private static final String POINT_USAGE_SIGN = "0"; // ポイント利用符号
    private static final String POINT_USAGE_NUMBER = "00000000"; // ポイント利用数
    private static final String USAGE_JUDGMENT_DATE =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // 利用判定日
    private static final String POINT_EXCHANGE_PARTNER_CODE =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // ポイント交換取引先コード
    private static final String POINT_PRESENT_MEMBER_ID =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // ポイントプレゼント会員ID
    private static final String PREVIOUS_POINT_TRANSACTION_DIVISION = "000"; // 前ポイント取引区分
    private static final String PREVIOUS_POINT_CHANGE_DIVISION = "000"; // 前ポイント変動区分
    private static final String PREVIOUS_PARTNER_BUSINESS_DATE =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // 前取引先営業日
    private static final String PREVIOUS_TRANSACTION_DATE =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // 前取引発生年月日
    private static final String PREVIOUS_TRANSACTION_TIME =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // 前取引発生時刻
    private static final String PREVIOUS_CHANNEL_DIVISION = "00"; // 前チャネル区分
    private static final String PREVIOUS_PARTNER_CODE = "000000"; // 前取引先コード
    private static final String PREVIOUS_PARTNER_BRANCH_CODE = "000000"; // 前取引先支部コード
    private static final String PREVIOUS_PARTNER_STORE_CODE = "000000000000"; // 前取引先店舗コード
    private static final String PREVIOUS_PARTNER_POS_TERMINAL_NO =
            "0000000000000"; // 前取引先POSNO/端末NO
    private static final String PREVIOUS_TRANSACTION_NUMBER = "00000000"; // 前取引番号
    private static final String PREVIOUS_RECEIPT_TRANSACTION_NUMBER =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // 前レシート取引番号
    private static final String COUPON_CODE =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // クーポンコード
    private static final String COUPON_NUMBER_SIGN = "0"; // クーポン数符号
    private static final String COUPON_NUMBER = "00"; // クーポン数
    private static final String LOCAL_IMPLEMENTATION_FLAG = "0"; // ローカル実施FLG
    private static final String BATCH_GRANT_REASON =
            "\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000"; // 一括付与事由
    private static final String FILLER =
            "\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020\\u0020"; // FILLER
    private static final String LINE_BREAK_CODE = "\n"; // 改行コード

    public static String buildTransactionString(
            String yyyyMMdd,
            String hhMMss,
            String fromCustCode,
            String memberId,
            String tradeNumber,
            String amount) {
        StringBuilder stringBuilder = new StringBuilder();

        /** ファイル 共通 ヘッダ */
        appendFileCommonHeader(stringBuilder);

        /** リアル 共通 ヘッダ */
        appendRealCommonHeader(stringBuilder);

        /** リクエスト 処理 モード */
        appendRequestProcessingMode(stringBuilder);

        /** リクエスト データ エリア */
        appendRequestDataArea(
                stringBuilder, yyyyMMdd, hhMMss, fromCustCode, memberId, tradeNumber, amount);

        return stringBuilder.toString();
    }

    // 拼接文件共通头部分
    private static void appendFileCommonHeader(StringBuilder sb) {
        sb.append(FILE_ID);
        sb.append(FILE_ID_VERSION);
        sb.append(MODE_DIVISION);
        sb.append(RECORD_IDENTIFICATION);
    }

    // 拼接实时共通头部分
    private static void appendRealCommonHeader(StringBuilder sb) {
        sb.append(REAL_ID);
        sb.append(REAL_VERSION);
    }

    // 拼接请求处理模式部分
    private static void appendRequestProcessingMode(StringBuilder sb) {
        sb.append(POINT_TRANSACTION_DIVISION);
        sb.append(POINT_CHANGE_DIVISION);
    }

    private static void appendRequestDataArea(
            StringBuilder sb,
            String yyyyMMdd,
            String hhMMss,
            String fromCustCode,
            String memberId,
            String tradeNumber,
            String pointAmount) {
        sb.append(yyyyMMdd);
        sb.append(yyyyMMdd);
        sb.append(hhMMss);
        sb.append(CHANNEL_DIVISION);
        sb.append(fromCustCode); // 取引先コード，这里假设固定，若需动态可改为参数传入
        sb.append(PARTNER_BRANCH_CODE);
        sb.append(PARTNER_STORE_CODE);
        sb.append(PARTNER_POS_TERMINAL_NO);
        sb.append(tradeNumber);
        sb.append(memberId);
        sb.append(POINT_TYPE_1);
        sb.append(POINT_COLOR_DIVISION_1);
        sb.append(POINT_GRANT_SIGN_1);
        sb.append(pointAmount);
        sb.append(POINT_TYPE_2);
        sb.append(POINT_COLOR_DIVISION_2);
        sb.append(POINT_GRANT_SIGN_2);
        sb.append(POINT_GRANT_NUMBER_2);
        sb.append(POINT_TYPE_3);
        sb.append(POINT_COLOR_DIVISION_3);
        sb.append(POINT_GRANT_SIGN_3);
        sb.append(POINT_GRANT_NUMBER_3);
        sb.append(VISIT_POINT_SIGN);
        sb.append(VISIT_POINT_NUMBER);
        sb.append(SALES_POINT_SIGN);
        sb.append(SALES_POINT_NUMBER);
        sb.append(SALES_AMOUNT_SIGN);
        sb.append(SALES_AMOUNT);
        sb.append(NON_SALES_AMOUNT_SIGN);
        sb.append(NON_SALES_AMOUNT);
        sb.append(tradeNumber);
        sb.append(POINT_USAGE_DIVISION);
        sb.append(POINT_USAGE_SIGN);
        sb.append(POINT_USAGE_NUMBER);
        sb.append(USAGE_JUDGMENT_DATE);
        sb.append(POINT_EXCHANGE_PARTNER_CODE);
        sb.append(POINT_PRESENT_MEMBER_ID);
        sb.append(PREVIOUS_POINT_TRANSACTION_DIVISION);
        sb.append(PREVIOUS_POINT_CHANGE_DIVISION);
        sb.append(PREVIOUS_PARTNER_BUSINESS_DATE);
        sb.append(PREVIOUS_TRANSACTION_DATE);
        sb.append(PREVIOUS_TRANSACTION_TIME);
        sb.append(PREVIOUS_CHANNEL_DIVISION);
        sb.append(PREVIOUS_PARTNER_CODE);
        sb.append(PREVIOUS_PARTNER_BRANCH_CODE);
        sb.append(PREVIOUS_PARTNER_STORE_CODE);
        sb.append(PREVIOUS_PARTNER_POS_TERMINAL_NO);
        sb.append(PREVIOUS_TRANSACTION_NUMBER);
        sb.append(PREVIOUS_RECEIPT_TRANSACTION_NUMBER);
        for (int i = 0; i < 5; i++) {
            sb.append(COUPON_CODE);
            sb.append(COUPON_NUMBER_SIGN);
            sb.append(COUPON_NUMBER);
        }
        sb.append(LOCAL_IMPLEMENTATION_FLAG);
        sb.append(BATCH_GRANT_REASON);
        sb.append(FILLER);
        sb.append(LINE_BREAK_CODE);
    }
}
