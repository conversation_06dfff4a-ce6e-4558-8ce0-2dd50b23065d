package point.common.component;

import com.amazonaws.services.sns.AmazonSNS;
import com.amazonaws.services.sns.AmazonSNSClientBuilder;
import com.amazonaws.services.sns.model.CreateTopicRequest;
import com.amazonaws.services.sns.model.CreateTopicResult;
import com.amazonaws.services.sns.model.MessageAttributeValue;
import com.amazonaws.services.sns.model.PublishRequest;
import com.amazonaws.services.sns.model.PublishResult;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import point.common.config.AwsConfig;
import point.common.constant.Authority;
import point.common.constant.ErrorCode;
import point.common.entity.User;
import point.common.entity.UserAuthority;
import point.common.entity.UserInfo;
import point.common.exception.CustomException;
import point.common.service.UserAuthorityService;
import point.common.service.UserInfoService;
import point.common.service.UserService;
import point.common.util.JsonUtil;

@RequiredArgsConstructor
@Component
public class SnsManager {

    private final AwsConfig awsConfig;

    private final UserAuthorityService userAuthorityService;

    private final UserInfoService userInfoService;

    private final UserService userService;

    @Getter private AmazonSNS snsClient;

    private Map<String, String> topicArnMap = new HashMap<>();

    @PostConstruct
    private void initialize() {
        snsClient =
                AmazonSNSClientBuilder.standard()
                        .withCredentials(awsConfig.getCredentialsProvider())
                        .withRegion(awsConfig.getRegion().getName())
                        .build();
    }

    public PublishResult publish(String queueName, Object object) {
        return snsClient.publish(
                new PublishRequest(getTopicArn(queueName), JsonUtil.encode(object)));
    }

    public String getTopicArn(String topicName) {
        String topicArn = topicArnMap.get(topicName);

        if (topicArn == null) {
            CreateTopicResult createTopicResult =
                    snsClient.createTopic(new CreateTopicRequest(topicName));
            topicArn = createTopicResult.getTopicArn();
            topicArnMap.put(topicName, topicArn);
        }

        return topicArn;
    }

    private PublishResult publish(
            String message,
            String phoneNumber,
            Map<String, MessageAttributeValue> messageAttributes) {
        return snsClient.publish(
                new PublishRequest()
                        .withMessage(message)
                        .withPhoneNumber(phoneNumber)
                        .withMessageAttributes(messageAttributes));
    }

    public PublishResult publish(String message, String phoneNumber) {
        Map<String, MessageAttributeValue> messageAttributes = new HashMap<>();
        return publish(message, phoneNumber, messageAttributes);
    }

    public String fetchPhoneNumber(Long userId) throws CustomException {
        String phoneNumber = "";
        UserAuthority authority = userAuthorityService.findByUserId(userId);
        User user = userService.findOne(userId);

        if (authority.getAuthority().contains(Authority.PERSONAL.toString())) {
            UserInfo userInfo = userInfoService.findOne(user.getUserInfoId());
            if (userInfo != null) {
                phoneNumber = userInfo.getPhoneNumber();
            }
        }
        if (StringUtils.isEmpty(phoneNumber)) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_NOTSETUP_PHONE_NUMBER);
        }
        return phoneNumber;
    }
}
