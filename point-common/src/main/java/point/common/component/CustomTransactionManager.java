package point.common.component;

import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import point.common.entity.AbstractEntity;
import point.common.exception.CustomException;
import point.common.exception.GameException;

@RequiredArgsConstructor
@Component
public class CustomTransactionManager {

    private static final CustomLogger log =
            new CustomLogger(CustomTransactionManager.class.getName());

    private final DataSourceManager dataSourceManager;

    public void execute(TransactionExecutor transactionExecutor) throws Exception {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();

        try {
            entityTransaction.begin();
            transactionExecutor.execute(entityManager);
            entityTransaction.commit();
        } catch (Exception e) {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }
            log.severe(getClass().getSimpleName(), e);
            if (e instanceof GameException) {
                throw e;
            }
            throw wrapCustomException(e);
        } finally {
            entityManager.close();
        }
    }

    public <T> T execute(TransactionExecutorReturner<T> transactionExecutorReturner)
            throws Exception {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();
        T object;

        try {
            entityTransaction.begin();
            object = transactionExecutorReturner.execute(entityManager);
            entityTransaction.commit();
        } catch (Exception e) {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }
            log.severe(getClass().getSimpleName(), e);
            if (e instanceof GameException) {
                throw e;
            }
            throw wrapCustomException(e);
        } finally {
            entityManager.close();
        }

        return object;
    }

    public <E extends AbstractEntity> void find(
            Class<E> entityClass, QueryExecutor<E> queryExecutor) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();

        try {
            entityTransaction.begin();
            queryExecutor.execute(entityClass, entityManager);
        } catch (Exception e) {
            Class<E> clazz = queryExecutor.getEntityClass();
            log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
            logCustomException(e);
        } finally {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }

            entityManager.clear();
            entityManager.close();
        }
    }

    public <E extends AbstractEntity, T> T find(
            Class<E> entityClass, QueryExecutorReturner<E, T> queryExecutorReturner) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();
        T object = null;

        try {
            entityTransaction.begin();
            object = queryExecutorReturner.execute(entityClass, entityManager);
        } catch (Exception e) {
            Class<E> clazz = queryExecutorReturner.getEntityClass();
            log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
            logCustomException(e);
        } finally {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }

            entityManager.clear();
            entityManager.close();
        }

        return object;
    }

    /**
     * Executes a query within an active EntityManager transaction. Note that this method does not
     * handle the closing of the SessionManager - the caller is responsible for proper resource
     * management.
     *
     * @param <E> the type of the entity class being queried
     * @param <T> the type of the response object to be returned
     * @param entityClass the class object representing the entity type
     * @param queryExecutorReturner a function that executes the query and returns the result
     * @param entityManager the EntityManager instance to use for the transaction
     * @return the result of the query execution
     */
    public <E extends AbstractEntity, T> T find(
            Class<E> entityClass,
            QueryExecutorReturner<E, T> queryExecutorReturner,
            EntityManager entityManager) {
        T object = null;
        try {
            object = queryExecutorReturner.execute(entityClass, entityManager);
        } catch (Exception e) {
            Class<E> clazz = queryExecutorReturner.getEntityClass();
            log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
            logCustomException(e);
        }
        return object;
    }

    public <E extends AbstractEntity> long count(
            Class<E> entityClass, QueryExecutorCounter<E> queryExecutorCounter) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();
        Long count = null;

        try {
            entityTransaction.begin();
            count = queryExecutorCounter.execute(entityClass, entityManager);
        } catch (Exception e) {
            Class<E> clazz = queryExecutorCounter.getEntityClass();
            log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
            logCustomException(e);
        } finally {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }

            entityManager.clear();
            entityManager.close();
        }

        return count != null ? count : 0;
    }

    public <E extends AbstractEntity> Object[] sum(
            Class<E> entityClass, QueryExecutorSum<E> queryExecutorSum) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();
        Object[] objects = null;

        try {
            entityTransaction.begin();
            objects = queryExecutorSum.execute(entityClass, entityManager);
        } catch (Exception e) {
            Class<E> clazz = queryExecutorSum.getEntityClass();
            log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
            logCustomException(e);
        } finally {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }

            entityManager.clear();
            entityManager.close();
        }

        return objects;
    }

    public <E extends AbstractEntity> List<Object[]> sumList(
            Class<E> entityClass, QueryExecutorSumList<E> queryExecutorSumList) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();
        List<Object[]> objects = null;

        try {
            entityTransaction.begin();
            objects = queryExecutorSumList.execute(entityClass, entityManager);
        } catch (Exception e) {
            Class<E> clazz = queryExecutorSumList.getEntityClass();
            log.severe(clazz == null ? "" : clazz.getSimpleName(), e);
            logCustomException(e);
        } finally {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }

            entityManager.clear();
            entityManager.close();
        }

        return objects;
    }

    public <E extends AbstractEntity> E save(E entity, EntityManager entityManager)
            throws Exception {
        return new EntityPersister<E>().save(entity, entityManager);
    }

    public <E extends AbstractEntity> E save(E entity) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();

        try {
            entityTransaction.begin();
            save(entity, entityManager);
            entityTransaction.commit();
        } catch (Exception e) {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }

            log.severe(entity == null ? "" : entity.getClass().getSimpleName(), e);
            logCustomException(e);
        } finally {
            entityManager.close();
        }

        return entity;
    }

    public <E extends AbstractEntity> void delete(E entity, EntityManager entityManager) {
        new EntityPersister<E>().delete(entity, entityManager);
    }

    public <E extends AbstractEntity> void delete(E entity) {
        EntityManager entityManager =
                dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
        EntityTransaction entityTransaction = entityManager.getTransaction();

        try {
            entityTransaction.begin();
            delete(entity, entityManager);
            entityTransaction.commit();
        } catch (Exception e) {
            if (entityTransaction.isActive()) {
                entityTransaction.rollback();
            }

            log.severe(entity == null ? "" : entity.getClass().getSimpleName(), e);
            logCustomException(e);
        } finally {
            entityManager.close();
        }
    }

    @Transactional(transactionManager = "masterTransactionManager")
    public int multiUpdate(String sql, MapSqlParameterSource mapSqlParameterSource) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate =
                new NamedParameterJdbcTemplate(dataSourceManager.getMasterJdbcTemplate());
        try {
            return namedParameterJdbcTemplate.update(sql, mapSqlParameterSource);
        } catch (Exception e) {
            log.severe(namedParameterJdbcTemplate.getClass().getSimpleName(), e);
            throw e;
        }
    }

    @Transactional(transactionManager = "masterTransactionManager")
    public void execute(String sql) {
        JdbcTemplate masterJdbcTemplate = dataSourceManager.getMasterJdbcTemplate();
        try {
            masterJdbcTemplate.execute(sql);
        } catch (Exception e) {
            log.severe(masterJdbcTemplate.getClass().getSimpleName(), e);
            throw e;
        }
    }

    private CustomException wrapCustomException(Exception e) {
        if (e instanceof CustomException) {
            return (CustomException) e;
        }
        return new CustomException(e);
    }

    private void logCustomException(Exception e) {
        if (!(e instanceof CustomException) || !((CustomException) e).hasDebugMessage()) {
            return;
        }
        log.severe(e.getClass().getName() + ": " + ((CustomException) e).getDebugMessage(), e);
    }
}
