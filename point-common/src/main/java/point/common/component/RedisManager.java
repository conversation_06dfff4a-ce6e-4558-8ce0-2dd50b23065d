package point.common.component;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import point.common.constant.ErrorCode;
import point.common.constant.LogGroup;
import point.common.exception.CustomException;
import point.common.exception.GameException;

@RequiredArgsConstructor
@Component
public class RedisManager {

    // worker, order用はredis lockがexpireされない設定とする(lockExpireSeconds=0)
    public enum LockParams {
        DEFAULT(5, 50, 100),
        ORDER(0, 3, 50),
        EXECUTE_ORDER(0, 1, 0),
        WORKER(0, 1, 0),
        LOCKKEY(0, 10, 100),
        EXECUTE_CHOICE_ACTIVITY_TOTAL(0, 1, 0);

        private final int lockExpireSeconds;

        private final int gettingLockCount;

        private final long gettingLockIntervalMillis;

        LockParams(int lockExpireSeconds, int gettingLockCount, long gettingLockIntervalMillis) {
            this.lockExpireSeconds = lockExpireSeconds;
            this.gettingLockCount = gettingLockCount;
            this.gettingLockIntervalMillis = gettingLockIntervalMillis;
        }
    }

    private static final CustomLogger log = new CustomLogger(RedisManager.class.getName());

    private static final long MAX_LOCKED_MILLIS = 5000;

    public static String getLockKey(String suffix) {
        return "lock:" + suffix;
    }

    private final RedisTemplate<String, Date> dateRedisTemplate;

    private final RedisTemplate<String, Integer> integerRedisTemplate;

    private final RedisTemplate<String, String> stringRedisTemplate;

    public Integer getInteger(String key) {
        return integerRedisTemplate.opsForValue().get(key);
    }

    public void setInteger(String key, Integer value) {
        integerRedisTemplate.opsForValue().set(key, value);
    }

    public void setInteger(String key, Integer value, long minute) {
        setInteger(key, value);
        integerRedisTemplate.expire(key, minute, TimeUnit.MINUTES);
    }

    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, String value, long minute) {
        set(key, value);
        stringRedisTemplate.expire(key, minute, TimeUnit.MINUTES);
    }

    public void hset(String key, Map<String, String> value, Integer ttl) {
        stringRedisTemplate.opsForHash().putAll(key, value);
        stringRedisTemplate.expire(key, ttl, TimeUnit.SECONDS);
    }

    public String hget(String key, String hashKey) {
        return (String) stringRedisTemplate.opsForHash().get(key, hashKey);
    }

    public Set<String> keys(String pattern) {
        return stringRedisTemplate.keys(pattern);
    }

    public List<String> multiGet(Collection<String> keys) {
        return stringRedisTemplate.opsForValue().multiGet(keys);
    }

    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }

    public void delete(List<String> keys) {
        stringRedisTemplate.delete(keys);
    }

    public boolean lock(String key, LockParams lockParams) throws CustomException {
        try {
            for (int i = 0; i < lockParams.gettingLockCount; i++) {
                Boolean lockable = dateRedisTemplate.opsForValue().setIfAbsent(key, new Date());

                if (lockable != null && lockable.booleanValue()) {
                    if (lockParams.lockExpireSeconds > 0) {
                        // lockExpireSecondsが設定されている場合、redisTemplateの機能で自動で指定の一定時刻でredis lock削除される
                        // 設定されていない場合(lockExpireSeconds=0)、redis lockはexpireしない
                        dateRedisTemplate.expire(
                                key, lockParams.lockExpireSeconds, TimeUnit.SECONDS);
                    }

                    return true;
                } else {
                    if (lockParams.gettingLockIntervalMillis > 0) {
                        Thread.sleep(lockParams.gettingLockIntervalMillis);
                    }
                }
            }

            Date beforeLockedDate = dateRedisTemplate.opsForValue().get(key);

            if (beforeLockedDate != null) {
                long now = new Date().getTime();
                long diff = now - beforeLockedDate.getTime();

                if (diff > MAX_LOCKED_MILLIS) {
                    log.info(
                            getClass().getName(),
                            key + ".lock. " + BigDecimal.valueOf(diff).doubleValue());
                }
            }
        } catch (Exception e) {
            unlock(key);
            throw new CustomException(ErrorCode.COMMON_ERROR_LOCK, e);
        }

        return false;
    }

    public void unlock(String key) {
        if (!Retryer.retry(key, 10, () -> dateRedisTemplate.delete(key))) {
            log.severe(LogGroup.ALERT.getName(), "``` <!here> fail to unlock " + key + "!");
        }
    }

    public boolean executeWithLock(String key, LockParams lockParams, Executor executor)
            throws Exception {
        if (!lock(key, lockParams)) {
            log.info(getClass().getSimpleName(), "could not get lock. key: " + key);
            return false;
        }

        try {
            executor.execute();
            return true;
        } catch (Exception e) {
            log.severe(getClass().getSimpleName(), e);
            if (e instanceof GameException) {
                throw e;
            }
            throw new CustomException(e);
        } finally {
            unlock(key);
        }
    }

    public Object executeWithLock(
            String key, LockParams lockParams, ExecutorReturner<?> executorReturner)
            throws Exception {
        if (!lock(key, lockParams)) {
            log.info(getClass().getSimpleName(), "could not get lock. key: " + key);
            return null;
        }

        try {
            return executorReturner.execute();
        } catch (Exception e) {
            log.severe(getClass().getSimpleName(), e);
            if (e instanceof GameException) {
                throw e;
            }
            throw new CustomException(e);
        } finally {
            unlock(key);
        }
    }

    public void publish(String channel, String message) {
        stringRedisTemplate.convertAndSend(channel, message);
    }

    public Date getLockedDate(String suffix) {
        return dateRedisTemplate.opsForValue().get(getLockKey(suffix));
    }

    public Long getNonce(String key) {
        try {
            return (Long)
                    executeWithLock(
                            "lock:" + key,
                            LockParams.DEFAULT,
                            (ExecutorReturner<Long>)
                                    () -> {
                                        String nonce = get(key);

                                        if (StringUtils.isEmpty(nonce)
                                                || !NumberUtils.isCreatable(nonce)) {
                                            nonce = "0";
                                        } else {
                                            nonce = Integer.toString(Integer.valueOf(nonce) + 1);
                                        }

                                        set(key, nonce);
                                        return Long.valueOf(nonce);
                                    });
        } catch (Exception e) {
            log.warning(getClass().getName(), e);
        }

        return 0L;
    }
}
