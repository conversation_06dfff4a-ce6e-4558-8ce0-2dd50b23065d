package point.common.component;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaQuery;
import point.common.entity.AbstractEntity;

public abstract class QueryExecutorCounter<E extends AbstractEntity>
        extends AbstractQueryExecutor<E> {

    protected CriteriaQuery<Long> criteriaQuery;

    public Long execute(Class<E> entityClass, EntityManager entityManager) {
        initialize(entityClass, entityManager);
        return query();
    }

    @Override
    protected void prepare() {
        criteriaQuery = criteriaBuilder.createQuery(Long.class);
        root = criteriaQuery.from(getEntityClass());
    }

    public abstract Long query();
}
