package point.common.component;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaQuery;
import point.common.entity.AbstractEntity;

public abstract class QueryExecutorReturner<E extends AbstractEntity, T>
        extends AbstractQueryExecutor<E> {

    protected CriteriaQuery<E> criteriaQuery;

    public T execute(Class<E> entityClass, EntityManager entityManager) {
        initialize(entityClass, entityManager);
        return query();
    }

    @Override
    protected void prepare() {
        criteriaQuery = criteriaBuilder.createQuery(getEntityClass());
        root = criteriaQuery.from(getEntityClass());
    }

    public abstract T query();
}
