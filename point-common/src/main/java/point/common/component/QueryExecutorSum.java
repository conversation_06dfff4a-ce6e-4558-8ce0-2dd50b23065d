package point.common.component;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaQuery;
import point.common.entity.AbstractEntity;

public abstract class QueryExecutorSum<E extends AbstractEntity> extends AbstractQueryExecutor<E> {

    protected CriteriaQuery<Object[]> criteriaQuery;

    public Object[] execute(Class<E> entityClass, EntityManager entityManager) {
        initialize(entityClass, entityManager);
        return query();
    }

    @Override
    protected void prepare() {
        criteriaQuery = criteriaBuilder.createQuery(Object[].class);
        root = criteriaQuery.from(getEntityClass());
    }

    public abstract Object[] query();
}
