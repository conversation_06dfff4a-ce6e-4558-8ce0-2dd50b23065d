package point.common.component;

import net.logstash.logback.argument.StructuredArguments;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import point.common.constant.ErrorCode;
import point.common.exception.CustomException;
import point.common.model.LogData;

public class CustomLogger {

    private final Logger logger;

    public CustomLogger(String name) {
        logger = LoggerFactory.getLogger(name);
    }

    private String createMessage(String group, Object data) {
        return new LogData(group, data instanceof Exception ? (Exception) data : data).toString();
    }

    public void fine(String group, Object data) {
        // http://www.slf4j.org/apidocs/org/slf4j/bridge/SLF4JBridgeHandler.html
        logger.debug(createMessage(group, data), StructuredArguments.value("group", group));
    }

    public void info(String group, Object data) {
        logger.info(createMessage(group, data), StructuredArguments.value("group", group));
    }

    public void warning(String group, Object data) {
        logger.warn(createMessage(group, data), StructuredArguments.value("group", group));
    }

    public void severe(String group, Object data) {
        // http://www.slf4j.org/apidocs/org/slf4j/bridge/SLF4JBridgeHandler.html
        if (data instanceof CustomException) {
            if (((CustomException) data)
                            .getErrorCode()
                            .equals(ErrorCode.ORDER_ERROR_AMOUNT_EXCEED_ASSET)
                    || ((CustomException) data)
                            .getErrorCode()
                            .equals(ErrorCode.ORDER_ERROR_INVALID_ASSET)
                    || ((CustomException) data)
                            .getErrorCode()
                            .equals(ErrorCode.ORDER_ERROR_ACTIVE_AMOUNT_OVER_MAX)
                    || ((CustomException) data)
                            .getErrorCode()
                            .equals(ErrorCode.ORDER_ERROR_INVALID_ORDER_STATUS)) {
                logger.info(createMessage(group, data), StructuredArguments.value("group", group));
            } else {
                logger.error(createMessage(group, data), StructuredArguments.value("group", group));
            }
        } else {
            logger.error(createMessage(group, data), StructuredArguments.value("group", group));
        }
    }
}
