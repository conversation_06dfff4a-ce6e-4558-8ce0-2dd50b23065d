package point.common.component;

import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class HistoricalDataSourceManager {

    @Getter
    @PersistenceUnit(unitName = "historicalEntityManagerFactory")
    private final EntityManagerFactory historicalEntityManagerFactory;

    @Getter
    @Qualifier("historicalJdbcTemplate")
    private final JdbcTemplate historicalJdbcTemplate;
}
