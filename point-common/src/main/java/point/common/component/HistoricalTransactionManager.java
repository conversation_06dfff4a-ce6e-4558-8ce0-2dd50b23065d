package point.common.component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import point.common.entity.AbstractEntity;

@Slf4j
@RequiredArgsConstructor
@Component
public class HistoricalTransactionManager {

    private final HistoricalDataSourceManager historicalDataSourceManager;

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public int archive(String sql) {
        log.info("sql:{}", sql);
        return historicalDataSourceManager.getHistoricalJdbcTemplate().update(sql);
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public int archiveWithParameter(String sql, PreparedStatementSetter preparedStatementSetter) {
        return historicalDataSourceManager
                .getHistoricalJdbcTemplate()
                .update(sql, preparedStatementSetter);
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public int[] batchUpdate(
            String sql, BatchPreparedStatementSetter batchPreparedStatementSetter) {
        return historicalDataSourceManager
                .getHistoricalJdbcTemplate()
                .batchUpdate(sql, batchPreparedStatementSetter);
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public <E extends AbstractEntity> E findOneFromHistory(
            String sql, Long id, RowMapper<E> rowMapper) {
        return historicalDataSourceManager
                .getHistoricalJdbcTemplate()
                .queryForObject(sql, rowMapper, new Object[] {id});
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public <E extends AbstractEntity> List<E> findFromHistory(
            String sql, MapSqlParameterSource mapSqlParameterSource, RowMapper<E> rowMapper) {
        return new NamedParameterJdbcTemplate(
                        historicalDataSourceManager.getHistoricalJdbcTemplate())
                .query(sql, mapSqlParameterSource, rowMapper);
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public Map<String, Object> queryForMapFromHistory(
            String sql, MapSqlParameterSource mapSqlParameterSource) {
        return new NamedParameterJdbcTemplate(
                        historicalDataSourceManager.getHistoricalJdbcTemplate())
                .queryForMap(sql, mapSqlParameterSource);
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public List<Map<String, Object>> queryForListFromHistory(
            String sql, MapSqlParameterSource mapSqlParameterSource) {
        return new NamedParameterJdbcTemplate(
                        historicalDataSourceManager.getHistoricalJdbcTemplate())
                .queryForList(sql, mapSqlParameterSource);
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public Long getMaxId(String sql) {
        return new NamedParameterJdbcTemplate(
                        historicalDataSourceManager.getHistoricalJdbcTemplate())
                .queryForObject(sql, Collections.emptyMap(), Long.class);
    }

    @Transactional(transactionManager = "historicalJpaTransactionManager")
    public <E extends AbstractEntity> E findSingleFromHistoryWithMultipleConditions(
            String sql, MapSqlParameterSource mapSqlParameterSource, RowMapper<E> rowMapper) {
        return new NamedParameterJdbcTemplate(
                        historicalDataSourceManager.getHistoricalJdbcTemplate())
                .queryForObject(sql, mapSqlParameterSource, rowMapper);
    }
}
