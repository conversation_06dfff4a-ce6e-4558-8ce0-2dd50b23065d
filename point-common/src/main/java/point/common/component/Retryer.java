package point.common.component;

public class Retryer {

    private static final CustomLogger log = new CustomLogger(Retryer.class.getName());

    public static boolean retry(String group, int retryCount, Executor executor) {
        return new Retryer(group, retryCount, executor).retry();
    }

    private int now = 0;

    private final String group;

    private final int retryCount;

    private final Executor executor;

    private Retryer(String group, int retryCount, Executor executor) {
        this.group = group;
        this.retryCount = retryCount;
        this.executor = executor;
    }

    private boolean retry() {
        try {
            executor.execute();
            return true;
        } catch (Exception e) {
            log.severe(group, e);

            if (now < retryCount) {
                now++;
                return retry();
            } else {
                return false;
            }
        }
    }
}
