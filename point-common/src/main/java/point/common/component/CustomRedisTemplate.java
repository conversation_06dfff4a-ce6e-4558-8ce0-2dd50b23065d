package point.common.component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.core.RedisTemplate;

public class CustomRedisTemplate<T> {
    private final RedisTemplate<String, T> redisTemplate;

    public CustomRedisTemplate(RedisTemplate<String, T> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void setValue(String key, T value, long expireMinutes) {
        redisTemplate.opsForValue().set(key, value);
        if (expireMinutes > 0) {
            redisTemplate.expire(key, expireMinutes, TimeUnit.MINUTES);
        }
    }

    public void setValue(String key, T value) {
        setValue(key, value, 1);
    }

    public void setUnexpireValue(String key, T value) {
        setValue(key, value, 0);
    }

    public T getValue(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public List<T> getValues(List<String> keys) {
        return redisTemplate.opsForValue().multiGet(keys);
    }

    public void delete(String key) {
        redisTemplate.delete(key);
    }

    public Long sadd(String key, T values, long expireMinutes) {
        Long affectCount = redisTemplate.opsForSet().add(key, values);
        if (expireMinutes > 0) {
            redisTemplate.expire(key, expireMinutes, TimeUnit.MINUTES);
        }
        return affectCount;
    }

    public Long scard(String key) {
        return redisTemplate.opsForSet().size(key);
    }

    public Long sremove(String key, T values) {
        return redisTemplate.opsForSet().remove(key, values);
    }

    // this method is not thread safe
    public boolean setIfUncached(String key, T value, int expireMinutes) {
        T previousValue = getValue(key);
        if (value.equals(previousValue)) {
            return false;
        }
        setValue(key, value, expireMinutes);
        return true;
    }

    public Boolean setIfAbsent(String key, T value, int expireSeconds) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, expireSeconds, TimeUnit.SECONDS);
    }

    public void convertAndSend(String channel, T message) {
        redisTemplate.convertAndSend(channel, message);
    }
}
