package point.common.component;

import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class DataSourceManager {

    @Getter
    @PersistenceUnit(unitName = "masterEntityManagerFactory")
    private final EntityManagerFactory masterEntityManagerFactory;

    @Getter
    @Qualifier("masterJdbcTemplate")
    private final JdbcTemplate masterJdbcTemplate;
}
