package point.common.component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import javax.mail.Address;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;
import point.common.config.SesConfig;
import point.common.config.SpringConfig;
import point.common.service.UserService;
import point.common.util.CharsetUtil;
import point.common.util.StringUtil;

@Component
@RequiredArgsConstructor
@Slf4j
public class SesManager {

    private static final String COMPANY_NAME = "backseat";

    private final SesConfig awsSesConfig;

    private final SpringConfig springConfig;

    private final UserService userService;

    public boolean send(String from, String to, String subject, String body) throws Exception {
        if (skipTradeUncapped(to)) {
            return true;
        }
        // thread
        new Thread(
                        () -> {
                            try {
                                sendMail(from, to, subject, body);
                            } catch (Exception e) {
                                log.error(this.getClass().getName(), e);
                            }
                        })
                .start();

        return true;
    }

    public void sendWithoutThread(String from, String to, String subject, String body)
            throws Exception {

        Properties props = System.getProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.port", awsSesConfig.getPort());
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.auth", "true");
        Session session = Session.getDefaultInstance(props);

        MimeMessage mimeMessage = new MimeMessage(session);
        mimeMessage.setSubject(subject, CharsetUtil.UTF_8.getCode());

        mimeMessage.setFrom(new InternetAddress(from, COMPANY_NAME, CharsetUtil.UTF_8.getCode()));
        mimeMessage.setRecipient(Message.RecipientType.TO, new InternetAddress(to));
        mimeMessage.setContent(
                StringUtil.replaceLineFeedToHtml(body),
                ContentType.TEXT_HTML.getMimeType() + "; charset=" + CharsetUtil.UTF_8.getCode());
        Transport transport = session.getTransport();

        try {
            transport.connect(
                    awsSesConfig.getHost(), awsSesConfig.getUsername(), awsSesConfig.getPassword());
            transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
        } catch (Exception e) {
            log.error(this.getClass().getName(), e);
            throw e;
        } finally {
            try {
                transport.close();
            } catch (Exception e) {
                log.error(this.getClass().getName(), e);
                throw e;
            }
        }
    }

    public void sendWithoutThreadByBCC(
            String from, List<String> bccList, String subject, String body) throws Exception {

        Properties props = System.getProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.port", awsSesConfig.getPort());
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.auth", "true");
        Session session = Session.getDefaultInstance(props);

        MimeMessage mimeMessage = new MimeMessage(session);
        mimeMessage.setSubject(subject, CharsetUtil.UTF_8.getCode());

        mimeMessage.setFrom(new InternetAddress(from, COMPANY_NAME, CharsetUtil.UTF_8.getCode()));
        List<InternetAddress> bccAddrList = new ArrayList<>();
        for (String bcc : bccList) {
            bccAddrList.add(new InternetAddress(bcc));
        }
        Address[] bccAddrs = bccAddrList.toArray(new InternetAddress[bccAddrList.size()]);
        mimeMessage.setRecipients(Message.RecipientType.BCC, bccAddrs);

        mimeMessage.setContent(
                StringUtil.replaceLineFeedToHtml(body),
                ContentType.TEXT_HTML.getMimeType() + "; charset=" + CharsetUtil.UTF_8.getCode());
        Transport transport = session.getTransport();

        try {
            transport.connect(
                    awsSesConfig.getHost(), awsSesConfig.getUsername(), awsSesConfig.getPassword());
            transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
            log.info("send success mail: " + subject + " address: " + Arrays.toString(bccAddrs));
        } catch (Exception e) {
            log.error(this.getClass().getName(), e);
            throw e;
        } finally {
            try {
                transport.close();
            } catch (Exception e) {
                log.error(this.getClass().getName(), e);
                throw e;
            }
        }
    }

    void sendMail(String from, String to, String subject, String body) throws Exception {
        Properties props = System.getProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.port", awsSesConfig.getPort());
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.auth", "true");
        Session session = Session.getDefaultInstance(props);

        MimeMessage mimeMessage = new MimeMessage(session);
        mimeMessage.setSubject(subject, CharsetUtil.UTF_8.getCode());

        mimeMessage.setFrom(new InternetAddress(from, COMPANY_NAME, CharsetUtil.UTF_8.getCode()));
        mimeMessage.setRecipient(Message.RecipientType.TO, new InternetAddress(to));
        mimeMessage.setContent(
                StringUtil.replaceLineFeedToHtml(body),
                ContentType.TEXT_HTML.getMimeType() + "; charset=" + CharsetUtil.UTF_8.getCode());
        Transport transport = session.getTransport();

        try {
            transport.connect(
                    awsSesConfig.getHost(), awsSesConfig.getUsername(), awsSesConfig.getPassword());
            transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
        } catch (Exception e) {
            log.error(this.getClass().getName(), e);
        } finally {
            try {
                transport.close();
            } catch (Exception e) {
                log.error(this.getClass().getName(), e);
            }
        }
    }

    public boolean skipTradeUncapped(String mailTo) {
        var skip = false;
        try {
            var user = userService.findByEmail(mailTo);
            skip = user.isTradeUncapped();
        } catch (Exception e) {
            // Nothing Todo
        }
        return skip;
    }
}
