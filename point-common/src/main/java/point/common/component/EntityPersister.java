package point.common.component;

import java.util.Date;
import javax.persistence.EntityManager;
import point.common.entity.AbstractEntity;
import point.common.exception.CustomException;

public class EntityPersister<E extends AbstractEntity> {

    private static final CustomLogger log = new CustomLogger(EntityPersister.class.getName());

    public E save(E entity, EntityManager entityManager) throws Exception {
        try {
            Date now = new Date();
            entity.setUpdatedAt(now);

            if (entity.isNew()) {
                entity.setCreatedAt(now);
                entityManager.persist(entity);
                return entity;
            } else {
                return entityManager.merge(entity);
            }
        } catch (Exception e) {
            log.severe(entity.getClass().getName(), e);
            throw new CustomException(e);
        }
    }

    public void delete(E entity, EntityManager entityManager) {
        try {
            entityManager.remove(
                    entityManager.contains(entity) ? entity : entityManager.merge(entity));
        } catch (Exception e) {
            log.severe(entity.getClass().getName(), e);
        }
    }
}
