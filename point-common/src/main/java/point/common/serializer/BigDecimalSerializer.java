package point.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.jsontype.TypeSerializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import java.io.IOException;
import java.math.BigDecimal;

public class BigDecimalSerializer extends StdSerializer<BigDecimal> {

    public BigDecimalSerializer() {
        this(null);
    }

    public BigDecimalSerializer(Class<BigDecimal> c) {
        super(c);
    }

    @Override
    public void serialize(
            BigDecimal bigDecimal, JsonGenerator generator, SerializerProvider provider)
            throws IOException {
        generator.writeString(bigDecimal.stripTrailingZeros().toPlainString());
    }

    @Override
    public void serializeWithType(
            BigDecimal value,
            JsonGenerator gen,
            SerializerProvider serializers,
            TypeSerializer typeSer)
            throws IOException {
        gen.writeString(value.stripTrailingZeros().toPlainString());
    }
}
