package point.common.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomerAttribute {
    INDIVIDUAL("0", "個人"); //

    private String code;

    private String label;

    @JsonValue
    String getCode() {
        return this.code;
    }

    @JsonCreator
    public static CustomerAttribute from(final String code) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
