package point.common.constant;

import java.util.List;
import java.util.Optional;

public enum KycStatus {
    WAITING_SET_PWD("パスワード設定待ち", UserStatus.REVIEWING, Optional.empty()),
    NONE("本人確認未申請", UserStatus.REVIEWING, Optional.empty()),
    DOCUMENT_WAITING_APPLY("本人確認書類受領まち", UserStatus.REVIEWING, Optional.empty()),
    DOCUMENT_RECEIVED("本人確認書類提出済", UserStatus.REVIEWING, Optional.empty()),
    DOCUMENT_CONFIRMING("本人確認資料確認中", UserStatus.REVIEWING, Optional.empty()),
    DOCUMENT_CONFIRMED("本人確認済み", UserStatus.REVIEWING, Optional.empty()),
    DOCUMENT_REJECTED("本人確認却下", UserStatus.REVIEWING, Optional.empty()),
    REFINITIV_CHECKED("Refinitiv連携済", UserStatus.REVIEWING, Optional.empty()),
    FIRST_SCREENING("1次審査中", UserStatus.REVIEWING, Optional.empty()),
    FIRST_SCREEN_FINISHED("1次審査完了", UserStatus.REVIEWING, Optional.empty()),
    SECOND_SCREENING("2次審査中", UserStatus.REVIEWING, Optional.empty()),
    AML_FIRST_SCREEN_WAITING("AML1次審査待ち", UserStatus.REVIEWING, Optional.empty()),
    AML_FIRST_SCREENING("AML1次審査中", UserStatus.REVIEWING, Optional.empty()),
    AML_SECOND_SCREEN_WAITING("AML2次審査待ち", UserStatus.REVIEWING, Optional.empty()),
    AML_SECOND_SCREENING("AML2次審査中", UserStatus.REVIEWING, Optional.empty()),
    EDD_REQUEST("EDD依頼", UserStatus.REVIEWING, Optional.empty()),
    EDD_REQUEST_CONFIRMING("EDD依頼対応中", UserStatus.REVIEWING, Optional.empty()),
    AML_EDD_SCREEN_WAITING("AML EDD審査待ち", UserStatus.REVIEWING, Optional.empty()),
    INFORMATION_REQUIRED(
            "不備あり(却下)", UserStatus.REVIEWING, Optional.of(MailNoreplyType.INFORMATION_REQUIRED)),
    ACCOUNT_OPENING_DONE(
            "口座開設完了", UserStatus.ACTIVE, Optional.of(MailNoreplyType.ACCOUNT_OPENING_DONE)),
    REJECTED("謝絶", UserStatus.BANNED, Optional.of(MailNoreplyType.REJECTED)),
    DEPARTMENT_RESPONSIBLE_PERSON_CONFIRM("事務部責任者確認", UserStatus.REVIEWING, Optional.empty()),
    AML_RESPONSIBLE_PERSON_CONFIRM("AML責任者確認", UserStatus.REVIEWING, Optional.empty()),
    CHANGE_ATTR_INQUIRED("変更問い合わせあり", UserStatus.ACTIVE, Optional.empty()),
    CHANGE_ATTR_APPLY_REQUESTED("変更申請提出依頼", UserStatus.REVIEWING, Optional.empty()),
    URL_EXPIRED("URL失効", UserStatus.REVIEWING, Optional.empty()),
    RECONFIRM_REQUIRED("要再審査", UserStatus.REVIEWING, Optional.empty()),
    DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS("疑わしい取引の判断", UserStatus.REVIEWING, Optional.empty()),
    ACCOUNT_FREEZE("口座凍結", UserStatus.BANNED, Optional.empty()),
    ACCOUNT_CLOSED("口座解約済", UserStatus.LEFT, Optional.of(MailNoreplyType.ACCOUNT_CLOSED)),
    DONE("承認（変更完了）", UserStatus.ACTIVE, Optional.of(MailNoreplyType.DONE)),
    CANCEL_APPLICATION("申込キャンセル", UserStatus.LEFT, Optional.empty()),
    ACCOUNT_DUPLICATION(
            "口座重複", UserStatus.REVIEWING, Optional.of(MailNoreplyType.ACCOUNT_DUPLICATION));

    public final String displayName;
    public final UserStatus userStatus;
    public final Optional<MailNoreplyType> mailNoreplyType;

    KycStatus(
            final String displayName,
            final UserStatus userStatus,
            final Optional<MailNoreplyType> mailNoreplyType) {
        this.displayName = displayName;
        this.userStatus = userStatus;
        this.mailNoreplyType = mailNoreplyType;
    }

    public static KycStatus valueOfName(String name) {
        for (KycStatus kycStatus : values()) {
            if (kycStatus.name().equals(name)) {
                return kycStatus;
            }
        }
        return null;
    }

    public List<KycStatus> nextStatuses() {
        return switch (this) {
            case WAITING_SET_PWD -> List.of(NONE, CANCEL_APPLICATION);
            case NONE -> List.of(DOCUMENT_WAITING_APPLY, CANCEL_APPLICATION);
            case DOCUMENT_WAITING_APPLY ->
                    List.of(DOCUMENT_RECEIVED, CANCEL_APPLICATION, URL_EXPIRED);
            case DOCUMENT_RECEIVED ->
                    List.of(DOCUMENT_CONFIRMING, FIRST_SCREENING, CANCEL_APPLICATION);
            case DOCUMENT_CONFIRMING -> List.of(DOCUMENT_CONFIRMED, DOCUMENT_REJECTED);
            case DOCUMENT_CONFIRMED -> List.of(REFINITIV_CHECKED);
            case DOCUMENT_REJECTED -> List.of(DOCUMENT_WAITING_APPLY, CANCEL_APPLICATION);
            case REFINITIV_CHECKED -> List.of(FIRST_SCREENING, CANCEL_APPLICATION);
            case FIRST_SCREENING -> List.of(FIRST_SCREEN_FINISHED, CANCEL_APPLICATION);
            case FIRST_SCREEN_FINISHED -> List.of(SECOND_SCREENING, CANCEL_APPLICATION);
            case SECOND_SCREENING ->
                    List.of(
                            AML_FIRST_SCREEN_WAITING,
                            INFORMATION_REQUIRED,
                            ACCOUNT_OPENING_DONE,
                            DONE,
                            REJECTED,
                            DEPARTMENT_RESPONSIBLE_PERSON_CONFIRM,
                            CANCEL_APPLICATION,
                            ACCOUNT_DUPLICATION);
            case AML_FIRST_SCREEN_WAITING -> List.of(AML_FIRST_SCREENING, CANCEL_APPLICATION);
            case AML_FIRST_SCREENING ->
                    List.of(
                            AML_SECOND_SCREEN_WAITING,
                            EDD_REQUEST,
                            ACCOUNT_OPENING_DONE,
                            DONE,
                            REJECTED,
                            SECOND_SCREENING,
                            CANCEL_APPLICATION,
                            ACCOUNT_DUPLICATION);
            case AML_SECOND_SCREEN_WAITING -> List.of(AML_SECOND_SCREENING, CANCEL_APPLICATION);
            case AML_SECOND_SCREENING ->
                    List.of(
                            EDD_REQUEST,
                            ACCOUNT_OPENING_DONE,
                            DONE,
                            REJECTED,
                            AML_RESPONSIBLE_PERSON_CONFIRM,
                            SECOND_SCREENING,
                            CANCEL_APPLICATION,
                            ACCOUNT_DUPLICATION);
            case EDD_REQUEST -> List.of(EDD_REQUEST_CONFIRMING, CANCEL_APPLICATION);
            case EDD_REQUEST_CONFIRMING -> List.of(AML_EDD_SCREEN_WAITING, CANCEL_APPLICATION);
            case AML_EDD_SCREEN_WAITING -> List.of(AML_FIRST_SCREENING, CANCEL_APPLICATION);
            case INFORMATION_REQUIRED -> List.of(DOCUMENT_WAITING_APPLY, CANCEL_APPLICATION);
            case ACCOUNT_OPENING_DONE ->
                    List.of(
                            ACCOUNT_FREEZE,
                            RECONFIRM_REQUIRED,
                            DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS,
                            ACCOUNT_CLOSED,
                            CHANGE_ATTR_INQUIRED,
                            CHANGE_ATTR_APPLY_REQUESTED);
            case REJECTED -> List.of(); // next statuses are not exist
            case DEPARTMENT_RESPONSIBLE_PERSON_CONFIRM ->
                    List.of(
                            AML_FIRST_SCREEN_WAITING,
                            INFORMATION_REQUIRED,
                            ACCOUNT_OPENING_DONE,
                            DONE,
                            REJECTED,
                            CANCEL_APPLICATION,
                            ACCOUNT_DUPLICATION);
            case AML_RESPONSIBLE_PERSON_CONFIRM ->
                    List.of(
                            EDD_REQUEST,
                            AML_FIRST_SCREEN_WAITING,
                            INFORMATION_REQUIRED,
                            ACCOUNT_OPENING_DONE,
                            DONE,
                            REJECTED,
                            CANCEL_APPLICATION,
                            ACCOUNT_DUPLICATION,
                            SECOND_SCREENING,
                            ACCOUNT_FREEZE);
            case CHANGE_ATTR_INQUIRED -> List.of(CHANGE_ATTR_APPLY_REQUESTED);
            case CHANGE_ATTR_APPLY_REQUESTED -> List.of(DOCUMENT_WAITING_APPLY);
            case URL_EXPIRED -> List.of(DOCUMENT_RECEIVED, CANCEL_APPLICATION);
            case RECONFIRM_REQUIRED -> List.of(FIRST_SCREENING, CHANGE_ATTR_APPLY_REQUESTED);
            case DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS ->
                    List.of(DONE, RECONFIRM_REQUIRED, ACCOUNT_FREEZE);
            case ACCOUNT_FREEZE -> List.of(); // next statuses are not exist
            case ACCOUNT_CLOSED -> List.of(); // next statuses are not exist
            case DONE ->
                    List.of(
                            ACCOUNT_FREEZE,
                            RECONFIRM_REQUIRED,
                            DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS,
                            ACCOUNT_CLOSED,
                            CHANGE_ATTR_INQUIRED,
                            CHANGE_ATTR_APPLY_REQUESTED);
            case CANCEL_APPLICATION -> List.of(); // next statuses are not exist
            case ACCOUNT_DUPLICATION -> List.of(ACCOUNT_OPENING_DONE, CANCEL_APPLICATION);
        };
    }
}
