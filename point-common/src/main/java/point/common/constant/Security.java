package point.common.constant;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

public class Security {

    public static final String XSRF_TOKEN = "XSRF-TOKEN";

    public static final String JSESSIONID = "JSESSIONID";

    public static final String X_XSRF_TOKEN = "X-XSRF-TOKEN";

    private static final int TOKEN_LENGTH = 16;

    public static String createToken() {
        byte[] token = new byte[TOKEN_LENGTH];
        StringBuffer buffer = new StringBuffer();
        SecureRandom random;
        try {
            random = SecureRandom.getInstance("SHA1PRNG");
            random.nextBytes(token);

            for (int i = 0; i < token.length; i++) {
                buffer.append(String.format("%02x", token[i]));
            }
        } catch (NoSuchAlgorithmException e) {
            return null;
        }

        return buffer.toString();
    }
}
