package point.common.constant;

import java.util.List;

public enum TmsStatus {
    OPEN("オープン"),
    AML_FIRST_SCREENING("AML1次審査中"),
    AML_SECOND_SCREEN_WAITING("AML2次審査待ち"),
    AML_SECOND_SCREENING("AML2次審査中"),
    EDD_REQUEST("EDD依頼"),
    EDD_WORK_ON("EDD対応中"),
    DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS("疑わしい取引の判断"),
    REPORTING_OF_SUSPICIOUS_TRANSACTIONS("疑わしい取引の届出"),
    REPORTING_OF_SUSPICIOUS_TRANSACTIONS_DONE("疑わしい取引の届出（済）"),
    DETERMINATION_OF_INAPPROPRIATE_TRANSACTIONS("不適正取引の判断"),
    REPORTING_OF_INAPPROPRIATE_TRANSACTIONS("不適正取引の届出"),
    REPORTING_OF_INAPPROPRIATE_TRANSACTIONS_DONE("不適正取引の届出（済）"),
    CLOSE("クローズ");

    public final String displayName;

    TmsStatus(final String displayName) {
        this.displayName = displayName;
    }

    /**
     * 遷移可能な次のステータスのリストを取得する
     *
     * @return 遷移可能な次のステータスのリスト
     */
    public List<TmsStatus> nextStatuses() {
        return switch (this) {
            // オープン
            case OPEN -> List.of(AML_FIRST_SCREENING);
            // AML1次審査中
            case AML_FIRST_SCREENING ->
                    List.of(
                            AML_SECOND_SCREEN_WAITING,
                            DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS,
                            DETERMINATION_OF_INAPPROPRIATE_TRANSACTIONS,
                            CLOSE);
            // AML2次審査待ち
            case AML_SECOND_SCREEN_WAITING -> List.of(AML_SECOND_SCREENING);
            // AML2次審査中
            case AML_SECOND_SCREENING ->
                    List.of(
                            EDD_REQUEST,
                            DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS,
                            DETERMINATION_OF_INAPPROPRIATE_TRANSACTIONS,
                            CLOSE);
            // EDD依頼
            case EDD_REQUEST -> List.of(EDD_WORK_ON);
            // EDD対応中
            case EDD_WORK_ON ->
                    List.of(
                            DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS,
                            REPORTING_OF_SUSPICIOUS_TRANSACTIONS,
                            DETERMINATION_OF_INAPPROPRIATE_TRANSACTIONS,
                            REPORTING_OF_INAPPROPRIATE_TRANSACTIONS,
                            CLOSE);
            // 疑わしい取引の判断
            case DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS ->
                    List.of(REPORTING_OF_SUSPICIOUS_TRANSACTIONS, CLOSE);
            // 疑わしい取引の届出
            case REPORTING_OF_SUSPICIOUS_TRANSACTIONS ->
                    List.of(REPORTING_OF_SUSPICIOUS_TRANSACTIONS_DONE);
            // 不適正取引の判断
            case DETERMINATION_OF_INAPPROPRIATE_TRANSACTIONS ->
                    List.of(REPORTING_OF_INAPPROPRIATE_TRANSACTIONS, CLOSE);
            // 不適正取引の届出
            case REPORTING_OF_INAPPROPRIATE_TRANSACTIONS ->
                    List.of(REPORTING_OF_INAPPROPRIATE_TRANSACTIONS_DONE);
            // 疑わしい取引の届出（済）, 不適正取引の届出（済）, クローズ
            case REPORTING_OF_SUSPICIOUS_TRANSACTIONS_DONE,
                            REPORTING_OF_INAPPROPRIATE_TRANSACTIONS_DONE,
                            CLOSE ->
                    List.of();
        };
    }
}
