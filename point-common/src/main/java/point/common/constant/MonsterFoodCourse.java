package point.common.constant;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MonsterFoodCourse implements Serializable {

    private String course;

    // 獲得した合計経験値
    private String totalExperience;

    private List<FoodDetails> FoodDetailsList;
}
