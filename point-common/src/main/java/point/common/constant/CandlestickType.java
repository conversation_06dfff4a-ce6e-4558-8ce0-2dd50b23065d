package point.common.constant;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

public enum CandlestickType {
    PT1M("1分足", 1, null, DateUnit.MINUTE, FormatPattern.YYYYMMDD),
    PT3M("3分足", 3, PT1M, DateUnit.MINUTE, FormatPattern.YYYYMMDD) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(Calendar.MINUTE, getUnit() * (targetAt.get(Calendar.MINUTE) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT5M("5分足", 5, PT1M, DateUnit.MINUTE, FormatPattern.YYYYMMDD) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(Calendar.MINUTE, getUnit() * (targetAt.get(Calendar.MINUTE) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT15M("15分足", 15, PT5M, DateUnit.MINUTE, FormatPattern.YYYYMMDD) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(Calendar.MINUTE, getUnit() * (targetAt.get(Calendar.MINUTE) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT30M("30分足", 30, PT15M, DateUnit.MINUTE, FormatPattern.YYYYMMDD) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(Calendar.MINUTE, getUnit() * (targetAt.get(Calendar.MINUTE) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT1H("1時間足", 1, PT30M, DateUnit.HOUR, FormatPattern.YYYYMMDD),
    PT2H("2時間足", 2, PT1H, DateUnit.HOUR, FormatPattern.YYYY) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(
                    Calendar.HOUR_OF_DAY,
                    getUnit() * (targetAt.get(Calendar.HOUR_OF_DAY) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT4H("4時間足", 4, PT2H, DateUnit.HOUR, FormatPattern.YYYY) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(
                    Calendar.HOUR_OF_DAY,
                    getUnit() * (targetAt.get(Calendar.HOUR_OF_DAY) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT6H("6時間足", 6, PT2H, DateUnit.HOUR, FormatPattern.YYYY) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(
                    Calendar.HOUR_OF_DAY,
                    getUnit() * (targetAt.get(Calendar.HOUR_OF_DAY) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT8H("8時間足", 8, PT4H, DateUnit.HOUR, FormatPattern.YYYY) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(
                    Calendar.HOUR_OF_DAY,
                    getUnit() * (targetAt.get(Calendar.HOUR_OF_DAY) / getUnit()));
            return targetAt.getTime();
        }
    },
    PT12H("12時間足", 12, PT6H, DateUnit.HOUR, FormatPattern.YYYY) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(
                    Calendar.HOUR_OF_DAY,
                    getUnit() * (targetAt.get(Calendar.HOUR_OF_DAY) / getUnit()));
            return targetAt.getTime();
        }
    },
    P1D("日足", 1, PT12H, DateUnit.DAY, FormatPattern.YYYY),
    P1W("週足", 7, P1D, DateUnit.DAY, FormatPattern.YYYY) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            return targetAt.getTime();
        }
    },
    P1M("月足", 1, P1D, DateUnit.DAY, FormatPattern.YYYY) {
        @Override
        public Date getTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(super.getTargetAt(date));
            targetAt.set(Calendar.DATE, 1);
            return targetAt.getTime();
        }

        @Override
        public Date getNextTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(getTargetAt(date));
            targetAt.add(Calendar.MONTH, 1);
            return targetAt.getTime();
        }

        @Override
        public Date getPreviousTargetAt(Date date) {
            Calendar targetAt = Calendar.getInstance();
            targetAt.setTime(getTargetAt(date));
            targetAt.add(Calendar.MONTH, -1);
            return targetAt.getTime();
        }
    };

    public static CandlestickType valueOfName(String name) {
        for (CandlestickType candlestickType : values()) {
            if (candlestickType.name().equals(name)) {
                return candlestickType;
            }
        }

        return null;
    }

    public static List<CandlestickType> valuesOfBitZ() {
        List<CandlestickType> candlestickTypes = new ArrayList<>();
        candlestickTypes.add(PT15M);
        candlestickTypes.add(PT1H);
        candlestickTypes.add(PT4H);
        candlestickTypes.add(P1D);
        return candlestickTypes;
    }

    @Getter private final String label;

    @Getter private final int unit;

    @Getter private final CandlestickType elementType;

    @Getter private final DateUnit dateUnit;

    @Getter private final FormatPattern formatPatternToS3;

    CandlestickType(
            String label,
            int unit,
            CandlestickType elementType,
            DateUnit dateUnit,
            FormatPattern formatPatternToS3) {
        this.label = label;
        this.unit = unit;
        this.elementType = elementType;
        this.dateUnit = dateUnit;
        this.formatPatternToS3 = formatPatternToS3;
    }

    public String getName() {
        return name();
    }

    public Long getSpan() {
        return dateUnit.getMillis() * unit;
    }

    public Date getTargetAt(Date date) {
        return dateUnit.truncate(date.getTime());
    }

    public Date getNextTargetAt(Date date) {
        return new Date(getTargetAt(date).getTime() + dateUnit.getMillis() * unit);
    }

    public Date getPreviousTargetAt(Date date) {
        return new Date(getTargetAt(date).getTime() - dateUnit.getMillis() * unit);
    }

    public Date getDateFrom(Date date) {
        return FormatUtil.parse(FormatUtil.format(date, formatPatternToS3), formatPatternToS3);
    }

    public Date getDateTo(Date from) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(from);

        switch (formatPatternToS3) {
            case YYYYMMDD:
                calendar.add(Calendar.DATE, 1);
                break;
            case YYYY:
                calendar.add(Calendar.YEAR, 1);
                break;
            default:
                break;
        }

        return calendar.getTime();
    }

    public String getS3Key(Date from) {
        return name() + "/" + FormatUtil.format(from, formatPatternToS3);
    }
}
