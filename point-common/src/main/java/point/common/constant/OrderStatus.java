package point.common.constant;

import java.util.Arrays;
import java.util.List;

public enum OrderStatus {
    WAITING,
    UNFILLED,
    PARTIALLY_FILLED,
    FULLY_FILLED,
    CANCELED_UNFILLED,
    CANCELED_PARTIALLY_FILLED;

    public static OrderStatus[] ACTIVE_ORDER_STATUSES =
            new OrderStatus[] {UNFILLED, PARTIALLY_FILLED};

    public static OrderStatus[] INACTIVE_ORDER_STATUSES =
            new OrderStatus[] {WAITING, FULLY_FILLED, CANCELED_UNFILLED, CANCELED_PARTIALLY_FILLED};

    public static OrderStatus valueOfName(String name) {
        for (OrderStatus orderStatus : values()) {
            if (orderStatus.name().equals(name)) {
                return orderStatus;
            }
        }
        return null;
    }

    public boolean isWaiting() {
        return this == WAITING;
    }

    public boolean isActive() {
        return this == UNFILLED || this == PARTIALLY_FILLED;
    }

    public boolean isCanceled() {
        return this == CANCELED_UNFILLED || this == CANCELED_PARTIALLY_FILLED;
    }

    public boolean isInactive() {
        return this == FULLY_FILLED || isCanceled();
    }

    public static List<OrderStatus> getNotFullyFilled() {
        return Arrays.asList(
                WAITING,
                UNFILLED,
                PARTIALLY_FILLED,
                // 約定済を除外
                // FULLY_FILLED,
                CANCELED_UNFILLED,
                CANCELED_PARTIALLY_FILLED);
    }
}
