package point.common.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import point.common.exception.CustomException;

public enum Country {
    JP("81", "Japan", "日本"),
    AF("93", "Afghanistan", "アフガニスタン"),
    AL("355", "Albania", "アルバニア"),
    AS("1", "684", "American Samoa", "アメリカ領サモア"),
    AD("376", "Andorra", "アンドラ"),
    AG("1", "268", "Antigua and Barbuda", "アンティグア・バーブーダ"),
    AI("1", "264", "Anguilla", "アンギラ"),
    AO("244", "Angola", "アンゴラ"),
    AQ("672", "Antarctica", "南極"),
    AR("54", "Argentina", "アルゼンチン"),
    AM("374", "Armenia", "アルメニア"),
    AW("297", "Aruba", "アルバ"),
    AU("61", "Australia", "オーストラリア"),
    AT("43", "Austria", "オーストリア"),
    AX("358", "18", "Åland Islands", "オーランド諸島"),
    AZ("994", "Azerbaijan", "アゼルバイジャン"),
    BH("973", "Bahrain", "バーレーン"),
    BB("1", "246", "Barbados", "バルバドス"),
    BD("880", "Bangladesh", "バングラデシュ"),
    BE("32", "Belgium", "ベルギー"),
    BA("387", "Bosnia and Herzegovina", "ボスニア・ヘルツェゴビナ"),
    BF("226", "Burkina Faso", "ブルキナファソ"),
    BG("359", "Bulgaria", "ブルガリア"),
    BI("257", "Burundi", "ブルンジ"),
    BJ("229", "Benin", "ベナン"),
    BM("1", "441", "Bermuda", "バミューダ諸島"),
    BN("673", "Brunei Darussalam", "ブルネイ"),
    BO("591", "Plurinational State of Bolivia", "ボリビア"),
    BQ("599", "3", "Bonaire, Saint Eustatius and Saba", "ボネール、シント・ユースタティウスおよびサバ"),
    BS("1", "242", "Bahamas", "バハマ"),
    BR("55", "Brazil", "ブラジル"),
    BT("975", "Bhutan", "ブータン"),
    BV("47", "Bouvet Island", "ブーベ島"),
    BW("267", "Botswana", "ボツワナ"),
    BY("375", "Belarus", "ベラルーシ"),
    BZ("501", "Belize", "ベリーズ"),
    CM("237", "Cameroon", "カメルーン"),
    CA("1", "Canada", "カナダ"),
    CC("61", "89162", "Cocos (Keeling) Islands", "ココス諸島"),
    CD("57", "Democratic Republic of the Congo", "コンゴ民主共和国"),
    CF("236", "Central African Republic", "中央アフリカ共和国"),
    CG("242", "Republic of Congo", "コンゴ共和国"),
    CK("682", "Cook Islands", "クック諸島"),
    CI("225", "Côte d'Ivoire", "コートジボワール"),
    CL("56", "Chile", "チリ"),
    CN("86", "China", "中国"),
    CO("57", "Colombia", "コロンビア"),
    CR("506", "Costa Rica", "コスタリカ"),
    CU("53", "Cuba", "キューバ"),
    CV("238", "Cape Verde", "カーボベルデ"),
    CW("599", "Curaçao", "キュラソー"),
    CX("61", "89164", "Christmas Island", "クリスマス島"),
    CY("357", "Cyprus", "キプロス"),
    CZ("420", "Czech Republic", "チェコ"),
    DK("45", "Denmark", "デンマーク"),
    DJ("253", "Djibouti", "ジブチ"),
    DM("1", "767", "Dominica", "ドミニカ国"),
    DO("1", "829", "Dominican Republic", "ドミニカ共和国"),
    DZ("213", "Algeria", "アルジェリア"),
    EC("593", "Ecuador", "エクアドル"),
    EG("20", "Egypt", "エジプト"),
    HR("385", "Croatia", "クロアチア"),
    IO("246", "British Indian Ocean Territory", "イギリス領インド洋地域"),
    KH("855", "Cambodia", "カンボジア"),
    KY("1", "345", "Cayman Islands", "ケイマン諸島"),
    KM("269", "Comoros", "コモロ"),
    TD("235", "Chad", "チャド"),
    SV("503", "El Salvador", "エルサルバドル"),
    GQ("240", "Equatorial Guinea", "赤道ギニア共和国"),
    ER("291", "Eritrea", "エリトリア"),
    EE("372", "Estonia", "エストニア"),
    ET("251", "Ethiopia", "エチオピア"),
    FK("500", "Falkland Islands (Malvinas)", "フォークランド諸島"),
    FO("298", "Faroe Islands", "フェロー諸島"),
    FJ("679", "Fiji", "フィジー"),
    FI("358", "Finland", "フィンランド"),
    FR("33", "France", "フランス"),
    GF("594", "French Guiana", "フランス領ギアナ"),
    PF("689", "French Polynesia", "フランス領ポリネシア"),
    TF("262", "French Southern Territories", "フランス領南方・南極地域"),
    GA("241", "Gabon", "ガボン"),
    GM("220", "Gambia", "ガンビア"),
    GE("995", "Georgia", "ジョージア"),
    DE("49", "Germany", "ドイツ"),
    GH("233", "Ghana", "ガーナ"),
    GI("350", "Gibraltar", "ジブラルタル"),
    GR("30", "Greece", "ギリシャ"),
    GL("299", "Greenland", "グリーンランド"),
    GD("1", "473", "Grenada", "グレナダ"),
    GP("590", "Guadeloupe", "グアドループ"),
    GU("1", "671", "Guam", "グアム"),
    GT("502", "Guatemala", "グアテマラ"),
    GG("44", "1481", "Guernsey", "ガーンジー"),
    GN("224", "Guinea", "ギニア共和国"),
    GW("245", "Guinea-Bissau", "ギニアビサウ"),
    GY("592", "Guyana", "ガイアナ"),
    HT("509", "Haiti", "ハイチ"),
    HM("672", "Heard Island and McDonald Islands", "ハード島とマクドナルド島"),
    VA("39", "06", "Holy See (Vatican City State)", "バチカン市国"),
    HN("504", "Honduras", "ホンジュラス"),
    HK("852", "Hong Kong", "香港"),
    HU("36", "Hungary", "ハンガリー"),
    IS("354", "Iceland", "アイスランド"),
    IN("91", "India", "インド"),
    ID("62", "Indonesia", "インドネシア"),
    IR("98", "Islamic Republic of Iran", "イラン"),
    IQ("964", "Iraq", "イラク"),
    IE("353", "Ireland", "アイルランド"),
    IM("44", "1624", "Isle of Man", "マン島"),
    IL("972", "Israel", "イスラエル"),
    IT("39", "Italy", "イタリア"),
    JM("1", "658", "Jamaica", "ジャマイカ"),
    JE("44", "1534", "Jersey", "ジャージー"),
    JO("962", "Jordan", "ヨルダン"),
    KZ("7", "Kazakhstan", "カザフスタン"),
    KE("254", "Kenya", "ケニア"),
    KI("686", "Kiribati", "キリバス"),
    KP("850", "Democratic People's Republic of Korea", "北朝鮮"),
    KR("82", "Republic of Korea", "韓国"),
    KW("965", "Kuwait", "クウェート"),
    KG("996", "Kyrgyzstan", "キルギス"),
    LA("856", "Lao People's Democratic Republic", "ラオス"),
    LV("371", "Latvia", "ラトビア"),
    LB("961", "Lebanon", "レバノン"),
    LS("266", "Lesotho", "レソト"),
    LR("231", "Liberia", "リベリア"),
    LY("218", "Libya", "リビア"),
    LI("423", "Liechtenstein", "リヒテンシュタイン"),
    LT("370", "Lithuania", "リトアニア"),
    LU("352", "Luxembourg", "ルクセンブルク"),
    MO("853", "Macao", "マカオ"),
    MK("389", "Former Yugoslav Republic of Macedonia", "マケドニア"),
    MG("261", "Madagascar", "マダガスカル"),
    MW("265", "Malawi", "マラウイ"),
    MY("60", "Malaysia", "マレーシア"),
    MV("960", "Maldives", "モルディブ"),
    ML("223", "Mali", "マリ"),
    MT("356", "Malta", "マルタ"),
    MH("692", "Marshall Islands", "マーシャル諸島"),
    MQ("596", "Martinique", "マルティニーク"),
    MR("222", "Mauritania", "モーリタニア"),
    MU("230", "Mauritius", "モーリシャス"),
    YT("262", "269", "Mayotte", "マヨット"),
    MX("52", "Mexico", "メキシコ"),
    FM("691", "Federated States of Micronesia", "ミクロネシア連邦"),
    MD("373", "Republic of Moldova", "モルドバ"),
    MC("377", "Monaco", "モナコ"),
    MN("976", "Mongolia", "モンゴル"),
    ME("382", "Montenegro", "モンテネグロ"),
    MS("1", "664", "Montserrat", "モントセラト"),
    MA("212", "Morocco", "モロッコ"),
    MZ("258", "Mozambique", "モザンビーク"),
    MM("95", "Myanmar", "ミャンマー"),
    NA("264", "Namibia", "ナミビア"),
    NR("674", "Nauru", "ナウル"),
    NP("977", "Nepal", "ネパール"),
    NL("31", "Netherlands", "オランダ"),
    NC("687", "New Caledonia", "ニューカレドニア"),
    NZ("64", "New Zealand", "ニュージーランド"),
    NI("505", "Nicaragua", "ニカラグア"),
    NE("227", "Niger", "ニジェール"),
    NG("234", "Nigeria", "ナイジェリア"),
    NU("683", "Niue", "ニウエ"),
    NF("672", "3", "Norfolk Island", "ノーフォーク島"),
    MP("1", "670", "Northern Mariana Islands", "北マリアナ諸島"),
    NO("47", "Norway", "ノルウェー"),
    OM("968", "Oman", "オマーン"),
    PK("92", "Pakistan", "パキスタン"),
    PW("680", "Palau", "パラオ"),
    PS("970", "Palestinian Territory, Occupied", "パレスチナ自治区"),
    PA("507", "Panama", "パナマ"),
    PG("675", "Papua New Guinea", "パプアニューギニア"),
    PY("595", "Paraguay", "パラグアイ"),
    PE("51", "Peru", "ペルー"),
    PH("63", "Philippines", "フィリピン"),
    PN("64", "00", "Pitcairn", "ピトケアン諸島"),
    PL("48", "Poland", "ポーランド"),
    PT("351", "Portugal", "ポルトガル"),
    PR("1", "787", "Puerto Rico", "プエルトリコ"),
    QA("974", "Qatar", "カタール"),
    RE("262", "Réunion", "レユニオン"),
    RO("40", "Romania", "ルーマニア"),
    RU("7", "Russian Federation", "ロシア"),
    RW("250", "Rwanda", "ルワンダ"),
    BL("590", "Saint Barthélemy", "サン・バルテルミー島"),
    SH("290", "Saint Helena, Ascension and Tristan da Cunha", "セントヘレナ"),
    KN("1", "869", "Saint Kitts and Nevis", "セントクリストファー・ネービス"),
    LC("1", "758", "Saint Lucia", "セントルシア"),
    MF("590", "Saint Martin (French part)", "セント・マーチン島"),
    PM("508", "Saint Pierre and Miquelon", "サンピエール島・ミクロン島"),
    VC("1", "Saint Vincent and the Grenadines", "セントビンセント及びグレナディーン諸島"),
    WS("685", "Samoa", "サモア独立国"),
    SM("378", "San Marino", "サンマリノ"),
    ST("239", "Sao Tome and Principe", "サントメ・プリンシペ"),
    SA("966", "Saudi Arabia", "サウジアラビア"),
    SN("221", "Senegal", "セネガル"),
    RS("381", "Serbia", "セルビア"),
    SC("248", "Seychelles", "セーシェル"),
    SL("232", "Sierra Leone", "シエラレオネ"),
    SG("65", "Singapore", "シンガポール"),
    SX("1", "721", "Sint Maarten (Dutch part)", "シント・マールテン"),
    SK("421", "Slovakia", "スロバキア"),
    SI("386", "Slovenia", "スロベニア"),
    SB("677", "Solomon Islands", "ソロモン諸島"),
    SO("252", "Somalia", "ソマリア"),
    ZA("27", "South Africa", "南アフリカ"),
    GS("500", "South Georgia and the South Sandwich Islands", "サウスジョージア・サウスサンドウィッチ諸島"),
    SS("211", "South Sudan", "南スーダン"),
    ES("34", "Spain", "スペイン"),
    LK("94", "Sri Lanka", "スリランカ"),
    SD("249", "Sudan", "スーダン"),
    SR("597", "Suriname", "スリナム"),
    SJ("47", "79", "Svalbard and Jan Mayen", "スヴァールバル諸島及びヤンマイエン島"),
    SZ("268", "Swaziland", "エスワティニ"),
    SE("46", "Sweden", "スウェーデン"),
    CH("41", "Switzerland", "スイス"),
    SY("963", "Syrian Arab Republic", "シリア"),
    TW("886", "Taiwan, Province of China", "台湾"),
    TJ("992", "Tajikistan", "タジキスタン"),
    TZ("255", "United Republic of Tanzania", "タンザニア"),
    TH("66", "Thailand", "タイ"),
    TL("670", "Timor-Leste", "東ティモール"),
    TG("228", "Togo", "トーゴ"),
    TK("690", "Tokelau", "トケラウ"),
    TO("676", "Tonga", "トンガ"),
    TT("1", "868", "Trinidad and Tobago", "トリニダード・トバゴ"),
    TN("216", "Tunisia", "チュニジア"),
    TR("90", "Turkey", "トルコ"),
    TM("993", "Turkmenistan", "トルクメニスタン"),
    TC("1", "649", "Turks and Caicos Islands", "タークス・カイコス諸島"),
    TV("688", "Tuvalu", "ツバル"),
    UG("256", "Uganda", "ウガンダ"),
    UA("380", "Ukraine", "ウクライナ"),
    AE("971", "United Arab Emirates", "アラブ首長国連邦"),
    GB("44", "United Kingdom", "イギリス"),
    US("1", "United States", "アメリカ"),
    UM("1", "United States Minor Outlying Islands", "合衆国領有小離島"),
    UY("598", "Uruguay", "ウルグアイ"),
    UZ("998", "Uzbekistan", "ウズベキスタン"),
    VU("678", "Vanuatu", "バヌアツ"),
    VE("58", "Bolivarian Republic of Venezuela", "ベネズエラ"),
    VN("84", "Viet Nam", "ベトナム"),
    VG("1", "284", "British Virgin Islands", "イギリス領ヴァージン諸島"),
    VI("1", "340", "U.S. Virgin Islands", "アメリカ領ヴァージン諸島"),
    WF("681", "Wallis and Futuna", "ウォリス・フツナ"),
    EH("212", "Western Sahara", "西サハラ"),
    YE("967", "Yemen", "イエメン"),
    ZM("260", "Zambia", "ザンビア"),
    ZW("263", "Zimbabwe", "ジンバブエ");

    public static Country valueOfName(String name) throws Exception {
        for (Country country : values()) {
            if (country.name().equals(name)) {
                return country;
            }
        }

        throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_COUNTRY_NAME);
    }

    public static Country valueOfLabel(String label) throws Exception {
        for (Country country : values()) {
            if (country.getLabel().equals(label)) {
                return country;
            }
        }

        throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_COUNTRY_NAME);
    }

    @Getter private final String callingCode;

    @Getter private final String areaCode;

    @Getter private final String label;

    @Getter private final String jpyLabel;

    private Country(String callingCode, String areaCode, String label, String jpyLabel) {
        this.callingCode = callingCode;
        this.areaCode = areaCode;
        this.label = label;
        this.jpyLabel = jpyLabel;
    }

    private Country(String callingCode, String label, String jpyLabel) {
        this(callingCode, StringUtils.EMPTY, label, jpyLabel);
    }
}
