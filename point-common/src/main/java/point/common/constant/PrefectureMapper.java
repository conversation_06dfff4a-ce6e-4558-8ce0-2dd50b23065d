package point.common.constant;

import java.util.HashMap;
import java.util.Map;

public class PrefectureMapper {

    private PrefectureMapper() {}

    public static final Map<String, String> PREFECTURE_MAP = new HashMap<>(47);

    static {
        PREFECTURE_MAP.put("01", "北海道");
        PREFECTURE_MAP.put("02", "青森県");
        PREFECTURE_MAP.put("03", "岩手県");
        PREFECTURE_MAP.put("04", "宮城県");
        PREFECTURE_MAP.put("05", "秋田県");
        PREFECTURE_MAP.put("06", "山形県");
        PREFECTURE_MAP.put("07", "福島県");
        PREFECTURE_MAP.put("08", "茨城県");
        PREFECTURE_MAP.put("09", "栃木県");
        PREFECTURE_MAP.put("10", "群馬県");
        PREFECTURE_MAP.put("11", "埼玉県");
        PREFECTURE_MAP.put("12", "千葉県");
        PREFECTURE_MAP.put("13", "東京都");
        PREFECTURE_MAP.put("14", "神奈川県");
        PREFECTURE_MAP.put("15", "新潟県");
        PREFECTURE_MAP.put("16", "富山県");
        PREFECTURE_MAP.put("17", "石川県");
        PREFECTURE_MAP.put("18", "福井県");
        PREFECTURE_MAP.put("19", "山梨県");
        PREFECTURE_MAP.put("20", "長野県");
        PREFECTURE_MAP.put("21", "岐阜県");
        PREFECTURE_MAP.put("22", "静岡県");
        PREFECTURE_MAP.put("23", "愛知県");
        PREFECTURE_MAP.put("24", "三重県");
        PREFECTURE_MAP.put("25", "滋賀県");
        PREFECTURE_MAP.put("26", "京都府");
        PREFECTURE_MAP.put("27", "大阪府");
        PREFECTURE_MAP.put("28", "兵庫県");
        PREFECTURE_MAP.put("29", "奈良県");
        PREFECTURE_MAP.put("30", "和歌山県");
        PREFECTURE_MAP.put("31", "鳥取県");
        PREFECTURE_MAP.put("32", "島根県");
        PREFECTURE_MAP.put("33", "岡山県");
        PREFECTURE_MAP.put("34", "広島県");
        PREFECTURE_MAP.put("35", "山口県");
        PREFECTURE_MAP.put("36", "徳島県");
        PREFECTURE_MAP.put("37", "香川県");
        PREFECTURE_MAP.put("38", "愛媛県");
        PREFECTURE_MAP.put("39", "高知県");
        PREFECTURE_MAP.put("40", "福岡県");
        PREFECTURE_MAP.put("41", "佐賀県");
        PREFECTURE_MAP.put("42", "長崎県");
        PREFECTURE_MAP.put("43", "熊本県");
        PREFECTURE_MAP.put("44", "大分県");
        PREFECTURE_MAP.put("45", "宮崎県");
        PREFECTURE_MAP.put("46", "鹿児島県");
        PREFECTURE_MAP.put("47", "沖縄県");
    }
}
