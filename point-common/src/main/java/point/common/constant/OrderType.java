package point.common.constant;

public enum OrderType {
    MARKET("成行"),
    SIMPLE_MARKET("SIMPLE_MARKET"),
    LIMIT("指値"),
    STOP("STOP"),
    OCO("OCO"),
    IFD("IFD"),
    IFD_OCO("IFD_OCO"),
    LOSS_CUT("ロスカット"),
    POS("POS");

    public final String displayName;

    private OrderType(String displayName) {
        this.displayName = displayName;
    }

    public static OrderType valueOfName(String name) {
        for (OrderType orderType : values()) {
            if (orderType.name().equals(name)) {
                return orderType;
            }
        }
        return null;
    }

    public String toLowerCase() {
        return name().toLowerCase();
    }

    public boolean isMarket() {
        return this == MARKET;
    }

    public boolean isSimpleMarket() {
        return this == SIMPLE_MARKET;
    }

    public boolean isLimit() {
        return this == LIMIT;
    }

    public boolean isStop() {
        return this == STOP;
    }

    public boolean isIfd() {
        return this == IFD;
    }

    public boolean isIfdOco() {
        return this == IFD_OCO;
    }

    public boolean isLossCut() {
        return this == LOSS_CUT;
    }

    public String getName() {
        return name();
    }
}
