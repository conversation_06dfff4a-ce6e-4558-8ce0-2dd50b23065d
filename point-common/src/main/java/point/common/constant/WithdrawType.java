package point.common.constant;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** BTC_PRICE, OTHER_ASSET, */
@Getter
@AllArgsConstructor
public enum WithdrawType {
    POINT("investPoint"),

    JPY("operatePoint");

    private final String value;
    private static final Map<String, WithdrawType> TYPE_MAP = new HashMap<>();

    static {
        for (WithdrawType type : WithdrawType.values()) {
            TYPE_MAP.put(type.value, type);
        }
    }

    public static WithdrawType fromString(String type) {
        if (!TYPE_MAP.containsKey(type)) {
            throw new IllegalArgumentException("Invalid withdraw type: " + type);
        }
        return TYPE_MAP.get(type);
    }
}
