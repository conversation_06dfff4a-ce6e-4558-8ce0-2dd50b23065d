package point.common.constant;

import java.util.ResourceBundle;
import lombok.Getter;

/** define the biz code for success response */
public enum BizCode {
    SUCCESS(200),

    PONTA_POINT_EXCHANGE_SUCCESS(200001),
    QUIZ_CORRECT_ANSWER(200002),
    WITHDRAW_SUCCESS(200003),
    CHOICE_P_BALANCE_CHANGE(200004),
    ORDER_SEND_SUCCESS(200005);

    private static final ResourceBundle bundle = ResourceBundle.getBundle("messages");
    @Getter private final int code;

    BizCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return bundle.getString(String.valueOf(this.code));
    }
}
