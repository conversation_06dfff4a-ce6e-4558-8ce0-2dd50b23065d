package point.common.constant;

public enum PointTransferStatusEnum {
    PROCESSING("PROCESSING", "振替処理中"),
    COMPLETED("COMPLETED", "振替処理完了"),
    FAILED("FAILED", "振替処理失敗");

    private final String code; // ステータスコード
    private final String description; // ステータスの説明

    PointTransferStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * ステータスコードから列挙型のインスタンスを取得します。
     *
     * @param code ステータスコード
     * @return 対応する列挙型のインスタンス
     * @throws IllegalArgumentException 無効なステータスコードの場合
     */
    public static PointTransferStatusEnum fromCode(String code) {
        for (PointTransferStatusEnum status : PointTransferStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("無効なステータスコード: " + code);
    }

    /**
     * このステータスが終了状態（COMPLETED または FAILED）かどうかを判定します。
     *
     * @return 終了状態の場合は true、それ以外の場合は false
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED;
    }
}
