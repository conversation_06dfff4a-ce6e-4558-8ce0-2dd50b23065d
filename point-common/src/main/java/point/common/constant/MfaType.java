package point.common.constant;

public enum MfaType {
    <PERSON>MAIL,
    GOOGLE,
    SMS;

    public static MfaType valueOfName(String name) {
        for (MfaType logType : values()) {
            if (logType.name().equals(name)) {
                return logType;
            }
        }
        return null;
    }

    public String toLowerCase() {
        return name().toLowerCase();
    }

    public String getName() {
        return name();
    }
}
