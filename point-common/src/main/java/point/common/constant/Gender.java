package point.common.constant;

import lombok.Getter;

public enum Gender {
    MAN(0, "男性"),
    WOMAN(1, "女性"),
    OTHER(2, "その他");

    @Getter private Integer code;

    @Getter private String kanjiName;

    private Gender(Integer code, String kanjiName) {
        this.code = code;
        this.kanjiName = kanjiName;
    }

    public static Gender valueOfCode(Integer code) {
        for (Gender gender : values()) {
            if (gender.code.equals(code)) {
                return gender;
            }
        }
        return null;
    }
}
