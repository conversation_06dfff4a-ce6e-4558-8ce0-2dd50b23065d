package point.common.constant;

public enum OrderOperator {
    ADMIN,
    USER,
    WORKER;

    public static OrderOperator valueOfName(String name) {
        for (OrderOperator operator : values()) {
            if (operator.name().equals(name)) {
                return operator;
            }
        }
        return null;
    }

    public String toLowerCase() {
        return name().toLowerCase();
    }

    public boolean isAdmin() {
        return this == ADMIN;
    }

    public boolean isUser() {
        return this == USER;
    }

    public boolean isWorker() {
        return this == WORKER;
    }

    public String getName() {
        return name();
    }
}
