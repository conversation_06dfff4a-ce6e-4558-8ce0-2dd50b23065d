package point.common.constant;

public enum AntisocialStatus {
    BEFORE_EXAMINATION("審査前"),
    OK("OK"),
    CONFIRMATION_REQUIRED("要確認"),
    NG("NG");

    public final String displayName;

    AntisocialStatus(final String displayName) {
        this.displayName = displayName;
    }

    public static AntisocialStatus valueOfName(String name) {
        for (AntisocialStatus antisocialStatus : values()) {
            if (antisocialStatus.name().equals(name)) {
                return antisocialStatus;
            }
        }
        return null;
    }
}
