package point.common.constant;

import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Getter;

// 有効な通貨一覧を取得する場合はcurrencyConfig(enabled=true)を使用すること
// currencyConfigの無効(enabled=false)通貨もここで保持すること。ここのみに存在する分は問題なし
public enum Currency {
    // fiat
    JPY("JPY", 0, 0, 0, "", "運用ポイント"),
    POINT("POINT", 0, 0, 0, "", "ポイント"),
    BALC("BALC", 6, 18, 0, "", "バランスコース"),
    BTC("BTC", 8, 18, 0, "", ""),
    ACTC("ACTC", 6, 18, 0, "", "アクティブコース"),
    USDC("USDC", 6, 18, 0, "", ""),
    SOL("SOL", 6, 18, 0, "", ""),
    DOGE("DOGE", 6, 18, 0, "", ""),
    XRP("XRP", 6, 18, 0, "", ""),
    AVAX("AVAX", 8, 18, 0, "", ""),
    ADA("ADA", 8, 18, 0, "", ""),
    ETH("ETH", 8, 18, 0, "", "");

    public static Currency valueOfName(String name) {
        for (Currency currency : values()) {
            if (currency.name().equals(name)) {
                return currency;
            }
        }

        return null;
    }

    @Getter private final String label;

    @Getter private final int precision;

    @Getter private final int operatePrecision;

    @Getter private final int scope;

    @Getter private final String chain;

    @Getter private final String description;

    Currency(
            String label,
            int precision,
            int operatePrecision,
            int scope,
            String chain,
            String description) {
        this.label = label;
        this.precision = precision;
        this.operatePrecision = operatePrecision;
        this.scope = scope;
        this.chain = chain;
        this.description = description;
    }

    public String getName() {
        return name();
    }

    public String toLowerCase() {
        return name().toLowerCase();
    }

    public String toCamelCase() {
        return name().charAt(0) + name().toLowerCase().substring(1);
    }

    public boolean isJpy() {
        return this == JPY;
    }

    public boolean isPoint() {
        return this == POINT;
    }

    public BigDecimal getScaledAmount(BigDecimal amount, RoundingMode roundingMode) {
        return amount.setScale(precision, roundingMode);
    }

    public BigDecimal getScaledAmount(BigDecimal amount) {
        return getScaledAmount(amount, RoundingMode.HALF_UP);
    }

    public BigDecimal getOperateScaledAmount(BigDecimal amount, RoundingMode roundingMode) {
        return amount.setScale(operatePrecision, roundingMode);
    }

    public BigDecimal getOperateScaledAmount(BigDecimal amount) {
        return getOperateScaledAmount(amount, RoundingMode.HALF_UP);
    }
}
