package point.common.constant;

import java.util.Arrays;
import java.util.List;
import lombok.Getter;

public enum PersonalIdType {
    DRIVERS_LICENSE_OR_HISTORY("運転免許証/運転経歴証明書", true, Arrays.asList("1_front", "2_back")),
    MYNUMBER_CARD("マイナンバーカード", true, Arrays.asList("1_front")),
    RESIDENT_CARD_OR_CERTIFICATE("在留カード/特別永住者証明書", true, Arrays.asList("1_front", "2_back")),
    PASSPORT("パスポート", true, Arrays.asList("1_face", "2_information")),
    BASIC_RESIDENCE_REGISTRATION_CARD("住民票の写し", true, Arrays.asList("1_front")),
    HEALTH_INSURANCE("健康保険証", false, Arrays.asList("1_front", "2_back")),
    CERTIFICATE_OF_RESIDENCE("住民票", false, Arrays.asList("1_front")),
    SOCIAL_INSURANCE_RECEIPT("社会保険の領収証", false, Arrays.asList("1_front")),
    TAX_PAYMENT_CERTIFICATE("国税、地方税の領収書または納税証明書", false, Arrays.asList("1_front")),
    SEAL_REGISTRATION_CERTIFICATE("印鑑登録証明書の写し", false, Arrays.asList("1_front")),
    UTILITY_BILL_PAYMENT_RECEIPT("公共料金の領収書", false, Arrays.asList("1_front"));

    @Getter private final String idName; // 日本語名
    @Getter private final boolean selfy; // 顔写真の有無
    @Getter private final List<String> imageNameList; // 画像名(front, back, etc.)

    PersonalIdType(String idName, boolean selfy, List<String> imageNameList) {
        this.idName = idName;
        this.selfy = selfy;
        this.imageNameList = imageNameList;
    }
}
