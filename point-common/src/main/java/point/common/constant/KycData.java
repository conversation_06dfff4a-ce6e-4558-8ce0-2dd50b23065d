package point.common.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KycData {
    NONE("0", "データなし"), //
    EXISTS("1", "データあり");

    private String code;

    private String label;

    @JsonCreator
    public static KycData from(final String code) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
