package point.common.constant;

/** BTC_PRICE, OTHER_ASSET, */
public enum SharedRecordType {
    INVEST_TRADE_HIST_SHARED,
    OPERATE_TRADE_HIST_SHARED,
    REWARD_HIST_SHARED;

    public ChoiceActivityRuleEnum getChoiceActivityRule() {
        return switch (this) {
            case INVEST_TRADE_HIST_SHARED, OPERATE_TRADE_HIST_SHARED ->
                    ChoiceActivityRuleEnum.CROP_HARVESTED_SHARED;
            case REWARD_HIST_SHARED -> ChoiceActivityRuleEnum.ELECTION_RESULTS_OF_VOTE;
            default -> null;
        };
    }

    public ChoiceActivityFunction getChoiceActivityFunction() {
        return switch (this) {
            case INVEST_TRADE_HIST_SHARED, OPERATE_TRADE_HIST_SHARED ->
                    ChoiceActivityFunction.CROP_HARVESTED_SHARED;
            case REWARD_HIST_SHARED -> ChoiceActivityFunction.ELECTION_RESULTS_OF_VOTE;
            default -> null;
        };
    }
}
