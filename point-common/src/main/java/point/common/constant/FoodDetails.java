package point.common.constant;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FoodDetails implements Serializable {

    // 取引時間
    private Date tradingHours;

    // 実現損益
    private BigDecimal realizedProfitAndLoss;

    // 獲得できた経験値
    private Long experienceGained;

    // same as userCropStatus.growthStageId
    private String treeLevel;
}
