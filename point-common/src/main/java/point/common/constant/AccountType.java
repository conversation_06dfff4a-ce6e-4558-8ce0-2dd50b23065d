package point.common.constant;

import lombok.Getter;

public enum AccountType {
    NORMAL("普通"),
    CURRENT("当座"),
    SAVINGS("貯蓄"),
    NOTICE("通知"),
    FIXED("定期"),
    INSTALLMENT("積立定期");

    @Getter private final String label;

    public static AccountType valueOfName(String name) {
        for (AccountType accountType : values()) {
            if (accountType.name().equals(name)) {
                return accountType;
            }
        }
        return null;
    }

    public String getName() {
        return name();
    }

    AccountType(String label) {
        this.label = label;
    }
}
