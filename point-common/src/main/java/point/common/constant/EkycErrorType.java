package point.common.constant;

import java.util.List;
import java.util.stream.Collectors;

public enum EkycErrorType {
    Q1("ご提出いただきました本人確認書類と顔写真が一致しなかったため、本人確認が取れませんでした。\n" + "改めて、本人確認書類と顔写真を撮影いただきご提出ください。"),

    Q2(
            "ご提出いただきました本人確認書類が、ご指定いただきました確認書類ではございませんでした。\n"
                    + "申請時に後指定いただきました本人確認書類中から有効なもの一点を改めてご提出ください。"),

    Q3(
            "ご提出いただきました本人確認書類の画像が不足しておりましたため、本人情報の\n"
                    + "ご登録が完了いたしませんでした。\n"
                    + "改めて、本人確認書類を撮影いただき、必要な面を全てご提出ください。"),

    Q4("ご提出いただきました本人確認書類のお名前の確認ができず、本人情報のご登録が完了いたしませんでした。\n" + "お名前が鮮明に確認出来る状態で、再度本人確認書類をご提出ください。"),

    Q5(
            "本人確認書類に記載されたお名前とご登録いただきましたお名前が相違しておりましたため、\n"
                    + "本人情報のご登録が完了いたしませんでした。\n"
                    + "ご登録情報をご確認いただき、正しいお名前で再度ご登録ください。"),

    Q6(
            "ご提出いただきました本人確認書類の生年月日の確認ができず、本人情報のご登録が完了いたしませんでした。\n"
                    + "生年月日が鮮明に確認出来る状態で、再度本人確認書類をご提出ください。"),

    Q7(
            "本人確認書類に記載された生年月日とご登録いただきましたご生年月日が相違\n"
                    + "しておりましたため、本人情報のご登録が完了いたしませんでした。\n"
                    + "ご登録情報をご確認いただき、正しいご生年月日で再度ご登録ください。"),

    Q8(
            "ご提出いただきました本人確認書類の住所の確認ができず、本人情報のご登録が\n"
                    + "完了いたしませんでした。\n"
                    + "住所が鮮明に確認出来る状態で、再度本人確認書類をご提出ください。\n"
                    + "なお、ご提出には再度ご登録情報からお手続きを進めてください。"),

    Q9(
            "本人確認書類に記載された住所とご登録いただきましたご住所が相違\n"
                    + "しておりましたため、本人情報のご登録が完了いたしませんでした。\n"
                    + "ご登録情報をご確認いただき、正しいご住所で再度ご登録ください。"),

    Q10(
            "ご提出いただきました本人確認書類の公印の確認ができず、本人情報のご登録が\n"
                    + "完了いたしませんでした。\n"
                    + "\n"
                    + "公印が鮮明に確認出来る状態で、再度本人確認書類をご提出ください。\n"
                    + "確認書類自体の公印が、こすれ、欠け等で鮮明でない場合は別の確認書類をご提出いただくか、\n"
                    + "確認書類を更新いただきご提出ください。\n"
                    + "更新手続き等で裏面に記載されている公印が鮮明でない場合は、更新された場所でお手続きをいただき、公印が鮮明に確認出来る状態でご提出ください。"),

    Q11(
            "ご提出いただきました本人確認書類の有効期限の確認ができず、本人情報のご登録が\n"
                    + "完了いたしませんでした。\n"
                    + "有効期限が鮮明に確認出来る状態で、再度本人確認書類をご提出ください。"),

    Q12(
            "ご提出いただきました本人確認書類の有効期限が提出日に過ぎておりましたため、本人情報の\n"
                    + "ご登録が完了いたしませんでした。\n"
                    + "改めて、有効期限内の本人確認書類をご提出ください。"),

    Q13("ご提出いただきました本人確認書類の有効性が確認できないため、本人情報の\n" + "ご登録が完了いたしませんでした。\n" + "改めて、本人確認書類をご提出ください。"),

    Q14(
            "ご提出いただきました本人確認書類の有効性が確認できないため、本人情報の\n"
                    + "ご登録が完了いたしませんでした。\n"
                    + "改めて、本人確認書類の原本をお撮りいただいたものをご提出ください。"),

    Q15(
            "ご提出いただきました本人確認書類の厚みの確認ができず、本人情報のご登録が\n"
                    + "完了いたしませんでした。\n"
                    + "本人確認書類の厚みが確認出来る状態で、再度本人確認書類をご提出ください。");
    private String description;

    EkycErrorType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static List<EkycErrorType> getEkycError(List<String> name) {
        return name.stream().map(EkycErrorType::valueOf).collect(Collectors.toList());
    }
}
