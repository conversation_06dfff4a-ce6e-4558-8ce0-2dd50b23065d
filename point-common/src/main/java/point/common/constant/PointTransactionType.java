package point.common.constant;

import lombok.Getter;

@Getter
public enum PointTransactionType {
    POINT_GRANTING("031", "031:ポイント付与"),
    POINT_USING("032", "032:ポイント利用"),
    POINT_GRANTING_AND_USING("033", "033:ポイント付与＋ポイント利用"),
    POINT_TRANSFER("051", "051:ポイント引継（随時移行）");

    private final String code;
    private final String description;

    PointTransactionType(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
