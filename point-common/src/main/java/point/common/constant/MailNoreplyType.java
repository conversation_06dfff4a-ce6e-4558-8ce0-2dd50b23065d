package point.common.constant;

public enum MailNoreplyType {
    ACCOUNT_CREATED,
    PASSWORD_COMPLETE,
    USER_EKYC_STARTED,
    USER_KYC_STATUS_CHANGED_EKYC_SUCCESS,
    EKYC_BPO_FAILED_ERROR,
    INFORMATION_REQUIRED, // 不備あり（却下）
    ACCOUNT_OPENING_DONE,
    DONE, // 承認
    REJECTED, // 謝絶
    ACCOUNT_CLOSED, // 口座解約
    ACCOUNT_DUPLICATION, // 口座重複
    DEPOSIT_DONE,
    FIAT_WITHDRAWAL_APPLY,
    FIAT_WITHDRAWAL_DONE,
    WITHDRAWAL_DONE,
    WIT<PERSON>RAWAL_REJECTED,
    WIT<PERSON>RAWAL_FAILED,
    DEPOSIT_DETECTED,
    DEPOSIT_RECOGNITION_CODE,

    MFA_CODE,
    SMS_CODE,

    POS_ORDE<PERSON>,
    P<PERSON>_TRA<PERSON>,
    POS_FAILED_ORDER,
    TRA<PERSON><PERSON><PERSON>_DONE,
    TRANSFER_RECEIVED_DONE,

    BALANCE_NOTIFY,
    VOTE_REWARD_RESULT_NOTIFY
}
