package point.common.constant;

public enum OrderSide {
    SELL("売付け"),
    BUY("買付け");

    public final String displayName;

    OrderSide(String displayName) {
        this.displayName = displayName;
    }

    public static OrderSide valueOfName(String name) {
        for (OrderSide orderSide : values()) {
            if (orderSide.name().equals(name)) {
                return orderSide;
            }
        }
        return null;
    }

    public static OrderSide valueOfLowerCase(String name) {
        for (OrderSide orderSide : OrderSide.values()) {
            if (orderSide.toLowerCase().equals(name)) {
                return orderSide;
            }
        }

        return null;
    }

    public boolean isSell() {
        return this == SELL;
    }

    public boolean isBuy() {
        return this == BUY;
    }

    public String getName() {
        return name();
    }

    public String toLowerCase() {
        return name().toLowerCase();
    }

    public OrderSide getOpposite() {
        return this == SELL ? BUY : SELL;
    }
}
