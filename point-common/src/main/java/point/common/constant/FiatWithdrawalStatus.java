package point.common.constant;

import java.util.Arrays;
import java.util.List;

public enum FiatWithdrawalStatus {
    APPROVING(true), // 受付完了
    WAITTING(true), // 待機中
    DONE(false), // 完了
    TRANSACTION_ERROR(false), // 出金エラー
    REJECTED(false),
    SYSTEM_ERROR(false);

    public final Boolean isPending;

    FiatWithdrawalStatus(Boolean isPending) {
        this.isPending = isPending;
    }

    public static List<FiatWithdrawalStatus> getPendingList() {
        return Arrays.stream(FiatWithdrawalStatus.values()).filter(it -> it.isPending).toList();
    }

    public static FiatWithdrawalStatus valueOfName(String name) {
        for (FiatWithdrawalStatus withdrawalStatus : values()) {
            if (withdrawalStatus.name().equals(name)) {
                return withdrawalStatus;
            }
        }

        return null;
    }
}
