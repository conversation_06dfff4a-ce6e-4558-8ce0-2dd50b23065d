package point.common.constant;

import java.util.Arrays;
import java.util.Optional;
import lombok.Getter;

@Getter
public enum CurrencyTypeMonster {
    BTC_INVEST(101L, "BTC実", "BTC"),
    ETH_INVEST(102L, "ETH実", "ETH"),
    XRP_INVEST(103L, "XRP実", "XRP"),
    BALC_OPERATE(201L, "バランス実", "バランス"),
    BTC_OPERATE(202L, "BTC実", "BTC"),
    ACTC_OPERATE(203L, "アクティブ実", "アクティブ"),
    USDC_OPERATE(204L, "USDC実", "USDC");
    private final Long id;
    private final String fruitName;
    private final String currencyPair;

    CurrencyTypeMonster(long id, String fruitName, String currencyPair) {
        this.id = id;
        this.fruitName = fruitName;
        this.currencyPair = currencyPair;
    }

    public String getName() {
        return name();
    }

    public static String getMonsterFruitNameById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<CurrencyTypeMonster> result =
                Arrays.stream(CurrencyTypeMonster.values())
                        .filter(currencyType -> currencyType.getId().equals(id))
                        .findFirst();
        return result.map(CurrencyTypeMonster::getFruitName).orElse(null);
    }

    public static String getMonsterCurrencyTypeById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<CurrencyTypeMonster> result =
                Arrays.stream(CurrencyTypeMonster.values())
                        .filter(currencyType -> currencyType.getId().equals(id))
                        .findFirst();
        return result.map(CurrencyTypeMonster::getCurrencyPair).orElse(null);
    }
}
