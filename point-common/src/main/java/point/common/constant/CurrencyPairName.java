package point.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CurrencyPairName {
    BALC_JPY(201L, "Balance", "バランスコース＿運用ポイント"),
    BTC_JPY(202L, "BTC", "BTC＿運用ポイント"),
    ACTC_JPY(203L, "Active", "アクティブコース＿運用ポイント"),
    USDC_JPY(204L, "USDC", "USDC_運用ポイント");

    private final Long id;

    private final String currencyPair;

    private final String currencyPairName;

    public static String getCurrencyPairNameById(Long id) {
        for (CurrencyPairName currencyPairName : CurrencyPairName.values()) {
            if (currencyPairName.getId().equals(id)) {
                return currencyPairName.getCurrencyPairName();
            }
        }
        return null;
    }
}
