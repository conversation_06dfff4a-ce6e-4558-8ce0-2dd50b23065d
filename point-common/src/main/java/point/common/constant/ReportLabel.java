package point.common.constant;

import lombok.Getter;

public class ReportLabel {

    /** 注文一覧用ヘッダー */
    public enum HeaderOrder {
        ID("注文ID"),
        USER_ID("ユーザーID"),
        CURRENCY_PAIR("通貨ペア"),
        AMOUNT("数量"),
        ORDER_SIDE("売買区分"),
        ORDER_TYPE("注文種別"),
        ORDER_CHANNEL("注文チャンネル"),
        PRICE("価格"),
        AVERAGE_PRICE("平均約定価格"),
        REMAINING_AMOUNT("未約定数量"),
        ORDER_STATUS("注文ステータス"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時"),
        TRADE_TYPE("取引タイプ"),
        NOTES("メモ");
        @Getter private final String label;

        public static HeaderOrder valueOfName(String name) {
            for (HeaderOrder header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderOrder(String label) {
            this.label = label;
        }
    }

    /** 約定一覧用ヘッダー */
    public enum HeaderTrade {
        ID("約定ID"),
        USER_ID("ユーザーID"),
        ORDER_ID("注文ID"),
        CURRENCY_PAIR("通貨ペア"),
        AMOUNT("数量"),
        ORDER_SIDE("売買区分"),
        ORDER_TYPE("注文種別"),
        TRADE_ACTION("メイカーテイカー区分"),
        ORDER_CHANNEL("注文チャンネル"),
        PRICE("価格"),
        FEE("手数料"),
        ASSET_AMOUNT("約定総額"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時"),
        TRADE_TYPE("取引タイプ");
        @Getter private final String label;

        public static HeaderTrade valueOfName(String name) {
            for (HeaderTrade header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderTrade(String label) {
            this.label = label;
        }
    }

    /** 管理画面約定一覧用ヘッダー */
    public enum HeaderTradeAdmin {
        ID("約定ID"),
        USER_ID("ユーザーID"),
        ORDER_ID("注文ID"),
        TARGET_USER_ID("相手方ユーザーID"),
        TARGET_ORDER_ID("相手方注文ID"),
        CURRENCY_PAIR("通貨ペア"),
        AMOUNT("数量"),
        ORDER_SIDE("売買区分"),
        ORDER_TYPE("注文種別"),
        TRADE_ACTION("メイカーテイカー区分"),
        ORDER_CHANNEL("注文チャンネル"),
        PRICE("価格"),
        FEE("手数料"),
        ASSET_AMOUNT("約定総額"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時"),
        TRADE_TYPE("取引タイプ");
        @Getter private final String label;

        public static HeaderTradeAdmin valueOfName(String name) {
            for (HeaderTradeAdmin header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderTradeAdmin(String label) {
            this.label = label;
        }
    }

    /** 資産一覧用ヘッダー */
    public enum HeaderAsset {
        USER_ID("顧客ID"),
        USER_NAME("顧客名"),
        EMAIL("メールアドレス"),
        CURRENCY("通貨"),
        ONHAND_AMOUNT("保有資産"),
        LOCKED_AMOUNT("ロック資産"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時");
        @Getter private final String label;

        public static HeaderAsset valueOfName(String name) {
            for (HeaderAsset header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderAsset(String label) {
            this.label = label;
        }
    }

    /** 年間取引報告書用ヘッダー */
    public enum HeaderAssetSummary {
        USER_ID("顧客ID"),
        USER_NAME("顧客名"),
        EMAIL("メールアドレス"),
        TARGET_AT("対象日"),
        CURRENCY("通貨"),
        CURRENT_AMOUNT("残高"),
        JPY_CONVERSION("円換算レート"),
        DEPOSIT_AMOUNT("入金額"),
        DEPOSIT_AMOUNT_JPY("入金額（円貨換算）"),
        DEPOSIT_FEE("入金手数料"),
        DEPOSIT_FEE_JPY("入金手数料（円貨換算）"),
        WITHDRAWAL_AMOUNT("出金額"),
        WITHDRAWAL_AMOUNT_JPY("出金額（円貨換算）"),
        WITHDRAWAL_FEE("出金手数料"),
        WITHDRAWAL_FEE_JPY("出金手数料（円貨換算）"),
        POS_TRADE_BUY_AMOUNT("約定数量（販売所）（買い）"),
        POS_TRADE_BUY_AMOUNT_JPY("約定数量（販売所）（買い）（円貨換算）"),
        POS_TRADE_SELL_AMOUNT("約定数量（販売所）（売り）"),
        POS_TRADE_SELL_AMOUNT_JPY("約定数量（販売所）（売り）（円貨換算）"),
        POS_TRADE_FEE("取引手数料（販売所）"),
        POS_TRADE_FEE_JPY("取引手数料（販売所）（円貨換算）"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時");
        @Getter private final String label;

        public static HeaderAssetSummary valueOfName(String name) {
            for (HeaderAssetSummary header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderAssetSummary(String label) {
            this.label = label;
        }
    }

    /** 販売所集計用ヘッダー */
    public enum HeaderPointSummary {
        ID("ID"),
        TARGET_AT("対象日"),
        CURRENCY("通貨"),
        CURRENT_AMOUNT("保有数量"),
        JPY_CONVERSION("円貨換算額"),
        DEPOSIT_AMOUNT("入金額"),
        DEPOSIT_AMOUNT_JPY("入金額（円貨換算）"),
        DEPOSIT_FEE("入金手数料"),
        DEPOSIT_FEE_JPY("入金手数料（円貨換算）"),
        WITHDRAWAL_AMOUNT("出金額"),
        WITHDRAWAL_AMOUNT_JPY("出金額（円貨換算）"),
        WITHDRAWAL_FEE("出金手数料"),
        WITHDRAWAL_FEE_JPY("出金手数料（円貨換算）"),
        TRANSACTION_FEE("トランザクション手数料"),
        TRANSACTION_FEE_JPY("トランザクション手数料（円貨換算）"),
        SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT("約定数量（投資）（買い）"),
        SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT_JPY("約定数量（投資）（買い）（円貨換算）"),
        SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT("約定数量（投資）（売り）"),
        SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT_JPY("約定数量（投資）（売り）（円貨換算）"),
        SIMPLE_MARKET_POS_TRADE_FEE("約定手数料（投資）"),
        SIMPLE_MARKET_POS_TRADE_FEE_JPY("約定手数料（投資）（円貨換算）"),
        REWARD_ACCUMULATE("ステーキング報酬（数量）"),
        REWARD_ACCUMULATE_JPY("ステーキング報酬（円貨換算）"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時");

        @Getter private final String label;

        public static HeaderPointSummary valueOfName(String name) {
            for (HeaderPointSummary header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderPointSummary(String label) {
            this.label = label;
        }
    }

    public enum HeaderPointSummaryOperate {
        ID("ID"),
        TARGET_AT("対象日"),
        CURRENCY("通貨"),
        CURRENT_AMOUNT("保有数量"),
        JPY_CONVERSION("円貨換算額"),
        DEPOSIT_AMOUNT("入金額"),
        DEPOSIT_AMOUNT_JPY("入金額（円貨換算）"),
        DEPOSIT_FEE("入金手数料"),
        DEPOSIT_FEE_JPY("入金手数料（円貨換算）"),
        WITHDRAWAL_AMOUNT("出金額"),
        WITHDRAWAL_AMOUNT_JPY("出金額（円貨換算）"),
        WITHDRAWAL_FEE("出金手数料"),
        WITHDRAWAL_FEE_JPY("出金手数料（円貨換算）"),
        TRANSACTION_FEE("トランザクション手数料"),
        TRANSACTION_FEE_JPY("トランザクション手数料（円貨換算）"),
        SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT("約定数量（運用）（買い）"),
        SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT_JPY("約定数量（運用）（買い）（円貨換算）"),
        SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT("約定数量（運用）（売り）"),
        SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT_JPY("約定数量（運用）（売り）（円貨換算）"),
        SIMPLE_MARKET_POS_TRADE_FEE("約定手数料（運用）"),
        SIMPLE_MARKET_POS_TRADE_FEE_JPY("約定手数料（運用）（円貨換算）"),
        REWARD_ACCUMULATE("ステーキング報酬（数量）"),
        REWARD_ACCUMULATE_JPY("ステーキング報酬（円貨換算）"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時");

        @Getter private final String label;

        public static HeaderPointSummaryOperate valueOfName(String name) {
            for (HeaderPointSummaryOperate header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderPointSummaryOperate(String label) {
            this.label = label;
        }
    }

    /** 年間取引報告書用ヘッダー */
    public enum HeaderHighValueTrader {
        USER_ID("ユーザーID"),
        POS_ORDER_LIMITS("指定額以上の指値注文数"),
        POS_TRADE_MARKETS("指定額以上の成行注文約定数"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時");
        @Getter private final String label;

        public static HeaderHighValueTrader valueOfName(String name) {
            for (HeaderHighValueTrader header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderHighValueTrader(String label) {
            this.label = label;
        }
    }

    public enum HeaderInvestmentPurposeDeviationUser {
        USER_ID("ユーザーID"),
        TRADES("約定数"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時");
        @Getter private final String label;

        public static HeaderInvestmentPurposeDeviationUser valueOfName(String name) {
            for (HeaderInvestmentPurposeDeviationUser header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderInvestmentPurposeDeviationUser(String label) {
            this.label = label;
        }
    }

    public enum HeaderFinancialAssetsDeviationUser {
        USER_ID("ユーザーID"),
        OVER_TRADES("約定数(閾値以上)"),
        CREATED_AT("作成日時"),
        UPDATED_AT("更新日時");
        @Getter private final String label;

        public static HeaderFinancialAssetsDeviationUser valueOfName(String name) {
            for (HeaderFinancialAssetsDeviationUser header : values()) {
                if (header.name().equals(name)) {
                    return header;
                }
            }
            return null;
        }

        HeaderFinancialAssetsDeviationUser(String label) {
            this.label = label;
        }
    }

    public enum OrderType {
        MARKET("成行"),
        SIMPLE_MARKET("販売所"),
        LIMIT("指値"),
        STOP("逆指値");
        @Getter private final String label;

        public static OrderType valueOfName(String name) {
            for (OrderType orderType : values()) {
                if (orderType.name().equals(name)) {
                    return orderType;
                }
            }
            return null;
        }

        OrderType(String label) {
            this.label = label;
        }
    }

    public enum OrderSide {
        BUY("買い"),
        SELL("売り");
        @Getter private final String label;

        public static OrderSide valueOfName(String name) {
            for (OrderSide orderSide : values()) {
                if (orderSide.name().equals(name)) {
                    return orderSide;
                }
            }
            return null;
        }

        OrderSide(String label) {
            this.label = label;
        }
    }

    public enum OrderChannel {
        PC_WEB("PC_WEB"),
        API_KEY("API_KEY"),
        IPHONE("IPHONE"),
        ANDROID("ANDROID"),
        UNKNOWN("UNKNOWN");
        @Getter private final String label;

        public static OrderChannel valueOfName(String name) {
            for (OrderChannel orderChannel : values()) {
                if (orderChannel.name().equals(name)) {
                    return orderChannel;
                }
            }
            return null;
        }

        OrderChannel(String label) {
            this.label = label;
        }
    }

    public enum TradeAction {
        MAKER("MAKER"),
        TAKER("TAKER");
        @Getter private final String label;

        public static TradeAction valueOfName(String name) {
            for (TradeAction tradeAction : values()) {
                if (tradeAction.name().equals(name)) {
                    return tradeAction;
                }
            }
            return null;
        }

        TradeAction(String label) {
            this.label = label;
        }
    }

    public enum OrderStatus {
        WAITING("処理待ち"),
        UNFILLED("未約定"),
        PARTIALLY_FILLED("部分約定"),
        FULLY_FILLED("約定済み"),
        CANCELED_UNFILLED("未約定キャンセル済"),
        CANCELED_PARTIALLY_FILLED("部分約定キャンセル済");
        @Getter private final String label;

        public static OrderStatus valueOfName(String name) {
            for (OrderStatus orderStatus : values()) {
                if (orderStatus.name().equals(name)) {
                    return orderStatus;
                }
            }
            return null;
        }

        OrderStatus(String label) {
            this.label = label;
        }
    }

    public enum PosOrderStatus {
        WAITING("処理待ち"),
        FAILED_FILLED("注文失効"),
        PARTIALLY_FILLED("部分約定"),
        FULLY_FILLED("約定済み");

        @Getter private final String label;

        public static PosOrderStatus valueOfName(String name) {
            for (PosOrderStatus posOrderStatus : values()) {
                if (posOrderStatus.name().equals(name)) {
                    return posOrderStatus;
                }
            }
            return null;
        }

        PosOrderStatus(String label) {
            this.label = label;
        }
    }

    public enum Authority {
        PERSONAL("個人"),
        SPECIAL("特別");
        @Getter private final String label;

        public static Authority valueOfName(String name) {
            for (Authority authority : values()) {
                if (authority.name().equals(name)) {
                    return authority;
                }
            }
            return null;
        }

        Authority(String label) {
            this.label = label;
        }
    }

    public enum UserStatus {
        REVIEWING("仮登録"),
        ACTIVE("有効"),
        BANNED("強制停止"),
        UNTRADABLE("取引停止"),
        UNWITHDRAWABLE("出金停止"),
        LEFT("退会済み");
        @Getter private final String label;

        public static UserStatus valueOfName(String name) {
            for (UserStatus userStatus : values()) {
                if (userStatus.name().equals(name)) {
                    return userStatus;
                }
            }
            return null;
        }

        UserStatus(String label) {
            this.label = label;
        }
    }

    public enum KycStatus {
        // Todo: このKycStatus enumは削除予定
        WAITING_SET_PWD("パスワード設定待ち"),
        NONE("本人確認未申請"),
        DOCUMENT_WAITING_APPLY("本人確認書類受領まち"),
        DOCUMENT_RECEIVED("本人確認書類提出済"),
        DOCUMENT_CONFIRMING("本人確認資料確認中"),
        DOCUMENT_CONFIRMED("本人確認済み"),
        DOCUMENT_REJECTED("本人確認却下"),
        REFINITIV_CHECKED("Refinitiv連携済"),
        FIRST_SCREENING("1次審査中"),
        FIRST_SCREEN_FINISHED("1次審査完了"),
        SECOND_SCREENING("2次審査中"),
        AML_FIRST_SCREEN_WAITING("AML1次審査待ち"),
        AML_FIRST_SCREENING("AML1次審査中"),
        AML_SECOND_SCREEN_WAITING("AML2次審査待ち"),
        AML_SECOND_SCREENING("AML2次審査中"),
        EDD_REQUEST("EDD依頼"),
        EDD_REQUEST_CONFIRMING("EDD依頼対応中"),
        AML_EDD_SCREEN_WAITING("AML EDD審査待ち"),
        INFORMATION_REQUIRED("不備あり(却下)"),
        ACCOUNT_OPENING_DONE("口座開設完了"),
        REJECTED("謝絶"),
        DEPARTMENT_RESPONSIBLE_PERSON_CONFIRM("事務部責任者確認"),
        AML_RESPONSIBLE_PERSON_CONFIRM("AML責任者確認"),
        CHANGE_ATTR_INQUIRED("変更問い合わせあり"),
        CHANGE_ATTR_APPLY_REQUESTED("変更申請提出依頼"),
        URL_EXPIRED("URL失効"),
        RECONFIRM_REQUIRED("要再審査"),
        DETERMINATION_OF_SUSPICIOUS_TRANSACTIONS("疑わしい取引の判断"),
        ACCOUNT_FREEZE("口座凍結"),
        ACCOUNT_CLOSED("口座解約済"),
        DONE("承認（変更完了）"),
        CANCEL_APPLICATION("申込キャンセル"),
        ACCOUNT_DUPLICATION("口座重複");

        @Getter private final String label;

        public static KycStatus valueOfName(String name) {
            for (KycStatus kycStatus : values()) {
                if (kycStatus.name().equals(name)) {
                    return kycStatus;
                }
            }
            return null;
        }

        KycStatus(String label) {
            this.label = label;
        }
    }

    public enum Occupation {
        OFFICER(1, "会社役員 / 団体役員"),
        WORKER(2, "会社員 / 団体職員"),
        CIVILSERVANT(3, "公務員"),
        DOCTOR(4, "医師"),
        PROFESSIONAL(5, "弁護士 / 会計士 / 税理士 / 司法書士 / その他士業"),
        TEACHER(6, "教職員"),
        PROPRIETORSHIP(7, "個人事業主 / 自営業"),
        PARTTIMEWORKER(8, "パート / アルバイト"),
        HOMEMAKER(9, "専業主婦 / 主夫"),
        STUDENT(10, "学生"),
        UNEMPLOYED(11, "無職");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static Occupation valueOfName(String name) {
            for (Occupation occupation : values()) {
                if (occupation.name().equals(name)) {
                    return occupation;
                }
            }
            return null;
        }

        public static Occupation valueOfCode(Integer code) {
            for (Occupation occupation : values()) {
                if (occupation.code.equals(code)) {
                    return occupation;
                }
            }
            return null;
        }

        private Occupation(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum Industry {
        AGRICULTURE(1, "農業 / 林業 / 漁業"),
        MINING(2, "鉱業 / 採石業 / 砂利採取業"),
        CONSTRUCTION(3, "建設業 / 製造業"),
        ELECTRICITY(4, "電気 / ガス / 熱供給 / 水道業"),
        TRANSPORTATION(5, "情報通信業 / 運輸業 / 郵便業"),
        WHOLESALE(6, "卸売業 / 小売業"),
        FINANCE(7, "金融業 / 保険業 / 暗号資産交換業"),
        REALESTATE(8, "不動産業 / 物品賃貸業"),
        ACADEMIC(9, "学術研究 / 専門 / 技術サービス業"),
        HOTEL(10, "宿泊業 / 飲食サービス業"),
        ENTERTAINMENT(11, "生活関連サービス業 / 娯楽業"),
        EDUCATION(12, "教育 / 学習支援業"),
        MEDICAL(13, "医療 / 福祉"),
        WASTETREATMENT(14, "廃棄物処理業"),
        OTHERS(15, "その他");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static Industry valueOfName(String name) {
            for (Industry industry : values()) {
                if (industry.name().equals(name)) {
                    return industry;
                }
            }
            return null;
        }

        public static Industry valueOfCode(Integer code) {
            for (Industry industry : values()) {
                if (industry.code.equals(code)) {
                    return industry;
                }
            }
            return null;
        }

        private Industry(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum Income {
        INCOME1(1, "100 万円未満"),
        INCOME2(2, "100 万円 ~ 300 万円未満"),
        INCOME3(3, "300 万円 ~ 500 万円未満"),
        INCOME4(4, "500 万円 ~ 1,000 万円未満"),
        INCOME5(5, "1,000 万円 ~ 2,000 万円未満"),
        INCOME6(6, "2,000 万円 ~ 5,000 万円未満"),
        INCOME7(7, "5,000 万円 ~ 1 億円未満"),
        INCOME8(8, "1 億円以上");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static Income valueOfName(String name) {
            for (Income income : values()) {
                if (income.name().equals(name)) {
                    return income;
                }
            }
            return null;
        }

        public static Income valueOfCode(Integer code) {
            for (Income income : values()) {
                if (income.code.equals(code)) {
                    return income;
                }
            }
            return null;
        }

        private Income(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum Purpose {
        PURPOSE1(1, "仮想通貨の購入・売却のため"),
        PURPOSE2(2, "仮想通貨の受取・送付のため"),
        PURPOSE3(3, "レバレッジ取引を行うため"),
        PURPOSE4(4, "決済機能の利用のため"),
        PURPOSE5(5, "勉強・情報収集のため"),
        PURPOSE6(6, "その他サービスの利用のため");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static Purpose valueOfName(String name) {
            for (Purpose purpose : values()) {
                if (purpose.name().equals(name)) {
                    return purpose;
                }
            }
            return null;
        }

        public static Purpose valueOfCode(Integer code) {
            for (Purpose purpose : values()) {
                if (purpose.code.equals(code)) {
                    return purpose;
                }
            }
            return null;
        }

        private Purpose(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum PriceFrom {
        PRICEFROM1(1, "銀行などの預貯金"),
        PRICEFROM2(2, "投資（トレード収益）"),
        PRICEFROM3(3, "投資（利息・配当など上記以外の収入）"),
        PRICEFROM4(4, "不動産投資"),
        PRICEFROM5(5, "年金"),
        PRICEFROM6(6, "生活保護"),
        PRICEFROM7(7, "配偶者・家族の収入"),
        PRICEFROM8(8, "仕送り");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static PriceFrom valueOfName(String name) {
            for (PriceFrom priceFrom : values()) {
                if (priceFrom.name().equals(name)) {
                    return priceFrom;
                }
            }
            return null;
        }

        public static PriceFrom valueOfCode(Integer code) {
            for (PriceFrom priceFrom : values()) {
                if (priceFrom.code.equals(code)) {
                    return priceFrom;
                }
            }
            return null;
        }

        private PriceFrom(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum InvestmentPurpose {
        INVESTMENT_PURPOSE0(0, "短期投資"),
        INVESTMENT_PURPOSE1(1, "中期投資"),
        INVESTMENT_PURPOSE2(2, "長期投資"),
        INVESTMENT_PURPOSE3(3, "安全性重視"),
        INVESTMENT_PURPOSE4(4, "リスク重視");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static InvestmentPurpose valueOfName(String name) {
            for (InvestmentPurpose investmentPurpose : values()) {
                if (investmentPurpose.name().equals(name)) {
                    return investmentPurpose;
                }
            }
            return null;
        }

        public static InvestmentPurpose valueOfCode(Integer code) {
            for (InvestmentPurpose investmentPurpose : values()) {
                if (investmentPurpose.code.equals(code)) {
                    return investmentPurpose;
                }
            }
            return null;
        }

        private InvestmentPurpose(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum Experience {
        EXPERIENCE0(0, "未経験"),
        EXPERIENCE1(1, "1年未満"),
        EXPERIENCE2(2, "1-3年未満"),
        EXPERIENCE3(3, "3-5年未満"),
        EXPERIENCE4(4, "5年以上");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static Experience valueOfName(String name) {
            for (Experience experience : values()) {
                if (experience.name().equals(name)) {
                    return experience;
                }
            }
            return null;
        }

        public static Experience valueOfCode(Integer code) {
            for (Experience experience : values()) {
                if (experience.code.equals(code)) {
                    return experience;
                }
            }
            return null;
        }

        private Experience(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum ApplicationHistory {
        INTERNET(1, "インターネット検索"),
        SNS(2, "SNS"),
        INTRODUCTION(3, "紹介"),
        HOMEPAGE(4, "ホームページ"),
        ADVERTISEMENT(5, "広告"),
        CAMPAIGN(6, "キャンペーン"),
        OTHER(7, "その他");

        @Getter private final Integer code;
        @Getter private final String kanjiName;

        public static ApplicationHistory valueOfName(String name) {
            for (ApplicationHistory applicationHistory : values()) {
                if (applicationHistory.name().equals(name)) {
                    return applicationHistory;
                }
            }
            return null;
        }

        public static ApplicationHistory valueOfCode(Integer code) {
            for (ApplicationHistory applicationHistory : values()) {
                if (applicationHistory.code.equals(code)) {
                    return applicationHistory;
                }
            }
            return null;
        }

        private ApplicationHistory(Integer code, String kanjiName) {
            this.code = code;
            this.kanjiName = kanjiName;
        }
    }

    public enum FiatDepositStatus {
        DONE("完了"),
        UNKNOWN_DEPOSIT("不明入金"),
        UNKNOWN_DEPOSIT_INFORMED("不明入金連絡済"),

        UNKNOWN_DEPOSIT_WAITING_FOR_CHANGE("不明入金変更待ち"),
        REJECTED("キャンセル");
        @Getter private final String label;

        public static FiatDepositStatus valueOfName(String name) {
            for (FiatDepositStatus fiatDepositStatus : values()) {
                if (fiatDepositStatus.name().equals(name)) {
                    return fiatDepositStatus;
                }
            }
            return null;
        }

        FiatDepositStatus(String label) {
            this.label = label;
        }
    }

    public enum FiatWithdrawalStatus {
        APPROVING("申請中"),
        WAITTING("申請中"),
        DONE("完了"),
        TRANSACTION_ERROR("出金エラー"),
        REJECTED("キャンセル"),
        SYSTEM_ERROR("出金エラー");

        @Getter private final String label;

        public static FiatWithdrawalStatus valueOfName(String name) {
            for (FiatWithdrawalStatus fiatWithdrawalStatus : values()) {
                if (fiatWithdrawalStatus.name().equals(name)) {
                    return fiatWithdrawalStatus;
                }
            }
            return null;
        }

        FiatWithdrawalStatus(String label) {
            this.label = label;
        }
    }
}
