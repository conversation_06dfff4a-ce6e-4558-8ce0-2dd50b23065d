package point.common.constant;

import lombok.Getter;

/** ChoiceActivityRuleEnum */
@Getter
public enum ChoiceActivityRuleEnum {

    // -- THE ID HARDCODE HERE THAT MUST BE THE SAME AS IN THE DATABASE --
    LOGIN_OPERATION_COURSE(1L, "運用コースにログインする"),
    LOGIN_INVESTMENT_COURSE(2L, "投資コースにログインする"),
    PASS_QUIZ(3L, "クイズに正解する"),
    OPEN_INVESTMENT_ACCOUNT(4L, "投資コースで口座開設を完了させる"),
    ELECTION_RESULTS_OF_VOTE(5L, "Pontaビットコイン投票で当選結果を投稿する"),
    CROP_HARVESTED_SHARED(6L, "Pontaビットコin牧場で収穫した作物をシェアする"),
    BUY_SELL_TRADE(7L, "売買取引を行う"),
    RAISE_MONSTER_LEVEL_1(8L, "モンスターを育成→レベル1"),
    RAISE_MONSTER_LEVEL_2(9L, "モンスターを育成→レベル2"),
    RAISE_MONSTER_LEVEL_3(10L, "モンスターを育成→レベル3"),
    RAISE_MONSTER_LEVEL_4(11L, "モンスターを育成→レベル4"),
    RAISE_MONSTER_LEVEL_5(12L, "モンスターを育成→レベル5"),
    RAISE_MONSTER_LEVEL_6(13L, "モンスターを育成→レベル6"),
    RAISE_MONSTER_LEVEL_7(14L, "モンスターを育成→レベル7"),
    RAISE_MONSTER_LEVEL_8(15L, "モンスターを育成→レベル8"),
    RAISE_MONSTER_LEVEL_9(16L, "モンスターを育成→レベル9"),
    RAISE_MONSTER_LEVEL_10(17L, "モンスターを育成→レベル10"),
    RAISE_MONSTER_LEVEL_11(18L, "モンスターを育成→レベル11"),
    RAISE_MONSTER_LEVEL_12(19L, "モンスターを育成→レベル12"),
    RAISE_MONSTER_LEVEL_13(20L, "モンスターを育成→レベル13"),
    RAISE_MONSTER_LEVEL_14(21L, "モンスターを育成→レベル14"),
    RAISE_MONSTER_LEVEL_15(22L, "モンスターを育成→レベル15"),
    RAISE_MONSTER_LEVEL_16(23L, "モンスターを育成→レベル16"),
    RAISE_MONSTER_LEVEL_17(24L, "モンスターを育成→レベル17"),
    RAISE_MONSTER_LEVEL_18(25L, "モンスターを育成→レベル18"),
    RAISE_MONSTER_LEVEL_19(26L, "モンスターを育成→レベル19"),
    RAISE_MONSTER_LEVEL_20(27L, "モンスターを育成→レベル20"),
    RAISE_MONSTER_LEVEL_21(28L, "モンスターを育成→レベル21"),
    RAISE_MONSTER_LEVEL_22(29L, "モンスターを育成→レベル22"),
    RAISE_MONSTER_LEVEL_23(30L, "モンスターを育成→レベル23"),
    RAISE_MONSTER_LEVEL_24(31L, "モンスターを育成→レベル24"),
    RAISE_MONSTER_LEVEL_25(32L, "モンスターを育成→レベル25"),
    RAISE_MONSTER_LEVEL_26(33L, "モンスターを育成→レベル26"),
    RAISE_MONSTER_LEVEL_27(34L, "モンスターを育成→レベル27"),
    RAISE_MONSTER_LEVEL_28(35L, "モンスターを育成→レベル28"),
    RAISE_MONSTER_LEVEL_29(36L, "モンスターを育成→レベル29"),
    RAISE_MONSTER_LEVEL_30(37L, "モンスターを育成→レベル30"),

    // -- WITHOUT DATABASE USE FOR CHOICE CONSUMPTION
    // -- NOTE: Please keep it don't conflict with the database id
    CHOICE_CONSUMPTION(100L, "チョイスで消費");

    private final Long id;
    private final String name;

    ChoiceActivityRuleEnum(Long id, String name) {
        this.id = id;
        this.name = name;
    }
}
