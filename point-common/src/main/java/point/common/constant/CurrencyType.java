package point.common.constant;

import java.util.Arrays;
import java.util.Optional;
import lombok.Getter;

@Getter
public enum CurrencyType {
    BALC(201L, "Balance", "BALC_JPY"),
    ACTC(203L, "Active", "ACTC_JPY");

    private final Long id;
    private final String currencyType;
    private final String currencyTypePair;

    CurrencyType(long id, String currencyType, String currencyTypePair) {
        this.id = id;
        this.currencyType = currencyType;
        this.currencyTypePair = currencyTypePair;
    }

    public String getName() {
        return name();
    }

    public String toLowerCase() {
        return name().toLowerCase();
    }

    public static String getCurrencyTypeById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<CurrencyType> result =
                Arrays.stream(CurrencyType.values())
                        .filter(currencyType -> currencyType.getId().equals(id))
                        .findFirst();
        return result.map(CurrencyType::getCurrencyType).orElse(null);
    }
}
