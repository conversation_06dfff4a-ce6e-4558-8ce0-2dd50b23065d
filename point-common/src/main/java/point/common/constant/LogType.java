package point.common.constant;

public enum LogType {
    NONE,
    REGISTER,
    PHISHING,
    EMAIL,
    PASSWORD,
    PASSWORD_FORGET,
    ISSUE_API_TOKEN,
    EMAIL_AUTH,
    SMS_AUTH;

    public static LogType valueOfName(String name) {
        for (LogType logType : values()) {
            if (logType.name().equals(name)) {
                return logType;
            }
        }
        return null;
    }

    public String toLowerCase() {
        return name().toLowerCase();
    }

    public boolean isPhishing() {
        return this == PHISHING;
    }

    public boolean isEMail() {
        return this == EMAIL;
    }

    public boolean isPassword() {
        return this == PASSWORD;
    }

    public boolean isSms() {
        return this == SMS_AUTH;
    }

    public boolean isIssueApiToken() {
        return this == ISSUE_API_TOKEN;
    }

    public String getName() {
        return name();
    }
}
