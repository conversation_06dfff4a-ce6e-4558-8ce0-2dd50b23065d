package point.common.constant;

public enum KycSubStatus {
    // DOCUMENT_REJECTED
    FACE_MATCHING_UNMATCHED("顔照合アンマッチ", KycStatus.DOCUMENT_REJECTED, ""),
    DISCREPANCY_BETWEEN_IMAGES("画像相違", KycStatus.DOCUMENT_REJECTED, ""),
    LACK_IMAGES("画像不足", KycStatus.DOCUMENT_REJECTED, ""),
    NOT_CLEAR_NAME("氏名不鮮明", KycStatus.DOCUMENT_REJECTED, ""),
    DISCREPANCY_NAME("氏名相違", KycStatus.DOCUMENT_REJECTED, ""),
    NOT_CLEAR_BIRTH_DATE("生年月日不鮮明", KycStatus.DOCUMENT_REJECTED, ""),
    DISCREPANCY_BIRTH_DATE("生年月日相違", KycStatus.DOCUMENT_REJECTED, ""),
    NOT_CLEAR_ADDRESS("住所不鮮明", KycStatus.DOCUMENT_REJECTED, ""),
    DISCREPANCY_ADDRESS("住所相違", KycStatus.DOCUMENT_REJECTED, ""),
    NO_OFFICIAL_SEAL("公印なし", KycStatus.DOCUMENT_REJECTED, ""),
    NOT_CLEAR_EFFECTIVE_DATE("有効期限不鮮明", KycStatus.DOCUMENT_REJECTED, ""),
    EXPIRATION_DATE("有効期限切れ", KycStatus.DOCUMENT_REJECTED, ""),
    JUDGEMENT_NOT_POSSIBLE("判定不可", KycStatus.DOCUMENT_REJECTED, ""),
    REPRODUCTION("複製", KycStatus.DOCUMENT_REJECTED, ""),
    THICKNESS_CHECK_NOT_POSSIBLE("厚みチェック不可", KycStatus.DOCUMENT_REJECTED, ""),
    // INFORMATION_REQUIRED
    OCCUPATION_WORKPLACE_UNMATCHED(
            "職業・勤務先アンマッチ",
            KycStatus.INFORMATION_REQUIRED,
            "ご登録いただきました職業と勤務先名称の整合性が取れませんでしたため本人情報の\n"
                    + "ご登録が完了いたしませんでした。\n"
                    + "ご登録情報をご確認いただき、正しい職業と勤務先名称で再度ご登録ください。"),
    INCOMPLETE_WORKPLACE(
            "勤務先入力不備",
            KycStatus.INFORMATION_REQUIRED,
            "弊社で確認いたしましたところ、ご登録の勤務先名称の確認がとれず、本人情報の\n"
                    + "ご登録が完了いたしませんでした。\n"
                    + "ご登録情報をご確認いただき、正しい勤務先名称で再度ご登録ください。"),
    RESIDENT_CARD_EXPIRATION_DATE(
            "在留カード期限切れ",
            KycStatus.INFORMATION_REQUIRED,
            "ご提出いただきました本人確認書類の有効期限が過ぎておりましたため、本人情報の\n"
                    + "ご登録が完了いたしませんでした。\n"
                    + "改めて、有効期限内の本人確認書類をご提出ください。"),
    REGISTRATION_COMMON_NAME(
            "通称名での登録",
            KycStatus.INFORMATION_REQUIRED,
            "恐れ入りますが、当社では通称名でのご登録はいただけないため、本人情報の\n"
                    + "ご登録が完了いたしませんでした。\n"
                    + "改めて本名でのご登録と、本名が記載された確認書類をご提出ください。"),
    NATIONALITY_CONFIRM(
            "国籍確認",
            KycStatus.INFORMATION_REQUIRED,
            "ご申請いただきました内容につきまして、確認事項がありご連絡いたしました。\n"
                    + "ご入力いただいた国籍に誤りがある場合は、訂正の上再度申請をお願いいたします。\n"
                    + "ご入力が誤っていた場合も、改めて本人確認書類もご提出ください。\n"
                    + "国籍が日本以外の場合は、本人確認書類として在留カードが必須となっております。\n"
                    + "恐れ入りますが、再度在留カードにて本人確認の申請をお願いいたします。"),
    OTHER("その他", KycStatus.INFORMATION_REQUIRED, "");

    public final String displayName;
    public final KycStatus parentKycStatus;
    public final String mailContent;

    KycSubStatus(
            final String displayName, final KycStatus parentKycStatus, final String mailContent) {
        this.displayName = displayName;
        this.parentKycStatus = parentKycStatus;
        this.mailContent = mailContent;
    }
}
