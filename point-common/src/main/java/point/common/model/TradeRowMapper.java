package point.common.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import point.common.constant.OrderSide;
import point.common.constant.TradeAction;
import point.common.entity.Trade;
import point.common.entity.Trade_;
import point.common.util.FormatUtil;

public abstract class TradeRowMapper<E extends Trade> extends AbstractEntityRowMapper<E> {

    @Override
    public E mapRow(ResultSet rs, int rowNum) throws SQLException {
        E trade = super.mapRow(rs, rowNum);

        if (trade != null) {
            trade.setUserId(rs.getLong(FormatUtil.formatCamelToSnake(Trade_.userId.getName())));
            trade.setOrderId(rs.getLong(FormatUtil.formatCamelToSnake(Trade_.orderId.getName())));
            trade.setOrderSide(
                    OrderSide.valueOf(
                            rs.getString(
                                    FormatUtil.formatCamelToSnake(Trade_.orderSide.getName()))));
            trade.setPrice(rs.getBigDecimal(FormatUtil.formatCamelToSnake(Trade_.price.getName())));
            trade.setAmount(
                    rs.getBigDecimal(FormatUtil.formatCamelToSnake(Trade_.amount.getName())));
            trade.setTradeAction(
                    TradeAction.valueOf(
                            rs.getString(
                                    FormatUtil.formatCamelToSnake(Trade_.tradeAction.getName()))));
            trade.setSymbolId(rs.getLong(FormatUtil.formatCamelToSnake(Trade_.symbolId.getName())));
            trade.setFee(rs.getBigDecimal(FormatUtil.formatCamelToSnake(Trade_.fee.getName())));
        }

        return trade;
    }
}
