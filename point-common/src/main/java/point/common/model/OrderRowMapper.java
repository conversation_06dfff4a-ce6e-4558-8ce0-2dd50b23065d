package point.common.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import point.common.entity.Order;
import point.common.entity.Order_;
import point.common.util.FormatUtil;

public abstract class OrderRowMapper<E extends Order> extends AbstractEntityRowMapper<E> {

    @Override
    public E mapRow(ResultSet rs, int rowNum) throws SQLException {
        E order = super.mapRow(rs, rowNum);

        if (order != null) {
            order.setUserId(rs.getLong(FormatUtil.formatCamelToSnake(Order_.userId.getName())));
            order.setSymbolId(rs.getLong(FormatUtil.formatCamelToSnake(Order_.symbolId.getName())));
            order.setAmount(
                    rs.getBigDecimal(FormatUtil.formatCamelToSnake(Order_.amount.getName())));
        }

        return order;
    }
}
