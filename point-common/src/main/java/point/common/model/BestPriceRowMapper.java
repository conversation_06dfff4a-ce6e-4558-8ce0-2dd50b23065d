package point.common.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import point.common.entity.BestPrice;
import point.common.entity.BestPrice_;
import point.common.util.FormatUtil;

public abstract class BestPriceRowMapper<E extends BestPrice> extends AbstractEntityRowMapper<E> {

    @Override
    public E mapRow(ResultSet rs, int rowNum) throws SQLException {
        E bestPrice = super.mapRow(rs, rowNum);

        if (bestPrice != null) {
            bestPrice.setSymbolId(
                    rs.getLong(FormatUtil.formatCamelToSnake(BestPrice_.symbolId.getName())));
            bestPrice.setBestAsk(
                    rs.getBigDecimal(FormatUtil.formatCamelToSnake(BestPrice_.bestAsk.getName())));
            bestPrice.setBestBid(
                    rs.getBigDecimal(FormatUtil.formatCamelToSnake(BestPrice_.bestBid.getName())));
        }

        return bestPrice;
    }
}
