package point.common.model;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.StringJoiner;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import point.common.exception.CustomException;
import point.common.util.JsonUtil;

public class LogData implements Serializable {

    private static final long serialVersionUID = -5229334650099875473L;

    @Getter protected final String group;

    @Getter protected String message = null;

    @Getter protected Object data = null;

    @Getter protected Integer errorCode = null;

    private LogData(String group) {
        this.group = group;
    }

    public LogData(String group, Object data) {
        this(group);

        this.data = data;

        if (data instanceof Exception) {
            Exception exception = (Exception) data;

            if (data instanceof CustomException) {
                errorCode = ((CustomException) exception).getErrorCode().getCode();
            }

            this.data = toStackTraceString(exception);
        }
    }

    private String toStackTraceString(Exception exception) {
        StringWriter sout = new StringWriter();
        PrintWriter out = new PrintWriter(sout);
        exception.printStackTrace(out);
        out.close();
        return sout.toString();
    }

    @Override
    public String toString() {
        StringJoiner stringJoiner = new StringJoiner("\t");

        if (data != null) {
            if (data instanceof String) {
                stringJoiner.add((String) data);
            } else if (data instanceof Number) {
                stringJoiner.add(data.toString());
            } else {
                stringJoiner.add(StringUtils.isEmpty(message) ? "-" : message);
                stringJoiner.add(JsonUtil.encode(data));
            }
        } else {
            stringJoiner.add(StringUtils.isEmpty(message) ? "-" : message);
        }

        if (errorCode != null) {
            stringJoiner.add("error_code:" + errorCode.toString());
        }

        stringJoiner.add("group:" + group);
        return stringJoiner.toString();
    }
}
