package point.common.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import point.common.entity.AbstractEntity;
import point.common.entity.AbstractEntity_;
import point.common.util.FormatUtil;

@Slf4j
public abstract class AbstractEntityRowMapper<E extends AbstractEntity> implements RowMapper<E> {

    protected abstract Class<E> getClazz();

    public E newInstance() {
        E entity = null;

        try {
            entity = getClazz().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("Failed to create instance", e);
        }

        return entity;
    }

    @Override
    public E mapRow(ResultSet rs, int rowNum) throws SQLException {
        E entity = newInstance();
        entity.setId(rs.getLong(FormatUtil.formatCamelToSnake(AbstractEntity_.id.getName())));
        entity.setCreatedAt(
                rs.getTimestamp(
                        FormatUtil.formatCamelToSnake(AbstractEntity_.createdAt.getName())));
        entity.setUpdatedAt(
                rs.getTimestamp(
                        FormatUtil.formatCamelToSnake(AbstractEntity_.updatedAt.getName())));
        return entity;
    }
}
