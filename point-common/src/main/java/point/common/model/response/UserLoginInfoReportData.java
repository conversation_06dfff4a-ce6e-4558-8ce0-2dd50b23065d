package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.entity.UserLoginInfo;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"ID", "ユーザーID", "メールアドレス", "時間", "ログイン方法", "IPアドレス"})
public class UserLoginInfoReportData implements Serializable {

    private static final long serialVersionUID = -4236169338232034583L;

    @JsonProperty("ID")
    @Getter
    @Setter
    private Long id;

    @JsonProperty("ユーザーID")
    @Getter
    @Setter
    private Long userId;

    @JsonProperty("メールアドレス")
    @Getter
    @Setter
    private String email;

    @JsonProperty("時間")
    @Getter
    @Setter
    private String createdAt;

    @JsonProperty("ログイン方法")
    @Getter
    @Setter
    private String loginMethod;

    @JsonProperty("IPアドレス")
    @Getter
    @Setter
    private String ipAddress;

    public UserLoginInfoReportData setProperties(UserLoginInfo userLoginInfo) {
        this.setId(userLoginInfo.getId());
        this.setUserId(userLoginInfo.getUserId());
        this.setEmail(userLoginInfo.getUserIdentity().getInvestUser().getEmail());
        this.setCreatedAt(
                FormatUtil.formatJst(
                        userLoginInfo.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS));
        this.setLoginMethod("Web");
        this.setIpAddress(userLoginInfo.getIpAddress());

        return this;
    }
}
