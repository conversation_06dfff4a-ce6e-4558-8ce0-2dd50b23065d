package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.entity.SameIpUser;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"ID", "作成日時", "更新日時", "ユーザーID", "IPアドレス"})
public class SameIpUserReportData implements Serializable {

    private static final long serialVersionUID = -4236169338232034583L;

    @JsonProperty("ID")
    @Getter
    @Setter
    private Long id;

    @JsonProperty("作成日時")
    @Getter
    @Setter
    private String createdAtString;

    @JsonProperty("更新日時")
    @Getter
    @Setter
    private String updatedAtString;

    @JsonProperty("ユーザーID")
    @Getter
    @Setter
    private String userIds;

    @JsonProperty("IPアドレス")
    @Getter
    @Setter
    private String ipAddress;

    public SameIpUserReportData setProperties(SameIpUser sameIpUser) {
        this.setId(sameIpUser.getId());
        this.setCreatedAtString(
                FormatUtil.formatJst(sameIpUser.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS));
        this.setUpdatedAtString(
                FormatUtil.formatJst(sameIpUser.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS));
        this.setUserIds(sameIpUser.getUserIds());
        this.setIpAddress(sameIpUser.getIpAddress());
        return this;
    }
}
