package point.common.model.response;

import java.util.Date;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AccountInfoResponse {

    private Long userId;
    private String memberId;
    private Date operateRegisteredAt;
    @Nullable private Date investRegisteredAt;

    private ChoiceVoteResponse vote;
}
