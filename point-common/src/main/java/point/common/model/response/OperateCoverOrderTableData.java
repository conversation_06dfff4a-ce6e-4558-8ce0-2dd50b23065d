package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.OrderSide;
import point.common.constant.OrderStatus;
import point.common.constant.OrderType;
import point.common.serializer.BigDecimalSerializer;
import point.operate.entity.OperateCoverOrder;

@Getter
@Setter
@NoArgsConstructor
public class OperateCoverOrderTableData implements Serializable {

    private String tableKey;

    private Long id;

    private Long symbolId;

    private String currencyPair;

    @Enumerated(EnumType.STRING)
    private OrderSide orderSide;

    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal price;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal usdtPrice;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal remainingAmount;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal fee;

    private String mm;

    private String mmOrderId;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    private OrderStatus orderStatus = OrderStatus.WAITING;

    public OperateCoverOrderTableData setProperties(
            String currencyPair, OperateCoverOrder operateCoverOrder) {
        this.setTableKey(operateCoverOrder.getSymbolId() + "_" + operateCoverOrder.getId());
        this.setId(operateCoverOrder.getId());
        this.setSymbolId(operateCoverOrder.getSymbolId());
        this.setCurrencyPair(currencyPair);
        this.setOrderSide(operateCoverOrder.getOrderSide());
        this.setOrderType(operateCoverOrder.getOrderType());
        this.setPrice(operateCoverOrder.getPrice());
        this.setUsdtPrice(operateCoverOrder.getUsdtPrice());
        this.setAmount(operateCoverOrder.getAmount());
        this.setRemainingAmount(operateCoverOrder.getRemainingAmount());
        this.setMm(operateCoverOrder.getMm().getName());
        this.setFee(operateCoverOrder.getFee());
        this.setMmOrderId(operateCoverOrder.getMmOrderId());
        this.setOrderStatus(operateCoverOrder.getOrderStatus());
        this.setCreatedAt(operateCoverOrder.getCreatedAt());
        this.setUpdatedAt(operateCoverOrder.getUpdatedAt());
        return this;
    }
}
