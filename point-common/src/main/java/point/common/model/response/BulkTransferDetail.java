package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** BulkTransferDetail */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BulkTransferDetail {
    @JsonAlias("transferStatus")
    private String transferStatus;

    @JsonAlias("transferStatusName")
    private String transferStatusName;

    @JsonAlias("transferTypeName")
    private String transferTypeName;

    @JsonAlias("remitterCode")
    private String remitterCode;

    @JsonAlias("isFeeFreeUse")
    private Boolean isFeeFreeUse;

    @JsonAlias("isFeePointUse")
    private Boolean isFeePointUse;

    @JsonAlias("pointName")
    private String pointName;

    @JsonAlias("feeLaterPaymentFlg")
    private Boolean feeLaterPaymentFlg;

    @JsonAlias("totalFee")
    private String totalFee;

    @JsonAlias("totalDebitAmount")
    private String totalDebitAmount;

    @JsonAlias("transferApplies")
    private List<TransferApply> transferApplies;

    @JsonAlias("transferAccepts")
    private List<TransferAccept> transferAccepts;

    @JsonAlias("bulktransferResponses")
    private List<BulkTransferResponse> bulktransferResponses;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BulkTransferDetail {\n");

        sb.append("    transferStatus: ").append(toIndentedString(transferStatus)).append("\n");
        sb.append("    transferStatusName: ")
                .append(toIndentedString(transferStatusName))
                .append("\n");
        sb.append("    transferTypeName: ").append(toIndentedString(transferTypeName)).append("\n");
        sb.append("    remitterCode: ").append(toIndentedString(remitterCode)).append("\n");
        sb.append("    isFeeFreeUse: ").append(toIndentedString(isFeeFreeUse)).append("\n");
        sb.append("    isFeePointUse: ").append(toIndentedString(isFeePointUse)).append("\n");
        sb.append("    pointName: ").append(toIndentedString(pointName)).append("\n");
        sb.append("    feeLaterPaymentFlg: ")
                .append(toIndentedString(feeLaterPaymentFlg))
                .append("\n");
        sb.append("    totalFee: ").append(toIndentedString(totalFee)).append("\n");
        sb.append("    totalDebitAmount: ").append(toIndentedString(totalDebitAmount)).append("\n");
        sb.append("    transferApplies: ").append(toIndentedString(transferApplies)).append("\n");
        sb.append("    transferAccepts: ").append(toIndentedString(transferAccepts)).append("\n");
        sb.append("    bulktransferResponses: ")
                .append(toIndentedString(bulktransferResponses))
                .append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
