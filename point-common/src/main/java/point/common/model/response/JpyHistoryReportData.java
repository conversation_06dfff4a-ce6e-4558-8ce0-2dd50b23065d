package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.constant.HistoryType;
import point.common.constant.ReportLabel;
import point.common.model.response.JpyHistoryData.JpyHistoryElement;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"日時", "入出金", "入出金額", "手数料", "ステータス", "入出金番号"})
public class JpyHistoryReportData implements Serializable {

    private static final long serialVersionUID = -823879922881347574L;

    @Getter
    @Setter
    @JsonProperty("日時")
    private String date;

    @Getter
    @Setter
    @JsonProperty("入出金")
    private String historyType;

    @Getter
    @Setter
    @JsonProperty("入出金額")
    private String amount;

    @Getter
    @Setter
    @JsonProperty("手数料")
    private String fee;

    @Getter
    @Setter
    @JsonProperty("ステータス")
    private String status;

    @Getter
    @Setter
    @JsonProperty("入出金番号")
    private Long id;

    public JpyHistoryReportData setProperties(JpyHistoryElement jpyHistoryElement) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        this.setId(jpyHistoryElement.getId());
        this.setHistoryType(
                jpyHistoryElement.getHistoryType() == HistoryType.DEPOSIT ? "入金" : "出金");
        this.setAmount(toStrigForReport(Currency.JPY, jpyHistoryElement.getAmount(), numberFormat));
        this.setFee(toStrigForReport(Currency.JPY, jpyHistoryElement.getFee(), numberFormat));
        if (jpyHistoryElement.getHistoryType() == HistoryType.DEPOSIT) {
            this.setStatus(
                    ReportLabel.FiatDepositStatus.valueOfName(jpyHistoryElement.getStatus())
                            .getLabel());
        } else {
            this.setStatus(
                    ReportLabel.FiatWithdrawalStatus.valueOfName(jpyHistoryElement.getStatus())
                            .getLabel());
        }
        this.setDate(jpyHistoryElement.getDate());
        return this;
    }

    private String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
}
