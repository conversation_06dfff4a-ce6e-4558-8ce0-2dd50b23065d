package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.*;
import point.common.constant.Currency;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyConfigData {

    @JsonProperty("symbolId")
    private Long symbolId;

    @JsonProperty("currency")
    @Enumerated(EnumType.STRING)
    private Currency currency;

    @JsonProperty("percentage")
    private BigDecimal percentage;
}
