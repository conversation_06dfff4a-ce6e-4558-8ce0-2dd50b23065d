package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Authority;
import point.common.constant.Currency;
import point.common.constant.ReportLabel.HeaderAssetSummary;
import point.common.entity.AssetSummary;
import point.common.entity.PointUser;
import point.common.entity.User;
import point.common.entity.UserInfo;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
    "userId",
    "userName",
    "email",
    "currency",
    "targetAt",
    "currentAmount",
    "jpyConversion",
    "depositAmount",
    "depositAmountJpy",
    "depositFee",
    "depositFeeJpy",
    "withdrawalAmount",
    "withdrawalAmountJpy",
    "withdrawalFee",
    "withdrawalFeeJpy",
    "posTradeBuyAmount",
    "posTradeBuyAmountJpy",
    "posTradeSellAmount",
    "posTradeSellAmountJpy",
    "posTradeFee",
    "posTradeFeeJpy"
})
public class AssetSummaryReportData implements Serializable {

    private static final long serialVersionUID = 7455882701082686975L;

    @Getter @Setter private Long userId;

    @Getter @Setter private String userName = "";

    @Getter @Setter private String email;

    @Getter @Setter private String currency;

    @Getter @Setter private String targetAt;

    @Getter @Setter private String currentAmount = "0";

    @Getter @Setter private String jpyConversion = "0";

    @Getter @Setter private String depositAmount = "0";

    @Getter @Setter private String depositAmountJpy = "0";

    @Getter @Setter private String depositFee = "0";

    @Getter @Setter private String depositFeeJpy = "0";

    @Getter @Setter private String withdrawalAmount = "0";

    @Getter @Setter private String withdrawalAmountJpy = "0";

    @Getter @Setter private String withdrawalFee = "0";

    @Getter @Setter private String withdrawalFeeJpy = "0";

    @Getter @Setter private String posTradeBuyAmount = "0";

    @Getter @Setter private String posTradeBuyAmountJpy = "0";

    @Getter @Setter private String posTradeSellAmount = "0";

    @Getter @Setter private String posTradeSellAmountJpy = "0";

    @Getter @Setter private String posTradeFee = "0";

    @Getter @Setter private String posTradeFeeJpy = "0";

    public AssetSummaryReportData setProperties(AssetSummary assetSummary, User user) {
        this.setUserId(user.getId());

        String authority = user.getAuthorities().get(0).getAuthority();
        if (Authority.PERSONAL.name().equals(authority)) {
            UserInfo userInfo = user.getUserInfo();
            if (userInfo != null) {
                this.setUserName(userInfo.getLastName() + " " + userInfo.getFirstName());
            }
        }

        this.setEmail(user.getEmail());
        this.setCurrency(assetSummary.getCurrency().getName());
        this.setTargetAt(
                FormatUtil.formatJst(
                        assetSummary.getTargetAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));

        NumberFormat numberFormat = NumberFormat.getNumberInstance();

        this.setCurrentAmount(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getCurrentAmount(), numberFormat));
        this.setJpyConversion(
                toStrigForReport(Currency.JPY, assetSummary.getJpyConversion(), numberFormat));

        this.setDepositAmount(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getDepositAmount(), numberFormat));
        this.setDepositAmountJpy(
                toStrigForReport(Currency.JPY, assetSummary.getDepositAmountJpy(), numberFormat));

        this.setDepositFee(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getDepositFee(), numberFormat));
        this.setDepositFeeJpy(
                toStrigForReport(Currency.JPY, assetSummary.getDepositFeeJpy(), numberFormat));

        this.setWithdrawalAmount(
                toStrigForReport(
                        assetSummary.getCurrency(),
                        assetSummary.getWithdrawalAmount(),
                        numberFormat));
        this.setWithdrawalAmountJpy(
                toStrigForReport(
                        Currency.JPY, assetSummary.getWithdrawalAmountJpy(), numberFormat));

        this.setWithdrawalFee(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getWithdrawalFee(), numberFormat));
        this.setWithdrawalFeeJpy(
                toStrigForReport(Currency.JPY, assetSummary.getWithdrawalFeeJpy(), numberFormat));
        this.setPosTradeBuyAmount(
                toStrigForReport(
                        assetSummary.getCurrency(),
                        assetSummary.getPosTradeBuyAmount(),
                        numberFormat));
        this.setPosTradeBuyAmountJpy(
                toStrigForReport(
                        Currency.JPY, assetSummary.getPosTradeBuyAmountJpy(), numberFormat));

        this.setPosTradeSellAmount(
                toStrigForReport(
                        assetSummary.getCurrency(),
                        assetSummary.getPosTradeSellAmount(),
                        numberFormat));
        this.setPosTradeSellAmountJpy(
                toStrigForReport(
                        Currency.JPY, assetSummary.getPosTradeSellAmountJpy(), numberFormat));

        this.setPosTradeFee(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getPosTradeFee(), numberFormat));
        this.setPosTradeFeeJpy(
                toStrigForReport(Currency.JPY, assetSummary.getPosTradeFeeJpy(), numberFormat));

        return this;
    }

    private String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }

    public static String getReportHeader() {
        return HeaderAssetSummary.USER_ID.getLabel()
                + ","
                + HeaderAssetSummary.USER_NAME.getLabel()
                + ","
                + HeaderAssetSummary.EMAIL.getLabel()
                + ","
                + HeaderAssetSummary.CURRENCY.getLabel()
                + ","
                + HeaderAssetSummary.TARGET_AT.getLabel()
                + ","
                + HeaderAssetSummary.CURRENT_AMOUNT.getLabel()
                + ","
                + HeaderAssetSummary.JPY_CONVERSION.getLabel()
                + ","
                + HeaderAssetSummary.DEPOSIT_AMOUNT.getLabel()
                + ","
                + HeaderAssetSummary.DEPOSIT_AMOUNT_JPY.getLabel()
                + ","
                + HeaderAssetSummary.DEPOSIT_FEE.getLabel()
                + ","
                + HeaderAssetSummary.DEPOSIT_FEE_JPY.getLabel()
                + ","
                + HeaderAssetSummary.WITHDRAWAL_AMOUNT.getLabel()
                + ","
                + HeaderAssetSummary.WITHDRAWAL_AMOUNT_JPY.getLabel()
                + ","
                + HeaderAssetSummary.WITHDRAWAL_FEE.getLabel()
                + ","
                + HeaderAssetSummary.WITHDRAWAL_FEE_JPY.getLabel()
                + ","
                + HeaderAssetSummary.POS_TRADE_BUY_AMOUNT.getLabel()
                + ","
                + HeaderAssetSummary.POS_TRADE_BUY_AMOUNT_JPY.getLabel()
                + ","
                + HeaderAssetSummary.POS_TRADE_SELL_AMOUNT.getLabel()
                + ","
                + HeaderAssetSummary.POS_TRADE_SELL_AMOUNT_JPY.getLabel()
                + ","
                + HeaderAssetSummary.POS_TRADE_FEE.getLabel()
                + ","
                + HeaderAssetSummary.POS_TRADE_FEE_JPY.getLabel();
    }

    public AssetSummaryReportData setProperties(AssetSummary assetSummary, PointUser user) {
        this.setUserId(user.getId());
        this.setEmail("");
        if (Objects.equals(Currency.ACTC, assetSummary.getCurrency())) {
            this.setCurrency(assetSummary.getCurrency().getDescription());
        } else {
            this.setCurrency(assetSummary.getCurrency().getName());
        }
        this.setTargetAt(
                FormatUtil.formatJst(
                        assetSummary.getTargetAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        this.setCurrentAmount(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getCurrentAmount(), numberFormat));
        this.setJpyConversion(
                toStrigForReport(Currency.JPY, assetSummary.getJpyConversion(), numberFormat));
        this.setDepositAmount(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getDepositAmount(), numberFormat));
        this.setDepositAmountJpy(
                toStrigForReport(Currency.JPY, assetSummary.getDepositAmountJpy(), numberFormat));
        this.setDepositFee(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getDepositFee(), numberFormat));
        this.setDepositFeeJpy(
                toStrigForReport(Currency.JPY, assetSummary.getDepositFeeJpy(), numberFormat));
        this.setWithdrawalAmount(
                toStrigForReport(
                        assetSummary.getCurrency(),
                        assetSummary.getWithdrawalAmount(),
                        numberFormat));
        this.setWithdrawalAmountJpy(
                toStrigForReport(
                        Currency.JPY, assetSummary.getWithdrawalAmountJpy(), numberFormat));
        this.setWithdrawalFee(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getWithdrawalFee(), numberFormat));
        this.setWithdrawalFeeJpy(
                toStrigForReport(Currency.JPY, assetSummary.getWithdrawalFeeJpy(), numberFormat));
        this.setPosTradeBuyAmount(
                toStrigForReport(
                        assetSummary.getCurrency(),
                        assetSummary.getPosTradeBuyAmount(),
                        numberFormat));
        this.setPosTradeBuyAmountJpy(
                toStrigForReport(
                        Currency.JPY, assetSummary.getPosTradeBuyAmountJpy(), numberFormat));
        this.setPosTradeSellAmount(
                toStrigForReport(
                        assetSummary.getCurrency(),
                        assetSummary.getPosTradeSellAmount(),
                        numberFormat));
        this.setPosTradeSellAmountJpy(
                toStrigForReport(
                        Currency.JPY, assetSummary.getPosTradeSellAmountJpy(), numberFormat));
        this.setPosTradeFee(
                toStrigForReport(
                        assetSummary.getCurrency(), assetSummary.getPosTradeFee(), numberFormat));
        this.setPosTradeFeeJpy(
                toStrigForReport(Currency.JPY, assetSummary.getPosTradeFeeJpy(), numberFormat));
        return this;
    }
}
