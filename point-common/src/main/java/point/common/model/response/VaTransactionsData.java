package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties
public class VaTransactionsData implements Serializable {

    private String vaId;

    private String transactionDate;

    private String valueDate;

    private String vaBranchCode;

    private String vaBranchNameKana;

    private String vaAccountNumber;

    private String vaAccountNameKana;

    private String depositAmount;

    private String remitterNameKana;

    private String paymentBankName;

    private String paymentBranchName;

    private String partnerName;

    private String remarks;

    private String itemKey;
}
