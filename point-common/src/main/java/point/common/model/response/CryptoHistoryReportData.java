package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.constant.HistoryType;
import point.common.model.response.CryptoHistoryData.CryptoHistoryElement;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"日時", "入出金", "通貨", "数量", "ステータス", "アドレス", "宛先タグ", "区別", "入出金番号"})
public class CryptoHistoryReportData implements Serializable {

    private static final long serialVersionUID = -823879922881347574L;

    @Getter
    @Setter
    @JsonProperty("日時")
    private String date;

    @Getter
    @Setter
    @JsonProperty("入出金")
    private String historyType;

    @Getter
    @Setter
    @JsonProperty("通貨")
    private String currency;

    @Getter
    @Setter
    @JsonProperty("数量")
    private String amount;

    @Getter
    @Setter
    @JsonProperty("ステータス")
    private String status;

    @Getter
    @Setter
    @JsonProperty("アドレス")
    private String address;

    @Getter
    @Setter
    @JsonProperty("区別")
    private String distinction;

    @Getter
    @Setter
    @JsonProperty("宛先タグ")
    private String destinationTag;

    @Getter
    @Setter
    @JsonProperty("入出金番号")
    private Long id;

    public CryptoHistoryReportData setProperties(CryptoHistoryElement criptoHistoryElement)
            throws ParseException {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        this.setId(criptoHistoryElement.getId());
        this.setHistoryType(
                criptoHistoryElement.getHistoryType() == HistoryType.DEPOSIT ? "入金" : "出金");
        this.setCurrency(criptoHistoryElement.getCurrency().getLabel());
        this.setAmount(
                toStrigForReport(
                        criptoHistoryElement.getCurrency(),
                        criptoHistoryElement.getAmount(),
                        numberFormat));

        if (criptoHistoryElement.getCurrency().equals(Currency.XRP)
                && criptoHistoryElement.getAddress().indexOf("@") > 0) {
            this.setAddress(
                    criptoHistoryElement
                            .getAddress()
                            .substring(0, criptoHistoryElement.getAddress().indexOf("@")));
            this.setDestinationTag(
                    criptoHistoryElement
                            .getAddress()
                            .substring(criptoHistoryElement.getAddress().indexOf("@") + 1));
        } else {
            this.setAddress(criptoHistoryElement.getAddress());
            this.setDestinationTag("");
        }
        if (criptoHistoryElement.getDistinction().equals("IN")) {
            this.setDistinction("入庫");
        }
        if (criptoHistoryElement.getDistinction().equals("Transfer")) {
            this.setDistinction("振替");
        }
        if (criptoHistoryElement.getDistinction().equals("OUT")) {
            this.setDistinction("出庫");
        }
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        Date date = sdf.parse(criptoHistoryElement.getDate());
        this.setDate(FormatUtil.format(date, FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
        return this;
    }

    private String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
}
