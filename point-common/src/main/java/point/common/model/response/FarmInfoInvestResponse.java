package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Course;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FarmInfoInvestResponse implements Serializable {

    private static final long serialVersionUID = -5680701498263865050L;

    @Getter @Setter private Long userId;

    // 現在の投資ポイントの残高
    @Getter @Setter private BigDecimal investBalancePoints;

    // 現在の投資JPYの残高
    @Getter @Setter private BigDecimal jpyBalance;

    // 現在の合計投資ポイント
    @Getter @Setter private BigDecimal sumInvestPoints;

    // 評価損益額
    @Getter @Setter private BigDecimal sumEvalProfitLossAmt;

    // 評価損益率
    @Getter @Setter private BigDecimal sumevalProfitLossAmtRate;

    // moster Level
    @Getter @Setter private Long monsterLevel;

    @Getter @Setter private List<Course> currencyList;
}
