package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** TransferDetailResponse */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TransferDetailResponse {
    @JsonAlias("beneficiaryBankNameKanji")
    private String beneficiaryBankNameKanji = null;

    @JsonAlias("beneficiaryBranchNameKanji")
    private String beneficiaryBranchNameKanji = null;

    @JsonAlias("usedPoint")
    private String usedPoint = null;

    @JsonAlias("isFeeFreeUsed")
    private Boolean isFeeFreeUsed = null;

    @JsonAlias("transferFee")
    private String transferFee = null;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TransferDetailResponse {\n");

        sb.append("    beneficiaryBankNameKanji: ")
                .append(toIndentedString(beneficiaryBankNameKanji))
                .append("\n");
        sb.append("    beneficiaryBranchNameKanji: ")
                .append(toIndentedString(beneficiaryBranchNameKanji))
                .append("\n");
        sb.append("    usedPoint: ").append(toIndentedString(usedPoint)).append("\n");
        sb.append("    isFeeFreeUsed: ").append(toIndentedString(isFeeFreeUsed)).append("\n");
        sb.append("    transferFee: ").append(toIndentedString(transferFee)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
