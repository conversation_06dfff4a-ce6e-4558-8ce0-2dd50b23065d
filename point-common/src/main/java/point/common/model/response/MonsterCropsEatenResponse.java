package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MonsterCropsEatenResponse implements Serializable {

    private String id;

    // コース
    private String course;

    // 作物名称
    private String cropName;

    // 個数
    private Long sumAmont;

    // 合計経験値
    private BigDecimal sumExperience;

    // 合計実現損益
    private BigDecimal sumIncome;
}
