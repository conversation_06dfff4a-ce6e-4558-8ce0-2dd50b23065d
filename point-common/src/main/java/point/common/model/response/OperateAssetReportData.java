package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.math.RoundingMode;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ReportLabel.HeaderAsset;
import point.common.entity.Asset;
import point.common.util.CurrencyUtils;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"userId", "currency", "onhandAmount", "lockedAmount"})
@NoArgsConstructor
public class OperateAssetReportData implements Serializable {

    private static final long serialVersionUID = 6502972389068401244L;

    private Long userId;
    private String currency;
    private String onhandAmount;
    private String lockedAmount;

    public OperateAssetReportData setProperties(Asset asset) {
        this.setUserId(asset.getUserId());
        CurrencyUtils.setCurrencyToEntity(this::setCurrency, asset.getCurrency());
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        String onhandAmountStr =
                asset.getCurrency()
                        .getScaledAmount(asset.getOnhandAmount(), RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        if (onhandAmountStr.indexOf(".") < 0) {
            this.setOnhandAmount(numberFormat.format(Long.valueOf(onhandAmountStr)));
        } else {
            // 整数部3桁区切り
            String onhandAmountInt = onhandAmountStr.substring(0, onhandAmountStr.indexOf("."));
            this.setOnhandAmount(
                    numberFormat.format(Long.valueOf(onhandAmountInt))
                            + onhandAmountStr.substring(onhandAmountStr.indexOf(".")));
        }

        String lockedAmountStr =
                asset.getCurrency()
                        .getScaledAmount(asset.getLockedAmount(), RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        if (lockedAmountStr.indexOf(".") < 0) {
            this.setLockedAmount(numberFormat.format(Long.valueOf(lockedAmountStr)));
        } else {
            // 整数部3桁区切り
            String lockedAmountInt = lockedAmountStr.substring(0, lockedAmountStr.indexOf("."));
            this.setLockedAmount(
                    numberFormat.format(Long.valueOf(lockedAmountInt))
                            + lockedAmountStr.substring(lockedAmountStr.indexOf(".")));
        }

        return this;
    }

    public static String getReportHeader() {
        return String.join(
                ",",
                HeaderAsset.USER_ID.getLabel(),
                HeaderAsset.CURRENCY.getLabel(),
                HeaderAsset.ONHAND_AMOUNT.getLabel(),
                HeaderAsset.LOCKED_AMOUNT.getLabel());
    }
}
