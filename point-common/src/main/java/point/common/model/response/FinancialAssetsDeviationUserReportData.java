package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ReportLabel.HeaderFinancialAssetsDeviationUser;
import point.common.entity.FinancialAssetsDeviationUser;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"userId", "overTrades", "createdAt", "updatedAt"})
public class FinancialAssetsDeviationUserReportData implements Serializable {

    private static final long serialVersionUID = 7571064377054142620L;

    @Getter @Setter private Long userId;

    @Getter @Setter private String overTrades;

    @Getter @Setter private String createdAt;

    @Getter @Setter private String updatedAt;

    public FinancialAssetsDeviationUserReportData setProperties(
            FinancialAssetsDeviationUser financialAssetsDeviationUser) {
        this.setUserId(financialAssetsDeviationUser.getUserId());

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        this.setOverTrades(numberFormat.format(financialAssetsDeviationUser.getOverTrades()));

        this.setCreatedAt(financialAssetsDeviationUser.getFormattedCreatedAt());
        this.setUpdatedAt(financialAssetsDeviationUser.getFormattedUpdatedAt());

        return this;
    }

    public static String getReportHeader() {
        return HeaderFinancialAssetsDeviationUser.USER_ID.getLabel()
                + ","
                + HeaderFinancialAssetsDeviationUser.OVER_TRADES.getLabel()
                + ","
                + HeaderFinancialAssetsDeviationUser.CREATED_AT.getLabel()
                + ","
                + HeaderFinancialAssetsDeviationUser.UPDATED_AT.getLabel();
    }
}
