package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** TransferApplyDetail */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TransferApplyDetail {
    @JsonAlias("applyDatetime")
    private String applyDatetime;

    @JsonAlias("applyStatus")
    private String applyStatus;

    @JsonAlias("applyUser")
    private String applyUser;

    @JsonAlias("applyComment")
    private String applyComment;

    @JsonAlias("approvalUser")
    private String approvalUser;

    @JsonAlias("approvalComment")
    private String approvalComment;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TransferApplyDetail {\n");

        sb.append("    applyDatetime: ").append(toIndentedString(applyDatetime)).append("\n");
        sb.append("    applyStatus: ").append(toIndentedString(applyStatus)).append("\n");
        sb.append("    applyUser: ").append(toIndentedString(applyUser)).append("\n");
        sb.append("    applyComment: ").append(toIndentedString(applyComment)).append("\n");
        sb.append("    approvalUser: ").append(toIndentedString(approvalUser)).append("\n");
        sb.append("    approvalComment: ").append(toIndentedString(approvalComment)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
