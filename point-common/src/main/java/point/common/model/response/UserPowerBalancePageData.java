package point.common.model.response;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserPowerBalancePageData {

    // ID
    private Long id;

    // ユーザーID(投票)
    private Long userId;

    // 獲得ポイント数
    private String partnerMemberId;

    // 報酬分配日時
    private String name;

    // 獲得ポイント残高
    private String partnerNumber;

    // 引き出すポイント数
    private String idType;

    // 引出日時
    private BigDecimal amount;
}
