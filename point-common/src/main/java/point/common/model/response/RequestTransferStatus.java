package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** RequestTransferStatus */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class RequestTransferStatus {
    @JsonAlias("requestTransferStatus")
    private String requestTransferStatus;

    public RequestTransferStatus requestTransferStatus(String requestTransferStatus) {
        this.requestTransferStatus = requestTransferStatus;
        return this;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RequestTransferStatus {\n");

        sb.append("    requestTransferStatus: ")
                .append(toIndentedString(requestTransferStatus))
                .append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
