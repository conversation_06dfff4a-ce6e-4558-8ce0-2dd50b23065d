package point.common.model.response;

import java.util.Date;
import lombok.Builder;
import lombok.Data;
import point.common.constant.ChoiceActivityStatus;

@Data
@Builder
public class CampaignListResponse {

    private Long id;

    private String activityName;

    private Long powerAmount;

    private String powerAmountRate;

    private Date effectiveDate;

    private Date expiryDate;

    private ChoiceActivityStatus status;
}
