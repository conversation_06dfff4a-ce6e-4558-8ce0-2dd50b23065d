package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Course;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FarmInfoOperateResponse implements Serializable {

    private static final long serialVersionUID = -5680701498263865050L;

    @Getter @Setter private Long userId;

    // 現在の運用ポイントの残高
    @Getter @Setter private BigDecimal operationBalancePoints;

    // 現在の合計運用ポイント
    @Getter @Setter private BigDecimal sumOperationPoints;

    // 評価損益額
    @Getter @Setter private BigDecimal sumEvalProfitLossAmt;

    // 評価損益率
    @Getter @Setter private BigDecimal sumevalProfitLossAmtRate;

    // moster Level
    @Getter @Setter private Long monsterLevel;

    @Getter @Setter private List<Course> currencyList;
}
