package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.serializer.BigDecimalSerializer;
import point.pos.entity.PosCandlestick;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PosCandlestickData implements Serializable {

    private static final long serialVersionUID = 7367596723044422833L;

    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CandlestickElement implements Serializable {

        private static final long serialVersionUID = -8337830627809305460L;

        @Getter
        @Setter
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal open;

        @Getter
        @Setter
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal high;

        @Getter
        @Setter
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal low;

        @Getter
        @Setter
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal close;

        @Getter
        @Setter
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal volume;

        @Getter @Setter private long time;
    }

    @Getter @Setter private Long symbolId;

    @Getter @Setter
    private List<CandlestickData.CandlestickElement> candlesticks = new ArrayList<>();

    @Getter @Setter private long timestamp = new Date().getTime();

    public PosCandlestickData(Long symbolId, List<PosCandlestick> candlesticks) {
        this.symbolId = symbolId;
        candlesticks.forEach(
                candlestick ->
                        this.candlesticks.add(
                                new CandlestickData.CandlestickElement(
                                        candlestick.getOpen(),
                                        candlestick.getHigh(),
                                        candlestick.getLow(),
                                        candlestick.getClose(),
                                        candlestick.getVolume(),
                                        candlestick.getTargetAt().getTime())));
    }
}
