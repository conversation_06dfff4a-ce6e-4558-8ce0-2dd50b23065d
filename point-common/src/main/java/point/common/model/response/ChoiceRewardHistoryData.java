package point.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ChoiceActivityVoteResult;
import point.common.constant.ChoiceVoteResult;
import point.common.constant.WithdrawStatusType;
import point.common.entity.ChoiceReward;
import point.common.entity.ChoiceVote;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChoiceRewardHistoryData implements Serializable {

    private Long rewardId;

    private Long userId;

    private ChoiceVoteResult voteResult;

    private BigDecimal amount;

    private Date voteTime;

    private ChoiceActivityVoteResult voteDirection;

    private WithdrawStatusType withdrawStatusType;

    private BigDecimal remainingAmount;

    public ChoiceRewardHistoryData(ChoiceReward choiceReward, ChoiceVote choiceVote) {
        this.rewardId = choiceReward.getId();
        this.userId = choiceVote.getUserId();
        this.withdrawStatusType = WithdrawStatusType.PENDING;
        this.remainingAmount = choiceReward.getRemainingAmount();
        this.voteResult = ChoiceVoteResult.PENDING;
        this.voteTime = choiceVote.getVoteTime();
        this.voteDirection = choiceVote.getVoteDirection();
    }

    public ChoiceRewardHistoryData(ChoiceVote choiceVote) {
        this.userId = choiceVote.getUserId();
        this.voteResult = choiceVote.getVoteResult();
        this.voteTime = choiceVote.getVoteTime();
        this.voteDirection = choiceVote.getVoteDirection();
    }
}
