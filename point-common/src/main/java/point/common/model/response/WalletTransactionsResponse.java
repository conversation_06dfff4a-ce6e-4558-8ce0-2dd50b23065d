package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WalletTransactionsResponse<T> implements Serializable {

    private static final long serialVersionUID = 150196569704075377L;

    @Getter
    @Setter
    @JsonProperty(value = "rows")
    private List<WalletTransaction> rows;
}
