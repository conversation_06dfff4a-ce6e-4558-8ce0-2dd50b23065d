package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.NoArgsConstructor;
import point.common.constant.ReportLabel.HeaderPointSummaryOperate;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
    "id",
    "targetAt",
    "currency",
    "currentAmount",
    "jpyConversion",
    "depositAmount",
    "depositAmountJpy",
    "depositFee",
    "depositFeeJpy",
    "withdrawalAmount",
    "withdrawalAmountJpy",
    "withdrawalFee",
    "withdrawalFeeJpy",
    "transactionFee",
    "transactionFeeJpy",
    "simpleMarketPosTradeBuyAmount",
    "simpleMarketPosTradeBuyAmountJpy",
    "simpleMarketPosTradeSellAmount",
    "simpleMarketPosTradeSellAmountJpy",
    "simpleMarketPosTradeFee",
    "simpleMarketPosTradeFeeJpy",
    "rewardAccumulate",
    "rewardAccumulateJpy",
    "createdAt",
    "updatedAt"
})
public class OperateExchangeSummaryReportData extends ExchangeSummaryReportData {
    public static String getReportHeader() {
        return HeaderPointSummaryOperate.ID.getLabel()
                + ","
                + HeaderPointSummaryOperate.TARGET_AT.getLabel()
                + ","
                + HeaderPointSummaryOperate.CURRENCY.getLabel()
                + ","
                + HeaderPointSummaryOperate.CURRENT_AMOUNT.getLabel()
                + ","
                + HeaderPointSummaryOperate.JPY_CONVERSION.getLabel()
                + ","
                + HeaderPointSummaryOperate.DEPOSIT_AMOUNT.getLabel()
                + ","
                + HeaderPointSummaryOperate.DEPOSIT_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.DEPOSIT_FEE.getLabel()
                + ","
                + HeaderPointSummaryOperate.DEPOSIT_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.WITHDRAWAL_AMOUNT.getLabel()
                + ","
                + HeaderPointSummaryOperate.WITHDRAWAL_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.WITHDRAWAL_FEE.getLabel()
                + ","
                + HeaderPointSummaryOperate.WITHDRAWAL_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.TRANSACTION_FEE.getLabel()
                + ","
                + HeaderPointSummaryOperate.TRANSACTION_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT.getLabel()
                + ","
                + HeaderPointSummaryOperate.SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT.getLabel()
                + ","
                + HeaderPointSummaryOperate.SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.SIMPLE_MARKET_POS_TRADE_FEE.getLabel()
                + ","
                + HeaderPointSummaryOperate.SIMPLE_MARKET_POS_TRADE_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.REWARD_ACCUMULATE.getLabel()
                + ","
                + HeaderPointSummaryOperate.REWARD_ACCUMULATE_JPY.getLabel()
                + ","
                + HeaderPointSummaryOperate.CREATED_AT.getLabel()
                + ","
                + HeaderPointSummaryOperate.UPDATED_AT.getLabel();
    }
}
