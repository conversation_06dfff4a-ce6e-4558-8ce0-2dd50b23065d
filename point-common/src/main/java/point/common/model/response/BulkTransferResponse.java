package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** BulkTransferResponse */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BulkTransferResponse {
    @JsonAlias("accountId")
    private String accountId = null;

    @JsonAlias("remitterName")
    private String remitterName = null;

    @JsonAlias("transferDesignatedDate")
    private String transferDesignatedDate = null;

    @JsonAlias("transferDataName")
    private String transferDataName = null;

    @JsonAlias("totalCount")
    private String totalCount = null;

    @JsonAlias("totalAmount")
    private String totalAmount = null;

    @JsonAlias("bulkTransferInfos")
    private List<BulkTransferInfo> bulkTransferInfos = null;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BulkTransferResponse {\n");

        sb.append("    accountId: ").append(toIndentedString(accountId)).append("\n");
        sb.append("    remitterName: ").append(toIndentedString(remitterName)).append("\n");
        sb.append("    transferDesignatedDate: ")
                .append(toIndentedString(transferDesignatedDate))
                .append("\n");
        sb.append("    transferDataName: ").append(toIndentedString(transferDataName)).append("\n");
        sb.append("    totalCount: ").append(toIndentedString(totalCount)).append("\n");
        sb.append("    totalAmount: ").append(toIndentedString(totalAmount)).append("\n");
        sb.append("    bulkTransferInfos: ")
                .append(toIndentedString(bulkTransferInfos))
                .append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
