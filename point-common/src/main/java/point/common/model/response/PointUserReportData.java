package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import point.common.entity.PointUser;
import point.common.util.FormatUtil;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"ユーザーID", "事業者ID", "事業者名", "会員ID(事業者ユーザーID)", "作成日時（初回ログイン日時）", "ユーザー最終ログイン日時"})
public class PointUserReportData implements Serializable {
    private static final long serialVersionUID = -4236169338232034583L;

    @JsonProperty("ユーザーID")
    private String id = StringUtils.EMPTY;

    @JsonProperty("事業者ID")
    private String partnerNumber = StringUtils.EMPTY;

    @JsonProperty("事業者名")
    private String name = StringUtils.EMPTY;

    @JsonProperty("会員ID(事業者ユーザーID)")
    private String partnerMemberId = StringUtils.EMPTY;

    @JsonProperty("作成日時（初回ログイン日時）")
    private String createdAt = StringUtils.EMPTY;

    @JsonProperty("ユーザー最終ログイン日時")
    private String updatedAt = StringUtils.EMPTY;

    public PointUserReportData setProperties(PointUser pointUser) {
        this.id = pointUser.getId().toString();
        this.partnerNumber =
                (pointUser.getPointPartner() != null
                                && pointUser.getPointPartner().getPartnerNumber() != null)
                        ? String.valueOf(pointUser.getPointPartner().getPartnerNumber())
                        : StringUtils.EMPTY;
        this.name = pointUser.getPointPartner().getName();
        this.partnerMemberId = pointUser.getPartnerMemberId();
        this.createdAt =
                FormatUtil.formatJst(
                        pointUser.getCreatedAt(), FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
        this.updatedAt =
                FormatUtil.formatJst(
                        pointUser.getUpdatedAt(), FormatUtil.FormatPattern.YYYY_MM_DD_HH_MM_SLASH);
        return this;
    }
}
