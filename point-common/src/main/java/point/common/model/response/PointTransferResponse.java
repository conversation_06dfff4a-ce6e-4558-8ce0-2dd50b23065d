package point.common.model.response;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class PointTransferResponse {

    private Long id;
    private Long userId;
    private String userIdType;
    private String transferType;
    private BigDecimal amount;
    private String status;
    private Date requestTime;
    private Date transferTime;
    private Date createdAt;
}
