package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** BulkTransferRequestResponse */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BulkTransferRequestResponse {
    /** 口座ID */
    @JsonAlias("accountId")
    private String accountId;

    /** 結果コード */
    @JsonAlias("resultCode")
    private String resultCode;

    /** 受付番号（振込申請番号） */
    @JsonAlias("applyNo")
    private String applyNo;

    /** 振込依頼完了日時 */
    @JsonAlias("applyEndDatetime")
    private String applyEndDatetime;

    //  public BulkTransferResponse vaTypeCode(String vaTypeCode) {
    //    this.vaTypeCode = vaTypeCode;
    //    return this;
    //  }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BulkTransferRequestResponse {\n");

        sb.append("    accountId: ").append(toIndentedString(accountId)).append("\n");
        sb.append("    resultCode: ").append(toIndentedString(resultCode)).append("\n");
        sb.append("    applyNo: ").append(toIndentedString(applyNo)).append("\n");
        sb.append("    applyEndDatetime: ").append(toIndentedString(applyEndDatetime)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
