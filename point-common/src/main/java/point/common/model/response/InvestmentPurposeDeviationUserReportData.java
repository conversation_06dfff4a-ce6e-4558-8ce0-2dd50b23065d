package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ReportLabel.HeaderInvestmentPurposeDeviationUser;
import point.common.entity.InvestmentPurposeDeviationUser;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"userId", "trades", "createdAt", "updatedAt"})
public class InvestmentPurposeDeviationUserReportData implements Serializable {

    private static final long serialVersionUID = -4459750193761416957L;

    @Getter @Setter private Long userId;

    @Getter @Setter private String trades;

    @Getter @Setter private String createdAt;

    @Getter @Setter private String updatedAt;

    public InvestmentPurposeDeviationUserReportData setProperties(
            InvestmentPurposeDeviationUser investmentPurposeDeviationUser) {
        this.setUserId(investmentPurposeDeviationUser.getUserId());

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        this.setTrades(numberFormat.format(investmentPurposeDeviationUser.getTrades()));

        this.setCreatedAt(investmentPurposeDeviationUser.getFormattedCreatedAt());
        this.setUpdatedAt(investmentPurposeDeviationUser.getFormattedUpdatedAt());

        return this;
    }

    public static String getReportHeader() {
        return HeaderInvestmentPurposeDeviationUser.USER_ID.getLabel()
                + ","
                + HeaderInvestmentPurposeDeviationUser.TRADES.getLabel()
                + ","
                + HeaderInvestmentPurposeDeviationUser.CREATED_AT.getLabel()
                + ","
                + HeaderInvestmentPurposeDeviationUser.UPDATED_AT.getLabel();
    }
}
