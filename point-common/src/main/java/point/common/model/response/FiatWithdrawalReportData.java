package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
    "ジョブ管理ID", "入金日時", "種別名", "会員名", "会員ID", "出金額", "差引出金額", "手数料", "銀行名", "支店名", "口座種別", "口座番号",
    "口座名義", "承認者1", "承認日時1", "承認者2", "承認日時2", "送金状態", "コメント"
})
public class FiatWithdrawalReportData implements Serializable {

    private static final long serialVersionUID = -4236169338232034583L;

    @Getter
    @Setter
    @JsonProperty("ジョブ管理ID")
    private String id;

    @Getter
    @Setter
    @JsonProperty("入金日時")
    private String createdAt;

    @Getter
    @Setter
    @JsonProperty("会員名")
    private String username;

    @Getter
    @Setter
    @JsonProperty("会員ID")
    private String userId;

    @Getter
    @Setter
    @JsonProperty("出金額")
    private String amount;

    @Getter
    @Setter
    @JsonProperty("差引出金額")
    private String totalAmount;

    @Getter
    @Setter
    @JsonProperty("手数料")
    private String fee;

    @Getter
    @Setter
    @JsonProperty("銀行名")
    private String bankName;

    @Getter
    @Setter
    @JsonProperty("支店名")
    private String branchName;

    @Getter
    @Setter
    @JsonProperty("口座種別")
    private String bankAccountType;

    @Getter
    @Setter
    @JsonProperty("口座番号")
    private String bankAccountNumber;

    @Getter
    @Setter
    @JsonProperty("口座名義")
    private String bankAccountName;

    @Getter
    @Setter
    @JsonProperty("承認者1")
    private String ApprovedBy1;

    @Getter
    @Setter
    @JsonProperty("承認日時1")
    private String ApprovedAt1;

    @Getter
    @Setter
    @JsonProperty("承認者2")
    private String ApprovedBy2;

    @Getter
    @Setter
    @JsonProperty("承認日時2")
    private String ApprovedAt2;

    @Getter
    @Setter
    @JsonProperty("送金状態")
    private String status;

    @Getter
    @Setter
    @JsonProperty("コメント")
    private String comment;
}
