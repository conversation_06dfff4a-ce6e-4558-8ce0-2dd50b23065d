package point.common.model.response;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import point.common.entity.CropGrowthStage;
import point.common.entity.CropGrowthStageHistory;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CropGrowthStageListData implements Serializable {

    @Serial private static final long serialVersionUID = -108049843318268735L;

    private CropGrowthStageHistory investLastestHist;

    private CropGrowthStageHistory operateLastestHist;

    private List<CropGrowthStage> listData;
}
