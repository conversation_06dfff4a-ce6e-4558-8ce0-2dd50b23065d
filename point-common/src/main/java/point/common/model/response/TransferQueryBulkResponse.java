package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** TransferQueryBulkResponse */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TransferQueryBulkResponse {
    @JsonAlias("dateFrom")
    private String dateFrom;

    @JsonAlias("dateTo")
    private String dateTo;

    @JsonAlias("requestNextItemKey")
    private String requestNextItemKey;

    @JsonAlias("requestTransferStatuses")
    private List<RequestTransferStatus> requestTransferStatuses;

    @JsonAlias("requestTransferClass")
    private String requestTransferClass;

    @JsonAlias("requestTransferTerm")
    private String requestTransferTerm;

    @JsonAlias("hasNext")
    private Boolean hasNext;

    @JsonAlias("nextItemKey")
    private String nextItemKey;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TransferQueryBulkResponse {\n");

        sb.append("    dateFrom: ").append(toIndentedString(dateFrom)).append("\n");
        sb.append("    dateTo: ").append(toIndentedString(dateTo)).append("\n");
        sb.append("    requestNextItemKey: ")
                .append(toIndentedString(requestNextItemKey))
                .append("\n");
        sb.append("    requestTransferStatuses: ")
                .append(toIndentedString(requestTransferStatuses))
                .append("\n");
        sb.append("    requestTransferClass: ")
                .append(toIndentedString(requestTransferClass))
                .append("\n");
        sb.append("    requestTransferTerm: ")
                .append(toIndentedString(requestTransferTerm))
                .append("\n");
        sb.append("    hasNext: ").append(toIndentedString(hasNext)).append("\n");
        sb.append("    nextItemKey: ").append(toIndentedString(nextItemKey)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
