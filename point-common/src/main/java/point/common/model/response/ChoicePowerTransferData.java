package point.common.model.response;

import java.util.Date;
import lombok.*;
import point.common.constant.ChoicePowerTransferType;
import point.common.model.dto.PointUserDTO;
import point.common.model.dto.UserDTO;
import point.common.model.dto.UserIdentityDTO;

/** admin パワー履歴一覧 使用的 不要轻易改动 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChoicePowerTransferData {

    private Long id;

    private Long userId;

    private Long amount;

    private ChoicePowerTransferType transferType;

    private Long choiceActivityRuleId;

    private String description;

    private UserIdentityDTO userIdentity;

    private PointUserDTO operatorPointUser;

    private UserDTO investPointUser;

    private Date createdAt;

    private Date updatedAt;
}
