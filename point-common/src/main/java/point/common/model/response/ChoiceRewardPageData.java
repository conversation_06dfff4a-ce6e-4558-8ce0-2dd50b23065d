package point.common.model.response;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChoiceRewardPageData {

    // ID
    private Long id;

    // ユーザーID(投票)
    private Long userId;

    //    // ユーザーID(変更)
    //    private Long userIdChange;

    // 獲得ポイント数
    private BigDecimal rewardPoints;

    // 報酬分配日時
    private Date rewardDate;

    // 獲得ポイント残高
    private BigDecimal rewardBalance;

    // 引き出すポイント数
    private BigDecimal withdrawalPoints;

    // 引出日時
    private Date withdrawalDate;
}
