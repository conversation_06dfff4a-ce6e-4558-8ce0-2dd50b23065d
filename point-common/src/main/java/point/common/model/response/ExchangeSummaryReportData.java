package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.constant.ReportLabel.HeaderPointSummary;
import point.common.entity.ExchangeSummary;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
    "id",
    "targetAt",
    "currency",
    "currentAmount",
    "jpyConversion",
    "depositAmount",
    "depositAmountJpy",
    "depositFee",
    "depositFeeJpy",
    "withdrawalAmount",
    "withdrawalAmountJpy",
    "withdrawalFee",
    "withdrawalFeeJpy",
    "transactionFee",
    "transactionFeeJpy",
    "simpleMarketPosTradeBuyAmount",
    "simpleMarketPosTradeBuyAmountJpy",
    "simpleMarketPosTradeSellAmount",
    "simpleMarketPosTradeSellAmountJpy",
    "simpleMarketPosTradeFee",
    "simpleMarketPosTradeFeeJpy",
    "rewardAccumulate",
    "rewardAccumulateJpy",
    "createdAt",
    "updatedAt"
})
public class ExchangeSummaryReportData implements Serializable {

    private static final long serialVersionUID = -709424307772900620L;

    @Getter @Setter private Long id;

    @Getter @Setter private String targetAt;

    @Getter @Setter private String currency;

    @Getter @Setter private String currentAmount = "0";

    @Getter @Setter private String jpyConversion = "0";

    @Getter @Setter private String depositAmount = "0";

    @Getter @Setter private String depositAmountJpy = "0";

    @Getter @Setter private String depositFee = "0";

    @Getter @Setter private String depositFeeJpy = "0";

    @Getter @Setter private String withdrawalAmount = "0";

    @Getter @Setter private String withdrawalAmountJpy = "0";

    @Getter @Setter private String withdrawalFee = "0";

    @Getter @Setter private String withdrawalFeeJpy = "0";

    @Getter @Setter private String transactionFee = "0";

    @Getter @Setter private String transactionFeeJpy = "0";

    @Getter @Setter private String simpleMarketPosTradeBuyAmount = "0";

    @Getter @Setter private String simpleMarketPosTradeBuyAmountJpy = "0";

    @Getter @Setter private String simpleMarketPosTradeSellAmount = "0";

    @Getter @Setter private String simpleMarketPosTradeSellAmountJpy = "0";

    @Getter @Setter private String simpleMarketPosTradeFee = "0";

    @Getter @Setter private String simpleMarketPosTradeFeeJpy = "0";

    @Getter @Setter private String rewardAccumulate = "0";

    @Getter @Setter private String rewardAccumulateJpy = "0";

    @Getter @Setter private String createdAt;

    @Getter @Setter private String updatedAt;

    public ExchangeSummaryReportData setProperties(ExchangeSummary exchangeSummary) {

        this.setId(exchangeSummary.getId());

        this.setTargetAt(
                FormatUtil.formatJst(
                        exchangeSummary.getTargetAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));

        this.setCurrency(exchangeSummary.getCurrency().getName());

        NumberFormat numberFormat = NumberFormat.getNumberInstance();

        this.setCurrentAmount(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getCurrentAmount(),
                        numberFormat));
        this.setJpyConversion(
                toStrigForReport(Currency.JPY, exchangeSummary.getJpyConversion(), numberFormat));

        this.setDepositAmount(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getDepositAmount(),
                        numberFormat));
        this.setDepositAmountJpy(
                toStrigForReport(
                        Currency.JPY, exchangeSummary.getDepositAmountJpy(), numberFormat));

        this.setDepositFee(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getDepositFee(),
                        numberFormat));
        this.setDepositFeeJpy(
                toStrigForReport(Currency.JPY, exchangeSummary.getDepositFeeJpy(), numberFormat));

        this.setWithdrawalAmount(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getWithdrawalAmount(),
                        numberFormat));
        this.setWithdrawalAmountJpy(
                toStrigForReport(
                        Currency.JPY, exchangeSummary.getWithdrawalAmountJpy(), numberFormat));

        this.setWithdrawalFee(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getWithdrawalFee(),
                        numberFormat));
        this.setWithdrawalFeeJpy(
                toStrigForReport(
                        Currency.JPY, exchangeSummary.getWithdrawalFeeJpy(), numberFormat));

        this.setTransactionFee(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getTransactionFee(),
                        numberFormat));
        this.setTransactionFeeJpy(
                toStrigForReport(
                        Currency.JPY, exchangeSummary.getTransactionFeeJpy(), numberFormat));

        this.setSimpleMarketPosTradeBuyAmount(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getSimpleMarketPosTradeBuyAmount(),
                        numberFormat));

        this.setSimpleMarketPosTradeBuyAmountJpy(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getSimpleMarketPosTradeBuyAmountJpy(),
                        numberFormat));

        this.setSimpleMarketPosTradeSellAmount(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getSimpleMarketPosTradeSellAmount(),
                        numberFormat));

        this.setSimpleMarketPosTradeSellAmountJpy(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getSimpleMarketPosTradeSellAmountJpy(),
                        numberFormat));

        this.setSimpleMarketPosTradeFee(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getSimpleMarketPosTradeFee(),
                        numberFormat));

        this.setSimpleMarketPosTradeFeeJpy(
                toStrigForReport(
                        exchangeSummary.getCurrency(),
                        exchangeSummary.getSimpleMarketPosTradeFeeJpy(),
                        numberFormat));

        BigDecimal amount =
                exchangeSummary
                        .getExpirationNotContinueReward()
                        .add(exchangeSummary.getExpirationContinueReward());

        this.setRewardAccumulate(
                toStrigForReport(exchangeSummary.getCurrency(), amount, numberFormat));

        this.setRewardAccumulateJpy(
                toStrigForReport(
                        Currency.JPY,
                        amount.multiply(exchangeSummary.getJpyConversion()),
                        numberFormat));

        this.setCreatedAt(
                FormatUtil.formatJst(
                        exchangeSummary.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));

        this.setUpdatedAt(
                FormatUtil.formatJst(
                        exchangeSummary.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
        return this;
    }

    private String toStrigForReport(
            Currency currency, BigDecimal value, NumberFormat numberFormat) {
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue =
                currency.getScaledAmount(value, RoundingMode.FLOOR)
                        .stripTrailingZeros()
                        .toPlainString();
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return numberFormat.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return numberFormat.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }

    public static String getReportHeader() {
        return HeaderPointSummary.ID.getLabel()
                + ","
                + HeaderPointSummary.TARGET_AT.getLabel()
                + ","
                + HeaderPointSummary.CURRENCY.getLabel()
                + ","
                + HeaderPointSummary.CURRENT_AMOUNT.getLabel()
                + ","
                + HeaderPointSummary.JPY_CONVERSION.getLabel()
                + ","
                + HeaderPointSummary.DEPOSIT_AMOUNT.getLabel()
                + ","
                + HeaderPointSummary.DEPOSIT_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummary.DEPOSIT_FEE.getLabel()
                + ","
                + HeaderPointSummary.DEPOSIT_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummary.WITHDRAWAL_AMOUNT.getLabel()
                + ","
                + HeaderPointSummary.WITHDRAWAL_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummary.WITHDRAWAL_FEE.getLabel()
                + ","
                + HeaderPointSummary.WITHDRAWAL_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummary.TRANSACTION_FEE.getLabel()
                + ","
                + HeaderPointSummary.TRANSACTION_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummary.SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT.getLabel()
                + ","
                + HeaderPointSummary.SIMPLE_MARKET_POS_TRADE_BUY_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummary.SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT.getLabel()
                + ","
                + HeaderPointSummary.SIMPLE_MARKET_POS_TRADE_SELL_AMOUNT_JPY.getLabel()
                + ","
                + HeaderPointSummary.SIMPLE_MARKET_POS_TRADE_FEE.getLabel()
                + ","
                + HeaderPointSummary.SIMPLE_MARKET_POS_TRADE_FEE_JPY.getLabel()
                + ","
                + HeaderPointSummary.REWARD_ACCUMULATE.getLabel()
                + ","
                + HeaderPointSummary.REWARD_ACCUMULATE_JPY.getLabel()
                + ","
                + HeaderPointSummary.CREATED_AT.getLabel()
                + ","
                + HeaderPointSummary.UPDATED_AT.getLabel();
    }
}
