package point.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChoiceRewardResponseData implements Serializable {

    private List<ChoiceRewardHistoryData> rewardHistory;

    private BigDecimal monthRewardAmount;
}
