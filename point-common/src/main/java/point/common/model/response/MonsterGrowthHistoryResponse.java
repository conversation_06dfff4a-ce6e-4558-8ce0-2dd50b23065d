package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MonsterGrowthHistoryResponse implements Serializable {

    private Long id;
    private Long userId;
    private String symbol; // 銘柄
    private String crop; // 作物
    private BigDecimal income; // 売却損益
    private String conversionRate; // 変換割合
    private String weekFood; // 今月食べたもの
    private String bonusRate; // ボーナス割合
    private Long experienceEarned; // 獲得経験値
    private Long currentExperience; // 現在経験値
    private Long nextLevelExperience; // 次のレベルの経験値
    private String growthRate; // 成長比率
    private Long levelBefore; // レベル(食前)
    private Long levelAfter; // レベル(食後)
    private Date lastUpdateTime; // 最新更新日時
}
