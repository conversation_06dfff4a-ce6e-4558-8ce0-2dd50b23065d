package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.Currency;
import point.common.constant.CurrencyPair;
import point.common.entity.CurrencyPairConfig;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CurrencyPairDisplayData implements Serializable {
    private static final long serialVersionUID = 1L;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private CurrencyPair currencyPair;

    @Getter @Setter private Long id;

    @Getter @Setter private BigDecimal quotePrecision;

    @Getter @Setter private BigDecimal precision;

    @Getter @Setter private BigDecimal volumePrecision;

    @Getter @Setter private BigDecimal minOrderAmount;

    @Getter @Setter private BigDecimal maxOrderAmount;

    @Getter @Setter private Currency baseCurrency;

    @Getter @Setter private BigDecimal basePrecision;

    @Getter @Setter private BigDecimal baseVolumePrecision;

    @Getter @Setter private Currency quoteCurrency;

    @Getter @Setter private BigDecimal quoteVolumePrecision;

    public CurrencyPairDisplayData(CurrencyPairConfig currencyPairConfig) {
        this.currencyPair = currencyPairConfig.getCurrencyPair();
        this.minOrderAmount = currencyPairConfig.getMinOrderAmount();
        this.maxOrderAmount = currencyPairConfig.getMaxOrderAmount();
    }
}
