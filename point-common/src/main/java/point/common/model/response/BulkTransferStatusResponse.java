package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** BulkTransferStatusResponse */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BulkTransferStatusResponse {
    @JsonAlias("acceptanceKeyClass")
    private String acceptanceKeyClass;

    @JsonAlias("detailInfoNecessity")
    private Boolean detailInfoNecessity;

    @JsonAlias("bulktransferItemKey")
    private String bulktransferItemKey;

    @JsonAlias("baseDate")
    private String baseDate;

    @JsonAlias("baseTime")
    private String baseTime;

    @JsonAlias("count")
    private String count;

    @JsonAlias("transferQueryBulkResponses")
    private List<TransferQueryBulkResponse> transferQueryBulkResponses;

    @JsonAlias("bulkTransferDetails")
    private List<BulkTransferDetail> bulkTransferDetails;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BulkTransferStatusResponse {\n");

        sb.append("    acceptanceKeyClass: ")
                .append(toIndentedString(acceptanceKeyClass))
                .append("\n");
        sb.append("    detailInfoNecessity: ")
                .append(toIndentedString(detailInfoNecessity))
                .append("\n");
        sb.append("    bulktransferItemKey: ")
                .append(toIndentedString(bulktransferItemKey))
                .append("\n");
        sb.append("    baseDate: ").append(toIndentedString(baseDate)).append("\n");
        sb.append("    baseTime: ").append(toIndentedString(baseTime)).append("\n");
        sb.append("    count: ").append(toIndentedString(count)).append("\n");
        sb.append("    transferQueryBulkResponses: ")
                .append(toIndentedString(transferQueryBulkResponses))
                .append("\n");
        sb.append("    bulkTransferDetails: ")
                .append(toIndentedString(bulkTransferDetails))
                .append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
