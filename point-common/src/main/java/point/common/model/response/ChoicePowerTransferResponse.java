package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ChoicePowerTransferType;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChoicePowerTransferResponse implements Serializable {

    private static final long serialVersionUID = -5680701498263865050L;

    @Getter @Setter private Long id;

    @Getter @Setter private String activityName;

    @Getter @Setter private Date createdAt;

    @Getter @Setter private Long amount;

    @Getter @Setter private ChoicePowerTransferType transferType;
}
