package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ReportLabel.HeaderHighValueTrader;
import point.common.entity.HighValueTrader;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({"userId", "posOrderLimits", "posTradeMarkets", "createdAt", "updatedAt"})
public class HighValueTraderReportData implements Serializable {

    private static final long serialVersionUID = 7571064377054142620L;

    @Getter @Setter private Long userId;

    @Getter @Setter private String posOrderLimits;

    @Getter @Setter private String posTradeMarkets;

    @Getter @Setter private String createdAt;

    @Getter @Setter private String updatedAt;

    public HighValueTraderReportData setProperties(HighValueTrader highValueTrader) {
        this.setUserId(highValueTrader.getUser().getId());

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        this.setPosOrderLimits(numberFormat.format(highValueTrader.getPosOrderLimits()));
        this.setPosTradeMarkets(numberFormat.format(highValueTrader.getPosTradeMarkets()));

        this.setCreatedAt(highValueTrader.getFormattedCreatedAt());
        this.setUpdatedAt(highValueTrader.getFormattedUpdatedAt());

        return this;
    }

    public static String getReportHeader() {
        return HeaderHighValueTrader.USER_ID.getLabel()
                + ","
                + HeaderHighValueTrader.POS_ORDER_LIMITS.getLabel()
                + ","
                + HeaderHighValueTrader.POS_TRADE_MARKETS.getLabel()
                + ","
                + HeaderHighValueTrader.CREATED_AT.getLabel()
                + ","
                + HeaderHighValueTrader.UPDATED_AT.getLabel();
    }
}
