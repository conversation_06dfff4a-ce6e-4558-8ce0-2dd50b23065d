package point.common.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.listener.ChannelTopic;

@Configuration
public class WebSocketChannelConfig {

    @Bean
    ChannelTopic assetTopic() {
        return new ChannelTopic("pubsub:asset");
    }

    @Bean
    ChannelTopic currencyPairTopic() {
        return new ChannelTopic("pubsub:currencypair");
    }

    @Bean
    ChannelTopic posCandlestickTopic() {
        return new ChannelTopic("pubsub:poscandlestick");
    }

    @Bean
    ChannelTopic posPriceTopic() {
        return new ChannelTopic("pubsub:posprice");
    }

    @Bean
    ChannelTopic posTradeUpdateTopic() {
        return new ChannelTopic("pubsub:postrade");
    }

    @Bean
    ChannelTopic choiceActivityTopic() {
        return new ChannelTopic("pubsub:choiceactivity");
    }
}
