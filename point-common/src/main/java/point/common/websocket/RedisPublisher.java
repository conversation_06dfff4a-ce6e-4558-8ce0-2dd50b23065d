package point.common.websocket;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.stereotype.Service;
import point.common.component.CustomRedisTemplate;
import point.common.constant.ChoiceActivityVoteAction;
import point.common.entity.Asset;
import point.common.entity.ChoiceVote;
import point.common.entity.CurrencyPairConfig;
import point.common.model.response.websocket.*;
import point.pos.entity.PosCandlestick;
import point.pos.entity.PosTrade;
import point.pos.model.PosBestPriceData;

@RequiredArgsConstructor
@Service
@Slf4j
public class RedisPublisher {
    private final CustomRedisTemplate<String> redisTemplate;
    private final CustomRedisTemplate<AssetDataWrapper> assetDataRedisTemplate;
    private final CustomRedisTemplate<CurrencyPairConfigDataWrapper> currencyPairConfigTemplate;
    private final CustomRedisTemplate<PosCandlestickDataWrapper> posCandlestickDataRedisTemplate;
    private final CustomRedisTemplate<PosBestPriceData> posBestPriceDataRedisTemplate;
    private final CustomRedisTemplate<PosTradeDataWrapper> posTradePriceDataRedisTemplate;
    private final CustomRedisTemplate<ChoiceActivityDataWrapper> choiceActivityDataRedisTemplate;
    private final ChannelTopic assetTopic;
    private final ChannelTopic currencyPairTopic;
    private final ChannelTopic posCandlestickTopic;
    private final ChannelTopic posPriceTopic;
    private final ChannelTopic posTradeUpdateTopic;
    private final ChannelTopic choiceActivityTopic;
    public boolean websocketEnabled = true;

    @Value("${exchange-websocket.redis-pubsub-cache.expire-in-minutes:5}")
    public int cacheExpireMinutes;

    @Value("${exchange-websocket.redis-pubsub-cache.enabled:true}")
    public boolean cacheEnabled;

    public void publish(final Asset entity) {
        if (isWebsocketDisabled()) {
            return;
        }
        // No cache
        assetDataRedisTemplate.convertAndSend(assetTopic.getTopic(), new AssetDataWrapper(entity));
    }

    public void publish(final CurrencyPairConfig entity) {
        if (isWebsocketDisabled()) {
            return;
        }
        // No cache
        currencyPairConfigTemplate.convertAndSend(
                currencyPairTopic.getTopic(), new CurrencyPairConfigDataWrapper(entity));
    }

    public void publish(
            final ChoiceVote choiceVote,
            ChoiceActivityVoteAction voteAction,
            @Nullable Long balanceVote) {
        if (isWebsocketDisabled()) {
            return;
        }
        ChoiceActivityDataWrapper dataWrapper =
                new ChoiceActivityDataWrapper(choiceVote, voteAction, balanceVote);
        String cacheKey = dataWrapper.getCacheKey();
        String checksum = dataWrapper.getChecksum();
        if (cached(cacheKey, checksum)) {
            log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
            return;
        }
        log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);
        choiceActivityDataRedisTemplate.convertAndSend(choiceActivityTopic.getTopic(), dataWrapper);
    }

    public void publish(final PosCandlestick entity) {
        if (isWebsocketDisabled()) {
            return;
        }
        PosCandlestickDataWrapper dataWrapper = new PosCandlestickDataWrapper(entity);
        String cacheKey = dataWrapper.getCacheKey();
        String checksum = dataWrapper.getChecksum();
        if (cached(cacheKey, checksum)) {
            log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
            return;
        }
        log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);
        posCandlestickDataRedisTemplate.convertAndSend(
                posCandlestickTopic.getTopic(), new PosCandlestickDataWrapper(entity));
    }

    public void publish(final PosBestPriceData entity) {
        if (isWebsocketDisabled()) {
            return;
        }
        PosBestPriceDataWrapper dataWrapper = new PosBestPriceDataWrapper(entity);
        String cacheKey = dataWrapper.getCacheKey();
        String checksum = dataWrapper.getChecksum();
        if (cached(cacheKey, checksum)) {
            log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
            return;
        }
        log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);

        posBestPriceDataRedisTemplate.convertAndSend(posPriceTopic.getTopic(), entity);
    }

    public void publish(final PosTrade entity) {
        if (isWebsocketDisabled()) {
            return;
        }
        // No cache
        posTradePriceDataRedisTemplate.convertAndSend(
                posTradeUpdateTopic.getTopic(), new PosTradeDataWrapper(entity));
    }

    // -- helper method
    private boolean isWebsocketDisabled() {
        boolean disabled = !websocketEnabled;
        if (disabled) {
            log.debug("WebSocket is disabled on this server.");
        }
        return disabled;
    }

    private boolean cached(String cacheKey, String checksum) {
        if (!cacheEnabled) {
            log.debug("WebSocket cached is disabled on this server.");
            return false;
        }
        if (checksum == null) {
            return false;
        }
        return !redisTemplate.setIfUncached(cacheKey, checksum, cacheExpireMinutes);
    }
}
